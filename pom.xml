<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.meituan.mdp</groupId>
        <artifactId>mdp-basic-parent</artifactId>
        <version>2.3.0</version>
        <relativePath/>
    </parent>

    <groupId>com.meituan.banma.llm.corpus</groupId>
    <artifactId>banma_llm_corpus</artifactId>
    <version>${revision}</version>
    <packaging>pom</packaging>
    <name>大模型语料处理服务</name>

    <modules>
        <module>banma_llm_corpus_client</module>
        <module>banma_llm_corpus_server</module>
    </modules>

    <properties>
        <java-version>1.8</java-version>
        <revision>1.0.0-SNAPSHOT</revision>
        <lombok.version>1.18.0</lombok.version>
        <org.mapstruct.version>1.2.0.Final</org.mapstruct.version>
        <rhino-client.version>1.5.0</rhino-client.version>
        <zebra.version>4.1.1</zebra.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>mss-java-sdk-s3</artifactId>
                <version>1.10.0</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.apache.httpcomponents</groupId>
                        <artifactId>httpclient</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>commons-logging</groupId>
                        <artifactId>commons-logging</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.dianping.dpsf</groupId>
                <artifactId>dpsf-net</artifactId>
                <version>3.8.1</version>
                <exclusions>
                    <exclusion>
                        <groupId>commons-logging</groupId>
                        <artifactId>commons-logging</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>javax.servlet</groupId>
                        <artifactId>servlet-api</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.meituan.banma.llm.corpus</groupId>
                <artifactId>banma_llm_corpus_client</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.meituan.banma.llm.corpus</groupId>
                <artifactId>banma_llm_corpus_server</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.meituan.mafka</groupId>
                <artifactId>mafka-client_2.10</artifactId>
                <version>3.9.1</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-log4j12</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>log4j</groupId>
                        <artifactId>log4j</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.meituan.octo</groupId>
                <artifactId>dorado-mesh</artifactId>
                <version>1.2.8</version>
            </dependency>
            <dependency>
                <groupId>com.meituan.service.inf.pki</groupId>
                <artifactId>mt-cert-sdk</artifactId>
                <version>1.2.2</version>
            </dependency>
            <dependency>
                <groupId>com.meituan.service.mobile</groupId>
                <artifactId>mtthrift</artifactId>
                <version>2.11.1</version>
                <exclusions>
                    <exclusion>
                        <groupId>log4j</groupId>
                        <artifactId>log4j</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-log4j12</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.google.code.findbugs</groupId>
                        <artifactId>annotations</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.sankuai.inf.dayu</groupId>
                <artifactId>dye-routing-all</artifactId>
                <version>1.3.0</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.rhino</groupId>
                <artifactId>rhino-client</artifactId>
                <version>${rhino-client.version}</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.rhino</groupId>
                <artifactId>rhino-cluster-limiter</artifactId>
                <version>1.5.0</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.rhino</groupId>
                <artifactId>rhino-extend</artifactId>
                <version>1.5.0</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.squirrel</groupId>
                <artifactId>squirrel-client</artifactId>
                <version>2.6.1</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.mockito</groupId>
                        <artifactId>mockito-core</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.dianping.zebra</groupId>
                <artifactId>zebra-api</artifactId>
                <version>4.1.1</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.junit.jupiter</groupId>
                        <artifactId>junit-jupiter-api</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.dianping.zebra</groupId>
                <artifactId>zebra-dao</artifactId>
                <version>4.1.1</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.zebra</groupId>
                <artifactId>zebra-ds-monitor-client</artifactId>
                <version>4.1.1</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.meituan.banma</groupId>
                <artifactId>qingniu_degrade</artifactId>
                <version>2.0.4</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.meituan.org</groupId>
                <artifactId>open-sdk</artifactId>
                <version>5.0.32</version>
                <exclusions>
                    <exclusion>
                        <groupId>commons-logging</groupId>
                        <artifactId>commons-logging</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-log4j12</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>log4j</groupId>
                        <artifactId>log4j</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.sankuai.octo</groupId>
                <artifactId>mns-invoker</artifactId>
                <version>1.15.1</version>
            </dependency>
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>5.1.49</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${org.mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok-mapstruct-binding</artifactId>
                <version>0.2.0</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok-mapstruct-binding</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                        <configuration>
                            <flattenMode>defaults</flattenMode>
                            <pomElements>
                                <parent>expand</parent>
                                <profiles>expand</profiles>
                                <dependencies>keep</dependencies>
                                <build>keep</build>
                            </pomElements>
                            <updatePomFile>true</updatePomFile>
                        </configuration>
                    </execution>
                    <execution>
                        <id>flatten-clean</id>
                        <phase>clean</phase>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.11.0</version>
                <configuration>
                    <source>${java-version}</source> <!-- or 1.7 or 1.8, .. -->
                    <target>${java-version}</target>
                    <encoding>UTF-8</encoding>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${org.mapstruct.version}</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
