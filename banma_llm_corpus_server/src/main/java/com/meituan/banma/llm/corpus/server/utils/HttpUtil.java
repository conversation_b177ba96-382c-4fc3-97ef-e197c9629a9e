package com.meituan.banma.llm.corpus.server.utils;

import com.alibaba.fastjson.JSONObject;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpUriRequest;
import org.apache.http.conn.HttpClientConnectionManager;
import lombok.Getter;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.io.IOException;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2024/12/4
 * http工具类, 提供全局httpclient, 提高资源利用率
 */
public class HttpUtil {
    private static final Logger log = LoggerFactory.getLogger(HttpUtil.class);

    // get获取HttpClient实例
    @Getter
    private static final CloseableHttpClient httpClient;

    // 空闲连接清理器实例
    private static final IdleConnectionEvictor connectionEvictor;

    // 初始化HttpClient
    static {
        PoolingHttpClientConnectionManager cm = new PoolingHttpClientConnectionManager();
        // 连接池总最大连接数100
        cm.setMaxTotal(100);
        // 路由最大连接数20
        cm.setDefaultMaxPerRoute(20);

        // TODO 优化配置请求参数
        RequestConfig requestConfig = RequestConfig.custom()
                .setConnectTimeout(3000) // 连接超时3秒
                .setSocketTimeout(5000) // 请求超时5秒
                .setConnectionRequestTimeout(2000) // 从连接池获取连接的超时2秒
                .build();

        httpClient = HttpClients.custom()
                .setConnectionManager(cm)
                .setDefaultRequestConfig(requestConfig)
                .build();

        // 启动空闲连接清理器
        connectionEvictor = new IdleConnectionEvictor(cm, 60, TimeUnit.SECONDS);
        connectionEvictor.start();

        // 注册关闭钩子，确保应用关闭时释放资源
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            try {
                httpClient.close();
                connectionEvictor.shutdown();
            } catch (IOException e) {
                log.error("Error closing HttpClient", e);
            }
        }));
    }

    /**
     * 空闲连接清理器类
     * 定期清理连接池中的空闲连接，避免资源泄露
     */
    private static class IdleConnectionEvictor extends Thread {
        private final HttpClientConnectionManager connMgr;
        private final long waitTime;
        private final TimeUnit timeUnit;
        private volatile boolean shutdown;

        public IdleConnectionEvictor(HttpClientConnectionManager connMgr, long waitTime, TimeUnit timeUnit) {
            this.connMgr = connMgr;
            this.waitTime = waitTime;
            this.timeUnit = timeUnit;
        }

        @Override
        public void run() {
            try {
                while (!shutdown) {
                    synchronized (this) {
                        wait(timeUnit.toMillis(waitTime));
                        connMgr.closeExpiredConnections();
                        connMgr.closeIdleConnections(waitTime, timeUnit);
                    }
                }
            } catch (InterruptedException ignored) {
                // 线程被中断，终止
            }
        }

        /**
         * 关闭清理器
         * 设置shutdown标志，并唤醒等待的线程
         */
        public void shutdown() {
            shutdown = true;
            synchronized (this) {
                notifyAll();
            }
        }
    }

    public static String doPost(String url, Map<String, String> headers, Map<String, Object> body) {
        HttpPost httpPost = new HttpPost(url);
        if (MapUtils.isNotEmpty(headers)) {
            headers.forEach(httpPost::addHeader);
        }
        String jsonString = JSONObject.toJSONString(body);
        httpPost.setEntity(new StringEntity(jsonString, ContentType.APPLICATION_JSON));
        logCurlCommand("POST", url, headers, jsonString);
        return executeRequest(httpPost);
    }

    /**
     * TT 相关使用，headers中添加了TT要求的字段
     */
    public static String doGet(String url, Map<String, String> headers) {
        HttpGet httpGet = new HttpGet(url);
        if (MapUtils.isNotEmpty(headers)) {
            headers.forEach(httpGet::addHeader);
        }
        logCurlCommand("GET", url, headers, null);
        return executeRequest(httpGet);
    }

    private static void logCurlCommand(String method, String url, Map<String, String> headers, String data) {
        StringBuilder curlCmdBuilder = new StringBuilder("curl -X ").append(method).append(" ");
        if (MapUtils.isNotEmpty(headers)) {
            headers.forEach(
                    (key, value) -> curlCmdBuilder.append("-H '").append(key).append(": ").append(value).append("' "));
        }
        if (StringUtils.isNotBlank(data)) {
            curlCmdBuilder.append("--data-raw '").append(data.replace("'", "\\'")).append("' ");
        }
        curlCmdBuilder.append(url);
        log.info("TicketQueryUtil {}: {}", method, curlCmdBuilder);
    }

    private static String executeRequest(HttpUriRequest request) {
        try (CloseableHttpResponse response = httpClient.execute(request)) {
            return EntityUtils.toString(response.getEntity(), "UTF-8");
        } catch (Exception e) {
            log.error("HTTP request error", e);
            throw new RuntimeException("Request failed", e);
        }
    }

}
