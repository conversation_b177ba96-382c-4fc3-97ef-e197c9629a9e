package com.meituan.banma.llm.corpus.server.common.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.ToString;

/**
 * Class IndexSchema
 * Project banma_llm_corpus_process_server
 *
 * <AUTHOR>
 * @date 2025/4/8
 * Description 索引描述
 */

@Data
@AllArgsConstructor
@ToString
public class IndexSchema {
    private String Non_unique;
    private String Key_name;
    private String Seq_in_index;
    private String Column_name;
    private String Cardinality;
}
