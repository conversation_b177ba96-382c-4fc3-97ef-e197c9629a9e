package com.meituan.banma.llm.corpus.server.service.impl;

import com.meituan.banma.llm.corpus.server.common.domain.dto.TicketRangeDTO;
import com.meituan.banma.llm.corpus.server.dal.entity.TicketRangeEntity;
import com.meituan.banma.llm.corpus.server.dal.mapper.TicketRangeMapper;
import com.meituan.banma.llm.corpus.server.service.ITicketRangeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 工单范围信息服务实现类
 */
@Slf4j
@Service
public class TicketRangeServiceImpl implements ITicketRangeService {
    
    @Resource
    private TicketRangeMapper ticketRangeMapper;
    
    @Override
    public boolean saveTicketRange(TicketRangeDTO rangeDTO) {
        if (rangeDTO == null || rangeDTO.getTicketId() == null) {
            log.warn("保存工单范围信息失败，参数为空");
            return false;
        }
        
        try {
            // 先查询是否已存在该工单记录
            TicketRangeEntity existingEntity = ticketRangeMapper.findByTicketId(rangeDTO.getTicketId());
            
            if (existingEntity != null) {
                // 使用已有的ID，确保更新而不是插入
                rangeDTO.setId(existingEntity.getId());
                log.debug("发现已有工单记录，保留原记录ID: ticketId={}, id={}",
                        rangeDTO.getTicketId(), existingEntity.getId());
            }
            
            // 使用insertOrUpdate方法更新或插入记录
            TicketRangeEntity entity = convertToEntity(rangeDTO);
            int rows;
            
            if (existingEntity != null) {
                // 如果存在记录，执行更新操作
                rows = ticketRangeMapper.updateByPrimaryKey(entity);
                log.debug("更新已有工单记录: ticketId={}, id={}", entity.getTicketId(), entity.getId());
            } else {
                // 如果不存在记录，执行插入操作
                rows = ticketRangeMapper.insert(entity);
                log.debug("插入新工单记录: ticketId={}", entity.getTicketId());
            }
            
            if (rows > 0) {
                log.info("保存工单范围信息成功, ticketId={}, isUpdate={}", 
                        rangeDTO.getTicketId(), existingEntity != null);
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error("保存工单范围信息异常, ticketId={}, error={}", 
                    rangeDTO.getTicketId(), e.getMessage(), e);
            return false;
        }
    }
    
    @Override
    public int batchSaveTicketRange(List<TicketRangeDTO> rangeDTOList) {
        if (rangeDTOList == null || rangeDTOList.isEmpty()) {
            return 0;
        }
        
        int successCount = 0;
        for (TicketRangeDTO rangeDTO : rangeDTOList) {
            if (saveTicketRange(rangeDTO)) {
                successCount++;
            }
        }
        
        log.info("批量保存工单范围信息完成, 总数={}, 成功数={}", rangeDTOList.size(), successCount);
        return successCount;
    }
    
    @Override
    public TicketRangeDTO getByTicketId(String ticketId) {
        if (ticketId == null) {
            return null;
        }
        
        try {
            TicketRangeEntity entity = ticketRangeMapper.findByTicketId(ticketId);
            return convertToDTO(entity);
        } catch (Exception e) {
            log.error("查询工单范围信息异常, ticketId={}, error={}", ticketId, e.getMessage(), e);
            return null;
        }
    }
    
    @Override
    public TicketRangeDTO getByTicketIdAndRgId(String ticketId, Long rgId) {
        if (ticketId == null || rgId == null) {
            return null;
        }
        
        try {
            TicketRangeEntity entity = ticketRangeMapper.findByTicketIdAndRgId(ticketId, rgId);
            return convertToDTO(entity);
        } catch (Exception e) {
            log.error("查询工单范围信息异常, ticketId={}, rgId={}, error={}", ticketId, rgId, e.getMessage(), e);
            return null;
        }
    }
    
    @Override
    public List<TicketRangeDTO> getByTicketIds(List<String> ticketIds) {
        if (ticketIds == null || ticketIds.isEmpty()) {
            return Collections.emptyList();
        }
        
        try {
            List<TicketRangeEntity> entities = ticketRangeMapper.findByTicketIds(ticketIds);
            if (entities == null || entities.isEmpty()) {
                return Collections.emptyList();
            }
            
            return entities.stream()
                    .map(this::convertToDTO)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("批量查询工单范围信息异常, ticketIds={}, error={}", ticketIds, e.getMessage(), e);
            return Collections.emptyList();
        }
    }
    
    /**
     * 将DTO转换为实体对象
     */
    private TicketRangeEntity convertToEntity(TicketRangeDTO dto) {
        if (dto == null) {
            return null;
        }
        
        TicketRangeEntity entity = new TicketRangeEntity();
        BeanUtils.copyProperties(dto, entity);
        return entity;
    }
    
    /**
     * 将实体对象转换为DTO
     */
    private TicketRangeDTO convertToDTO(TicketRangeEntity entity) {
        if (entity == null) {
            return null;
        }
        
        TicketRangeDTO dto = new TicketRangeDTO();
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }
} 