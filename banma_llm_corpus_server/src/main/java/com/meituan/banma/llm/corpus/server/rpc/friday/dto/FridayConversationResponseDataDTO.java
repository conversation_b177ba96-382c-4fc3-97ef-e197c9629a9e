package com.meituan.banma.llm.corpus.server.rpc.friday.dto;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import lombok.ToString;

import java.util.List;

@Data
@ToString
public class FridayConversationResponseDataDTO {
    private String conversationId;
    private String requestId;
    private String messageId;
    private List<Content> contents;
    private MetaInfo metaInfo;
    private String rid;
    private String appid;
    private String status;
    private Long duration;
    private List<VerboseContent> verboseContents;

    public static class VerboseContent {
        private String type;
        private String title;
        private String content;
    }
    // 内部类 Content
    public static class Content {
        private String type;
        private String text;
        private String href;

        // Getters and Setters
        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public String getText() {
            return text;
        }

        public void setText(String text) {
            this.text = text;
        }

        public String getHref() {
            return href;
        }

        public void setHref(String href) {
            this.href = href;
        }
    }

    // 内部类 MetaInfo
    public static class MetaInfo {
        private String modelName;
        private List<Workflow> workflow;

        // Getters and Setters
        public String getModelName() {
            return modelName;
        }

        public void setModelName(String modelName) {
            this.modelName = modelName;
        }

        public List<Workflow> getWorkflow() {
            return workflow;
        }

        public void setWorkflow(List<Workflow> workflow) {
            this.workflow = workflow;
        }
    }

    // 内部类 Workflow
    public static class Workflow {
        private String workflowId;
        private String nodeId;
        private String nodeSubType;
        private String runStatus;
        private List<Output> outputs;

        // Getters and Setters
        public String getWorkflowId() {
            return workflowId;
        }

        public void setWorkflowId(String workflowId) {
            this.workflowId = workflowId;
        }

        public String getNodeId() {
            return nodeId;
        }

        public void setNodeId(String nodeId) {
            this.nodeId = nodeId;
        }

        public String getNodeSubType() {
            return nodeSubType;
        }

        public void setNodeSubType(String nodeSubType) {
            this.nodeSubType = nodeSubType;
        }

        public String getRunStatus() {
            return runStatus;
        }

        public void setRunStatus(String runStatus) {
            this.runStatus = runStatus;
        }

        public List<Output> getOutputs() {
            return outputs;
        }

        public void setOutputs(List<Output> outputs) {
            this.outputs = outputs;
        }
    }

    // 内部类 Output
    public static class Output {
        private String name;
        private List<JSONObject> value;

        // Getters and Setters
        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public List<JSONObject> getValue() {
            return value;
        }

        public void setValue(List<JSONObject> value) {
            this.value = value;
        }
    }
}