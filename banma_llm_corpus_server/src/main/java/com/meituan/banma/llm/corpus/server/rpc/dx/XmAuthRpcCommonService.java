package com.meituan.banma.llm.corpus.server.rpc.dx;

import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.xm.openplatform.auth.entity.AccessTokenResp;
import com.sankuai.xm.openplatform.auth.entity.AppAuthInfo;
import com.sankuai.xm.openplatform.auth.service.XmAuthServiceI;
import groovy.util.logging.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @date 2025/3/25
 * 办公开放平台通用token。访问大象，学城的api都需要带这个token
 */
@Slf4j
@Service
public class XmAuthRpcCommonService {
    private static final Logger log = LoggerFactory.getLogger(XmAuthRpcCommonService.class);

    private final static long FIVE_MINUTES = 1000 * 60 * 5;

    // <appKey, token>
    private Map<String, String> accessTokenMap = new ConcurrentHashMap<>();

    // <appKey, expireTime>
    private Map<String, Long> expireTimeMap = new ConcurrentHashMap<>();

    @Resource
    private XmAuthServiceI.Iface xmAuthService;

    @MdpConfig("robot_app_id_secret_mapping:{\"6615a022218201u3\":\"68f3144df45c15dbffa12d8fc9eece11\"}")
    private HashMap<String, String> appKeySecretMap;

    public String getToken(String appKey) {
        String appSecret = appKeySecretMap.get(appKey);
        if (StringUtils.isBlank(appSecret)) {
            throw new RuntimeException("XmAuthRpcCommonService#根据robot appId获取appSecret为空");
        }
        return getToken(appKey, appSecret);
    }

        // 获取Token（appKey其实就是办公后台的App ID）
    // getToken本身qps不高，就不按appKey维度加锁了，并发场景也就多请求一次accessToken
    public String getToken(String appKey, String appSecret) {
        // 获取当前token
        String accessToken = accessTokenMap.getOrDefault(appKey, "");

        // 获取当前token过期时间
        Long expireTime = expireTimeMap.getOrDefault(appKey, 0L);

        // 如果当前Token未过期且距离过期时间超过五分钟，直接返回当前Token
        long nowTime = System.currentTimeMillis();
        if (expireTime - FIVE_MINUTES > nowTime) {
            return accessToken;
        }

        AppAuthInfo appAuthInfo = new AppAuthInfo();
        appAuthInfo.setAppkey(appKey);
        appAuthInfo.setAppSecret(appSecret);
        try {
            AccessTokenResp resp = xmAuthService.accessToken(appAuthInfo);
            log.info("生成开放平台Token:{}", resp);
            if (resp.status.getCode() == 0) {
                accessToken = resp.getAccessToken().getToken();
                accessTokenMap.put(appKey, accessToken);
                expireTime = resp.getAccessToken().getExpireTime();
                expireTimeMap.put(appKey, expireTime);
            } else {
                throw new RuntimeException("生成开放平台Token失败");
            }
            return accessToken;
        } catch (TException e) {
            log.error("生成开放平台Token失败", e);
            throw new RuntimeException("生成开放平台Token失败", e);
        }
    }
}
