package com.meituan.banma.llm.corpus.server.thrift;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dianping.pigeon.util.CollectionUtils;
import com.dianping.rhino.Rhino;
import com.dianping.rhino.threadpool.DefaultThreadPoolProperties;
import com.dianping.rhino.threadpool.ThreadPool;
import com.google.gson.Gson;
import com.meituan.banma.llm.corpus.api.exception.LlmCorpusException;
import com.meituan.banma.llm.corpus.server.common.MtConfigService;
import com.meituan.banma.llm.corpus.server.common.domain.dto.*;
import com.meituan.banma.llm.corpus.server.common.constants.enums.ConvertTaskPlatformId;
import com.meituan.banma.llm.corpus.server.rpc.aiops.AiOpsRpcService;
import com.meituan.banma.llm.corpus.server.rpc.dx.XmAuthRpcService;
import com.meituan.banma.llm.corpus.server.service.ICorpusBotChatService;
import com.meituan.banma.llm.corpus.server.service.IDxGroupChatService;
import com.meituan.banma.llm.corpus.server.service.IFridayService;
import com.meituan.banma.llm.corpus.server.service.IKnowledgeBaseService;
import com.meituan.banma.llm.corpus.server.service.ITicketQueryService;
import com.meituan.banma.llm.corpus.server.utils.RgQueryUtil;
import com.meituan.banma.llm.corpus.server.utils.TicketQueryUtil;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftServer;
import com.sankuai.banma.jarvis.aiops.api.request.ChatSummaryRequest;
import com.sankuai.meituan.banma.qingniu.degrade.utils.QingniuDegradeUtils;
import com.sankuai.xm.openplatform.api.entity.*;
import com.sankuai.xm.openplatform.api.service.open.OpenCardServiceI;
import com.sankuai.xm.openplatform.callback.api.XmOpenCallbackServiceI;
import com.sankuai.xm.openplatform.callback.constant.CallBackEventTypeEnum;
import com.sankuai.xm.openplatform.callback.event.OpenEvent;
import com.sankuai.xm.openplatform.callback.event.data.GroupMsgEvent;
import com.sankuai.xm.openplatform.callback.event.data.PubMsgEvent;
import com.sankuai.xm.openplatform.common.entity.EmptyResp;
import com.sankuai.xm.openplatform.common.entity.RespStatus;
import com.sankuai.xm.openplatform.common.enums.ResCodeEnum;
import groovy.util.logging.Slf4j;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

import static com.meituan.banma.llm.corpus.server.service.impl.TicketQueryServiceImpl.parseJsonToRgInfoDTOList;
import static com.sankuai.xm.openplatform.callback.constant.CallBackEventTypeEnum.*;

import java.util.HashMap;
import java.util.Map;
import com.meituan.banma.llm.corpus.server.utils.PushContentUtils;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 大象机器人回调类
 */
@Slf4j
@MdpThriftServer
public class EventCallbackServiceImpl implements XmOpenCallbackServiceI.Iface {
    private static final Logger log = LoggerFactory.getLogger(EventCallbackServiceImpl.class);

    @Resource
    private IDxGroupChatService dxGroupChatService;

    @Resource
    private ITicketQueryService ticketQueryService;

    @Resource
    private IKnowledgeBaseService knowledgeBaseService;

    @Resource
    private OpenCardServiceI.Iface openCardService;

    @Resource
    private XmAuthRpcService xmAuthRpcService;

    @Resource
    private MtConfigService mtConfigService;

    @Resource
    private RgQueryUtil rgQueryUtil;

    @Resource
    private TicketQueryUtil ticketQueryUtil;

    @Resource
    private IFridayService fridayService;

    @Resource
    private AiOpsRpcService aiOpsRpcService;

    @Autowired
    private ICorpusBotChatService corpusBotChatService;

    // 触发词
    private static final String ADD_KNOWLEDGE = "加知识库";

    private static final ThreadPool threadPool = Rhino.newThreadPool("threadPool-event",
            DefaultThreadPoolProperties.Setter().withCoreSize(10).withMaxSize(20));

    @Override
    public EmptyResp eventCallback(int eventType, String jsonEvent) {
        log.info("event = {}", jsonEvent);
        EmptyResp emptyResp = new EmptyResp();
        try {
            // 异步处理机器人回调事件
            threadPool.execute(() -> {
                try {
                    handOpenEvent(jsonEvent);
                } catch (LlmCorpusException e) {
                    log.error("Error in handOpenEvent", e);
                } catch (Exception e) {
                    log.error("Unexpected error in handOpenEvent", e);
                }
            });
        } catch (Exception e) {
            log.error("Failed to execute event callback task", e);
        }
        RespStatus status = new RespStatus();
        status.setCode(ResCodeEnum.SUCCESS.getCode());
        status.setMsg(ResCodeEnum.SUCCESS.getMsg());
        emptyResp.setStatus(status);
        return emptyResp;
    }

    private void handOpenEvent(String jsonEvent) throws LlmCorpusException {
        OpenEvent<JSONObject> openEvent = JSON.parseObject(jsonEvent, OpenEvent.class);
        CallBackEventTypeEnum eventTypeEnum = openEvent.getEventTypeEnum();
        JSONObject data = openEvent.getData();

        // 保存消息记录
        if (eventTypeEnum == GROUP_MESSAGE || eventTypeEnum == ROBOT_SINGLE_CHAT_MESSAGE) {
            try{
                handleSaveChatMessageRecord(data, eventTypeEnum);
            }catch (Exception e){
                log.error("handleSaveChatMessageRecord error,event:{}", jsonEvent, e);
            }
        }

        // 解析GROUP_MESSAGE类型, 识别"加知识库"关键词
        if (eventTypeEnum == GROUP_MESSAGE) {
            GroupMsgEvent groupMsgEvent = JSON.parseObject(data.toJSONString(), GroupMsgEvent.class);
            // 如果是"加知识库", 进行处理
            if (isAddKnowledge(groupMsgEvent)) {
                processAddKnowledgeRequest(groupMsgEvent);
            } else if (isAtMessage(groupMsgEvent))  {
                // 创建交互消息按钮
                String buttonName = "点击此处";
                // 使用回调接口地址，需要在X-Open平台注册
                String targetUrl = mtConfigService.getKnowledgeAddCallbackUrl();
                
                // 构建业务参数
                Map<String, Object> bizParams = new HashMap<>();
                bizParams.put("groupId", groupMsgEvent.getGid());
                bizParams.put("userId", groupMsgEvent.getFromUid());
                bizParams.put("action", "addKnowledge");
                
                try {
                    // 使用PushContentUtils工具类创建交互消息内容
                    String interactiveMsg = PushContentUtils.createSimpleInteractiveContent(buttonName, targetUrl, bizParams);

                    String finalMessage = mtConfigService.getUnrecognizedKeywordMessageTemplate(interactiveMsg);

                    log.info("发送未识别关键词回复消息: {}", finalMessage);
                    dxGroupChatService.sendMessageToGroup(groupMsgEvent.getGid(), finalMessage);
                } catch (Exception e) {
                    log.error("构建交互消息链接异常: {}", e.getMessage(), e);
                    // 出错时使用默认文本
                    String defaultMsg = mtConfigService.getUnrecognizedKeywordMessageTemplate();
                    dxGroupChatService.sendMessageToGroup(groupMsgEvent.getGid(), defaultMsg);
                }
            }
        } else if (eventTypeEnum == GROUP_BOT_ADD_SELF) {
            try {
                // 延迟一段时间后再执行后续操作, 避免tt数据延迟问题
                Thread.sleep(mtConfigService.getFridayAutoReplySleepTime());
                GroupMsgEvent groupMsgEvent = JSON.parseObject(data.toJSONString(), GroupMsgEvent.class);

                // 获取群信息
                GroupInfoDto groupInfo = dxGroupChatService.getGroupNameAndCreatorMisId(groupMsgEvent.getGid());
                String groupName = groupInfo.getGroupName();

                if (groupName != null && groupName.contains("事件") && groupName.matches(".*事件\\d+.*")) {
                    log.info("This is an event group. groupName: {}, creatorMisId: {}, groupMsgEvent: {}",
                            groupName, groupInfo.getMisId(), groupMsgEvent);

                    if (QingniuDegradeUtils.isNotDegrade("event_group_auto_reply_degrade")) {
                        handleEventGroupAiReply(groupMsgEvent);
                    }

                    if (QingniuDegradeUtils.isNotDegrade("event_group_auto_reply_v2_degrade")) {
                        handleEventGroupAiReplyV2(groupMsgEvent);
                    }

                    return;
                }

                NoticeDetailDto noticeDetailDto = dxGroupChatService.getNoticeDetailByDxGroupNotice(groupMsgEvent.getGid());
                if (noticeDetailDto == null) {
                    log.error("noticeDetailDto is null, groupMsgEvent:{}", groupMsgEvent);
                    return;
                }
                String ticketId = noticeDetailDto.getTicketId();
                String misId = noticeDetailDto.getHandlerMisId();
                if (ticketId == null || ticketId.isEmpty()) {
                    log.info("ticketId is empty, groupMsgEvent:{}", groupMsgEvent);
                } else {
                    TTInfoDTO ttInfo = ticketQueryService.getTTInfoByTicketId(misId, ticketId, groupMsgEvent.getGid());
                    Map<String, FridayAutoReplyConfig> rgAppMapping = mtConfigService.getFridayAutoReplyConversationConfig();
                    if (rgAppMapping == null || rgAppMapping.isEmpty() || null == rgAppMapping.get(String.valueOf(ttInfo.getRgId()))) {
                        log.error("rgAppMapping is null, rgId:{}", ttInfo.getRgId());
                        return;
                    }
                    
                    // 检查最后更新时间是否超过一周
                    long oneWeekAgo = System.currentTimeMillis() - mtConfigService.getAutoReplyExpireTerm() * 24 * 60 * 60 * 1000L;
                    if (ttInfo.getUpdatedAt() < oneWeekAgo) {
                        log.info("TT最后更新时间超过一周，不进行自动回复。ticketId: {}, updatedAt: {}", 
                            ticketId, new Date(ttInfo.getUpdatedAt()));
                        return;
                    }
                    
                    FridayAutoReplyConfig fridayAutoReplyConfig = rgAppMapping.get(String.valueOf(ttInfo.getRgId()));
                    FridayAutoReplyResultDto autoReply = fridayService.autoReply(fridayAutoReplyConfig, misId, ttInfo);
                    dxGroupChatService.sendMessageToGroupSpecifyRobot(groupMsgEvent.getGid(), autoReply.getText(), fridayAutoReplyConfig.getRobotAppId(), fridayAutoReplyConfig.getRobotAppSecret());
                }
            } catch (Exception e) {
                log.error("Unexpected error in processing knowledge addition", e);
            }
        } else if (eventTypeEnum == ROBOT_SINGLE_CHAT_MESSAGE) {
            PubMsgEvent pubMsgEvent = JSON.parseObject(data.toJSONString(), PubMsgEvent.class);
            String msgExt = pubMsgEvent.getMsgExt();
            JSONObject msgExtJson = JSON.parseObject(msgExt);
            String isMergeMessage = null;
            if (msgExtJson != null&& !msgExtJson.isEmpty()) {
                isMergeMessage = msgExtJson.getString("isMergeMessage");
            }

            if ("true".equals(isMergeMessage)) {
                try {
                    String misId = dxGroupChatService.getMisIdByUid(pubMsgEvent.getFromId());
                    handleNullTicketId(pubMsgEvent, misId);
                } catch (Exception e) {
                    log.error("Unexpected error in processing knowledge addition", e);
                    String msg = "处理失败，请重试";
                    dxGroupChatService.sendMessageToSingle(pubMsgEvent.getFromId(), msg);
                }
            } else{
                String msg = mtConfigService.getUnrecognizedKeywordMessageTemplate();
                dxGroupChatService.sendMessageToSingle(pubMsgEvent.getFromId(), msg);
            }
        }
    }

    private void handleSaveChatMessageRecord(JSONObject data, CallBackEventTypeEnum eventTypeEnum){
        switch (eventTypeEnum){
            case ROBOT_SINGLE_CHAT_MESSAGE:
                PubMsgEvent pubMsgEvent = JSON.parseObject(data.toJSONString(), PubMsgEvent.class);
                if (pubMsgEvent == null || pubMsgEvent.getMessage() == null || pubMsgEvent.getMsgExt() == null) {
                    log.warn("pubMsgEvent is null,data:{}, eventTypeEnum: {}", data, eventTypeEnum);
                    break;
                }
                SaveCorpusBotChatMessageParam pubParam = buildSaveCorpusBotChatMessageParam(pubMsgEvent);
                if (pubParam != null) {
                    try {
                        corpusBotChatService.saveChatMessage(pubParam);
                    } catch (LlmCorpusException e) {
                        log.error("保存单聊消息记录失败: {}", e.getMessage(), e);
                    }
                }
                break;
            case GROUP_MESSAGE:
                GroupMsgEvent groupMsgEvent = JSON.parseObject(data.toJSONString(), GroupMsgEvent.class);
                if (groupMsgEvent == null || groupMsgEvent.getMessage() == null || groupMsgEvent.getMsgExt() == null) {
                    log.warn("groupMsgEvent is null,data:{}, eventTypeEnum: {}", data, eventTypeEnum);
                    break;
                }
                // 只有艾特了机器人的消息才保存
                if (isAtMessage(groupMsgEvent)) {
                    SaveCorpusBotChatMessageParam groupParam = buildSaveCorpusBotChatMessageParam(groupMsgEvent);
                    if (groupParam != null) {
                        try {
                            corpusBotChatService.saveChatMessage(groupParam);
                        } catch (LlmCorpusException e) {
                            log.error("保存群聊消息记录失败: {}", e.getMessage(), e);
                        }
                    }
                }
                break;
            default:
                log.warn("不支持的事件类型，不保存消息: {}", eventTypeEnum);
                break;
        }
    }

    private SaveCorpusBotChatMessageParam buildSaveCorpusBotChatMessageParam(PubMsgEvent pubMsgEvent){
        try {
            String misId = dxGroupChatService.getMisIdByUid(pubMsgEvent.getFromId());
            
            return SaveCorpusBotChatMessageParam.builder()
                    .msgId(String.valueOf(pubMsgEvent.getMsgId()))
                    .fromUid(String.valueOf(pubMsgEvent.getFromId()))
                    .fromPubId(String.valueOf(pubMsgEvent.getToId()))
                    .gid(null) // 单聊没有群组ID
                    .cts(pubMsgEvent.getCts())
                    .type(pubMsgEvent.getType())
                    .message(pubMsgEvent.getMessage())
                    .msgExt(pubMsgEvent.getMsgExt())
                    .fromMis(misId)
                    .build();
        } catch (Exception e) {
            log.error("构建SaveCorpusBotChatMessageParam失败: {}", e.getMessage(), e);
            return null;
        }
    }
    
    private SaveCorpusBotChatMessageParam buildSaveCorpusBotChatMessageParam(GroupMsgEvent groupMsgEvent){
        try {
            String misId = dxGroupChatService.getMisIdByUid(groupMsgEvent.getFromUid());
            
            return SaveCorpusBotChatMessageParam.builder()
                    .msgId(String.valueOf(groupMsgEvent.getMsgId()))
                    .fromUid(String.valueOf(groupMsgEvent.getFromUid()))
                    .fromPubId(String.valueOf(groupMsgEvent.getFromPubId()))
                    .gid(String.valueOf(groupMsgEvent.getGid()))
                    .cts(groupMsgEvent.getCts())
                    .type(groupMsgEvent.getType())
                    .message(groupMsgEvent.getMessage())
                    .msgExt(groupMsgEvent.getMsgExt())
                    .fromMis(misId)
                    .build();
        } catch (Exception e) {
            log.error("构建SaveCorpusBotChatMessageParam失败: {}", e.getMessage(), e);
            return null;
        }
    }

    private void handleNullTicketId(PubMsgEvent pubMsgEvent, String misId) throws TException {
        //改用特定卡片
        long thirdPartyGroupCardTemplateId = mtConfigService.getSingleChatCardTemplateId();
        String token = xmAuthRpcService.getToken();
        Set<Long> empIdList = dxGroupChatService.getEmpIdListByUidList(Collections.singletonList(pubMsgEvent.getFromId()));

        if (empIdList.isEmpty()) {
            String msg = "未查询到值班组信息，请重试";
            log.error("No duty group information found for uid: {}", pubMsgEvent.getFromId());
            dxGroupChatService.sendMessageToSingle(pubMsgEvent.getFromId(), msg);
            return;
        }

        SendExclusionCardReq sendExclusionCardReq = createSendExclusionCardReq(pubMsgEvent, thirdPartyGroupCardTemplateId, empIdList, buildPrivateVariableValue(misId),buildPublicVariableValue());
        SendExclusionCardResp sendExclusionCardResp = openCardService.sendExclusionCard(token, sendExclusionCardReq);

        if (sendExclusionCardResp.status.code != 0) {
            String msg = String.format("卡片发送失败，错误码：%d，错误信息：%s，请重试", sendExclusionCardResp.status.code, sendExclusionCardResp.status.msg);
            log.error("Error in sending exclusion card: {}", msg);
            dxGroupChatService.sendMessageToSingle(pubMsgEvent.getFromId(), msg);
        }
    }


    private void handleNullTicketId(GroupMsgEvent groupMsgEvent, String misId) throws TException {
        long thirdPartyGroupCardTemplateId = mtConfigService.getThirdPartyGroupCardTemplateId();
        String token = xmAuthRpcService.getToken();
        Set<Long> empIdList = dxGroupChatService.getEmpIdListByUidList(Collections.singletonList(groupMsgEvent.getFromUid()));

        if (empIdList.isEmpty()) {
            String msg = "未查询到值班组信息，请重试";
            log.error("No duty group information found for uid: {}", groupMsgEvent.getFromUid());
            dxGroupChatService.sendMessageToGroupWithAt(groupMsgEvent.getGid(), groupMsgEvent.getFromUid(), groupMsgEvent.getFromName(), msg);
            return;
        }

        SendExclusionCardReq sendExclusionCardReq = createSendExclusionCardReq(groupMsgEvent, thirdPartyGroupCardTemplateId, empIdList, buildPrivateVariableValue(misId),buildPublicVariableValue());
        SendExclusionCardResp sendExclusionCardResp = openCardService.sendExclusionCard(token, sendExclusionCardReq);

        if (sendExclusionCardResp.status.code != 0) {
            String msg = String.format("卡片发送失败，错误码：%d，错误信息：%s，请重试", sendExclusionCardResp.status.code, sendExclusionCardResp.status.msg);
            log.error("Error in sending exclusion card: {}", msg);
            dxGroupChatService.sendMessageToGroupWithAt(groupMsgEvent.getGid(), groupMsgEvent.getFromUid(), groupMsgEvent.getFromName(), msg);
        }
    }

    private String buildPublicVariableValue() {
        // 创建一个单一选项的Map
        Map<String, String> singleOption = new HashMap<>();
        singleOption.put("value", "-1");
        singleOption.put("name", "非调用人，无权限，无需点击卡片");
        singleOption.put("rgId", "-1");

        // 将单一选项添加到列表中
        List<Map<String, String>> options = new ArrayList<>();
        options.add(singleOption);

        // 创建结果Map
        Map<String, Object> result = new HashMap<>();
        result.put("rgItems", options);

        // 使用Gson库将result转换为JSON字符串
        return new Gson().toJson(result);
    }

    private String buildPrivateVariableValue(String misId) {
        String rgInfoDTOListJSONString = rgQueryUtil.queryMyRgList(misId, 1, 200);
        List<RgInfoDTO> rgInfoDTOList = parseJsonToRgInfoDTOList(rgInfoDTOListJSONString);
        List<Map<String, String>> options = rgInfoDTOList.stream()
                .map(rgInfo -> {
                    Map<String, String> option = new HashMap<>();
                    option.put("value", String.valueOf(rgInfo.getId())); // 使用id作为value
                    option.put("name", rgInfo.getName()); // 使用name作为name
                    option.put("rgId", String.valueOf(rgInfo.getId())); // 使用id作为rgId
                    return option;
                })
                .collect(Collectors.toList());

        Map<String, Object> result = new HashMap<>();
        result.put("rgItems", options);

        // 使用 Gson 库将 result 转换为 JSON 字符串
        return new Gson().toJson(result);
    }

    private SendExclusionCardReq createSendExclusionCardReq(GroupMsgEvent groupMsgEvent, long templateId, Set<Long> empIdList, String privateVariableValue, String publicVariableValue) {
        SendExclusionCardReq req = new SendExclusionCardReq();
        req.setRequestId(UUID.randomUUID().toString());
        req.setSerialNum(String.valueOf(Instant.now().toEpochMilli()));
        req.setFieldTypes(Collections.singleton(CardFieldTypeEnum.GROUP_CHAT));
        req.setCardFieldData(new CardFieldData().setImGroupChat(new IMGroupChat().setGid(groupMsgEvent.getGid())));

        PublicCardData publicCardData = new PublicCardData();
        publicCardData.setTemplateId(templateId);
        publicCardData.setAbstractText("请选择TT所属值班组");
        publicCardData.setVariableValue(publicVariableValue);

        PrivateCardData privateCardData = new PrivateCardData();
        privateCardData.setTemplateId(templateId);
        privateCardData.setEmpIds(empIdList);
        privateCardData.setVariableValue(privateVariableValue);
        privateCardData.setAbstractText("请选择TT所属值班组");

        CardData cardData = new CardData(Collections.singletonList(privateCardData), publicCardData, Instant.now().toEpochMilli());
        req.setCardData(cardData);
        req.setCardOption(new CardOption().setUpdateType(CardUpdateTypeEnum.PUSH));
        log.info("Creating SendExclusionCardReq for group: {}, req: {}", groupMsgEvent.getGid(), req);
        return req;
    }

    private SendExclusionCardReq createSendExclusionCardReq(PubMsgEvent pubMsgEvent, long templateId, Set<Long> empIdList, String privateVariableValue, String publicVariableValue) {
        SendExclusionCardReq req = new SendExclusionCardReq();
        req.setRequestId(UUID.randomUUID().toString());
        req.setSerialNum(String.valueOf(Instant.now().toEpochMilli()));
        req.setFieldTypes(Collections.singleton(CardFieldTypeEnum.PUB_CHAT));
        req.setCardFieldData(new CardFieldData().setImBotChat(new IMBotChat().setEmpIds(empIdList)));

        PublicCardData publicCardData = new PublicCardData();
        publicCardData.setTemplateId(templateId);
        publicCardData.setAbstractText("请选择TT所属值班组");
        publicCardData.setVariableValue(publicVariableValue);

        PrivateCardData privateCardData = new PrivateCardData();
        privateCardData.setTemplateId(templateId);
        privateCardData.setEmpIds(empIdList);
        privateCardData.setVariableValue(privateVariableValue);
        privateCardData.setAbstractText("请选择TT所属值班组");

        CardData cardData = new CardData(Collections.singletonList(privateCardData), publicCardData, Instant.now().toEpochMilli());
        req.setCardData(cardData);
        req.setCardOption(new CardOption().setUpdateType(CardUpdateTypeEnum.PUSH));
        log.info("Creating SendExclusionCardReq for single: {}, req: {}", pubMsgEvent.getFromId(), req);
        return req;
    }

    private void processExistingTicket(GroupMsgEvent groupMsgEvent, String misId, String ticketId) throws LlmCorpusException, TException  {
        log.info("Processing existing ticket for group: {} with ticket ID: {}", groupMsgEvent.getGid(), ticketId);
        TTInfoDTO ttInfo = ticketQueryService.getTTInfoByTicketId(misId, ticketId, groupMsgEvent.getGid());
        boolean permission = knowledgeBaseService.validateUserPermission(misId, ttInfo.getRgId());
        if (!permission){
            log.info("用户非tt所在值班组成员，作为三方群处理：groupMsgEvent:{},misId:{}", groupMsgEvent, misId);
            handleNullTicketId(groupMsgEvent, misId);
            return;
        }
        ConvertTtToKnowledgeParam param = new ConvertTtToKnowledgeParam();
        param.setTtId(ticketId);
        param.setDxGroupId(groupMsgEvent.getGid());
        param.setRgId(ttInfo.getRgId());
        param.setMisId(misId);
        param.setCreatorDxUserId(groupMsgEvent.getFromUid());
        param.setCreatorUserName(groupMsgEvent.getFromName());
        param.setPlatformId(ConvertTaskPlatformId.DX_GROUP_BOT);
        TTInfoDTO ttInfoDTO = ticketQueryUtil.getTtInfo(param.getMisId(), param.getTtId(), param.getDxGroupId());
        String task = knowledgeBaseService.createConvertTtToKnowledgeTask(param, ttInfoDTO);
        log.info("convertTtToKnowledgeTask: {}", task);
        dxGroupChatService.sendMessageToGroupWithAt(groupMsgEvent.getGid(), groupMsgEvent.getFromUid(), groupMsgEvent.getFromName(), "开始处理，请等待大象通知");
    }

    private boolean isAtMessage(GroupMsgEvent groupMsgEvent){
        if (groupMsgEvent == null || groupMsgEvent.getMessage() == null || groupMsgEvent.getMsgExt() == null) {
            return false;
        }
        JSONObject ext = JSON.parseObject(groupMsgEvent.getMsgExt());
        if (ext == null) {
            return false;
        }
        JSONArray botAtList = ext.getJSONArray("botAt");
        if (CollectionUtils.isEmpty(botAtList)){
            return false;
        }
        Long botId = mtConfigService.getChatBotId();
        for (Object botAt : botAtList) {
            Long botAtId = NumberUtils.toLong((String) botAt);
            if (botId.equals(botAtId)) {
                return true;
            }
        }
        return false;
    }
    private boolean isAddKnowledge(GroupMsgEvent groupMsgEvent) {
        if (groupMsgEvent == null || groupMsgEvent.getMessage() == null || groupMsgEvent.getMsgExt() == null) {
            return false;
        }
        if (!isAtMessage(groupMsgEvent)){
            return false;
        }
        JSONObject messageJson = JSON.parseObject(groupMsgEvent.getMessage());
        String text = messageJson.getString("text");
        return text.contains(ADD_KNOWLEDGE);
    }

    /**
     * 处理事件群组的自动回复
     *
     * @param groupMsgEvent 群消息事件
     */
    private void handleEventGroupAiReply(GroupMsgEvent groupMsgEvent) {
        // 从配置中获取机器人ID
        AiopsAutoReplyConfig aiopsConfig = mtConfigService.getAiopsAutoReplyConfig();

        long botId = aiopsConfig.getAiopsBotid();
        List<Long> botIds = Collections.singletonList(botId);
        dxGroupChatService.addBotsToGroup(groupMsgEvent.getGid(), botIds);
        log.info("#handleEventGroupAiReply ##Attempted to add bot {} to event group {}", botId, groupMsgEvent.getGid());

        try {
            // 获取群历史消息
            List<DxChatMessageRecord> historyMsgs = dxGroupChatService.getTTChatInfo(groupMsgEvent.getGid());
            log.info("Retrieved {} messages from event group {}", historyMsgs.size(), groupMsgEvent.getGid());

            if (!historyMsgs.isEmpty()) {
                // 初始化最新消息为null
                DxChatMessageRecord latestMsg = null;

                // 从后往前遍历，找到第一条来自机器人的消息
                for (int i = historyMsgs.size() - 1; i >= 0; i--) {
                    DxChatMessageRecord currentMsg = historyMsgs.get(i);
                    if (currentMsg.getFromUid() == aiopsConfig.getFromUid()) {
                        latestMsg = currentMsg;
                        break;
                    }
                }

                // 如果没有找到符合条件的消息，直接返回
                if (latestMsg == null) {
                    return;
                }

                try {
                    String messageText = "";
                    if (latestMsg.getMessage() != null) {
                        if (latestMsg.getType() == 1) {
                            messageText = latestMsg.getMessage().getString("text");
                        } else {
                            messageText = latestMsg.getMessage().toJSONString();
                        }
                    }

                    if (!messageText.isEmpty()) {
                        String userMisId = mtConfigService.getSsoLoginName();

                        AiopsAutoReplyResultDto aiopsReply = fridayService.aiopsAutoReply(aiopsConfig, userMisId, messageText);

                        log.info("#handOpenEvent# Sending Aiops auto reply to group {}: {}", groupMsgEvent.getGid(), aiopsReply.getText());
                        dxGroupChatService.sendMessageToGroupSpecifyRobot(
                            groupMsgEvent.getGid(),
                            aiopsReply.getText(),
                            aiopsConfig.getRobotAppId(),
                            aiopsConfig.getRobotAppSecret());
                    }
                } catch (Exception e) {
                    log.error("#handOpenEvent# Error processing Aiops auto reply for group {}", groupMsgEvent.getGid(), e);
                }
            }
        } catch (Exception e) {
            log.error("#handOpenEvent# Error retrieving chat history for event group {}", groupMsgEvent.getGid(), e);
        }
    }

    /**
     * 处理事件群组的自动回复V2(雷达拉群自动开始群聊内容整理)
     * @param groupMsgEvent 群消息事件
     */
    private void handleEventGroupAiReplyV2(GroupMsgEvent groupMsgEvent) throws TException, InterruptedException {
        // 等待指定的时间后执行雷达自动摘要任务
        Thread.sleep(mtConfigService.getRadarAutoSummarySleepTime());
        // 获取雷达总指挥的misId
        String misId = dxGroupChatService.getRadarOwnerMisIdByDxGroupNotice(groupMsgEvent.getGid());
        if (misId == null || misId.isEmpty()) {
            log.error("雷达总指挥的misId为空, groupMsgEvent:{}", groupMsgEvent);
            return;
        }
        // 获取群组ID
        long groupId = groupMsgEvent.getGid();
        // 提交雷达自动摘要任务
        aiOpsRpcService.submitRadarGroupSummaryTask(misId, groupId);
    }

    /**
     * 处理加知识库请求
     * 
     * @param groupMsgEvent 群消息事件
     */
    public void processAddKnowledgeRequest(GroupMsgEvent groupMsgEvent) {
        try {
            String misId = dxGroupChatService.getMisIdByUid(groupMsgEvent.getFromUid());
            String ticketId = dxGroupChatService.getTicketIdByDxGroupNotice(groupMsgEvent.getGid());
            if (ticketId == null || ticketId.isEmpty()) {
                handleNullTicketId(groupMsgEvent, misId);
            } else {
                processExistingTicket(groupMsgEvent, misId, ticketId);
            }
        } catch (LlmCorpusException e) {
            log.error("Error in processing knowledge addition", e);
            String msg = e.getMessage();
            dxGroupChatService.sendMessageToGroupWithAt(groupMsgEvent.getGid(), groupMsgEvent.getFromUid(), groupMsgEvent.getFromName(), msg);
        } catch (Exception e) {
            log.error("Unexpected error in processing knowledge addition", e);
            String msg = "处理失败，请重试";
            dxGroupChatService.sendMessageToGroupWithAt(groupMsgEvent.getGid(), groupMsgEvent.getFromUid(), groupMsgEvent.getFromName(), msg);
        }
    }
}
