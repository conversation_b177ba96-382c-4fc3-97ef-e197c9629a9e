package com.meituan.banma.llm.corpus.server.thrift;

import com.meituan.banma.llm.corpus.api.client.BmLlmCorpusCommonToolsThriftService;
import com.meituan.banma.llm.corpus.api.constants.BizCode;
import com.meituan.banma.llm.corpus.api.exception.LlmCorpusException;
import com.meituan.banma.llm.corpus.api.request.ImageToTextBatchRequest;
import com.meituan.banma.llm.corpus.api.request.StatisticsReportRequestParam;
import com.meituan.banma.llm.corpus.api.response.CommonResponse;
import com.meituan.banma.llm.corpus.api.response.ImageToTextResponse;
import com.meituan.banma.llm.corpus.server.common.domain.dto.ImageToTextResultDTO;
import com.meituan.banma.llm.corpus.server.common.domain.dto.StatisticsReportParam;
import com.meituan.banma.llm.corpus.server.common.domain.dto.SopDefaultConfig;
import com.meituan.banma.llm.corpus.server.common.MtConfigService;
import com.meituan.banma.llm.corpus.server.dal.entity.RgRuleEntity;
import com.meituan.banma.llm.corpus.server.dal.entity.RgSopEntity;
import com.meituan.banma.llm.corpus.server.dal.entity.RgBackgroundKnowledgeEntity;
import com.meituan.banma.llm.corpus.server.dal.mapper.RgRuleMapper;
import com.meituan.banma.llm.corpus.server.dal.mapper.RgSopMapper;
import com.meituan.banma.llm.corpus.server.dal.mapper.RgBackgroundKnowledgeMapper;
import com.meituan.banma.llm.corpus.server.service.IFridayService;
import com.meituan.banma.llm.corpus.server.service.IStatisticsService;
import com.meituan.banma.llm.corpus.server.service.IRgTagsService;
import com.meituan.banma.llm.corpus.server.service.mapper.FridayMapper;
import com.meituan.banma.llm.corpus.server.service.mapper.StatisticsMapper;
import com.meituan.banma.llm.corpus.server.utils.AsyncTaskUtils;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftServer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.HashMap;

@Slf4j
@MdpThriftServer()
public class BmLlmCorpusCommonToolsThriftServiceImpl implements BmLlmCorpusCommonToolsThriftService {
    @Autowired
    private IFridayService fridayService;

    @Autowired
    private IStatisticsService statisticsService;

    @Autowired
    private IRgTagsService rgTagsService;

    @Autowired
    private RgSopMapper rgSopMapper;

    @Autowired
    private RgBackgroundKnowledgeMapper rgBackgroundKnowledgeMapper;

    @Autowired
    private RgRuleMapper rgRuleMapper;

    @Autowired
    private MtConfigService mtConfigService;

    private final StatisticsMapper statisticsMapper = StatisticsMapper.get();

    private final FridayMapper fridayMapper = FridayMapper.get();
    @Override
    public ImageToTextResponse imageToText(String imageUrl, String prompt, String misId) {
        ImageToTextResponse response = new ImageToTextResponse();

        if (StringUtils.isBlank(imageUrl) || StringUtils.isBlank(prompt)) {
            response.setImageUrl(imageUrl);
            response.setText("参数缺失");
            return response;
        }
        try {
            ImageToTextResultDTO resultDTO = fridayService.convertImageToText(misId, prompt, imageUrl);
            response.setImageUrl(imageUrl);
            response.setText(resultDTO.getText());
        } catch (LlmCorpusException e) {
            response.setImageUrl(imageUrl);
            response.setText(e.getMessage());
        } catch (Exception e) {
            response.setImageUrl(imageUrl);
            response.setText("调用图片识别接口失败");
            log.error("调用Friday接口失败,imageUrl:{},prompt:{},misId:{}", imageUrl, prompt, misId, e);
        }
        return response;
    }

    @Override
    public CommonResponse<String> statisticsReport(StatisticsReportRequestParam request) {
        CommonResponse<String> response = new CommonResponse<>();
        ExecutorService statisticsReportThreadPool = AsyncTaskUtils.getStatisticsReportThreadPool();
        statisticsReportThreadPool.execute(() -> {
            try {
                StatisticsReportParam param = statisticsMapper.toStatisticsReportParam(request);
                statisticsService.reportQuestionRetrievalResult(param);
            } catch (Exception e) {
                log.error("统计上报异常,request:{}", request, e);
            }
        });
        response.setCode(BizCode.SUCCESS.getCode());
        response.setMessage("上报成功");
        return response;
    }


    @Override
    public CommonResponse<String> queryLatestSopByRgId(long rgId) {
        CommonResponse<String> response = new CommonResponse<>();
        String res;
        String message = "未查到数据，使用默认模版";
        
        try {
            List<RgSopEntity> sops = rgSopMapper.queryRgSopByRgId(rgId, 1, 0);
            if (!sops.isEmpty()) {
                res = sops.get(0).getSop();
                message = "查询成功";
            } else {
                // 获取配置中心的默认rgId的最新版本数据
                SopDefaultConfig sopDefaultConfig = mtConfigService.getSopDefaultConfig();
                List<RgSopEntity> defaultSops = rgSopMapper.queryRgSopByRgId(sopDefaultConfig.getDefaultRgId(), 1, 0);
                if (!defaultSops.isEmpty()) {
                    res = defaultSops.get(0).getSop();
                    message = "使用默认rgId查询成功";
                } else {
                    // 返回默认模板
                    res = sopDefaultConfig.getDefaultTemplate();
                    message = "未查到数据，使用默认模版";
                }
            }
        } catch (Exception e) {
            // 异常情况下使用配置中心的默认模板
            SopDefaultConfig sopDefaultConfig = mtConfigService.getSopDefaultConfig();
            res = sopDefaultConfig.getDefaultTemplate();
            response.setCode(BizCode.ILLEGAL_ARGUMENT.getCode());
            message = "查询值班组最新SOP模板失败，使用默认模板";
            log.error("查询值班组最新SOP模板失败,e:{}", e.getMessage());
        }

        response.setCode(BizCode.SUCCESS.getCode());
        response.setMessage(message);
        response.setData(res);
        return response;
    }

    @Override
    public CommonResponse<List<ImageToTextResponse>> imageToTextBatch(ImageToTextBatchRequest request) {
        CommonResponse<List<ImageToTextResponse>> response = new CommonResponse<>();
        try{
            List<ImageToTextResultDTO> resp = fridayService.convertImageToTextBatch(request.getMisId(), request.getPrompt(), request.getImageUrls());
            response.setData(fridayMapper.trans2ImageToTextResponse(resp));
        }catch (LlmCorpusException e){
            response.setCode(e.getCode());
            response.setMessage(e.getMessage());
        }catch (Exception e){
            response.setCode(BizCode.SERVER_INTERNAL_ERROR.getCode());
            response.setMessage("批量图片识别失败");
            log.error("批量图片识别失败,request:{}", request, e);
        }
        return response;
    }

    @Override
    public CommonResponse<String> queryLatestBackgroundKnowledgeByRgId(long rgId) {
        CommonResponse<String> response = new CommonResponse<>();
        String res = "";
        response.setMessage("未查到数据，背景知识为空");
        try {
            RgBackgroundKnowledgeEntity knowledge = rgBackgroundKnowledgeMapper.queryLatestByRgId(rgId);
            if (knowledge != null && knowledge.getKnowledgeContent() != null && !knowledge.getKnowledgeContent().isEmpty()) {
                res = knowledge.getKnowledgeContent();
                response.setMessage("查询成功");
                response.setCode(BizCode.SUCCESS.getCode());
            } else {
                response.setCode(BizCode.ILLEGAL_ARGUMENT.getCode());
            }
        } catch (Exception e) {
            response.setCode(BizCode.ILLEGAL_ARGUMENT.getCode());
            response.setMessage("查询值班组最新背景知识失败，背景知识为空");
            log.error("查询值班组最新背景知识失败,e:{}", e.getMessage());
        }
        response.setData(res);
        return response;
    }

    @Override
    public CommonResponse<String> queryLatestRuleByRgId(long rgId) {
        CommonResponse<String> response = new CommonResponse<>();
        String res = "";
        response.setMessage("未查到数据，自定义规则为空");
        try {
            RgRuleEntity knowledge = rgRuleMapper.queryLatestByRgId(rgId);
            if (knowledge != null && knowledge.getRule() != null && !knowledge.getRule().isEmpty()) {
                res = knowledge.getRule();
                response.setMessage("查询成功");
                response.setCode(BizCode.SUCCESS.getCode());
            } else {
                response.setCode(BizCode.ILLEGAL_ARGUMENT.getCode());
            }
        } catch (Exception e) {
            response.setCode(BizCode.ILLEGAL_ARGUMENT.getCode());
            response.setMessage("查询值班组最新规则失败，自定义规则为空");
            log.error("查询值班组最新规则失败,e:{}", e.getMessage());
        }
        response.setData(res);
        return response;
    }

    @Override
    public CommonResponse<Map<String, String>> queryTagsByRgId(long rgId) {
        CommonResponse<Map<String, String>> response = new CommonResponse<>();
        try {
            // 先查询传参的rgId对应的标签记录
            Map<String, String> tagsMap = rgTagsService.getTagsMapByRgId(rgId);
            
//            // 如果查询不到标签，则查询配置中心获取的默认rgId的记录
//            if (tagsMap == null || tagsMap.isEmpty()) {
//                Long defaultTagRgId = mtConfigService.getDefaultTagRgId();
//                log.info("#BmLlmCorpusCommonToolsThriftServiceImpl.queryTagsByRgId 传参rgId={}未查询到标签，使用默认rgId={}查询", rgId, defaultTagRgId);
//                tagsMap = rgTagsService.getTagsMapByRgId(defaultTagRgId);
//            }
            
            response.setCode(BizCode.SUCCESS.getCode());
            response.setMessage("查询成功");
            response.setData(tagsMap != null ? tagsMap : new HashMap<>());
            log.info("#BmLlmCorpusCommonToolsThriftServiceImpl.queryTagsByRgId 查询值班组标签成功: rgId={}, 标签数量={}", 
                    rgId, tagsMap != null ? tagsMap.size() : 0);
        } catch (Exception e) {
            response.setCode(BizCode.ILLEGAL_ARGUMENT.getCode());
            response.setMessage("查询值班组标签失败");
            response.setData(null);
            log.error("#BmLlmCorpusCommonToolsThriftServiceImpl.queryTagsByRgId 查询值班组标签失败: rgId={}", rgId, e);
        }
        return response;
    }
}
