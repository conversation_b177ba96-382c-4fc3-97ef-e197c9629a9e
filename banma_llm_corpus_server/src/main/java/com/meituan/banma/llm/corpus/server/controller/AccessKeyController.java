package com.meituan.banma.llm.corpus.server.controller;

import com.meituan.banma.llm.corpus.server.common.CommonResult;
import com.meituan.banma.llm.corpus.server.dal.entity.AccessKeyEntity;
import com.meituan.banma.llm.corpus.server.service.IAccessKeyService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * AccessKey控制器
 * 提供AccessKey相关的接口
 */
@Slf4j
@RestController
@RequestMapping("/accessKey")
public class AccessKeyController {

    @Autowired
    private IAccessKeyService accessKeyService;

    /**
     * 根据rgId和misId查询AccessKey列表
     * 返回结果包含该值班组的默认AccessKey
     *
     * @param rgId 值班组ID
     * @param misId 用户MIS ID
     * @return AccessKey列表
     */
    @GetMapping("/list")
    public Object findAccessKeysByRgIdAndMisId(@RequestParam("rgId") Long rgId,
                                               @RequestParam("misId") String misId) {
        try {
            if (rgId == null || rgId <= 0 || StringUtils.isBlank(misId)) {
                return CommonResult.error("参数无效，rgId和misId不能为空");
            }
            
            List<AccessKeyEntity> accessKeys = accessKeyService.findAccessKeysByRgIdAndMisId(rgId, misId);
            log.info("findAccessKeysByRgIdAndMisId# 查询AccessKey列表成功, rgId:{}, misId:{}, 结果数量:{}", 
                    rgId, misId, accessKeys.size());
            return CommonResult.success(accessKeys);
        } catch (Exception e) {
            log.error("findAccessKeysByRgIdAndMisId# 查询AccessKey列表失败, rgId:{}, misId:{}, 异常原因:{}", 
                    rgId, misId, e.getMessage(), e);
            return CommonResult.error(String.format("查询AccessKey列表失败，异常原因=%s", e.getMessage()));
        }
    }

    /**
     * 生成并保存自定义AccessKey
     *
     * @param misId 用户MIS ID
     * @param akName AccessKey名称
     * @param rgIds 值班组ID列表（逗号分隔）
     * @return 生成的AccessKey
     */
    @PostMapping("/generate")
    public Object generateAndSaveAccessKey(@RequestParam("misId") String misId,
                                           @RequestParam("akName") String akName,
                                           @RequestParam("rgIds") String rgIds) {
        try {
            if (StringUtils.isBlank(misId) || StringUtils.isBlank(akName) || StringUtils.isBlank(rgIds)) {
                return CommonResult.error("参数无效，misId、akName和rgIds不能为空");
            }
            
            AccessKeyEntity accessKey = accessKeyService.generateAndSaveAccessKey(misId, akName, rgIds);
            if (accessKey == null) {
                return CommonResult.error("生成AccessKey失败");
            }
            
            log.info("generateAndSaveAccessKey# 生成AccessKey成功, misId:{}, akName:{}, rgIds:{}, ak:{}", 
                    misId, akName, rgIds, accessKey.getAk());
            return CommonResult.success(accessKey);
        } catch (Exception e) {
            log.error("generateAndSaveAccessKey# 生成AccessKey失败, misId:{}, akName:{}, rgIds:{}, 异常原因:{}", 
                    misId, akName, rgIds, e.getMessage(), e);
            return CommonResult.error(String.format("生成AccessKey失败，异常原因=%s", e.getMessage()));
        }
    }

    /**
     * 修改AccessKey名称
     * 修改前会验证ak与misId是否匹配，只有匹配才能修改
     *
     * @param ak AccessKey
     * @param misId 用户MIS ID
     * @param newAkName 新的AccessKey名称
     * @return 修改结果
     */
    @PostMapping("/updateName")
    public Object updateAccessKeyName(@RequestParam("ak") String ak,
                                      @RequestParam("misId") String misId,
                                      @RequestParam("newAkName") String newAkName) {
        try {
            if (StringUtils.isBlank(ak) || StringUtils.isBlank(misId) || StringUtils.isBlank(newAkName)) {
                return CommonResult.error("参数无效，ak、misId和newAkName不能为空");
            }
            
            boolean result = accessKeyService.updateAccessKeyName(ak, misId, newAkName);
            if (result) {
                log.info("updateAccessKeyName# 修改AccessKey名称成功, ak:{}, misId:{}, newAkName:{}", 
                        ak, misId, newAkName);
                
                Map<String, Object> data = new HashMap<>();
                data.put("success", true);
                data.put("message", "修改成功");
                return CommonResult.success(data);
            } else {
                log.warn("updateAccessKeyName# 修改AccessKey名称失败, ak:{}, misId:{}, newAkName:{}", 
                        ak, misId, newAkName);
                return CommonResult.error("修改AccessKey名称失败，可能是权限不足或AccessKey不存在");
            }
        } catch (Exception e) {
            log.error("updateAccessKeyName# 修改AccessKey名称失败, ak:{}, misId:{}, newAkName:{}, 异常原因:{}", 
                    ak, misId, newAkName, e.getMessage(), e);
            return CommonResult.error(String.format("修改AccessKey名称失败，异常原因=%s", e.getMessage()));
        }
    }

    /**
     * 删除AccessKey
     * 删除前会验证ak与misId是否匹配，只有匹配才能删除
     * 注意：默认AccessKey不允许删除
     *
     * @param ak AccessKey
     * @param misId 用户MIS ID
     * @return 删除结果
     */
    @PostMapping("/delete")
    public Object deleteAccessKey(@RequestParam("ak") String ak,
                                  @RequestParam("misId") String misId) {
        try {
            if (StringUtils.isBlank(ak) || StringUtils.isBlank(misId)) {
                return CommonResult.error("参数无效，ak和misId不能为空");
            }
            
            boolean result = accessKeyService.deleteAccessKey(ak, misId);
            if (result) {
                log.info("deleteAccessKey# 删除AccessKey成功, ak:{}, misId:{}", 
                        ak, misId);
                
                Map<String, Object> data = new HashMap<>();
                data.put("success", true);
                data.put("message", "删除成功");
                return CommonResult.success(data);
            } else {
                log.warn("deleteAccessKey# 删除AccessKey失败, ak:{}, misId:{}", 
                        ak, misId);
                return CommonResult.error("删除AccessKey失败");
            }
        } catch (Exception e) {
            log.error("deleteAccessKey# 删除AccessKey失败, ak:{}, misId:{}, 异常原因:{}", 
                    ak, misId, e.getMessage(), e);
            return CommonResult.error(String.format("删除AccessKey失败，异常原因=%s", e.getMessage()));
        }
    }
} 