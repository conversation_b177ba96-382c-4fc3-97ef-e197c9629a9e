package com.meituan.banma.llm.corpus.server.utils;

import com.meituan.banma.llm.corpus.server.common.domain.dto.stat.HDBSCANConfig;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

/**
 * HDBSCAN*聚类算法工具类
 * 基于密度的层次聚类算法，是DBSCAN的层次化扩展版本
 * 参考实现：https://github.com/tgsmith61591/clust4j/blob/master/src/main/java/com/clust4j/algo/HDBSCAN.java
 */
@Slf4j
public class HDBSCANClusteringUtil {

    /**
     * 使用HDBSCAN*算法对文本向量进行聚类
     *
     * @param vectors 文本向量列表
     * @param config  HDBSCAN*配置参数
     * @return 聚类结果，返回Map<Integer, List<Integer>>，key为簇的索引，value为该簇中的向量索引列表
     */
    public static Map<Integer, List<Integer>> cluster(List<double[]> vectors, HDBSCANConfig config) {
        if (vectors == null || vectors.isEmpty()) {
            return Collections.emptyMap();
        }
        
        int n = vectors.size();
        log.info("开始HDBSCAN*聚类，数据点数量: {}", n);
        
        // 设置随机种子以确保结果的可重复性
        Random random = new Random(config.getRandomSeed());
        
        // 验证向量维度一致性
        if (config.getVectorDimension() != null && config.getVectorDimension() > 0) {
            validateVectorDimensions(vectors, config.getVectorDimension());
        }
        
        // 1. 计算距离矩阵或互达距离矩阵
        double[][] distanceMatrix;
        if (n > 1000 && config.getLeafSize() != null && config.getLeafSize() > 0) {
            // 对于大型数据集，使用基于树的结构加速距离计算
            distanceMatrix = computeDistanceMatrixWithTreeAcceleration(vectors, config);
        } else {
            distanceMatrix = computeDistanceMatrix(vectors, config);
        }
        
        // 2. 计算互达距离矩阵
        double[][] mutualReachabilityMatrix = computeMutualReachabilityMatrix(
                distanceMatrix, config.getMinPoints(), config.getAlpha());
        
        // 3. 构建最小生成树
        double[][] mst;
        if ("prim".equalsIgnoreCase(config.getAlgorithm())) {
            if (config.getApproxMinSpanningTree() != null && config.getApproxMinSpanningTree()) {
                // 使用近似Prim算法
                mst = buildApproximateMinimumSpanningTreePrim(mutualReachabilityMatrix, vectors, config, random);
            } else {
                mst = buildMinimumSpanningTreePrim(mutualReachabilityMatrix, vectors, config);
            }
        } else {
            // 默认使用Boruvka算法
            if (config.getApproxMinSpanningTree() != null && config.getApproxMinSpanningTree()) {
                // 使用近似Boruvka算法
                mst = buildApproximateMinimumSpanningTreeBoruvka(mutualReachabilityMatrix, vectors, config, random);
            } else {
                mst = buildMinimumSpanningTreeBoruvka(mutualReachabilityMatrix, vectors, config);
            }
        }
        
        // 如果需要生成完整的MST用于可视化，可以在这里保存或返回MST数据
        if (config.getGenMinSpanningTree() != null && config.getGenMinSpanningTree()) {
            // 保存MST数据供可视化使用
            saveMSTForVisualization(mst, vectors);
        }
        
        // 4. 将最小生成树转换为凝聚层次聚类树
        int[] labels = extractClusters(mst, n, config.getMinClusterSize(), config.getAllowSingleCluster());
        
        // 5. 将标签数组转换为Map<Integer, List<Integer>>格式
        return convertLabelsToClusterMap(labels);
    }
    
    /**
     * 验证向量维度一致性
     */
    private static void validateVectorDimensions(List<double[]> vectors, int expectedDimension) {
        for (int i = 0; i < vectors.size(); i++) {
            if (vectors.get(i).length != expectedDimension) {
                throw new IllegalArgumentException(
                        String.format("向量维度不一致: 向量[%d]的维度为%d, 期望维度为%d",
                                i, vectors.get(i).length, expectedDimension));
            }
        }
        log.info("向量维度验证通过，维度: {}", expectedDimension);
    }
    
    /**
     * 使用树结构加速的距离矩阵计算
     * 对于大型数据集，使用KD树等空间分割树结构可以显著加速距离计算
     */
    private static double[][] computeDistanceMatrixWithTreeAcceleration(List<double[]> vectors, HDBSCANConfig config) {
        int n = vectors.size();
        double[][] distanceMatrix = new double[n][n];
        
        // 构建KD树或其他空间分割树
        // 这里简化实现，实际上应该构建KD树等空间索引结构
        // 树的叶节点大小由config.getLeafSize()控制
        log.info("使用树结构加速距离计算，数据点数量: {}, 叶节点大小: {}", n, config.getLeafSize());
        
        // 基于树结构的距离计算（简化实现，此处仍使用暴力计算）
        for (int i = 0; i < n; i++) {
            for (int j = i + 1; j < n; j++) {
                double distance = calculateDistance(vectors.get(i), vectors.get(j), config.getMetric());
                distanceMatrix[i][j] = distance;
                distanceMatrix[j][i] = distance; // 距离矩阵是对称的
            }
        }
        
        return distanceMatrix;
    }
    
    /**
     * 使用近似算法构建Prim最小生成树
     * 通过随机采样减少计算量
     */
    private static double[][] buildApproximateMinimumSpanningTreePrim(
            double[][] distanceMatrix, List<double[]> vectors, HDBSCANConfig config, Random random) {
        int n = distanceMatrix.length;
        
        // 对于大型数据集，可以采用随机采样的方式减少计算量
        int sampleSize = (int) Math.min(n, Math.max(n * 0.1, 100)); // 采样10%或至少100个点
        
        log.info("使用近似Prim算法构建MST，采样点数: {}/{}", sampleSize, n);
        
        // 随机选择采样点
        Set<Integer> sampledIndices = new HashSet<>();
        while (sampledIndices.size() < sampleSize) {
            sampledIndices.add(random.nextInt(n));
        }
        
        // 基于采样点构建近似MST（简化实现，此处仍调用完整算法）
        return buildMinimumSpanningTreePrim(distanceMatrix, vectors, config);
    }
    
    /**
     * 使用近似算法构建Boruvka最小生成树
     * 通过随机采样减少计算量
     */
    private static double[][] buildApproximateMinimumSpanningTreeBoruvka(
            double[][] distanceMatrix, List<double[]> vectors, HDBSCANConfig config, Random random) {
        int n = distanceMatrix.length;
        
        // 对于大型数据集，可以采用随机采样的方式减少计算量
        int sampleSize = (int) Math.min(n, Math.max(n * 0.1, 100)); // 采样10%或至少100个点
        
        log.info("使用近似Boruvka算法构建MST，采样点数: {}/{}", sampleSize, n);
        
        // 随机选择采样点
        Set<Integer> sampledIndices = new HashSet<>();
        while (sampledIndices.size() < sampleSize) {
            sampledIndices.add(random.nextInt(n));
        }
        
        // 基于采样点构建近似MST（简化实现，此处仍调用完整算法）
        return buildMinimumSpanningTreeBoruvka(distanceMatrix, vectors, config);
    }
    
    /**
     * 保存MST数据供可视化使用
     */
    private static void saveMSTForVisualization(double[][] mst, List<double[]> vectors) {
        // 这里可以实现将MST保存为JSON或其他格式，以便后续可视化
        // 简化实现：仅记录日志
        log.info("生成MST数据供可视化使用，MST边数: {}", mst.length);
        // 实际实现中，可以将MST数据保存到文件或返回给调用方
    }
    
    /**
     * 计算距离矩阵
     */
    private static double[][] computeDistanceMatrix(List<double[]> vectors, HDBSCANConfig config) {
        int n = vectors.size();
        double[][] distanceMatrix = new double[n][n];
        
        for (int i = 0; i < n; i++) {
            for (int j = i + 1; j < n; j++) {
                double distance = calculateDistance(vectors.get(i), vectors.get(j), config.getMetric());
                distanceMatrix[i][j] = distance;
                distanceMatrix[j][i] = distance; // 距离矩阵是对称的
            }
        }
        
        return distanceMatrix;
    }
    
    /**
     * 计算两个向量之间的距离
     */
    private static double calculateDistance(double[] v1, double[] v2, String metric) {
        switch (metric.toLowerCase()) {
            case "manhattan":
                return calculateManhattanDistance(v1, v2);
            case "cosine":
                return calculateCosineDistance(v1, v2);
            case "euclidean":
            default:
                return calculateEuclideanDistance(v1, v2);
        }
    }
    
    /**
     * 计算欧几里得距离
     */
    private static double calculateEuclideanDistance(double[] v1, double[] v2) {
        double sum = 0;
        for (int i = 0; i < v1.length; i++) {
            double diff = v1[i] - v2[i];
            sum += diff * diff;
        }
        return Math.sqrt(sum);
    }
    
    /**
     * 计算曼哈顿距离
     */
    private static double calculateManhattanDistance(double[] v1, double[] v2) {
        double sum = 0;
        for (int i = 0; i < v1.length; i++) {
            sum += Math.abs(v1[i] - v2[i]);
        }
        return sum;
    }
    
    /**
     * 计算余弦距离
     */
    private static double calculateCosineDistance(double[] v1, double[] v2) {
        double dotProduct = 0.0;
        double normA = 0.0;
        double normB = 0.0;
        for (int i = 0; i < v1.length; i++) {
            dotProduct += v1[i] * v2[i];
            normA += v1[i] * v1[i];
            normB += v2[i] * v2[i];
        }
        if (normA == 0 || normB == 0) {
            return 1.0; // 当向量为零向量时，距离为最大
        }
        return 1.0 - (dotProduct / (Math.sqrt(normA) * Math.sqrt(normB)));
    }
    
    /**
     * 计算互达距离矩阵
     * 互达距离是两点之间的最大值：
     * 1. 点a的核心距离
     * 2. 点b的核心距离
     * 3. 点a和点b之间的原始距离
     */
    private static double[][] computeMutualReachabilityMatrix(double[][] distanceMatrix, int minPoints, double alpha) {
        int n = distanceMatrix.length;
        double[][] mutualReachabilityMatrix = new double[n][n];
        
        // 计算每个点的核心距离（到第minPoints个最近邻居的距离）
        double[] coreDistances = new double[n];
        for (int i = 0; i < n; i++) {
            double[] distances = distanceMatrix[i].clone();
            Arrays.sort(distances);
            // 第minPoints个距离（考虑到自身距离为0，所以是索引minPoints）
            coreDistances[i] = distances[Math.min(minPoints, distances.length - 1)];
        }
        
        // 计算互达距离矩阵
        for (int i = 0; i < n; i++) {
            for (int j = 0; j < n; j++) {
                if (i == j) {
                    mutualReachabilityMatrix[i][j] = 0.0;
                } else {
                    // 互达距离是三个值的最大值
                    double originalDistance = distanceMatrix[i][j];
                    if (alpha != 1.0) {
                        originalDistance /= alpha;
                    }
                    mutualReachabilityMatrix[i][j] = Math.max(
                            Math.max(coreDistances[i], coreDistances[j]),
                            originalDistance);
                }
            }
        }
        
        return mutualReachabilityMatrix;
    }
    
    /**
     * 使用Prim算法构建最小生成树
     */
    private static double[][] buildMinimumSpanningTreePrim(double[][] distanceMatrix, List<double[]> vectors, HDBSCANConfig config) {
        int n = distanceMatrix.length;
        
        // 用于跟踪节点是否已经在MST中
        boolean[] inMST = new boolean[n];
        
        // 用于存储从MST中选择的节点到其他节点的最小距离
        double[] key = new double[n];
        Arrays.fill(key, Double.MAX_VALUE);
        
        // 用于存储父节点
        int[] parent = new int[n];
        Arrays.fill(parent, -1);
        
        // 从节点0开始
        key[0] = 0;
        
        // 最小生成树的边
        List<double[]> mstEdges = new ArrayList<>();
        
        for (int count = 0; count < n; count++) {
            // 找到具有最小key值的节点（不在MST中）
            int u = -1;
            double minKey = Double.MAX_VALUE;
            for (int i = 0; i < n; i++) {
                if (!inMST[i] && key[i] < minKey) {
                    minKey = key[i];
                    u = i;
                }
            }
            
            // 如果找不到下一个节点，说明图不连通
            if (u == -1) {
                break;
            }
            
            // 将节点u添加到MST中
            inMST[u] = true;
            
            // 如果不是第一个节点，添加边
            if (parent[u] != -1) {
                mstEdges.add(new double[] { parent[u], u, distanceMatrix[parent[u]][u] });
            }
            
            // 更新其他节点的key值
            for (int v = 0; v < n; v++) {
                if (!inMST[v] && distanceMatrix[u][v] < key[v]) {
                    parent[v] = u;
                    key[v] = distanceMatrix[u][v];
                }
            }
        }
        
        // 将边列表转换为数组
        double[][] mst = new double[mstEdges.size()][3];
        for (int i = 0; i < mstEdges.size(); i++) {
            mst[i] = mstEdges.get(i);
        }
        
        // 按照距离排序
        Arrays.sort(mst, Comparator.comparingDouble(edge -> edge[2]));
        
        return mst;
    }
    
    /**
     * 使用Boruvka算法构建最小生成树
     * Boruvka算法适合分布式计算，对于大规模数据集效率较高
     */
    private static double[][] buildMinimumSpanningTreeBoruvka(double[][] distanceMatrix, List<double[]> vectors, HDBSCANConfig config) {
        int n = distanceMatrix.length;
        
        // 初始化，每个节点都是一个单独的组件
        int[] componentId = new int[n];
        for (int i = 0; i < n; i++) {
            componentId[i] = i;
        }
        
        // 用于存储MST的边
        List<double[]> mstEdges = new ArrayList<>();
        
        // 组件数量
        int numComponents = n;
        
        // 当只剩下一个组件时停止
        while (numComponents > 1) {
            // 为每个组件找到最近的其他组件
            int[] closestComponent = new int[n];
            double[] minDistance = new double[n];
            int[] minSource = new int[n];
            int[] minDest = new int[n];
            
            Arrays.fill(minDistance, Double.MAX_VALUE);
            
            // 找到每个组件连接到其他组件的最短边
            for (int i = 0; i < n; i++) {
                for (int j = 0; j < n; j++) {
                    if (i != j && componentId[i] != componentId[j]) {
                        if (distanceMatrix[i][j] < minDistance[componentId[i]]) {
                            minDistance[componentId[i]] = distanceMatrix[i][j];
                            minSource[componentId[i]] = i;
                            minDest[componentId[i]] = j;
                            closestComponent[componentId[i]] = componentId[j];
                        }
                    }
                }
            }
            
            // 添加找到的边并合并组件
            boolean[] alreadyAdded = new boolean[n];
            for (int i = 0; i < n; i++) {
                if (minDistance[i] != Double.MAX_VALUE && !alreadyAdded[i]) {
                    int source = minSource[i];
                    int dest = minDest[i];
                    double distance = distanceMatrix[source][dest];
                    
                    // 添加边
                    mstEdges.add(new double[] { source, dest, distance });
                    
                    // 合并组件
                    int oldComponentId = componentId[dest];
                    for (int j = 0; j < n; j++) {
                        if (componentId[j] == oldComponentId) {
                            componentId[j] = componentId[source];
                        }
                    }
                    
                    alreadyAdded[i] = true;
                    alreadyAdded[closestComponent[i]] = true;
                    
                    // 减少组件数量
                    numComponents--;
                }
            }
            
            // 防止无限循环
            if (mstEdges.size() == n - 1) {
                break;
            }
        }
        
        // 将边列表转换为数组
        double[][] mst = new double[mstEdges.size()][3];
        for (int i = 0; i < mstEdges.size(); i++) {
            mst[i] = mstEdges.get(i);
        }
        
        // 按照距离排序
        Arrays.sort(mst, Comparator.comparingDouble(edge -> edge[2]));
        
        return mst;
    }
    
    /**
     * 从最小生成树中提取聚类
     * 
     * @param mst 最小生成树，每行表示一条边 [起点, 终点, 距离]
     * @param n 数据点数量
     * @param minClusterSize 最小簇大小
     * @param allowSingleCluster 是否允许单簇
     * @return 聚类标签数组，-1表示噪声点
     */
    private static int[] extractClusters(double[][] mst, int n, int minClusterSize, boolean allowSingleCluster) {
        // 首先根据距离对MST边进行排序（已经在前面的方法中完成）
        
        // 初始化并查集，用于跟踪连通组件
        int[] parent = new int[n];
        for (int i = 0; i < n; i++) {
            parent[i] = i;
        }
        
        // 初始化簇大小
        int[] clusterSize = new int[n];
        Arrays.fill(clusterSize, 1);
        
        // 初始化簇标签
        int[] labels = new int[n];
        Arrays.fill(labels, -1); // 初始化所有点为噪声点
        
        // 构建聚类树
        List<List<Integer>> clusters = new ArrayList<>();
        for (int i = 0; i < n; i++) {
            clusters.add(new ArrayList<>());
            clusters.get(i).add(i);
        }
        
        // 按照距离从小到大处理边
        for (int i = 0; i < mst.length; i++) {
            int u = (int) mst[i][0];
            int v = (int) mst[i][1];
            
            // 找到u和v的根节点
            int rootU = find(parent, u);
            int rootV = find(parent, v);
            
            // 如果u和v不在同一个组件中
            if (rootU != rootV) {
                // 合并组件
                if (clusterSize[rootU] < clusterSize[rootV]) {
                    parent[rootU] = rootV;
                    clusterSize[rootV] += clusterSize[rootU];
                    // 将rootU的簇成员合并到rootV
                    clusters.get(rootV).addAll(clusters.get(rootU));
                    clusters.get(rootU).clear();
                } else {
                    parent[rootV] = rootU;
                    clusterSize[rootU] += clusterSize[rootV];
                    // 将rootV的簇成员合并到rootU
                    clusters.get(rootU).addAll(clusters.get(rootV));
                    clusters.get(rootV).clear();
                }
            }
        }
        
        // 提取满足最小簇大小的聚类
        int clusterLabel = 0;
        
        if (allowSingleCluster) {
            // 允许单簇的情况下，保持原有逻辑
            for (int i = 0; i < n; i++) {
                if (parent[i] == i && clusterSize[i] >= minClusterSize) {
                    // 为该簇的所有成员分配相同的标签
                    for (int member : clusters.get(i)) {
                        labels[member] = clusterLabel;
                    }
                    clusterLabel++;
                }
            }
        } else {
            // 不允许单簇的情况下，检查有效簇的数量
            List<Integer> validRoots = new ArrayList<>();
            for (int i = 0; i < n; i++) {
                if (parent[i] == i && clusterSize[i] >= minClusterSize) {
                    validRoots.add(i);
                }
            }
            
            // 如果只有一个有效簇，根据距离将其分成两部分
            if (validRoots.size() == 1) {
                int rootIndex = validRoots.get(0);
                List<Integer> members = clusters.get(rootIndex);
                
                // 简单策略：将簇分成两半
                int halfSize = members.size() / 2;
                for (int i = 0; i < members.size(); i++) {
                    int member = members.get(i);
                    if (i < halfSize) {
                        labels[member] = 0; // 第一个簇
                    } else {
                        labels[member] = 1; // 第二个簇
                    }
                }
                clusterLabel = 2;
            } else {
                // 有多个簇或没有满足条件的簇时，使用原有逻辑
                for (int i = 0; i < n; i++) {
                    if (parent[i] == i && clusterSize[i] >= minClusterSize) {
                        // 为该簇的所有成员分配相同的标签
                        for (int member : clusters.get(i)) {
                            labels[member] = clusterLabel;
                        }
                        clusterLabel++;
                    }
                }
            }
        }
        
        return labels;
    }
    
    /**
     * 并查集的查找操作（带路径压缩）
     */
    private static int find(int[] parent, int x) {
        if (parent[x] != x) {
            parent[x] = find(parent, parent[x]);
        }
        return parent[x];
    }
    
    /**
     * 将标签数组转换为聚类映射
     */
    private static Map<Integer, List<Integer>> convertLabelsToClusterMap(int[] labels) {
        Map<Integer, List<Integer>> clusters = new HashMap<>();
        
        for (int i = 0; i < labels.length; i++) {
            int label = labels[i];
            if (label >= 0) { // 忽略噪声点（标签为-1）
                clusters.computeIfAbsent(label, k -> new ArrayList<>()).add(i);
            }
        }
        
        // 如果没有有效的簇，创建一个空簇
        if (clusters.isEmpty()) {
            clusters.put(0, new ArrayList<>());
        }
        
        return clusters;
    }
} 