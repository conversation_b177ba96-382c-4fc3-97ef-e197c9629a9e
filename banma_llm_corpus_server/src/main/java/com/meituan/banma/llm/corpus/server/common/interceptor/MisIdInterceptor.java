package com.meituan.banma.llm.corpus.server.common.interceptor;

import com.meituan.banma.llm.corpus.api.constants.BizCode;
import com.meituan.banma.llm.corpus.api.exception.LlmCorpusException;
import com.meituan.banma.llm.corpus.server.common.CommonResult;
import com.meituan.banma.llm.corpus.server.service.IKnowledgeBaseService;
import com.sankuai.meituan.auth.util.UserUtils;
import com.sankuai.meituan.auth.vo.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.Arrays;
import java.util.List;

/**
 * 用户权限拦截器
 * 用于验证请求中的misId参数与当前登录用户是否匹配
 */
@Slf4j
@Component
public class MisIdInterceptor implements HandlerInterceptor {
    @Autowired
    private IKnowledgeBaseService knowledgeBaseService;

    private static final ThreadLocal<String> misIdThreadLocal = new ThreadLocal<>();
    private static final ThreadLocal<String> ssoidThreadLocal = new ThreadLocal<>();
    
    @Value("${mdp.sso.exclude-paths:}")
    private String excludePaths;
    
    private final AntPathMatcher pathMatcher = new AntPathMatcher();

    public static String getMisId() {
        return misIdThreadLocal.get();
    }

    /**
     * 获取当前线程的SSOID令牌
     * @return SSOID令牌
     */
    public static String getSsoId() {
        return ssoidThreadLocal.get();
    }
    
    /**
     * 获取当前线程的KM SSOID令牌 (兼容旧方法)
     * @return KM SSOID令牌
     */
    public static String getKmSsoid() {
        return getSsoId();
    }
    
    /**
     * 获取当前线程的用户SsoId令牌
     * @return 用户SsoId令牌
     */
    public static String getUserSsoId() {
        return getSsoId();
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 检查当前请求路径是否在排除路径列表中
        String requestPath = request.getRequestURI();
        if (isPathExcluded(requestPath)) {
            log.info("MisIdInterceptor# 请求路径: {} 在排除列表中，跳过拦截", requestPath);
            return true;
        }
        
        // 获取当前登录用户
        User user = UserUtils.getUser();
        if (user == null) {
            log.error("MisIdInterceptor# 用户未登录");
            writeResponse(response, CommonResult.error("用户未登录"));
            return false;
        }

        // 存储SSOID令牌
        extractAndStoreSsoId(request);

        // 获取请求中的misId参数
        String misId = request.getParameter("misId");
        
        // 如果请求中包含misId，且不为"0"，则进行权限校验
        if (misId != null && !"".equals(misId) && !"0".equals(misId)) {
            if (!misId.equals(user.getLogin())) {
                log.error("MisIdInterceptor# 权限校验失败, 当前用户:{}, 请求用户:{}", user.getLogin(), misId);
                writeResponse(response, CommonResult.error("无权限访问其他用户的数据"));
                return false;
            }
        }
        misId = user.getLogin();
        misIdThreadLocal.set(user.getLogin());
        String rgId = request.getParameter("rgId");
        if(rgId != null && !rgId.isEmpty()){
            boolean permission = knowledgeBaseService.validateUserPermission(misId, Long.valueOf(rgId));
            if (!permission) {
                throw new LlmCorpusException(BizCode.NO_PERMISSION.getCode(), "用户无对应值班组权限");
            }
        }
        return true;
    }
    
    /**
     * 判断请求路径是否在排除路径列表中
     * 
     * @param requestPath 请求路径
     * @return 是否排除
     */
    private boolean isPathExcluded(String requestPath) {
        if (excludePaths == null || excludePaths.isEmpty()) {
            return false;
        }
        
        List<String> pathPatterns = Arrays.asList(excludePaths.split(","));
        for (String pattern : pathPatterns) {
            if (pathMatcher.match(pattern.trim(), requestPath)) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * 从请求中提取并存储SSOID令牌
     * @param request HTTP请求
     */
    private void extractAndStoreSsoId(HttpServletRequest request){
        try {
            // 获取Cookies
            Cookie[] cookies = request.getCookies();
            if (cookies != null) {
                for (Cookie cookie : cookies) {
                    String name = cookie.getName();
                    String value = cookie.getValue();
                    // 检查名称是否以_ssoid结尾
                    if (name.endsWith("_ssoid")) {
                        log.info("Found _ssoid cookie: {} = {}", name, value);
                        ssoidThreadLocal.set(value);
                        return;
                    }
                }
            }
            log.warn("No _ssoid cookie found");
            throw LlmCorpusException.buildWithMsg(BizCode.LOGIN_FAIL.getCode(), "未登录");
        } catch (Exception e) {
            log.error("MisIdInterceptor# 提取SSOID失败:", e);
            ssoidThreadLocal.set("");
        }
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) throws Exception {
        // 请求处理之后进行调用
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        // 在整个请求结束之后被调用
        // 清除 ThreadLocal 中的数据
        misIdThreadLocal.remove();
        ssoidThreadLocal.remove();
    }

    /**
     * 向客户端写入响应
     */
    private void writeResponse(HttpServletResponse response, Object result) throws IOException {
        response.setContentType("application/json;charset=UTF-8");
        PrintWriter writer = response.getWriter();
        writer.write(com.alibaba.fastjson.JSON.toJSONString(result));
        writer.flush();
        writer.close();
    }
} 