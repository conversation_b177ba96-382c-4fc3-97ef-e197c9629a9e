package com.meituan.banma.llm.corpus.server.controller;

import com.google.common.base.Preconditions;
import com.meituan.banma.llm.corpus.server.service.ICallbackService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 交互消息回调处理Controller
 */
@Slf4j
@RestController
@RequestMapping("/callback")
public class CallbackController {

    @Autowired
    private ICallbackService callbackService;

    /**
     * 处理加知识库按钮回调
     * 
     * @param request 回调请求参数
     * @return 回调响应
     */
    @PostMapping("/knowledge/add")
    @ResponseBody
    public Map<String, Object> handleKnowledgeAddCallback(@RequestBody Map<String, Object> request) {
        Map<String, Object> result = new HashMap<>();
        Map<String, Object> data = new HashMap<>();
        
        try {
            log.info("收到加知识库回调请求: {}", request);
            
            // 参数校验
            Preconditions.checkArgument(request != null, "请求参数不能为空");
            
            Map<String, Object> bizParams = (Map<String, Object>) request.get("bizParams");
            Map<String, Object> userInfo = (Map<String, Object>) request.get("userInfo");
            
            Preconditions.checkArgument(bizParams != null, "bizParams不能为空");
            Preconditions.checkArgument(userInfo != null, "userInfo不能为空");
            Preconditions.checkArgument(bizParams.get("groupId") != null, "groupId不能为空");
            Preconditions.checkArgument(userInfo.get("uid") != null, "用户ID不能为空");

            return callbackService.handleKnowledgeAddCallback(request);
            
        } catch (IllegalArgumentException e) {
            log.error("处理加知识库回调失败, 参数校验异常: {}", e.getMessage(), e);
            data.put("message", e.getMessage());
            result.put("data", data);
            result.put("rescode", 400);
            return result;
        } catch (Exception e) {
            log.error("处理加知识库回调异常: {}", e.getMessage(), e);
            data.put("message", "系统异常: " + e.getMessage());
            result.put("data", data);
            result.put("rescode", 500);
            return result;
        }
    }
} 