package com.meituan.banma.llm.corpus.server.common.domain.dto.stat;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CorpusOperationStatusDTO {
    /**
     * 周期内语料助手机器人在区间内被@以及私聊消息列表
     */
    private List<CorpusBotMessageItem> corpusBotMessages;

    /**
     * 周期内语料生成任务列表
     */
    private List<CorpusGenerateTaskItemForStats> dailyCorpusGenerateTaskCountList;

    /**
     * 接入的所有值班组信息
     */
    private List<CorpusStatsRgInfoItem> corpusStatsRgInfoList;

    /**
     * 接入的所有Friday空间信息
     */
    private List<CorpusStatsFridaySpaceItem> corpusStatsFridaySpaceList;

}
