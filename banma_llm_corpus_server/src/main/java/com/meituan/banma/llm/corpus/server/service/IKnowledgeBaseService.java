package com.meituan.banma.llm.corpus.server.service;

import com.meituan.banma.llm.corpus.api.exception.LlmCorpusException;
import com.meituan.banma.llm.corpus.server.common.domain.dto.*;

import java.util.List;

public interface IKnowledgeBaseService {

    /**
     * 校验用户权限
     * @param misId 用户misId
     * @param rgId 值班组id
     * @return true/false
     */
    boolean validateUserPermission(String misId, Long rgId);

    /**
     * 将TT转换为知识
     * @param param 入参
     * @return task ticket
     */
    String createConvertTtToKnowledgeTask(ConvertTtToKnowledgeParam param, TTInfoDTO ttInfoDTO) throws LlmCorpusException;

    /**
     * 查询TT转换为知识任务状态
     * @param taskId id
     * @return 任务状态
     */
    TtToCorpusTaskInfo queryConvertTtToKnowledgeTask(String taskId) throws LlmCorpusException;

    /**
     * 查询TT转换为知识任务状态
     * @param ttId ttId
     * @return 任务状态
     */
    TtToCorpusTaskInfo queryConvertTtToKnowledgeTaskByTtId(String ttId);

    /**
     * 查询与query相似知识
     *
     * @param query 查询文本
     * @return 相似度列表
     */
    List<KnowledgeSimilarityRecord> queryKnowledgeSimilarity(Long rgId, String query) throws LlmCorpusException;

    /**
     * 查询与query相似知识(更新)，返回基础字段+分数
     *
     * @param query 查询文本
     * @return 相似度列表
     */
    List<KnowledgeSimilarityRecordWithScore> queryKnowledgeSimilarityWithScore(Long rgId, String query) throws LlmCorpusException;

    String createKnowledgeMergeTask(KnowledgeMergeParam param) throws LlmCorpusException;

    /**
     * 根据rgId查询所有相关的转换任务信息
     *
     * @param rgId 值班组ID
     * @return 转换任务信息列表
     */
    PageDTO<TtToCorpusTaskInfo> queryConvertTtToKnowledgeTasksByRgId(Long rgId, int pageNum, int pageSize);

    /**
     * 根据rgId和misId查询所有相关的转换任务信息
     *
     * @param rgId 值班组ID
     * @param misId 用户MIS ID
     * @return 转换任务信息列表
     */
    PageDTO<TtToCorpusTaskInfo> queryConvertTtToKnowledgeTasksByRgIdAndMisId(Long rgId, String misId, int pageNum, int pageSize);

    /**
     * 统计指定值班组和用户下ask_status为0的任务数量
     *
     * @param rgId 值班组ID
     * @param misId 用户MIS ID
     * @return 任务数量
     */
    int countModelOutputsWithAskStatusZero(Long rgId, String misId);

    /**
     * 获取语料质量评估结果
     *
     * @param query 待评估的语料内容
     * @return 语料质量评估结果
     * @throws LlmCorpusException 异常信息
     */
    ContentQualityDTO getContentQualityAssessment(String query) throws LlmCorpusException;

    /**
     * 更新任务状态为失败
     *
     * @param taskId 任务ID
     * @param message 错误消息
     * @param param 转换任务参数
     */
    void llmTaskFail(String taskId, String message, ConvertTtToKnowledgeParam param);

    List<KmToQaCorpusDTO> queryConvertKmToQaAsList(String taskId) throws LlmCorpusException;
}
