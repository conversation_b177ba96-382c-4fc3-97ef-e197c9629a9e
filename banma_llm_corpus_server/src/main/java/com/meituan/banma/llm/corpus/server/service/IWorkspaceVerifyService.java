package com.meituan.banma.llm.corpus.server.service;

/**
 * 工作空间验证重试服务接口
 */
public interface IWorkspaceVerifyService {
    
    /**
     * 记录工作空间验证失败任务
     * 
     * @param rgId 值班组ID
     * @param spaceId 工作空间ID
     * @param spaceName 工作空间名称
     * @param accessKey 访问密钥
     * @param appSecret 应用密钥
     * @param misId 用户ID
     * @param failReason 失败原因
     */
    void recordFailedTask(Long rgId, String spaceId, String spaceName, String accessKey, String appSecret, 
                        String misId, String failReason);
    
    /**
     * 执行定时重试任务
     */
    void processRetryTasks();
    
    /**
     * 重试验证工作空间
     * 
     * @param rgId 值班组ID
     * @param spaceId 工作空间ID
     * @param spaceName 工作空间名称
     * @param accessKey 访问密钥
     * @param appSecret 应用密钥
     * @return 验证结果
     */
    boolean retryVerifyWorkspace(Long rgId, String spaceId, String spaceName, String accessKey, String appSecret, String misId);
} 