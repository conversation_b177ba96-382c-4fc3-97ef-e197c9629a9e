package com.meituan.banma.llm.corpus.server.common.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Arrays;
import java.util.List;

/**
 * 灰度配置数据传输对象
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GrayscaleConfigDTO {
    
    /**
     * 监控组ID列表，逗号分隔的字符串
     */
    private String monitorGroupIds;
    
    /**
     * MIS ID列表，逗号分隔的字符串
     */
    private String misIds;
    
    /**
     * 获取监控组ID列表
     * 
     * @return 监控组ID列表
     */
    public List<String> getMonitorGroupIdList() {
        if (monitorGroupIds == null || monitorGroupIds.isEmpty()) {
            return null;
        }
        return Arrays.asList(monitorGroupIds.split(","));
    }
    
    /**
     * 获取MIS ID列表
     * 
     * @return MIS ID列表
     */
    public List<String> getMisIdList() {
        if (misIds == null || misIds.isEmpty()) {
            return null;
        }
        return Arrays.asList(misIds.split(","));
    }
} 