package com.meituan.banma.llm.corpus.server.service.impl;

import com.meituan.banma.llm.corpus.server.dal.entity.WorkspaceVerifyRetryEntity;
import com.meituan.banma.llm.corpus.server.dal.mapper.WorkspaceVerifyRetryMapper;
import com.meituan.banma.llm.corpus.server.service.IWorkspaceVerifyService;
import com.cip.crane.client.spring.annotation.Crane;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 工作空间验证重试服务实现
 */
@Slf4j
@Service
public class WorkspaceVerifyServiceImpl implements IWorkspaceVerifyService {
    
    @Autowired
    private WorkspaceVerifyRetryMapper workspaceVerifyRetryMapper;
    
    @Lazy
    @Autowired
    private WorkspaceServiceImpl workspaceService;
    
    /**
     * 最大重试次数
     */
    private static final int MAX_RETRY_COUNT = 3;
    
    /**
     * 每次处理的最大任务数
     */
    private static final int BATCH_SIZE = 20;
    
    @Override
    public void recordFailedTask(Long rgId, String spaceId, String spaceName, String accessKey, String appSecret, 
                              String misId, String failReason) {
        try {
            // 查询是否已有相同任务
            WorkspaceVerifyRetryEntity existingTask = workspaceVerifyRetryMapper.findTask(rgId, spaceId);
            
            if (existingTask != null) {
                // 已有任务，更新重试次数和状态
                existingTask.setRetryCount(existingTask.getRetryCount() + 1);
                existingTask.setStatus(0); // 重置为待处理
                existingTask.setFailReason(failReason);
                workspaceVerifyRetryMapper.updateTaskStatus(existingTask);
                log.info("更新工作空间验证重试任务: rgId={}, spaceId={}, retryCount={}", 
                        rgId, spaceId, existingTask.getRetryCount());
            } else {
                // 创建新任务
                WorkspaceVerifyRetryEntity task = WorkspaceVerifyRetryEntity.builder()
                        .rgId(rgId)
                        .spaceId(spaceId)
                        .spaceName(spaceName)
                        .accessKey(accessKey)
                        .appSecret(appSecret)
                        .retryCount(1)
                        .failReason(failReason)
                        .misId(misId)
                        .status(0) // 待处理
                        .build();
                workspaceVerifyRetryMapper.insertRetryTask(task);
                log.info("创建工作空间验证重试任务: rgId={}, spaceId={}", rgId, spaceId);
            }
        } catch (Exception e) {
            log.error("记录工作空间验证失败任务出错: rgId={}, spaceId={}, error={}", 
                    rgId, spaceId, e.getMessage(), e);
        }
    }
    
    @Crane("workspace-verify-retry-task")
    @Override
    public void processRetryTasks() {
        log.info("开始处理工作空间验证重试任务");
        try {
            // 查询已经卡住的任务（处理中但超时）
            List<WorkspaceVerifyRetryEntity> stuckTasks = workspaceVerifyRetryMapper.findStuckTasks();
            for (WorkspaceVerifyRetryEntity task : stuckTasks) {
                task.setStatus(0); // 重置为待处理
                workspaceVerifyRetryMapper.updateTaskStatus(task);
                log.info("重置卡住的任务: id={}, rgId={}, spaceId={}", task.getId(), task.getRgId(), task.getSpaceId());
            }
            
            // 查询待处理的任务
            List<WorkspaceVerifyRetryEntity> pendingTasks = workspaceVerifyRetryMapper.findPendingTasks(BATCH_SIZE);
            if (pendingTasks.isEmpty()) {
                log.info("没有待处理的工作空间验证重试任务");
                return;
            }
            
            log.info("发现{}个待处理的工作空间验证重试任务", pendingTasks.size());
            
            for (WorkspaceVerifyRetryEntity task : pendingTasks) {
                // 标记为处理中
                task.setStatus(1);
                workspaceVerifyRetryMapper.updateTaskStatus(task);
                
                try {
                    // 执行重试
                    if (task.getRetryCount() > MAX_RETRY_COUNT) {
                        log.warn("工作空间验证重试任务超过最大重试次数: id={}, rgId={}, spaceId={}, retryCount={}",
                                task.getId(), task.getRgId(), task.getSpaceId(), task.getRetryCount());
                        task.setStatus(3); // 处理失败
                        task.setFailReason("超过最大重试次数");
                        workspaceVerifyRetryMapper.updateTaskStatus(task);
                        continue;
                    }
                    
                    boolean success = retryVerifyWorkspace(
                            task.getRgId(), task.getSpaceId(), task.getSpaceName(), 
                            task.getAccessKey(), task.getAppSecret(), task.getMisId());
                    
                    if (success) {
                        // 处理成功
                        task.setStatus(2);
                        workspaceVerifyRetryMapper.updateTaskStatus(task);
                        log.info("工作空间验证重试任务处理成功: id={}, rgId={}, spaceId={}",
                                task.getId(), task.getRgId(), task.getSpaceId());
                    } else {
                        // 处理失败，增加重试次数
                        task.setStatus(0); // 重置为待处理
                        task.setRetryCount(task.getRetryCount() + 1);
                        task.setFailReason("重试验证失败");
                        workspaceVerifyRetryMapper.updateTaskStatus(task);
                        log.warn("工作空间验证重试任务处理失败: id={}, rgId={}, spaceId={}, retryCount={}",
                                task.getId(), task.getRgId(), task.getSpaceId(), task.getRetryCount());
                    }
                } catch (Exception e) {
                    // 处理异常
                    task.setStatus(0); // 重置为待处理
                    task.setRetryCount(task.getRetryCount() + 1);
                    task.setFailReason(e.getMessage());
                    workspaceVerifyRetryMapper.updateTaskStatus(task);
                    log.error("工作空间验证重试任务处理异常: id={}, rgId={}, spaceId={}, error={}",
                            task.getId(), task.getRgId(), task.getSpaceId(), e.getMessage(), e);
                }
            }
        } catch (Exception e) {
            log.error("处理工作空间验证重试任务出错: error={}", e.getMessage(), e);
        }
    }
    
    @Override
    public boolean retryVerifyWorkspace(Long rgId, String spaceId, String spaceName, String accessKey, String appSecret, String misId) {
        int retryCount = 0;
        String lastError = null;
        
        // 最多重试3次
        while (retryCount < 3) {
            try {
                // 获取访问令牌
                String accessToken = workspaceService.getWorkspaceAccessToken(accessKey, appSecret);
                
                // 调用工作空间验证方法
                boolean result = workspaceService.checkAppFactoryStatus(rgId, spaceId, accessToken, accessKey, appSecret, spaceName);
                if (result) {
                    log.info("工作空间验证成功: rgId={}, spaceId={}, 重试次数={}", rgId, spaceId, retryCount);
                    return true;
                }
                
                // 验证失败，记录日志
                log.warn("工作空间验证失败: rgId={}, spaceId={}, 重试次数={}", rgId, spaceId, retryCount);
                retryCount++;
                
                // 如果还有重试次数，短暂延迟后重试
                if (retryCount < 3) {
                    Thread.sleep(1000 * retryCount); // 随着重试次数增加等待时间
                }
            } catch (Exception e) {
                lastError = e.getMessage();
                retryCount++;
                
                log.warn("工作空间验证异常 (尝试 {}/3): rgId={}, spaceId={}, error={}",
                        retryCount, rgId, spaceId, e.getMessage());
                
                if (!isNetworkError(e)) {
                    // 如果不是网络错误，不再继续重试
                    log.error("非网络错误，中止重试: rgId={}, spaceId={}", rgId, spaceId);
                    break;
                }
                
                // 如果还有重试次数，短暂延迟后重试
                if (retryCount < 3) {
                    try {
                        Thread.sleep(1000 * retryCount); // 随着重试次数增加等待时间
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                    }
                }
            }
        }
        
        // 如果到这里，说明重试次数已用完或者遇到了非网络错误
        log.error("工作空间验证失败，已达到最大重试次数: rgId={}, spaceId={}, 最后错误={}",
                rgId, spaceId, lastError);
        return false;
    }
    
    /**
     * 判断是否为网络错误
     */
    private boolean isNetworkError(Exception e) {
        String message = e.getMessage();
        if (message == null) {
            return false;
        }
        
        // 检查是否包含常见网络错误关键字
        return message.contains("timeout") || 
               message.contains("connection") || 
               message.contains("网络") || 
               message.contains("network") || 
               message.contains("连接") ||
               message.contains("Connection refused") ||
               message.contains("Connection reset");
    }
} 