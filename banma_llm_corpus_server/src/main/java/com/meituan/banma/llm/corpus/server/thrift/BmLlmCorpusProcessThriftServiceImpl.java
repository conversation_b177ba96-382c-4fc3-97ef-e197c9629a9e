package com.meituan.banma.llm.corpus.server.thrift;

import com.meituan.banma.llm.corpus.api.client.BmLlmCorpusProcessThriftService;
import com.meituan.banma.llm.corpus.server.service.IReviewService;
import com.meituan.banma.llm.corpus.server.utils.HtmlContentUtil;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftServer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
@MdpThriftServer()
public class BmLlmCorpusProcessThriftServiceImpl implements BmLlmCorpusProcessThriftService {

    @Autowired
    private IReviewService reviewService;

    @Override
    public String queryLatestContentByRgId(long rgId, String ak) {
        log.info("#BmLlmCorpusProcessIfaceImpl.queryLatestContentByRgId# rgId={}, ak={}", rgId, ak);
        String Content = reviewService.queryLatestContentByRgId(rgId, ak);
        return Content;
    }

    @Override
    public String testString() {
        log.info("#BmLlmCorpusProcessIfaceImpl:testString");
        return HtmlContentUtil.testString();
    }

    @Override
    public String testRandomString() {
        log.info("#BmLlmCorpusProcessIfaceImpl:testRandomString");
        return HtmlContentUtil.testRandomString();
    }
}
