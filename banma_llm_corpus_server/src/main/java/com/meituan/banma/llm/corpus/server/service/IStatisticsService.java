package com.meituan.banma.llm.corpus.server.service;

import com.meituan.banma.llm.corpus.api.exception.LlmCorpusException;
import com.meituan.banma.llm.corpus.server.common.domain.dto.FridayAppInfo;
import com.meituan.banma.llm.corpus.server.common.domain.dto.FridayConversationMessageDTO;
import com.meituan.banma.llm.corpus.server.common.domain.dto.StatisticsReportParam;
import com.meituan.banma.llm.corpus.server.common.domain.dto.stat.AppStatusDTO;
import com.meituan.banma.llm.corpus.server.common.domain.dto.stat.CorpusOperationStatusDTO;
import com.meituan.banma.llm.corpus.server.dal.entity.FridayBotConversationEntity;

import java.util.List;

public interface IStatisticsService {
    /**
     * 上报问题知识库检索结果
     * @param param
     */
    void reportQuestionRetrievalResult(StatisticsReportParam param) throws LlmCorpusException;

    /**
     * 根据应用ID和日期范围查询统计信息
     * @param appId 应用ID
     * @param beginDate 开始日期时间戳（毫秒）
     * @param endDate 结束日期时间戳（毫秒）
     * @param forceGenerate 是否强制重新生成报表，不查询已有数据
     * @return 统计报表DTO
     * @throws LlmCorpusException 自定义异常
     */
    AppStatusDTO queryStatByAppIdAndDate(String appId, Long beginDate, Long endTime, Boolean forceGenerate) throws LlmCorpusException;
    
    /**
     * 原查询方法，保持参数兼容性
     * @param appId 应用ID
     * @param beginDate 开始日期时间戳（毫秒）
     * @param endDate 结束日期时间戳（毫秒）
     * @return 统计报表DTO
     * @throws LlmCorpusException 自定义异常
     */
    AppStatusDTO queryStatByAppIdAndDate(String appId, Long beginDate, Long endTime) throws LlmCorpusException;

    /**
     * 查询所有Friday应用信息列表
     * @return Friday应用信息列表
     */
    List<FridayAppInfo> queryFridayAppInfoList();

    List<FridayAppInfo> queryFridayAppInfoListWithValidate(String misId);

    /**
     * 验证用户是否有查看指定应用统计数据的权限
     * @param misId 用户ID
     * @param appId 应用ID
     * @return 是否有权限
     */
    boolean validateUserPermissionForApp(String misId, String appId);
    /**
     * 根据会话ID查询会话的所有消息记录
     * @param conversationId 会话ID
     * @return 消息记录列表
     * @throws LlmCorpusException 自定义异常
     */
    List<FridayConversationMessageDTO> queryConversationMessages(String conversationId) throws LlmCorpusException;
    
    /**
     * 定时生成统计报表任务
     * 将根据配置时间范围生成所有应用的报表并存储到数据库
     */
    void generateStatisticsReportTask();
    
    /**
     * 生成上一自然周（周一至周日）的统计报表定时任务
     * 为所有应用生成上一周的统计报表
     */
    void generateWeeklyStatisticsReportTask();
    
    /**
     * 手动触发重新生成指定应用和时间范围的统计报表
     * @param appId 应用ID
     * @param beginDate 开始日期时间戳（毫秒）
     * @param endDate 结束日期时间戳（毫秒）
     * @return 是否生成成功
     * @throws LlmCorpusException 自定义异常
     */
    boolean regenerateStatisticsReport(String appId, Long beginDate, Long endDate) throws LlmCorpusException;

    /**
     * 获取语料服务运营报表
     * @param beginDate 开始日期时间戳（毫秒）
     * @param endTime 结束日期时间戳（毫秒）
     * @param forceGenerate 是否强制重新生成报表，不查询已有数据
     */
    CorpusOperationStatusDTO getCorpusOperationStatus(Long beginDate, Long endTime, Boolean forceGenerate) throws LlmCorpusException;

    /**
     * 生成语料运营状态报表定时任务
     * 每天自动生成最近7天的语料运营状态报表
     */
    void generateCorpusOperationStatisticsReportTask();

    /**
     * 生成上一自然周的语料运营状态报表定时任务
     */
    void generateWeeklyCorpusOperationStatisticsReportTask();
}
