package com.meituan.banma.llm.corpus.server.dal.mapper;

import com.meituan.banma.llm.corpus.server.dal.entity.RgBackgroundKnowledgeEntity;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import java.util.List;

@Mapper
public interface RgBackgroundKnowledgeMapper {
    // 查询某个值班组的背景知识总数
    @Select("SELECT COUNT(*) FROM rg_background_knowledge WHERE rg_id=#{rgId}")
    int countByRgId(@Param("rgId") long rgId);

    // 根据rgId分页查询历史背景知识
    @Select("SELECT * FROM rg_background_knowledge WHERE rg_id=#{rgId} ORDER BY version DESC LIMIT #{pageSize} OFFSET #{offset}")
    List<RgBackgroundKnowledgeEntity> queryByRgId(@Param("rgId") long rgId, @Param("pageSize") int pageSize, @Param("offset") int offset);

    // 插入（仅当version更大时才插入）
    @Insert("INSERT INTO rg_background_knowledge (rg_id, knowledge_content, version, update_time, mis_id) " +
            "SELECT #{rgId}, #{knowledgeContent}, #{version}, #{updateTime}, #{misId} " +
            "WHERE NOT EXISTS (SELECT 1 FROM rg_background_knowledge WHERE rg_id=#{rgId} AND version>=#{version})")
    int insertWithVersionCheck(RgBackgroundKnowledgeEntity entity);

    // 查询最新一条背景知识
    @Select("SELECT * FROM rg_background_knowledge WHERE rg_id=#{rgId} ORDER BY version DESC LIMIT 1")
    RgBackgroundKnowledgeEntity queryLatestByRgId(@Param("rgId") long rgId);

    // 根据ID查询单条背景知识记录
    @Select("SELECT * FROM rg_background_knowledge WHERE id=#{id}")
    RgBackgroundKnowledgeEntity queryById(@Param("id") Long id);
} 