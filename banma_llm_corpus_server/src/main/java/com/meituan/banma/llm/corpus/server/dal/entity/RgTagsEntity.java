package com.meituan.banma.llm.corpus.server.dal.entity;

import lombok.Data;

import java.sql.Timestamp;

@Data
public class RgTagsEntity {
    // 主键，自增ID
    private Long id;

    // 值班组ID
    private Long rgId;

    // 标签名
    private String tagName;

    // 标签描述
    private String tagDesc;

    // 操作人misID
    private String misId;

    // 创建时间
    private Timestamp ctime;

    // 更新时间
    private Timestamp utime;
} 