package com.meituan.banma.llm.corpus.server.common.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import com.alibaba.fastjson.annotation.JSONField;

import java.util.List;

@Data
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class LlmDxGroupMonitoringTaskMessageDTO {
    /**
     * 重点监控的用户misId，这些用户的发言是需要关注的提问的概率会更大
     */
    @JSONField(ordinal = 1)
    private List<String> importantMisIdList;
    /**
     * 重点监控的组织id列表，这些组织的人的发言是需要关注的提问的概率会更大
     */
    @JSONField(ordinal = 2)
    private List<String> importantOrgIdList;
    /**
     * 重点监控的关键词列表，这些关键词的发言是需要关注的提问的概率会更大
     */
    @JSONField(ordinal = 3)
    private List<String> importantKeyWordList;

    /**
     * 问题类型列表，大模型应当将问题归类到列表中的类型
     */
    private List<QuestionType> questionTypeList;
    /**
     * 群聊记录
     */
    @JSONField(ordinal = 4)
    private List<DxChatMessageRecord> chatMessageRecordList;
}
