package com.meituan.banma.llm.corpus.server.service;

import com.meituan.banma.llm.corpus.api.exception.LlmCorpusException;
import com.meituan.banma.llm.corpus.server.common.domain.dto.RelatedIdsDTO;
import com.meituan.banma.llm.corpus.server.dal.entity.RgDatasetDocumentEntity;
import java.util.List;


public interface IReviewService {
    /**
     * 查询模型输出根据ticketId
     * @param ticketId
     * @return
     */
    Object queryModelOutputByTicketId(String ticketId);

    /**
     * 保存用户修改后输出
     * @param ticketId
     * @param modifiedContent
     * @param misId
     * @param rgId
     * @param tagsIds 标签id列表，英文逗号分隔，如1,2,3
     */
    void saveModifiedOutput(String ticketId, String title, String modifiedContent, String misId, Long rgId, String taskId, String tagsIds);

    /**
     * 刷新知识库
     * @param rgId
     * @param misId
     */
    void refreshKbDocument(Long rgId, String misId);
    
    /**
     * 创建数据集和文档
     * 该方法会自动获取accessToken，然后调用内部方法创建数据集和文档
     * 
     * @param rgId 研发组ID
     * @param misId 用户ID
     * @return 创建的数据集文档实体
     */
    RgDatasetDocumentEntity createDatasetDocument(Long rgId, String misId, String spaceId, String spaceName) throws LlmCorpusException;

    /**
     * 根据rgId查询知识库文档记录
     * @param rgId 研发组ID
     * @return 知识库文档记录列表
     */
    List<RgDatasetDocumentEntity> findDatasetDocumentByRgId(Long rgId) throws LlmCorpusException;

    /**
     * 查询某个rgId下所有ticketId的最新版本内容
     * @param rgId
     * @return
     */
    String queryLatestContentByRgId(Long rgId, String ak);

    /**
     * 获取某个rgId对应的三个关联表的最大版本ID
     * @param rgId 值班组ID
     * @return 包含三个关联表ID的DTO
     */
    RelatedIdsDTO getRelatedIds(long rgId);

    String testString();

    String testRandomString();
}
