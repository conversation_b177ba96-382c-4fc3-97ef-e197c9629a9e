package com.meituan.banma.llm.corpus.server.dal.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InquiryMonitoringEntity {
    private Long id;
    /**
     * 监控组id
     */
    private Long monitorGroupId;
    /**
     * 提问人misId
     */
    private String questionerMisId;
    /**
     * 提问人empId
     */
    private String questionerEmpId;
    /**
     * 提问人组织id
     */
    private String questionerOrgId;
    /**
     * 提问人组织名称
     */
    private String questionerOrgName;
    /**
     * 提问消息原文
     */
    private String rawQuestionMsg;
    /**
     * AI处理后的提问消息
     */
    private String summarizedQuestion;
    /**
     * 提问类型
     */
    private Integer questionType;

    /**
     * 回复状态：0-无回应，1-有回应
     */
    private Integer replyStatus;

    /**
     * 提取自大象消息id
     */
    private Long fromMessageId;
    /**
     * 消息发送时间戳
     */
    private Long messageCts;
    /**
     * 大象群id
     */
    private Long dxGroupId;
    
    /**
     * 大象群名称
     */
    private String dxGroupName;

    private Long ctime;
    private Long utime;
}
