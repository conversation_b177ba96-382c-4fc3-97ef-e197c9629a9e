package com.meituan.banma.llm.corpus.server.common.domain.dto;

import groovy.transform.builder.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
@Builder
public class FridayConversationUtterance {
    
    private String title;
    private String description;
    private String orgId;
    private String ttId;
    private List<DxChatMessageRecord> chatMessageRecord;
    private List<Map<String, String>> imageUrls;
}
