package com.meituan.banma.llm.corpus.server.rpc.emp.impl;

import com.dianping.rhino.annotation.Degrade;
import com.meituan.banma.llm.corpus.api.constants.BizCode;
import com.meituan.banma.llm.corpus.api.exception.LlmCorpusException;
import com.meituan.banma.llm.corpus.server.rpc.emp.EmpQueryRpcService;
import com.sankuai.meituan.org.opensdk.model.domain.Emp;
import com.sankuai.meituan.org.opensdk.service.EmpService;
import com.sankuai.meituan.org.queryservice.exception.MDMThriftException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class EmpQueryRpcServiceImpl implements EmpQueryRpcService {
    @Autowired
    private EmpService empService;

    @Override
    @Degrade(rhinoKey = "EmpQueryRpcServiceImpl.batchQueryByMis", fallBackMethod = "batchQueryByMisFallback")
    public List<Emp> batchQueryByMis(List<String> misIdList, Date snapshot) throws LlmCorpusException {
        try {
            return empService.batchQueryByMis(misIdList, snapshot);
        } catch (MDMThriftException e) {
            log.error("#EmpQueryRpcServiceImpl.batchQueryByMis# error,misIdList:{},snapshot:{}", misIdList, snapshot,
                    e);
            throw LlmCorpusException.buildWithMsg(BizCode.EMP_QUERY_ERROR, "查询员工信息失败");
        } catch (Exception e) {
            log.error("#EmpQueryRpcServiceImpl.batchQueryByMis# error,misIdList:{},snapshot:{}", misIdList, snapshot,
                    e);
            throw LlmCorpusException.buildWithMsg(BizCode.SERVER_INTERNAL_ERROR, "查询员工信息出错");
        }
    }

    public List<Emp> batchQueryByMisFallback(List<Long> misIdList, Date snapshot) {
        log.warn("#EmpQueryRpcServiceImpl.batchQueryByMis# fallback,misIdList:{},snapshot:{}", misIdList, snapshot);
        return null;
    }

    @Override
    @Degrade(rhinoKey = "EmpQueryRpcServiceImpl.batchQueryByEmpId", fallBackMethod = "batchQueryByEmpIdFallback")
    public List<Emp> batchQueryByEmpId(List<String> empIdList, Date snapshot) throws LlmCorpusException {
        try {
            return empService.batchQuery(empIdList, snapshot);
        } catch (MDMThriftException e) {
            log.error("#EmpQueryRpcServiceImpl.batchQueryByMis# error,misIdList:{},snapshot:{}", empIdList, snapshot,
                    e);
            throw LlmCorpusException.buildWithMsg(BizCode.EMP_QUERY_ERROR, "查询员工信息失败");
        } catch (Exception e) {
            log.error("#EmpQueryRpcServiceImpl.batchQueryByMis# error,misIdList:{},snapshot:{}", empIdList, snapshot,
                    e);
            throw LlmCorpusException.buildWithMsg(BizCode.SERVER_INTERNAL_ERROR, "查询员工信息出错");
        }
    }
    public List<Emp> batchQueryByEmpIdFallback(List<String> empIdList, Date snapshot) {
        log.warn("#EmpQueryRpcServiceImpl.batchQueryByEmpId# fallback,empIdList:{},snapshot:{}", empIdList, snapshot);
        return null;
    }
}
