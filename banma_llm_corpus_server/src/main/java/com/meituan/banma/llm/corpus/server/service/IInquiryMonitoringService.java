package com.meituan.banma.llm.corpus.server.service;

import com.meituan.banma.llm.corpus.api.exception.LlmCorpusException;
import com.meituan.banma.llm.corpus.server.common.domain.dto.PageDTO;
import com.meituan.banma.llm.corpus.server.controller.request.monitoring.MonitoringGroupRequest;
import com.meituan.banma.llm.corpus.server.controller.request.monitoring.QuestionTypeRequest;
import com.meituan.banma.llm.corpus.server.dal.entity.InquiryMonitoringEntity;
import com.meituan.banma.llm.corpus.server.dal.entity.MonitoringGroupEntity;
import com.meituan.banma.llm.corpus.server.dal.entity.QuestionTypeEntity;
import java.util.List;
import java.util.Map;

/**
 * 问询监控服务接口
 */
public interface IInquiryMonitoringService {
    
    /**
     * 根据时间范围查询监控数据并分页返回
     * 
     * @param startTime 开始时间戳（毫秒）
     * @param endTime 结束时间戳（毫秒）
     * @param pageNum 页码
     * @param pageSize 每页记录数
     * @param monitorGroupIds 监控组ID列表，为空时从配置中获取
     * @return 分页监控数据
     * @throws LlmCorpusException 业务异常
     */
    PageDTO<InquiryMonitoringEntity> queryMonitoringData(Long startTime, Long endTime, Integer pageNum, Integer pageSize, List<Long> monitorGroupIds) throws LlmCorpusException;
    
    /**
     * 根据时间范围查询监控数据，生成Excel并上传到S3
     * 
     * @param startTime 开始时间戳（毫秒）
     * @param endTime 结束时间戳（毫秒）
     * @param monitorGroupIds 监控组ID列表，为空时从配置中获取
     * @return S3下载链接，生成失败则返回null
     * @throws LlmCorpusException 业务异常
     */
    String generateExcelAndUploadToS3(Long startTime, Long endTime, List<Long> monitorGroupIds) throws LlmCorpusException;

    /**
     * 验证监控任务是否可行
     * 检查小助手和所有监控组成员是否在指定的群聊中
     *
     * @param monitoringGroupEntity 监控组实体
     * @throws LlmCorpusException 业务异常
     */
    void validateMonitoringTask(MonitoringGroupEntity monitoringGroupEntity) throws LlmCorpusException;
    
    /**
     * 新增监控组
     *
     * @param monitoringGroupEntity 监控组实体
     * @return 新增的监控组ID
     * @throws LlmCorpusException 业务异常
     */
    Long addMonitoringGroup(MonitoringGroupEntity monitoringGroupEntity) throws LlmCorpusException;
    
    /**
     * 将监控组请求对象转换为实体对象
     * 
     * @param request 监控组请求对象
     * @return 监控组实体对象
     */
    MonitoringGroupEntity convertRequestToEntity(MonitoringGroupRequest request);
    
    /**
     * 保存问题类型列表
     *
     * @param monitoringGroupId 监控组ID
     * @param questionTypes 问题类型列表
     * @throws LlmCorpusException 业务异常
     */
    void saveQuestionTypes(Long monitoringGroupId, List<QuestionTypeRequest> questionTypes) throws LlmCorpusException;
    
    /**
     * 获取监控组的问题类型列表
     *
     * @param monitoringGroupId 监控组ID
     * @return 问题类型列表
     */
    List<QuestionTypeEntity> getQuestionTypesByMonitoringGroupId(Long monitoringGroupId);

    /**
     * 根据MIS ID查询该用户参与的所有监控组
     *
     * @param misId 用户MIS ID
     * @return 监控组列表
     * @throws LlmCorpusException 业务异常
     */
    List<MonitoringGroupEntity> getMonitoringGroupsByMisId(String misId) throws LlmCorpusException;

    /**
     * 更新监控组信息
     *
     * @param monitoringGroupEntity 监控组实体
     * @return 是否更新成功
     * @throws LlmCorpusException 业务异常
     */
    boolean updateMonitoringGroup(MonitoringGroupEntity monitoringGroupEntity) throws LlmCorpusException;

    /**
     * 根据ID获取监控组信息
     *
     * @param monitoringGroupId 监控组ID
     * @return 监控组实体，不存在则返回null
     * @throws LlmCorpusException 业务异常
     */
    MonitoringGroupEntity getMonitoringGroupById(Long monitoringGroupId) throws LlmCorpusException;

    /**
     * 删除监控组
     *
     * @param monitoringGroupId 监控组ID
     * @return 是否删除成功
     * @throws LlmCorpusException 业务异常
     */
    boolean deleteMonitoringGroup(Long monitoringGroupId) throws LlmCorpusException;

    /**
     * 获取指定时间范围和监控组的咨询统计数据
     *
     * @param startTime 开始时间戳（毫秒）
     * @param endTime 结束时间戳（毫秒）
     * @param monitorGroupIds 监控组ID列表
     * @return 统计数据，包含总咨询数量、问题类型数量、总咨询人数等信息
     * @throws LlmCorpusException 业务异常
     */
    Map<String, Object> getInquiryStatistics(Long startTime, Long endTime, List<Long> monitorGroupIds) throws LlmCorpusException;

    /**
     * 查询全部监控数据（不分页）
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param monitorGroupIds 监控组ID列表
     * @return 监控数据列表
     * @throws LlmCorpusException 业务异常
     */
    List<InquiryMonitoringEntity> queryAllMonitoringData(Long startTime, Long endTime, List<Long> monitorGroupIds) throws LlmCorpusException;

    /**
     * 修改监控组信息（带特殊校验）
     * @param request 监控组请求参数
     * @return 修改结果Map，包含monitoringGroup、updated等
     * @throws LlmCorpusException 业务异常
     */
    Map<String, Object> updateMonitoringGroupWithValidation(MonitoringGroupRequest request) throws LlmCorpusException;
} 