package com.meituan.banma.llm.corpus.server.service.impl;

import com.dianping.zebra.group.jdbc.GroupDataSource;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.meituan.banma.llm.corpus.server.common.JacksonUtil;
import com.meituan.banma.llm.corpus.server.common.domain.vo.ColumnSchema;
import com.meituan.banma.llm.corpus.server.common.domain.vo.IndexSchema;
import com.meituan.banma.llm.corpus.server.common.domain.vo.TableSchema;
import com.meituan.banma.llm.corpus.server.service.ICommonSqlExecuteService;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfigListener;
import com.meituan.mdp.boot.starter.config.client.MdpConfigClient;
import com.meituan.mdp.boot.starter.config.vo.ConfigEvent;
import com.sankuai.meituan.banma.qingniu.degrade.utils.QingniuDegradeUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.jetbrains.annotations.NotNull;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Class CommonSqlExecuteService
 * Project banma_llm_corpus_process_server
 *
 * <AUTHOR>
 * @date 2025/4/8
 * Description 通用sql查询
 */

@Slf4j
@Service
public class CommonSqlExecuteService implements ICommonSqlExecuteService, ApplicationListener<ApplicationReadyEvent> {
    private static final String COMMON_SQL_EXECUTE_CONFIG = "common_sql_execute_config";

    // <jdbcRef, >
    private static final Map<String, Map<String, TableConfig>> configMap = Maps.newConcurrentMap();

    // <jdbcRef, GroupDataSource>
    private Map<String, GroupDataSource> groupDataSourceMap = Maps.newConcurrentMap();

    // [jdbcRef]
    private Set<String> datasourceBlacklist = Sets.newConcurrentHashSet();

    // <jdbcRef, JdbcTemplate>
    private Map<String, JdbcTemplate> jdbcTemplateMap = Maps.newConcurrentMap();

    // <jdbcRef, tableName>
    @MdpConfig("common_sql_execute_table_whitelist:{}")
    private HashMap<String, ArrayList<String>> tableWhitelist;

    // <jdbcRef, tableName>
    @MdpConfig("common_sql_execute_table_blacklist:{}")
    private HashMap<String, ArrayList<String>> tableBlacklist;

    @Resource
    private MdpConfigClient mdpConfigClient;

    /**
     * 获取所有表结构
     */
    @Override
    public List<TableSchema> getAllTableSchema() {
        List<String> jdbcRefList = new ArrayList<>(groupDataSourceMap.keySet());
        jdbcRefList.removeAll(datasourceBlacklist);
        List<TableSchema> tableSchemaList = new ArrayList<>();
        for (String jdbcRef : jdbcRefList) {
            JdbcTemplate jdbcTemplate = jdbcTemplateMap.get(jdbcRef);
            // 获取表名
            List<Map<String, Object>> tables = listAllTablesName(jdbcTemplate)
                    .stream()
                    .filter(e -> !e.get("table_name").toString().startsWith("_shadow"))
                    .collect(Collectors.toList());
            for (Map<String, Object> table : tables) {
                String tableName = table.get("table_name").toString();
                String tableDescription = table.get("table_comment").toString();

                if (QingniuDegradeUtils.isNotDegrade("common_sql_execute_table_whitelist_degrade")) {
                    if (MapUtils.isEmpty(tableWhitelist) || CollectionUtils.isEmpty(tableWhitelist.get(jdbcRef)) || !tableWhitelist.get(jdbcRef).contains(tableName)) {
                        continue;
                    }
                } else {
                    if (MapUtils.isNotEmpty(tableBlacklist) && CollectionUtils.isNotEmpty(tableBlacklist.get(jdbcRef)) && tableBlacklist.get(jdbcRef).contains(tableName)) {
                        continue;
                    }
                }

                // 获取字段信息
                List<Map<String, Object>> columns = getTableSchema(tableName, jdbcTemplate);
                TableConfig tableConfig = configMap.get(jdbcRef).get(tableName);

                List<ColumnSchema> columnSchemaList = new ArrayList<>();
                for (Map<String, Object> column : columns) {
                    String columnName = column.get("column_name").toString();
                    String dataType = column.get("data_type").toString();
                    String columnDescription = column.get("column_comment").toString();
                    ColumnSchema columnSchema = new ColumnSchema(columnName, dataType, columnDescription);
                    // 字段描述替换
                    if (tableConfig != null && MapUtils.isNotEmpty(tableConfig.getFieldDescMap()) && tableConfig.getFieldDescMap().get(columnName) != null) {
                        columnSchema.setColumnDescription(tableConfig.getFieldDescMap().get(columnName));
                    }

                    columnSchemaList.add(columnSchema);
                }
                // 获取索引信息
                List<Map<String, Object>> indexes = getTableIndex(tableName, jdbcTemplate);
                List<IndexSchema> indexSchemaList = indexes.stream()
                        .map(index -> new IndexSchema(index.get("Non_unique").toString(), index.get("Key_name").toString(), index.get("Seq_in_index").toString(), index.get("Column_name").toString(), index.get("Cardinality").toString()))
                        .collect(Collectors.toList());

                // 暂时不查索引
                TableSchema tableSchema = new TableSchema(jdbcRef, tableName, tableDescription, columnSchemaList, indexSchemaList.stream().map(IndexSchema::toString).collect(Collectors.toList()));
                // 表描述替换
                if (tableConfig != null && Strings.isNotBlank(tableConfig.getTableDesc())) {
                    tableSchema.setTableDescription(tableConfig.getTableDesc());
                }
                // 表索引替换
                if (tableConfig != null && CollectionUtils.isNotEmpty((tableConfig.getIndexList()))) {
                    tableSchema.setIndexList(tableConfig.getIndexList());
                }

                tableSchemaList.add(tableSchema);
            }
        }
        return tableSchemaList;
    }

    /**
     * select sql
     *
     * @param sql
     * @param jdbcRef
     */
    @Override
    public List<Map<String, Object>> query(String sql, String jdbcRef) {
        if (!sql.toLowerCase().startsWith("select")) {
            throw new IllegalArgumentException("Only SELECT queries are allowed.");
        }
        JdbcTemplate jdbcTemplate = jdbcTemplateMap.get(jdbcRef);
        if (jdbcTemplate == null) {
            throw new IllegalArgumentException("Invalid jdbcRef: " + jdbcRef);
        }
        return jdbcTemplate.queryForList(sql);
    }

    /**
     * explain sql
     *
     * @param sql
     * @param jdbcRef
     */
    @Override
    public String explain(String sql, String jdbcRef) {
        if (!sql.toLowerCase().startsWith("select")) {
            throw new IllegalArgumentException("Only SELECT queries are allowed.");
        }
        sql = "EXPLAIN " + sql;
        JdbcTemplate jdbcTemplate = jdbcTemplateMap.get(jdbcRef);
        if (jdbcTemplate == null) {
            throw new IllegalArgumentException("Invalid jdbcRef: " + jdbcRef);
        }
        return JacksonUtil.serializeWithoutException(jdbcTemplate.queryForList(sql));
    }


    /**
     * description "Return all table names in the database separated by comma."
     */
    public List<Map<String, Object>> listAllTablesName(JdbcTemplate jdbcTemplate) {
        return jdbcTemplate.queryForList(
                "SELECT table_name, table_comment FROM information_schema.tables WHERE table_schema = DATABASE()");
    }

    /**
     * description "Returns schema and relation information for the given table. Includes column name, data type and constraints."
     *
     * @param tableName Table name
     * @return List<Map < String, Object>>
     */
    public List<Map<String, Object>> getTableSchema(String tableName, JdbcTemplate jdbcTemplate) {
        String sql = "SELECT column_name, data_type, column_comment " +
                "FROM information_schema.columns WHERE table_schema = DATABASE() and table_name = ?";
        return jdbcTemplate
                .queryForList(sql, tableName);
    }

    public List<Map<String, Object>> getTableIndex(String tableName, JdbcTemplate jdbcTemplate) {
        String sql = "SHOW INDEX FROM " + tableName;
        return jdbcTemplate.queryForList(sql);
    }

    @MdpConfigListener(COMMON_SQL_EXECUTE_CONFIG)
    public void initConfigWithMcc(ConfigEvent configEvent) {
        if (configEvent == null) {
            return;
        }
        try {
            String newValue = configEvent.getNewValue();
            initConfig(newValue);
        } catch (Exception e) {
            log.error("initConfigWithMcc error", e);
        }
    }

    @Override
    public void onApplicationEvent(@NotNull ApplicationReadyEvent applicationReadyEvent) {
        try {
            initConfig(mdpConfigClient.get(COMMON_SQL_EXECUTE_CONFIG));
        } catch (Exception e) {
            log.error("CommonSqlExecuteService.onApplicationEvent init config error", e);
            throw new IllegalArgumentException("初始化配置失败", e);
        }
    }

    private void initConfig(String configJson) throws IOException {
        if (StringUtils.isBlank(configJson)) {
            return;
        }

        ObjectMapper mapper = new ObjectMapper();
        Map<String, Map<String, TableConfig>> newConfigMap = mapper.readValue(configJson,
                new TypeReference<Map<String, Map<String, TableConfig>>>() {
                });

        if (MapUtils.isEmpty(newConfigMap)) {
            return;
        }

        List<String> updatedDatasourceList = new ArrayList<>(newConfigMap.keySet());
        Set<String> removedDatasourceSet = new HashSet<>(configMap.keySet());
        removedDatasourceSet.removeAll(newConfigMap.keySet());
        List<String> removedDatasourceList = new ArrayList<>(removedDatasourceSet);

        // 更新mapping
        configMap.clear();
        configMap.putAll(newConfigMap);

        // 新增/更新数据源
        for (String datasource : updatedDatasourceList) {
            initGroupDataSource(datasource);
        }

        // 移除数据源
        for (String datasource : removedDatasourceList) {
            removeGroupDataSource(datasource);
        }
    }

    private void initGroupDataSource(String jdbcRef) {
        if (groupDataSourceMap.containsKey(jdbcRef)) {
            if (datasourceBlacklist.contains(jdbcRef)) {
                log.info("jdbcRef has been removed from datasourceBlacklist, jdbcRef:{}", jdbcRef);
                datasourceBlacklist.remove(jdbcRef);
                return;
            }
            log.info("jdbcRef has been existed, jdbcRef:{}", jdbcRef);
            return;
        }
        GroupDataSource groupDataSource = new GroupDataSource();
        groupDataSource.setJdbcRef(jdbcRef);
        groupDataSource.setExtraJdbcUrlParams("socketTimeout=30000&amp;useUnicode=true&amp;characterEncoding=utf8&amp;zeroDateTimeBehavior=convertToNull&amp;allowMultiQueries=true");
        groupDataSource.setInitialPoolSize(5);
        groupDataSource.setMaxPoolSize(10);
        groupDataSource.setLazyInit(false);
        groupDataSource.setPoolType("druid");
        groupDataSource.setPreferredTestQuery("SELECT 1");
        groupDataSource.init();
        groupDataSourceMap.put(jdbcRef, groupDataSource);

        JdbcTemplate jdbcTemplate = new JdbcTemplate(groupDataSource);
        jdbcTemplateMap.put(jdbcRef, jdbcTemplate);
    }

    private void removeGroupDataSource(String jdbcRef) {
        datasourceBlacklist.add(jdbcRef);
    }


    @Data
    private static class TableConfig {
        private String tableDesc;
        private Map<String, String> fieldDescMap;
        private List<String> indexList;
    }
}
