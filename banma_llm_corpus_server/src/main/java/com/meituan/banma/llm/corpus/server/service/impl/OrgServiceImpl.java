package com.meituan.banma.llm.corpus.server.service.impl;

import com.meituan.banma.llm.corpus.api.constants.BizCode;
import com.meituan.banma.llm.corpus.api.exception.LlmCorpusException;
import com.meituan.banma.llm.corpus.server.service.IOrgService;
import com.sankuai.meituan.org.openapi.model.OrgCond;
import com.sankuai.meituan.org.opensdk.model.domain.items.OrgItems;
import com.sankuai.meituan.org.opensdk.model.domain.Org;
import com.sankuai.meituan.org.opensdk.service.OrgService;
import com.sankuai.meituan.org.queryservice.domain.base.Paging;
import com.sankuai.meituan.org.queryservice.exception.MDMThriftException;
import com.sankuai.meituan.org.treeservice.domain.param.OrgHierarchyCond;
import com.sankuai.meituan.org.treeservice.enums.OrgSearchType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 组织架构服务实现类
 */
@Slf4j
@Service
public class OrgServiceImpl implements IOrgService {

    @Autowired
    private OrgService orgService;

    /**
     * 根据关键词搜索组织
     *
     * @param keyword 搜索关键词
     * @param offset 起始位置，从0开始
     * @param size 每页记录数
     * @return 匹配的组织列表
     */
    @Override
    public OrgItems searchOrg(String keyword, Integer offset, Integer size) throws LlmCorpusException {
        try {
            // 参数范围校验
            if (offset < 0) {
                offset = 0;
            }
            if (size < 1) {
                size = 9999;
            }

            OrgCond cond = new OrgCond();
            cond.statusET(1);
            cond.typeET(0);

            // 创建分页参数
            Paging paging = new Paging();
            paging.setOffset(offset);
            paging.setSize(size);
            
            // 调用组织服务查询
            OrgItems orgItems = orgService.search(keyword, cond, paging);
            
            log.info("searchOrg# 搜索组织成功, keyword:{}, offset:{}, size:{}, 结果数量:{}", 
                    keyword, offset, size, orgItems == null ? 0 : orgItems.getCount());
            
            return orgItems;
        } catch (MDMThriftException e) {
            log.error("searchOrg# 搜索组织失败, MDM异常, 关键词:{}, 异常原因:{}", keyword, e.getMessage(), e);
            throw LlmCorpusException.buildWithMsg(BizCode.ORG_ERROR.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("searchOrg# 搜索组织失败, 未知异常, 关键词:{}, 异常原因:{}", keyword, e.getMessage(), e);
            throw LlmCorpusException.buildWithMsg(BizCode.ORG_ERROR.getCode(), e.getMessage());
        }
    }

    /**
     * 根据上级组织ID查询子组织(不包含本身)
     *
     * @param superiorId 上级组织ID
     * @param offset 起始位置，从0开始
     * @param size 每页记录数
     * @return 匹配的子组织列表
     */
    @Override
    public OrgItems querySubordinateOrgs(String superiorId, Integer offset, Integer size) throws LlmCorpusException {
        try {
            // 参数范围校验
            if (offset < 0) {
                offset = 0;
            }
            if (size < 1) {
                size = 9999;
            }

            // 创建分页参数
            Paging paging = new Paging();
            paging.setOffset(offset);
            paging.setSize(size);
            
            // 设置dept只能为1，表示只查询下一级组织
            Integer dept = 1;
            
            // 创建组织层级查询条件
            OrgHierarchyCond cond = new OrgHierarchyCond();
            // 设置只查询有效组织(status=1)
            cond.StatusET(1);
            // 设置只查询行政组织(NORMAL)
            cond.orgSearchType(OrgSearchType.NORMAL);
            
            // 调用组织服务查询子组织
            OrgItems subOrgItems = orgService.queryBySuperior(superiorId, dept, cond, paging);
            
            log.info("querySubordinateOrgs# 查询子组织成功, superiorId:{}, dept:{}, offset:{}, size:{}, 结果数量:{}", 
                    superiorId, dept, offset, size, subOrgItems == null ? 0 : subOrgItems.getCount());
            
            return subOrgItems;
        } catch (MDMThriftException e) {
            log.error("querySubordinateOrgs# 查询子组织失败, MDM异常, superiorId:{}, 异常原因:{}", superiorId, e.getMessage(), e);
            throw LlmCorpusException.buildWithMsg(BizCode.ORG_ERROR.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("querySubordinateOrgs# 查询子组织失败, 未知异常, superiorId:{}, 异常原因:{}", superiorId, e.getMessage(), e);
            throw LlmCorpusException.buildWithMsg(BizCode.ORG_ERROR.getCode(), e.getMessage());
        }
    }

    /**
     * 根据组织ID查询组织信息
     *
     * @param id 组织ID
     * @return 组织信息
     */
    @Override
    public Org queryOrgById(String id) throws LlmCorpusException {
        try {
            // 调用组织服务查询
            Org org = orgService.query(id, null);
            
            log.info("queryOrgById# 查询组织成功, id:{}", id);
            
            return org;
        } catch (MDMThriftException e) {
            log.error("queryOrgById# 查询组织失败, MDM异常, id:{}, 异常原因:{}", id, e.getMessage(), e);
            throw LlmCorpusException.buildWithMsg(BizCode.ORG_ERROR.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("queryOrgById# 查询组织失败, 未知异常, id:{}, 异常原因:{}", id, e.getMessage(), e);
            throw LlmCorpusException.buildWithMsg(BizCode.ORG_ERROR.getCode(), e.getMessage());
        }
    }

    /**
     * 根据组织名称路径查询组织信息
     *
     * @param orgNamePath 组织路径(有效组织)
     * @return 组织信息
     */
    @Override
    public Org queryByNamePath(String orgNamePath) throws LlmCorpusException {
        try {
            // 调用组织服务查询
            Org org = orgService.queryByNamePath(orgNamePath);
            
            log.info("queryByNamePath# 查询组织成功, orgNamePath:{}", orgNamePath);
            
            return org;
        } catch (MDMThriftException e) {
            log.error("queryByNamePath# 查询组织失败, MDM异常, orgNamePath:{}, 异常原因:{}", orgNamePath, e.getMessage(), e);
            throw LlmCorpusException.buildWithMsg(BizCode.ORG_ERROR.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("queryByNamePath# 查询组织失败, 未知异常, orgNamePath:{}, 异常原因:{}", orgNamePath, e.getMessage(), e);
            throw LlmCorpusException.buildWithMsg(BizCode.ORG_ERROR.getCode(), e.getMessage());
        }
    }
} 