package com.meituan.banma.llm.corpus.server.common.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

@Data
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class LlmKmToCorpusTaskResultDTO {
    /**
     * 知识库问答列表
     */
    List<LlmKmToCorpusTaskResultItem> kmQaList;

    public static class LlmKmToCorpusTaskResultItem {
        /**
         * 标题
         */
        private String title;
        
        /**
         * 结果文档
         */
        private ResultDoc resultDoc;
        
        /**
         * 缺失信息列表
         */
        private List<String> missingInfo;
        
        /**
         * 标签id列表，英文逗号分隔，如1,2,3
         */
        private String tagsIds;

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public ResultDoc getResultDoc() {
            return resultDoc;
        }

        public void setResultDoc(ResultDoc resultDoc) {
            this.resultDoc = resultDoc;
        }

        public List<String> getMissingInfo() {
            return missingInfo;
        }

        public void setMissingInfo(List<String> missingInfo) {
            this.missingInfo = missingInfo;
        }

        public String getTagsIds() {
            return tagsIds;
        }

        public void setTagsIds(String tagsIds) {
            this.tagsIds = tagsIds;
        }

        public static class ResultDoc {
            /**
             * 问题内容
             */
            private String question;
            
            /**
             * 文档内容
             */
            private String docs;

            public String getQuestion() {
                return question;
            }

            public void setQuestion(String question) {
                this.question = question;
            }

            public String getDocs() {
                return docs;
            }

            public void setDocs(String docs) {
                this.docs = docs;
            }
        }
    }
}
