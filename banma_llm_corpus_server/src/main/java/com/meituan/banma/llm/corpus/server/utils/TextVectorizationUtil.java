package com.meituan.banma.llm.corpus.server.utils;

import com.meituan.banma.llm.corpus.api.exception.LlmCorpusException;
import com.meituan.banma.llm.corpus.server.common.domain.dto.stat.AppStatusDTO;
import com.meituan.banma.llm.corpus.server.common.domain.dto.FridayEmbeddingResDTO;
import com.meituan.banma.llm.corpus.server.service.IFridayService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 文本向量化工具类，将文本转换为向量
 */
@Slf4j
public class TextVectorizationUtil {

    /**
     * 将问题列表转换为向量表示
     * 使用embedding模型进行向量化，效果优于TF-IDF
     *
     * @param questions 问题列表
     * @param fridayService Friday服务，用于调用嵌入模型
     * @param misId 用户ID
     * @return 问题ID到向量的映射
     */
    public static Map<String, double[]> convertToVectors(List<AppStatusDTO.Question> questions, IFridayService fridayService, String misId) {
        if (CollectionUtils.isEmpty(questions)) {
            return Collections.emptyMap();
        }

        try {
            // 1. 提取问题文本内容和对应的问题ID
            Map<String, String> questionIdToText = new HashMap<>();
            List<String> texts = new ArrayList<>();
            List<String> questionIds = new ArrayList<>();
            
            for (AppStatusDTO.Question question : questions) {
                if (StringUtils.isNotBlank(question.getQuestion())) {
                    String questionId = question.getQuestionMessageId();
                    String text = question.getQuestion();
                    
                    questionIdToText.put(questionId, text);
                    texts.add(text);
                    questionIds.add(questionId);
                }
            }

            if (texts.isEmpty()) {
                return Collections.emptyMap();
            }
            
            log.info("开始使用embedding模型进行向量化，问题数量: {}", texts.size());

            // 2. 调用Friday服务获取嵌入向量
            List<FridayEmbeddingResDTO> embeddings = fridayService.getEmbedding(misId, texts);
            if (CollectionUtils.isEmpty(embeddings)) {
                log.warn("embedding模型返回结果为空");
                return Collections.emptyMap();
            }
            
            log.info("成功获取嵌入向量，数量: {}", embeddings.size());

            // 3. 将嵌入向量转换为问题ID到向量的映射
            Map<String, double[]> questionIdToVector = new HashMap<>();
            for (int i = 0; i < embeddings.size(); i++) {
                FridayEmbeddingResDTO embedding = embeddings.get(i);
                if (embedding != null && embedding.getEmbedding() != null && i < questionIds.size()) {
                    String questionId = questionIds.get(i);
                    List<Double> embeddingValues = embedding.getEmbedding();
                    
                    // 将List<Double>转换为double[]
                    double[] doubleEmbedding = new double[embeddingValues.size()];
                    for (int j = 0; j < embeddingValues.size(); j++) {
                        doubleEmbedding[j] = embeddingValues.get(j);
                    }
                    
                    questionIdToVector.put(questionId, doubleEmbedding);
                }
            }
            
            log.info("向量化完成，成功处理问题数量: {}", questionIdToVector.size());
            return questionIdToVector;
            
        } catch (LlmCorpusException e) {
            log.error("调用embedding模型失败: {}", e.getMessage(), e);
            return Collections.emptyMap();
        } catch (Exception e) {
            log.error("向量化过程发生异常: {}", e.getMessage(), e);
            return Collections.emptyMap();
        }
    }
} 