package com.meituan.banma.llm.corpus.server.common.constants.enums;

import lombok.Getter;

@Getter
public enum ImMessageTypeEnum {
    /**
     * 大象消息类型
     */
    TEXT(1, "文本消息"),
    AUDIO(2, "语音消息"),
    VIDEO(3, "视频消息"),
    IMAGE(4, "图片消息"),
    CALENDAR(5, "日历消息"),
    LINK(6, "图文消息"),
    MULTI_LINK(7, "多图文消息"),
    FILE(8, "文件消息"),
    GPS(9, "位置消息"),
    VCARD(10, "名片消息"),
    EMOTION(11, "表情消息"),
    EVENT(12, "事件消息"),
    CUSTOM(13, "模板消息"),
    PUB_NOTICE(14, "公告消息"),
    CALL(15, "通话消息"),
    RED_PACKET(16, "红包消息"),
    GENERAL(17, "通用消息"),
    GROUP_VCARD(18, "群名片消息"),
    NEW_EMOTION(19, "新表情消息"),
    QUOTE(20, "引用消息"),
    DYNAMIC(21, "动态消息"),;


    private final Integer code;
    private final String desc;

    ImMessageTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ImMessageTypeEnum findByCode(Integer code) {
        for (ImMessageTypeEnum item : ImMessageTypeEnum.values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return null;
    }
}
