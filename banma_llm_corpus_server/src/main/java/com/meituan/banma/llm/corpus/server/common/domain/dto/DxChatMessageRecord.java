package com.meituan.banma.llm.corpus.server.common.domain.dto;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2024/12/4
 * 转换姓名后的大象群聊消息
 * 文档: https://km.sankuai.com/collabpage/1357048485#id-8.%E8%8E%B7%E5%8F%96%E7%BE%A4%E8%81%8A%E5%8E%86%E5%8F%B2%E6%B6%88%E6%81%AF
 */
@Data
@ToString
public class DxChatMessageRecord {
    private long msgId;
    private long fromUid;
    private long fromPubId;
    private long gid;
    private boolean isCancel;
    private long cts;
    private int type;
    private JSONObject message; //参见https://km.sankuai.com/collabpage/635952308
    private String fromName; //真实姓名，需要查询转换后填充
    private JSONObject msgExt;
    private String userOrgId;
    private String userOrgName;
    private String fromMis;
    /**
     * 是否标记为重要消息
     */
    private boolean markAsImportantMessage;
}
