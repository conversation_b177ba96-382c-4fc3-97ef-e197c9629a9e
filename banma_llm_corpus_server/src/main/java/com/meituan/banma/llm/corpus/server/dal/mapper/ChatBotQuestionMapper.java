package com.meituan.banma.llm.corpus.server.dal.mapper;

import com.meituan.banma.llm.corpus.server.dal.entity.ChatBotQuestionEntity;
import com.meituan.banma.llm.corpus.server.dal.query.ChatBotQuestionQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ChatBotQuestionMapper {
    int insert(ChatBotQuestionEntity entity);

    ChatBotQuestionEntity selectByQuestionId(@Param("questionId") String questionId);

    List<ChatBotQuestionEntity> selectByMisId(ChatBotQuestionQuery query);

    int updateByQuestionId(ChatBotQuestionEntity entity);
}

