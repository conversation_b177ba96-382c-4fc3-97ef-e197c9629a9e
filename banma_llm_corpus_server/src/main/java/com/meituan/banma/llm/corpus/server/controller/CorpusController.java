package com.meituan.banma.llm.corpus.server.controller;

import com.dianping.lion.shade.org.apache.curator.shaded.com.google.common.base.Preconditions;
import com.dianping.zebra.util.StringUtils;
import com.google.common.collect.Lists;
import com.meituan.banma.llm.corpus.api.constants.BizCode;
import com.meituan.banma.llm.corpus.api.exception.LlmCorpusException;
import com.meituan.banma.llm.corpus.server.common.CommonResult;
import com.meituan.banma.llm.corpus.server.common.constants.enums.MergeTriggerSourceEnum;
import com.meituan.banma.llm.corpus.server.common.domain.dto.BackgroundKnowledgeDTO;
import com.meituan.banma.llm.corpus.server.common.domain.dto.CorpusInfoDTO;
import com.meituan.banma.llm.corpus.server.common.domain.dto.CorpusModifyDTO;
import com.meituan.banma.llm.corpus.server.common.domain.dto.PageDTO;
import com.meituan.banma.llm.corpus.server.common.domain.dto.RuleDTO;
import com.meituan.banma.llm.corpus.server.common.domain.dto.SopDTO;
import com.meituan.banma.llm.corpus.server.controller.request.corpus.BackgroundKnowledgeFormRequest;
import com.meituan.banma.llm.corpus.server.controller.request.corpus.CorpusFormRequest;
import com.meituan.banma.llm.corpus.server.controller.request.corpus.MergeCorpusRequest;
import com.meituan.banma.llm.corpus.server.controller.request.corpus.RuleFormRequest;
import com.meituan.banma.llm.corpus.server.controller.request.corpus.SaveKnowledgeCorpusRequest;
import com.meituan.banma.llm.corpus.server.controller.request.corpus.SopFormRequest;
import com.meituan.banma.llm.corpus.server.dal.entity.FridayBotConversationViewEntity;
import com.meituan.banma.llm.corpus.server.dal.entity.RgDatasetDocumentEntity;
import com.meituan.banma.llm.corpus.server.service.ICorpusService;
import com.meituan.banma.llm.corpus.server.service.IFridayHiveService;
import com.meituan.banma.llm.corpus.server.service.IKnowledgeBaseService;
import com.meituan.banma.llm.corpus.server.service.IReviewService;
import com.meituan.banma.llm.corpus.server.service.mapper.KnowledgeBaseMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */

@RestController
@Slf4j
@RequestMapping("/corpus")
@ResponseBody
public class CorpusController {

    @Autowired
    private ICorpusService corpusService;

    @Autowired
    private IKnowledgeBaseService knowledgeBaseService;

    @Autowired
    private IReviewService reviewService;

    @Autowired
    private IFridayHiveService fridayHiveService;

    private KnowledgeBaseMapper knowledgeBaseMapper = KnowledgeBaseMapper.get();

    @GetMapping("/queryCorpusListByRgId")
    public Object queryCorpusListByRgId(@RequestParam(value = "rgId", defaultValue = "0")  long rgId,
                                        @RequestParam(value = "misId", defaultValue = "") String misId,
                                        @RequestParam(value = "ticketId", required = false) String ticketId,
                                        @RequestParam(value = "title", required = false) String title,
                                        @RequestParam(value = "content", required = false) String content,
                                        @RequestParam(value = "source", required = false) Integer source,
                                        @RequestParam(value = "creator", required = false) String creator,
                                        @RequestParam(value = "startTime", required = false) String startTime,
                                        @RequestParam(value = "endTime", required = false) String endTime,
                                        @RequestParam(value = "strMatch", defaultValue = "") String strMatch,
                                        @RequestParam(value = "tagsIds", required = false) String tagsIds,
                                        @RequestParam(value = "pageNum", defaultValue = "1") int pageNum,
                                        @RequestParam(value = "pageSize", defaultValue = "100") int pageSize) {
        try {
            Preconditions.checkArgument(rgId > 0,"rgId 必须大于0");
            Preconditions.checkArgument(misId != null && !misId.isEmpty(),"misId 不能为空");
            PageDTO<CorpusInfoDTO> corpusDetailDTO = corpusService.queryCorpusListByCondition(rgId, misId, ticketId, title, content, source, creator, startTime, endTime, strMatch, tagsIds, pageNum, pageSize);
            Map<String, Object> result = new HashMap<>();
            result.put("currentPage",corpusDetailDTO.getPageNum());
            result.put("pageSize",pageSize);
            result.put("total",corpusDetailDTO.getTotalCount());
            result.put("totalPage",corpusDetailDTO.getTotalPage());
            result.put("list",corpusDetailDTO.getData());
            return CommonResult.success("语料查询成功", result);
        } catch (Exception e) {
            log.error("queryCorpusListByRgId 执行失败, rgId:{}, 异常原因:{}", rgId, e.getMessage());
            return CommonResult.error((String.format("执行失败,异常原因=%s",e.getMessage())));
        }
    }

    @PostMapping("/addCorpus")
    public Object addCorpus(@RequestBody CorpusFormRequest corpusForm,
                            @RequestParam(value = "rgId", defaultValue = "0")  long rgId,
                            @RequestParam(value = "misId", defaultValue = "") String misId) {
        try {
            Preconditions.checkArgument(rgId > 0,"rgId 必须大于0");
            Preconditions.checkArgument(misId != null && !misId.isEmpty(),"misId 不能为空");
            Preconditions.checkArgument(corpusForm != null, "参数不能为空");
            Preconditions.checkArgument(corpusForm.getContent() != null && !corpusForm.getContent().isEmpty(), "content 必须不为空");
            Preconditions.checkArgument(corpusForm.getTitle() != null && !corpusForm.getTitle().isEmpty(), "title 不能为空");

            // 在服务中处理插入操作，并将 misId 传递给相应的方法
            boolean addResult = corpusService.addCorpus(corpusForm, rgId, misId);
            if(addResult){
                return CommonResult.success("添加成功");
            }else {
                return CommonResult.error("添加失败");
            }
        } catch (Exception e) {
            log.error("addCorpus 执行失败, 异常原因:{}", e.getMessage());
            return CommonResult.error(String.format("执行失败,异常原因=%s", e.getMessage()));
        }
    }

    @GetMapping("/queryCorpusByTicketIdRgId")
    public Object queryCorpusByTicketIdRgId(@RequestParam(value = "ticketId", defaultValue = "0") String ticketId,
                                            @RequestParam(value = "rgId", defaultValue = "0")  long rgId,
                                            @RequestParam(value = "misId", defaultValue = "") String misId) {
        try {
            Preconditions.checkArgument(ticketId != null && !ticketId.isEmpty(),"ticketId 不为空");
            Preconditions.checkArgument(rgId > 0,"rgId 必须大于0");
            Preconditions.checkArgument(misId != null && !misId.isEmpty(),"misId 不能为空");
            CorpusModifyDTO corpusModifyDTO = corpusService.queryCorpusByTicketIdRgId(ticketId, rgId, misId);
            if(corpusModifyDTO == null){
                return CommonResult.success("查询成功，未查到相关语料",null);
            }
            return CommonResult.success("查询成功",corpusModifyDTO);
        } catch (Exception e) {
            log.error("queryCorpusByTicketIdRgId 执行失败, ticketId:{}, rgId:{}, 异常原因:{}", ticketId, rgId, e.getMessage());
            return CommonResult.error((String.format("执行失败,异常原因=%s",e.getMessage())));
        }
    }

    @GetMapping("/queryCorpusAllByTicketIdRgId")
    public Object queryCorpusAllByTicketIdRgId(@RequestParam(value = "ticketId", defaultValue = "0") String ticketId,
                                               @RequestParam(value = "rgId", defaultValue = "0")  long rgId,
                                               @RequestParam(value = "misId", defaultValue = "") String misId) {
        try {
            Preconditions.checkArgument(ticketId != null && !ticketId.isEmpty(),"ticketId 不为空");
            Preconditions.checkArgument(rgId > 0,"rgId 必须大于0");
            Preconditions.checkArgument(misId != null && !misId.isEmpty(),"misId 不能为空");
            CorpusInfoDTO corpusInfoDTO = corpusService.queryCorpusAllByTicketIdRgId(ticketId, rgId, misId);
            if(corpusInfoDTO == null){
                return CommonResult.success("查询成功，未查到相关语料",null);
            }
            return CommonResult.success("查询成功",corpusInfoDTO);
        } catch (Exception e) {
            log.error("queryCorpusAllByTicketIdRgId 执行失败, ticketId:{}, rgId:{}, 异常原因:{}", ticketId, rgId, e.getMessage());
            return CommonResult.error((String.format("执行失败,异常原因=%s",e.getMessage())));
        }
    }

    @PostMapping("/modifyCorpusByTicketId")
    public Object modifyCorpusByTicketId(@RequestBody CorpusFormRequest corpusForm,
                                         @RequestParam(value = "ticketId", defaultValue = "") String ticketId,
                                         @RequestParam(value = "rgId", defaultValue = "0")  long rgId,
                                         @RequestParam(value = "misId", defaultValue = "") String misId) {
        try {
            Preconditions.checkArgument(rgId > 0,"rgId 必须大于0");
            Preconditions.checkArgument(misId != null && !misId.isEmpty(),"misId 不能为空");
            Preconditions.checkArgument(ticketId != null && !ticketId.isEmpty(),"ticketId 不为空");
            Preconditions.checkArgument(corpusForm != null, "参数不能为空");
            Preconditions.checkArgument(corpusForm.getContent() != null && !corpusForm.getContent().isEmpty(), "content 必须不为空");
            Preconditions.checkArgument(corpusForm.getTitle() != null && !corpusForm.getTitle().isEmpty(), "title 不能为空");

            // 在服务中处理修改操作，并将 misId 传递给相应的方法
            boolean modifyResult = corpusService.modifyCorpus(corpusForm, ticketId,rgId, misId);
            if(modifyResult){
                return CommonResult.success("修改成功");
            }else {
                return CommonResult.error("修改失败");
            }
        } catch (Exception e) {
            log.error("modifyCorpus 执行失败, ticketId:{}, rgId:{} 异常原因:{}", ticketId, rgId, e.getMessage());
            return CommonResult.error(String.format("执行失败,异常原因=%s", e.getMessage()));
        }
    }
    @PostMapping("/deleteCorpusByTicketIds")
    public Object deleteCorpusByTicketIds(@RequestParam(value = "ticketIds", defaultValue = "") List<String> ticketIds,
                                          @RequestParam(value = "rgId", defaultValue = "0")  long rgId,
                                          @RequestParam(value = "misId", defaultValue = "") String misId) {
        try {
            Preconditions.checkArgument(rgId > 0,"rgId 必须大于0");
            Preconditions.checkArgument(misId != null && !misId.isEmpty(),"misId 不能为空");
            Preconditions.checkArgument(ticketIds != null && !ticketIds.isEmpty(),"ticketIds 不能为空");
            corpusService.deleteCorpusByTicketIds(ticketIds, rgId, misId);
            return CommonResult.success("删除成功");
        } catch (Exception e) {
            assert ticketIds != null;
            log.error("deleteCorpusByTicketIds 执行失败, ticketIds:{}, 异常原因:{}", ticketIds, e.getMessage());
            return CommonResult.error(String.format("执行失败,异常原因=%s", e.getMessage()));
        }
    }

    @PostMapping("/createMergeCorpusTask")
    public Object mergeCorpus(@RequestBody MergeCorpusRequest mergeCorpusRequest){
        try {
            Preconditions.checkArgument(mergeCorpusRequest != null, "参数不能为空");
            Preconditions.checkArgument(mergeCorpusRequest.getRgId() > 0, "rgId 必须大于0");
            Preconditions.checkArgument(mergeCorpusRequest.getMisId() != null && !mergeCorpusRequest.getMisId().isEmpty(), "用户信息不能为空");
            MergeTriggerSourceEnum source = MergeTriggerSourceEnum.fromCode(mergeCorpusRequest.getTriggerSource());
            if (source == null){
                throw LlmCorpusException.buildWithMsg(BizCode.ILLEGAL_ARGUMENT,"触发场景参数错误");
            }
            if (MergeTriggerSourceEnum.CORPUS_LIST.equals(source)){
                Preconditions.checkArgument(mergeCorpusRequest.getCorpusTextList() == null || CollectionUtils.isEmpty(mergeCorpusRequest.getCorpusTextList()), "该场景下语料文本列表需要为空");
                Preconditions.checkArgument(CollectionUtils.isNotEmpty(mergeCorpusRequest.getCorpusIdList()), "该场景下语料ID列表不能为空");
            }
            if (MergeTriggerSourceEnum.REVIEW_PAGE.equals(source)){
                Preconditions.checkArgument(StringUtils.isNotBlank(mergeCorpusRequest.getTicketId()), "该场景下语料ID不能为空");
                Preconditions.checkArgument(CollectionUtils.isNotEmpty(mergeCorpusRequest.getCorpusIdList()), "该场景下语料ID列表不能为空");
                Preconditions.checkArgument(CollectionUtils.isNotEmpty(mergeCorpusRequest.getCorpusTextList()), "该场景下语料文本列表不能为空");
            }
            String taskId = knowledgeBaseService.createKnowledgeMergeTask(knowledgeBaseMapper.trans(mergeCorpusRequest));
            return CommonResult.success("提交合并任务成功", taskId);
        }catch (Exception e){
            log.error("mergeCorpus 执行失败, 异常原因:{}", e.getMessage());
            return CommonResult.error(String.format("执行失败,异常原因=%s", e.getMessage()));
        }
    }

    /**
     * 保存合并语料
     * @param request 保存合并语料请求
     * @return 处理结果
     */
    @PostMapping("/saveMergeCorpus")
    public Object saveMergeCorpus(@RequestBody SaveKnowledgeCorpusRequest request) {
        try {
            Preconditions.checkArgument(request != null, "请求参数不能为空");
            Preconditions.checkArgument(request.getRgId() > 0, "rgId 必须大于0");
            Preconditions.checkArgument(StringUtils.isNotBlank(request.getMisId()), "misId 不能为空");
            Preconditions.checkArgument(StringUtils.isNotBlank(request.getTicketId()), "ticketId 不能为空");
            Preconditions.checkArgument(StringUtils.isNotBlank(request.getTitle()), "title 不能为空");
            Preconditions.checkArgument(StringUtils.isNotBlank(request.getContent()), "content 不能为空");
            Preconditions.checkArgument(CollectionUtils.isNotEmpty(request.getCorpusIdList()), "语料ID列表不能为空");

            corpusService.saveMergeCorpus(knowledgeBaseMapper.trans(request));
            return CommonResult.success("保存合并语料成功");
        } catch (Exception e) {
            log.error("saveMergeCorpus 执行失败, 请求: {}, 异常原因: {}", request, e.getMessage());
            return CommonResult.error(String.format("执行失败,异常原因=%s", e.getMessage()));
        }
    }

    @PostMapping("/saveIgnoreCorpus")
    public Object saveIgnoreCorpus(@RequestBody CorpusFormRequest corpusForm,
                                   @RequestParam(value = "ticketId", defaultValue = "") String ticketId,
                                   @RequestParam(value = "rgId", defaultValue = "0")  long rgId,
                                   @RequestParam(value = "misId", defaultValue = "") String misId) {
        try {
            Preconditions.checkArgument(rgId > 0,"rgId 必须大于0");
            Preconditions.checkArgument(misId != null && !misId.isEmpty(),"misId 不能为空");
            Preconditions.checkArgument(ticketId != null && !ticketId.isEmpty(),"ticketId 不为空");
            Preconditions.checkArgument(corpusForm != null, "参数不能为空");
            Preconditions.checkArgument(corpusForm.getTitle() != null && !corpusForm.getTitle().isEmpty(), "title 不能为空");

            // 在服务中处理插入操作，并将 misId 传递给相应的方法
            boolean addResult = corpusService.addIgnoreCorpus(corpusForm, rgId, misId,ticketId);
            if(addResult){
                return CommonResult.success("添加Ignore语料成功");
            }else {
                return CommonResult.error("添加Ignore语料失败");
            }
        } catch (Exception e) {
            log.error("saveIgnoreCorpus 执行失败, ticketId:{}, rgId:{} 异常原因:{}", ticketId, rgId, e.getMessage());
            return CommonResult.error(String.format("执行失败,异常原因=%s", e.getMessage()));
        }
    }

    @PostMapping("/createRgDatasetDocument")
    public Object createRgDatasetDocument(@RequestParam(value = "rgId", defaultValue = "0")  long rgId,
                                          @RequestParam(value = "misId", defaultValue = "") String misId,
                                          @RequestParam(value = "spaceId", defaultValue = "") String spaceId,
                                          @RequestParam(value = "spaceName", defaultValue = "") String spaceName) {
        try {
            Preconditions.checkArgument(rgId > 0,"rgId 必须大于0");
            Preconditions.checkArgument(misId != null && !misId.isEmpty(),"misId 不能为空");

            // 调用reviewService的createDatasetDocument方法创建数据集和文档
            RgDatasetDocumentEntity documentEntity = reviewService.createDatasetDocument(rgId, misId, spaceId, spaceName);
            if (documentEntity.getTimestamp() != null) {
                return CommonResult.success("数据集文档创建成功", documentEntity);
            } else {
                return CommonResult.success("数据集文档已存在", documentEntity);
            }
        } catch (Exception e) {
            log.error("createRgDatasetDocument 执行失败, rgId:{}, misId:{} 异常原因:{}", rgId, misId, e.getMessage());
            return CommonResult.error(String.format("执行失败,异常原因=%s", e.getMessage()));
        }
    }

    @PostMapping("/refreshKbDocument")
    public Object refreshKbDocument(@RequestParam(value = "rgId", defaultValue = "0")  long rgId,
                                    @RequestParam(value = "misId", defaultValue = "") String misId) {
        try {
            Preconditions.checkArgument(rgId > 0,"rgId 必须大于0");
            Preconditions.checkArgument(misId != null && !misId.isEmpty(),"misId 不能为空");

            // 调用reviewService刷新知识库文档
            reviewService.refreshKbDocument(rgId, misId);
            return CommonResult.success("知识库文档刷新成功");
        } catch (Exception e) {
            log.error("refreshKbDocument 执行失败, rgId:{}, misId:{}, 异常原因:{}", rgId, misId, e.getMessage());
            return CommonResult.error(String.format("执行失败,异常原因=%s", e.getMessage()));
        }
    }

    @RequestMapping("/testTalos")
    public Object testTalos(){
        try{
            List<FridayBotConversationViewEntity> res = fridayHiveService.queryFridayConversationsByDate(Lists.newArrayList("1892877403769061448"),"20250325");
            return CommonResult.success("查询成功",res);
        }catch (Exception e){
            log.error("testTalos 执行失败, 异常原因:{}", e.getMessage());
            return CommonResult.error(String.format("执行失败,异常原因=%s", e.getMessage()));
        }
    }

    @GetMapping("/queryCorpusListByRgIdContentId")
    public Object queryCorpusListByRgIdContentId(@RequestParam(value = "rgId", defaultValue = "0")  long rgId,
                                                 @RequestParam(value = "misId", defaultValue = "") String misId,
                                                 @RequestParam(value = "contentId", defaultValue = "0") long contentId,
                                                 @RequestParam(value = "pageNum", defaultValue = "1") int pageNum,
                                                 @RequestParam(value = "pageSize", defaultValue = "1000") int pageSize,
                                                 @RequestParam(value = "strMatch", defaultValue = "") String strMatch) {
        try {
            Preconditions.checkArgument(rgId > 0,"rgId 必须大于0");
            Preconditions.checkArgument(misId != null && !misId.isEmpty(),"misId 不能为空");
            Preconditions.checkArgument(contentId > 0,"contentId 必须大于0");
            PageDTO<CorpusInfoDTO> corpusDetailDTO= corpusService.queryCorpusListByRgIdContentId(rgId, misId, contentId, pageNum, pageSize, strMatch);
            Map<String, Object> result = new HashMap<>();
            result.put("currentPage",corpusDetailDTO.getPageNum());
            result.put("pageSize",pageSize);
            result.put("total",corpusDetailDTO.getTotalCount());
            result.put("totalPage",corpusDetailDTO.getTotalPage());
            result.put("list",corpusDetailDTO.getData());
            return CommonResult.success("success", result);
        } catch (Exception e) {
            log.error("queryCorpusListByRgIdContentId 执行失败, rgId:{}, 异常原因:{}", rgId, e.getMessage());
            return CommonResult.error((String.format("执行失败,异常原因=%s",e.getMessage())));
        }
    }

    @PostMapping("/updateSop")
    public Object updateSop(@RequestBody SopFormRequest sopFormRequest,
                         @RequestParam(value = "rgId", defaultValue = "0")  long rgId,
                         @RequestParam(value = "misId", defaultValue = "") String misId
    ){
        try {
            Preconditions.checkArgument(sopFormRequest != null, "请求参数不能为空");
            Preconditions.checkArgument(rgId > 0, "rgId 必须大于0");
            Preconditions.checkArgument(misId != null && !misId.isEmpty(), "misId 不能为空");
            Preconditions.checkArgument(sopFormRequest.getSop() != null && !sopFormRequest.getSop().isEmpty(), "sop 不能为空");
            
            boolean addResult = corpusService.updateSop(sopFormRequest, rgId, misId);
            if(addResult){
                return CommonResult.success("更新值班组SOP成功");
            }else {
                return CommonResult.error("更新值班组SOP失败");
            }
        }catch (Exception e){
            log.error("updateSop 执行失败, rgId:{}, misId:{} 异常原因:{}", rgId, misId, e.getMessage());
            return CommonResult.error(String.format("执行失败,异常原因=%s", e.getMessage()));
        }
    }

    @GetMapping("/querySopByRgId")
    public Object querySop(@RequestParam(value = "rgId", defaultValue = "0")  long rgId,
                            @RequestParam(value = "misId", defaultValue = "") String misId,
                           @RequestParam(value = "pageNum", defaultValue = "1") int pageNum,
                           @RequestParam(value = "pageSize", defaultValue = "100") int pageSize){
        try {
            Preconditions.checkArgument(rgId > 0, "rgId 必须大于0");
            Preconditions.checkArgument(misId != null && !misId.isEmpty(), "misId 不能为空");
            PageDTO<SopDTO> sopDTO = corpusService.querySopByRgId(rgId, misId, pageNum, pageSize);
            Map<String, Object> result = new HashMap<>();
            result.put("currentPage",sopDTO.getPageNum());
            result.put("pageSize",pageSize);
            result.put("total",sopDTO.getTotalCount());
            result.put("totalPage",sopDTO.getTotalPage());
            result.put("list",sopDTO.getData());
            return CommonResult.success("查询成功",result);
        }catch (Exception e){
            log.error("querySop 执行失败, rgId:{}, misId:{} 异常原因:{}", rgId, misId, e.getMessage());
            return CommonResult.error(String.format("执行失败,异常原因=%s", e.getMessage()));
        }
    }

    @GetMapping("/queryLatestSopByRgId")
    public Object queryLatestSopByRgId(@RequestParam(value = "rgId", defaultValue = "0")  long rgId,
                                       @RequestParam(value = "misId", defaultValue = "") String misId){
        try {
            Preconditions.checkArgument(rgId > 0, "rgId 必须大于0");
            Preconditions.checkArgument(misId != null && !misId.isEmpty(), "misId 不能为空");
            String sop = corpusService.queryLatestSopByRgId(rgId);
            return CommonResult.success("查询成功",sop);
        }catch (Exception e){
            log.error("queryLatestSopByRgId 执行失败, rgId:{}, misId:{} 异常原因:{}", rgId, misId, e.getMessage());
            return CommonResult.error(String.format("执行失败,异常原因=%s", e.getMessage()));
        }

    }

    /**
     * 更新自定义背景知识
     */
    @PostMapping("/updateBackgroundKnowledge")
    public Object updateBackgroundKnowledge(@RequestBody BackgroundKnowledgeFormRequest request) {
        try {
            Preconditions.checkArgument(request != null, "请求参数不能为空");
            Preconditions.checkArgument(request.getRgId() > 0, "rgId 必须大于0");
            Preconditions.checkArgument(request.getMisId() != null && !request.getMisId().isEmpty(), "misId 不能为空");
            
            boolean addResult = corpusService.updateBackgroundKnowledge(request.getRgId(), request.getMisId(), request.getKnowledgeContent());
            if (addResult) {
                return CommonResult.success("更新自定义背景知识成功");
            } else {
                return CommonResult.error("更新自定义背景知识失败");
            }
        } catch (Exception e) {
            log.error("updateBackgroundKnowledge 执行失败, rgId:{}, misId:{} 异常原因:{}", 
                request != null ? request.getRgId() : null, 
                request != null ? request.getMisId() : null, 
                e.getMessage());
            return CommonResult.error(String.format("执行失败,异常原因=%s", e.getMessage()));
        }
    }

    /**
     * 分页查询自定义背景知识
     */
    @GetMapping("/queryBackgroundKnowledgeByRgId")
    public Object queryBackgroundKnowledgeByRgId(@RequestParam(value = "rgId", defaultValue = "0") long rgId,
                                                 @RequestParam(value = "misId", defaultValue = "") String misId,
                                                 @RequestParam(value = "pageNum", defaultValue = "1") int pageNum,
                                                 @RequestParam(value = "pageSize", defaultValue = "100") int pageSize) {
        try {
            Preconditions.checkArgument(rgId > 0, "rgId 必须大于0");
            Preconditions.checkArgument(misId != null && !misId.isEmpty(), "misId 不能为空");
            PageDTO<BackgroundKnowledgeDTO> dto = corpusService.queryBackgroundKnowledgeByRgId(rgId, misId, pageNum, pageSize);
            Map<String, Object> result = new HashMap<>();
            result.put("currentPage", dto.getPageNum());
            result.put("pageSize", pageSize);
            result.put("total", dto.getTotalCount());
            result.put("totalPage", dto.getTotalPage());
            result.put("list", dto.getData());
            return CommonResult.success("查询成功", result);
        } catch (Exception e) {
            log.error("queryBackgroundKnowledgeByRgId 执行失败, rgId:{}, misId:{} 异常原因:{}", rgId, misId, e.getMessage());
            return CommonResult.error(String.format("执行失败,异常原因=%s", e.getMessage()));
        }
    }

    /**
     * 查询最新自定义背景知识
     */
    @GetMapping("/queryLatestBackgroundKnowledgeByRgId")
    public Object queryLatestBackgroundKnowledgeByRgId(@RequestParam(value = "rgId", defaultValue = "0") long rgId,
                                                       @RequestParam(value = "misId", defaultValue = "") String misId) {
        try {
            Preconditions.checkArgument(rgId > 0, "rgId 必须大于0");
            Preconditions.checkArgument(misId != null && !misId.isEmpty(), "misId 不能为空");
            String knowledge = corpusService.queryLatestBackgroundKnowledgeByRgId(rgId);
            return CommonResult.success("查询成功", knowledge);
        } catch (Exception e) {
            log.error("queryLatestBackgroundKnowledgeByRgId 执行失败, rgId:{}, misId:{} 异常原因:{}", rgId, misId, e.getMessage());
            return CommonResult.error(String.format("执行失败,异常原因=%s", e.getMessage()));
        }
    }

    /**
     * 更新自定义规则
     */
    @PostMapping("/updateRule")
    public Object updateRule(@RequestBody RuleFormRequest request) {
        try {
            Preconditions.checkArgument(request != null, "请求参数不能为空");
            Preconditions.checkArgument(request.getRgId() > 0, "rgId 必须大于0");
            Preconditions.checkArgument(request.getMisId() != null && !request.getMisId().isEmpty(), "misId 不能为空");
            
            boolean addResult = corpusService.updateRule(request.getRgId(), request.getMisId(), request.getRule());
            if (addResult) {
                return CommonResult.success("更新自定义规则成功");
            } else {
                return CommonResult.error("更新自定义规则失败");
            }
        } catch (Exception e) {
            log.error("updateRule 执行失败, rgId:{}, misId:{} 异常原因:{}", 
                request != null ? request.getRgId() : null, 
                request != null ? request.getMisId() : null, 
                e.getMessage());
            return CommonResult.error(String.format("执行失败,异常原因=%s", e.getMessage()));
        }
    }

    /**
     * 分页查询自定义规则
     */
    @GetMapping("/queryRule")
    public Object queryRule(@RequestParam(value = "rgId", defaultValue = "0") long rgId,
                                                 @RequestParam(value = "misId", defaultValue = "") String misId,
                                                 @RequestParam(value = "pageNum", defaultValue = "1") int pageNum,
                                                 @RequestParam(value = "pageSize", defaultValue = "100") int pageSize) {
        try {
            Preconditions.checkArgument(rgId > 0, "rgId 必须大于0");
            Preconditions.checkArgument(misId != null && !misId.isEmpty(), "misId 不能为空");
            PageDTO<RuleDTO> dto = corpusService.queryRuleByRgId(rgId, misId, pageNum, pageSize);
            Map<String, Object> result = new HashMap<>();
            result.put("currentPage", dto.getPageNum());
            result.put("pageSize", pageSize);
            result.put("total", dto.getTotalCount());
            result.put("totalPage", dto.getTotalPage());
            result.put("list", dto.getData());
            return CommonResult.success("查询成功", result);
        } catch (Exception e) {
            log.error("queryRule 执行失败, rgId:{}, misId:{} 异常原因:{}", rgId, misId, e.getMessage());
            return CommonResult.error(String.format("执行失败,异常原因=%s", e.getMessage()));
        }
    }

    /**
     * 查询最新自定义规则
     */
    @GetMapping("/queryLatestRuleByRgId")
    public Object queryLatestRuleByRgId(@RequestParam(value = "rgId", defaultValue = "0") long rgId,
                                                       @RequestParam(value = "misId", defaultValue = "") String misId) {
        try {
            Preconditions.checkArgument(rgId > 0, "rgId 必须大于0");
            Preconditions.checkArgument(misId != null && !misId.isEmpty(), "misId 不能为空");
            String knowledge = corpusService.queryLatestRuleByRgId(rgId);
            return CommonResult.success("查询成功", knowledge);
        } catch (Exception e) {
            log.error("queryLatestRuleByRgId 执行失败, rgId:{}, misId:{} 异常原因:{}", rgId, misId, e.getMessage());
            return CommonResult.error(String.format("执行失败,异常原因=%s", e.getMessage()));
        }
    }

    @GetMapping("/exportAllCorpusToExcel")
    public Object exportAllCorpusToExcel(@RequestParam(value = "rgId", defaultValue = "0") long rgId,
                                         @RequestParam(value = "misId", defaultValue = "") String misId,
                                         @RequestParam(value = "ticketId", required = false) String ticketId,
                                         @RequestParam(value = "title", required = false) String title,
                                         @RequestParam(value = "content", required = false) String content,
                                         @RequestParam(value = "source", required = false) Integer source,
                                         @RequestParam(value = "creator", required = false) String creator,
                                         @RequestParam(value = "startTime", required = false) String startTime,
                                         @RequestParam(value = "endTime", required = false) String endTime,
                                         @RequestParam(value = "strMatch", defaultValue = "") String strMatch,
                                         @RequestParam(value = "tagsIds", required = false) String tagsIds) {
        try {
            Preconditions.checkArgument(rgId > 0, "rgId 必须大于0");
            Preconditions.checkArgument(misId != null && !misId.isEmpty(), "misId 不能为空");
            String downloadLink = corpusService.exportAllCorpusToExcelAndUploadToS3(
                rgId, misId, ticketId, title, content, source, creator,
                startTime, endTime, strMatch, tagsIds
            );
            if (downloadLink != null) {
                Map<String, Object> result = new HashMap<>();
                result.put("excelDownloadLink", downloadLink);
                return CommonResult.success("导出成功", result);
            } else {
                return CommonResult.error("导出失败，未生成下载链接");
            }
        } catch (Exception e) {
            log.error("exportAllCorpusToExcel 执行失败, rgId:{}, 异常原因:{}", rgId, e.getMessage());
            return CommonResult.error(String.format("执行失败,异常原因=%s", e.getMessage()));
        }
    }
}
