package com.meituan.banma.llm.corpus.server.common.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/4
 */
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PageDTO<T>{
    private int pageNum;
    private int pageSize;
    private int totalCount;
    private int totalPage;
    private List<T> data;

    public static <T> PageDTO<T> emptyPage(int pageNum, int pageSize) {
        return PageDTO.<T>builder()
                .pageNum(pageNum)
                .pageSize(pageSize)
                .totalCount(0)
                .totalPage(0)
                .data(Collections.emptyList())
                .build();
    }
}
