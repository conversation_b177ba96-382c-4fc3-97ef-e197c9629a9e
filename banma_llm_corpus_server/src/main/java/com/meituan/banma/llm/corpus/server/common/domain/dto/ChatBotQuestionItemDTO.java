
package com.meituan.banma.llm.corpus.server.common.domain.dto;

import lombok.Data;

import java.sql.Timestamp;

@Data
public class ChatBotQuestionItemDTO {
    private Long id;
    private String questionId;
    private String question;
    private String misId;
    private String botName;
    private Integer retrievalTotalCount;
    private Integer retrievalGeneratedCount;
    private Timestamp ctime;
    private Timestamp utime;
}