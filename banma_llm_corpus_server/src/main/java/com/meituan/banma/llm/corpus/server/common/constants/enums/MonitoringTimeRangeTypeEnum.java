package com.meituan.banma.llm.corpus.server.common.constants.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum MonitoringTimeRangeTypeEnum {

    LAST_SEVEN_DAYS(0, "7天"),
    LAST_FOURTEEN_DAYS(1, "14天"),
    LAST_THIRTY_DAYS(2, "30天")
    ;

    private final Integer code;
    private final String desc;

    public static MonitoringTimeRangeTypeEnum fromCode(Integer code){
        for (MonitoringTimeRangeTypeEnum item : MonitoringTimeRangeTypeEnum.values()){
            if (item.getCode().equals(code)){
                return item;
            }
        }
        return null;
    }
}
