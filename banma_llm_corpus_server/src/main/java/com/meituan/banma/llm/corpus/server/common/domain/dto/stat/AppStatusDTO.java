package com.meituan.banma.llm.corpus.server.common.domain.dto.stat;

import com.meituan.banma.llm.corpus.server.common.domain.dto.RgItem;
import com.meituan.banma.llm.corpus.server.common.domain.dto.TicketDetailDTO;
import io.swagger.models.auth.In;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;
import java.util.Map;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AppStatusDTO {
    private String appId;
    private String appName;
    private Long beginTime;
    private Long endTime;
    private Integer conversationCount;
    private Integer userMessageCount;
    private List<QuestionCorpusRecallDetailItem> questionAndRecallInfos;
    private List<Question> questionList;
    private List<Long> relatedRgList;
    private List<TicketDetailDTO> timeRangeTicketIdList;
    private List<QuestionCluster> questionClusters;

    @Data
    @ToString
    @AllArgsConstructor
    public static class Question {
        private String question;
        private String questionMessageId;
        private String questionConversationId;
        private Long questionTime;
        private Boolean solved;
        
        public Question(String question, String questionMessageId, String questionConversationId, Long questionTime) {
            this.question = question;
            this.questionMessageId = questionMessageId;
            this.questionConversationId = questionConversationId;
            this.questionTime = questionTime;
            this.solved = true; // 默认为解决
        }
    }
}
