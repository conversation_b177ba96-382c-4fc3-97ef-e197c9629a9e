package com.meituan.banma.llm.corpus.server.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson2.JSONObject;
import com.cip.crane.client.spring.annotation.Crane;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.meituan.banma.llm.corpus.api.constants.BizCode;
import com.meituan.banma.llm.corpus.api.exception.LlmCorpusException;
import com.meituan.banma.llm.corpus.server.common.MtConfigService;
import com.meituan.banma.llm.corpus.server.common.constants.enums.CorpusChunkSourceEnum;
import com.meituan.banma.llm.corpus.server.common.constants.enums.FridayConversationUserTypeEnum;
import com.meituan.banma.llm.corpus.server.common.constants.enums.FridayMessageGenerateTypeEnum;
import com.meituan.banma.llm.corpus.server.common.domain.dto.DocRecallItem;
import com.meituan.banma.llm.corpus.server.common.domain.dto.FridayAppInfo;
import com.meituan.banma.llm.corpus.server.common.domain.dto.FridayConversationMessageDTO;
import com.meituan.banma.llm.corpus.server.common.domain.dto.FridayConversationRecallInfoDTO;
import com.meituan.banma.llm.corpus.server.common.domain.dto.FridayQuestionClusterNamingResDTO;
import com.meituan.banma.llm.corpus.server.common.domain.dto.FridayQuestionClusteringItemDTO;
import com.meituan.banma.llm.corpus.server.common.domain.dto.FridayQuestionResolveStateItem;
import com.meituan.banma.llm.corpus.server.common.domain.dto.QuestionAnswerPairDTO;
import com.meituan.banma.llm.corpus.server.common.domain.dto.RgUserInfo;
import com.meituan.banma.llm.corpus.server.common.domain.dto.StatisticsReportParam;
import com.meituan.banma.llm.corpus.server.common.domain.dto.TicketDetailDTO;
import com.meituan.banma.llm.corpus.server.common.domain.dto.stat.AppStatusDTO;
import com.meituan.banma.llm.corpus.server.common.domain.dto.stat.CorpusBotMessageItem;
import com.meituan.banma.llm.corpus.server.common.domain.dto.stat.CorpusGenerateTaskItemForStats;
import com.meituan.banma.llm.corpus.server.common.domain.dto.stat.CorpusOperationStatusDTO;
import com.meituan.banma.llm.corpus.server.common.domain.dto.stat.CorpusStatsFridaySpaceItem;
import com.meituan.banma.llm.corpus.server.common.domain.dto.stat.CorpusStatsRgInfoItem;
import com.meituan.banma.llm.corpus.server.common.domain.dto.stat.QuestionCluster;
import com.meituan.banma.llm.corpus.server.common.domain.dto.stat.QuestionCorpusRecallDetailItem;
import com.meituan.banma.llm.corpus.server.common.domain.dto.stat.RecalledCorpusChunkInfo;
import com.meituan.banma.llm.corpus.server.common.domain.dto.stat.StatQueryPageSettings;
import com.meituan.banma.llm.corpus.server.common.constants.enums.ClusterValidationMethod;
import com.meituan.banma.llm.corpus.server.dal.entity.ChatBotQuestionEntity;
import com.meituan.banma.llm.corpus.server.dal.entity.ChatBotQuestionKbRetrievalEntity;
import com.meituan.banma.llm.corpus.server.dal.entity.CorpusBotChatMessageEntity;
import com.meituan.banma.llm.corpus.server.dal.entity.FridayBotConversationEntity;
import com.meituan.banma.llm.corpus.server.dal.entity.StatisticsReportEntity;
import com.meituan.banma.llm.corpus.server.dal.mapper.ChatBotQuestionKbRetrievalMapper;
import com.meituan.banma.llm.corpus.server.dal.mapper.ChatBotQuestionMapper;
import com.meituan.banma.llm.corpus.server.dal.mapper.CorpusBotChatMessageMapper;
import com.meituan.banma.llm.corpus.server.dal.mapper.FridayBotConversationMapper;
import com.meituan.banma.llm.corpus.server.dal.mapper.ReviewMapper;
import com.meituan.banma.llm.corpus.server.dal.mapper.StatisticsReportMapper;
import com.meituan.banma.llm.corpus.server.service.ICorpusBotChatService;
import com.meituan.banma.llm.corpus.server.service.IFridayService;
import com.meituan.banma.llm.corpus.server.service.IStatisticsService;
import com.meituan.banma.llm.corpus.server.utils.DatetimeUtils;
import com.meituan.banma.llm.corpus.server.utils.TicketQueryUtil;
import com.sankuai.meituan.auth.util.UserUtils;
import com.sankuai.meituan.auth.vo.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.ExecutorService;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.Date;
import com.meituan.banma.llm.corpus.server.utils.AsyncTaskUtils;
import com.meituan.banma.llm.corpus.server.common.domain.dto.stat.KMeansConfig;
import com.meituan.banma.llm.corpus.server.utils.TextVectorizationUtil;
import com.meituan.banma.llm.corpus.server.common.constants.enums.ClusteringAlgorithmType;
import com.meituan.banma.llm.corpus.server.common.domain.dto.stat.HDBSCANConfig;
import com.meituan.banma.llm.corpus.server.utils.ClusteringFactory;
import com.meituan.banma.llm.corpus.server.common.domain.dto.AllocateQuestionsToClustersRequestParam;
import com.meituan.banma.llm.corpus.server.common.domain.dto.FridayQuestionClassficationItemDTO;
import java.util.Calendar;
import java.util.Collections;
import com.meituan.banma.llm.corpus.server.dal.mapper.ModelOutputMapper;
import com.meituan.banma.llm.corpus.server.dal.entity.ModelOutputTaskEntity;
import java.util.HashSet;
import com.meituan.banma.llm.corpus.server.service.ITicketQueryService;
import com.meituan.banma.llm.corpus.server.service.IEmpQueryService;
import com.meituan.banma.llm.corpus.server.common.domain.dto.RgListItemDTO;
import com.meituan.banma.llm.corpus.server.common.domain.dto.RgInfoDTO;
import com.meituan.banma.llm.corpus.server.dal.mapper.RgDatasetDocumentMapper;
import com.meituan.banma.llm.corpus.server.dal.entity.RgDatasetDocumentEntity;
import com.sankuai.meituan.org.opensdk.service.OrgService;
import com.meituan.banma.llm.corpus.server.common.constants.enums.ConvertTaskPlatformId;
import com.meituan.banma.llm.corpus.server.utils.RgQueryUtil;

import static com.meituan.banma.llm.corpus.server.service.impl.TicketQueryServiceImpl.parseJsonToRgInfoDTOList;

@Slf4j
@Service
public class StatisticsServiceImpl implements IStatisticsService {

    @Autowired
    private ChatBotQuestionKbRetrievalMapper chatBotQuestionKbRetrievalMapper;
    @Autowired
    private ChatBotQuestionMapper chatBotQuestionMapper;
    @Autowired
    private FridayBotConversationMapper fridayBotConversationMapper;
    
    @Autowired
    private StatisticsReportMapper statisticsReportMapper;

    @Autowired
    private MtConfigService mtConfigService;

    @Autowired
    private TicketQueryUtil ticketQueryUtil;

    @Autowired
    private ITicketQueryService ticketQueryService;

    @Autowired
    private IFridayService fridayService;
    
    @Autowired
    private ReviewMapper reviewMapper;

    @Autowired
    private ICorpusBotChatService corpusBotChatService;

    @Autowired
    private CorpusBotChatMessageMapper corpusBotChatMessageMapper;

    @Autowired
    private ModelOutputMapper modelOutputMapper;

    @Autowired
    private IEmpQueryService empQueryService;

    @Autowired
    private RgDatasetDocumentMapper rgDatasetDocumentMapper;

    @Autowired
    private OrgService orgService;

    @Autowired
    private RgQueryUtil rgQueryUtil;

    private static final Pattern corpusIdPattern = Pattern.compile("【(.*?)】");
    private static final Pattern titlePattern = Pattern.compile("】(.*?)\\n");

    /**
     * 默认统计时间范围：7天前的00:00到昨天23:59:59
     */
    private static final int DEFAULT_DAYS_BEFORE = 7;
    private static final int MILLIS_PER_DAY = 24 * 60 * 60 * 1000;
    private static final int SECONDS_PER_DAY = 24 * 60 * 60;

    @Override
    public void reportQuestionRetrievalResult(StatisticsReportParam param) throws LlmCorpusException {
        if (param == null || StringUtils.isBlank(param.getQuestion()) || StringUtils.isBlank(param.getMisId())
                || StringUtils.isBlank(param.getBotName())) {
            log.error("#StatisticsServiceImpl.reportQuestionRetrievalResult#error,参数错误：param:{}", param);
            throw LlmCorpusException.build(BizCode.ILLEGAL_ARGUMENT, "参数错误, 上报失败。");
        }
        if (param.getDocRecallItems() == null) {
            log.warn("#StatisticsServiceImpl.reportQuestionRetrievalResult#error,召回结果为null");
            param.setDocRecallItems(Lists.newArrayList());
        }
        List<DocRecallItem> generated = param.getDocRecallItems().stream()
                .filter(item -> StringUtils.isNotBlank(item.getDataId())).collect(Collectors.toList());
        ChatBotQuestionEntity chatBotQuestion = new ChatBotQuestionEntity();
        String questionId = UUID.randomUUID().toString();
        chatBotQuestion.setQuestionId(questionId);
        chatBotQuestion.setQuestion(param.getQuestion());
        chatBotQuestion.setMisId(param.getMisId());
        chatBotQuestion.setBotName(param.getBotName());
        chatBotQuestion.setRetrievalTotalCount(param.getDocRecallItems().size());
        chatBotQuestion.setRetrievalGeneratedCount(generated.size());
        chatBotQuestion.setCtime(new Timestamp(System.currentTimeMillis()));
        chatBotQuestion.setUtime(new Timestamp(System.currentTimeMillis()));
        int resp = 0;
        try {
            resp = chatBotQuestionMapper.insert(chatBotQuestion);

        } catch (Exception e) {
            log.error("#StatisticsServiceImpl.reportQuestionRetrievalResult#error,插入问题失败，chatBotQuestion:{}",
                    chatBotQuestion, e);
            throw LlmCorpusException.build(BizCode.REPORT_STATISTICS_ERROR, "插入问题失败");
        }
        if (resp <= 0) {
            log.error("#StatisticsServiceImpl.reportQuestionRetrievalResult#error,插入问题失败，chatBotQuestion:{}",
                    chatBotQuestion);
            throw LlmCorpusException.build(BizCode.REPORT_STATISTICS_ERROR, "插入问题失败");
        }
        // 插入召回的由本系统生成的结果
        List<ChatBotQuestionKbRetrievalEntity> generatedChatBotQuestionKbRetrievals = Lists.newArrayList();
        for (DocRecallItem docRecallItem : generated) {
            ChatBotQuestionKbRetrievalEntity chatBotQuestionKbRetrieval = new ChatBotQuestionKbRetrievalEntity();
            chatBotQuestionKbRetrieval.setQuestionId(questionId);
            chatBotQuestionKbRetrieval.setKnowledgeBaseName(docRecallItem.getDocumentName());
            chatBotQuestionKbRetrieval.setKnowledgeBaseSliceId(docRecallItem.getDataId());
            chatBotQuestionKbRetrieval.setRetrievalScore(docRecallItem.getScore());
            chatBotQuestionKbRetrieval.setCtime(new Timestamp(System.currentTimeMillis()));
            chatBotQuestionKbRetrieval.setUtime(new Timestamp(System.currentTimeMillis()));
            generatedChatBotQuestionKbRetrievals.add(chatBotQuestionKbRetrieval);
        }
        if (CollectionUtils.isEmpty(generatedChatBotQuestionKbRetrievals)) {
            log.info("#StatisticsServiceImpl.reportQuestionRetrievalResult#error,召回结果为空,param:{}", param);
            return;
        }
        try {
            chatBotQuestionKbRetrievalMapper.batchInsert(generatedChatBotQuestionKbRetrievals);
        } catch (Exception e) {
            log.warn(
                    "#StatisticsServiceImpl.reportQuestionRetrievalResult#error,插入召回结果失败，generatedChatBotQuestionKbRetrievals:{}",
                    generatedChatBotQuestionKbRetrievals, e);
        }
    }

    @Override
    public AppStatusDTO queryStatByAppIdAndDate(String appId, Long beginDate, Long endDate, Boolean forceGenerate) throws LlmCorpusException {
        if (StringUtils.isBlank(appId) || beginDate <= 0 || endDate <= 0) {
            log.error("#StatisticsServiceImpl.queryStatByAppIdAndDate#error,参数错误：appId:{},beginDate:{},endDate:{}",
                    appId, beginDate, endDate);
            throw LlmCorpusException.build(BizCode.ILLEGAL_ARGUMENT, "参数错误, 查询失败。");
        }
        
        // 如果不是强制生成，先尝试从数据库读取
        if (!forceGenerate) {
            try {
                StatisticsReportEntity reportEntity = statisticsReportMapper.selectByAppIdAndTimeRange(appId, beginDate, endDate);
                if (reportEntity != null && StringUtils.isNotBlank(reportEntity.getReportData())) {
                    log.info("从数据库读取到预计算的报表数据: appId={}, reportId={}", appId, reportEntity.getId());
                    return JSON.parseObject(reportEntity.getReportData(), AppStatusDTO.class);
                }
            } catch (Exception e) {
                log.warn("读取数据库报表失败，将进行实时计算: appId={}, error={}", appId, e.getMessage());
            }
        }

        // TODO : 配置
        if (endDate - beginDate > 14 * 24 * 60 * 60 * 1000L) {
            log.error("#StatisticsServiceImpl.queryStatByAppIdAndDate#error,时间间隔超过14天：appId:{},beginDate:{},endDate:{}",
                    appId, beginDate, endDate);
            throw LlmCorpusException.buildWithMsg(BizCode.ILLEGAL_ARGUMENT, "时间间隔超过14天, 查询失败。");
        }

        // 实时计算统计数据
        AppStatusDTO appStatusDTO = new AppStatusDTO();
        appStatusDTO.setAppId(appId);
        appStatusDTO.setBeginTime(beginDate);
        appStatusDTO.setEndTime(endDate);

        StatQueryPageSettings queryPageSettings = mtConfigService.getStatQueryPageSettings();

        // 直接获取总会话数
        int queryCount = fridayBotConversationMapper.countByAppIdsAndDtRangeForAssistant(Lists.newArrayList(appId), DatetimeUtils.getDateKey(beginDate),
                DatetimeUtils.getDateKey(endDate));

        int currentPageSize = queryPageSettings.getBatchSize();
        int maxPage = queryCount / queryPageSettings.getBatchSize() + 1;
        int page = 1;
        List<QuestionCorpusRecallDetailItem> questionAndRecallInfoResult = Lists.newArrayList();
        List<AppStatusDTO.Question> questionList = Lists.newArrayList();
        Set<String> conversationIdSet = Sets.newHashSet();
        
        // 新增问答对列表
        List<QuestionAnswerPairDTO> questionAnswerPairs = Lists.newArrayList();
        // 用于临时存储问题，以便与回答配对
        Map<String, AppStatusDTO.Question> questionMap = Maps.newHashMap();
        
        while (page <= maxPage && currentPageSize >= queryPageSettings.getBatchSize()) {
            List<FridayBotConversationEntity> conversations = fridayBotConversationMapper
                    .queryByAppIdsAndDtRangeForAssistant(Lists.newArrayList(appId), DatetimeUtils.getDateKey(beginDate), DatetimeUtils.getDateKey(endDate),
                            (page - 1) * queryPageSettings.getBatchSize(), queryPageSettings.getBatchSize());
            page++;
            if (CollectionUtils.isEmpty(conversations)) {
                break;
            }
            currentPageSize = conversations.size();
            for (FridayBotConversationEntity conversation : conversations) {
                // 移除对conversationCountMap的累加统计，因为已经通过countByAppIdsAndDtRange获取了总数
                if (StringUtils.isBlank(conversation.getRole())) {
                    log.warn("#queryStatByAppIdAndDate#warn,message user_type is null, conversation:{}", conversation);
                    continue;
                }
                
                if (StringUtils.isNotBlank(conversation.getConversationId())) {
                    conversationIdSet.add(conversation.getConversationId());
                }
                
                // 处理AI助手回复
                if (FridayConversationUserTypeEnum.ASSISTANT.getType().equals(conversation.getRole())) {
                    if (FridayMessageGenerateTypeEnum.WELCOME.getType().equals(conversation.getGenerateType())) {
                        log.warn("#queryStatByAppIdAndDate#warn,message GenerateType is WELCOME, skip recall_info collecting, message_id:{}", conversation.getMessageId());
                        continue;
                    }
                    
                    String recallInfoString = conversation.getRecallInfo();
                    if (StringUtils.isBlank(recallInfoString)) {
                        log.warn("#queryStatByAppIdAndDate#warn,message recall_info string is null, conversation:{}", conversation);
                        FridayBotConversationEntity parentMessage = fridayBotConversationMapper.queryByMessageId(conversation.getParentMessageId());
                        if (parentMessage == null) {
                            log.warn("#queryStatByAppIdAndDate#warn,message parent_message is null, conversation:{}", conversation);
                            continue;
                        }
                        QuestionCorpusRecallDetailItem questionAndRecallInfo = null;
                        for (QuestionCorpusRecallDetailItem questionAndRecallInfoItem : questionAndRecallInfoResult) {
                            if (questionAndRecallInfoItem.getQuestionMessageId().equals(parentMessage.getParentMessageId())) {
                                questionAndRecallInfo = questionAndRecallInfoItem;
                                break;
                            }
                        }
                        if (questionAndRecallInfo == null) {
                            questionAndRecallInfo = new QuestionCorpusRecallDetailItem();
                            questionAndRecallInfo.setQuestionMessageId(parentMessage.getParentMessageId());
                            questionAndRecallInfo.setQuestionContent(parentMessage.getMessage());
                            questionAndRecallInfo.setQuestionConversationId(parentMessage.getConversationId());
                            questionAndRecallInfoResult.add(questionAndRecallInfo);
                        }
                        long addTime = 0;
                        if (conversation.getUpdateTime() != null) {
                            addTime = conversation.getUpdateTime().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
                        }
                        
                        AppStatusDTO.Question question = new AppStatusDTO.Question(parentMessage.getMessage(), conversation.getParentMessageId(), conversation.getConversationId(), addTime);
                        questionList.add(question);
                        questionMap.put(conversation.getParentMessageId(), question);
                        
                        // 添加问答对
                        long answerTime = 0;
                        if (conversation.getUpdateTime() != null) {
                            answerTime = conversation.getUpdateTime().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
                        }
                        
                        QuestionAnswerPairDTO questionAnswerPair = QuestionAnswerPairDTO.builder()
                                .question(question)
                                .answer(conversation.getMessage())
                                .answerMessageId(conversation.getMessageId())
                                .answerTime(answerTime)
                                .conversationId(conversation.getConversationId())
                                .build();
                        questionAnswerPairs.add(questionAnswerPair);
                        
                        continue;
                    }
                    // 成功获取到recallInfo
                    recallInfoString = removeAllLargeFields(recallInfoString);
                    FridayConversationRecallInfoDTO recallInfo = null;
                    try{
                         recallInfo = JSONObject.parseObject(recallInfoString,
                                FridayConversationRecallInfoDTO.class);
                    }catch (Exception e){
                        log.error("#queryStatByAppIdAndDate#error,message recall_info convert result is null,recall_info:{}",
                                recallInfoString,e);
                        continue;
                    }

                    if (recallInfo == null) {
                        log.warn("#queryStatByAppIdAndDate#warn,message recall_info convert result is null,recall_info:{}",
                                recallInfoString);
                        continue;
                    }
                    if (recallInfo.getRecallRequest() == null) {
                        log.warn("#queryStatByAppIdAndDate#warn,message recall_info request is null,recall_info:{}",
                                recallInfo);
                        continue;
                    }
                    if (recallInfo.getRecallResponse() == null) {
                        log.warn("#queryStatByAppIdAndDate#warn,message recall_info is null,recall_info:{}", recallInfo);
                        continue;
                    }
                    FridayConversationRecallInfoDTO.RecallResponse recallResponse = recallInfo.getRecallResponse();
                    FridayConversationRecallInfoDTO.RecallRequest recallRequest = recallInfo.getRecallRequest();
                    if (recallRequest.getDocQaRequest() == null) {
                        log.warn("#queryStatByAppIdAndDate#warn,message recall_info doc_qa_request is null,recall_info:{}", recallInfo);
                        continue;
                    }
                    FridayConversationRecallInfoDTO.DocQaRequest docQaRequest = recallRequest.getDocQaRequest();
                    if (StringUtils.isBlank(docQaRequest.getText())) {
                        log.warn("#queryStatByAppIdAndDate#warn,message recall_info question is null,recall_info:{}", recallInfo);
                        continue;
                    }
                    String question = recallInfo.getRecallRequest().getDocQaRequest().getText();
                    if (StringUtils.isBlank(question)) {
                        log.warn("#queryStatByAppIdAndDate#warn,message recall_info question is null,recall_info:{}",
                                recallInfo);
                        continue;
                    }
                    QuestionCorpusRecallDetailItem questionAndRecallInfo = null;
                    for (QuestionCorpusRecallDetailItem questionAndRecallInfoItem : questionAndRecallInfoResult) {
                        if (questionAndRecallInfoItem.getQuestionMessageId().equals(conversation.getParentMessageId())) {
                            questionAndRecallInfo = questionAndRecallInfoItem;
                            break;
                        }
                    }
                    if (questionAndRecallInfo == null) {
                        questionAndRecallInfo = new QuestionCorpusRecallDetailItem();
                        questionAndRecallInfo.setQuestionMessageId(conversation.getParentMessageId());
                        questionAndRecallInfo.setQuestionContent(question);
                        questionAndRecallInfo.setQuestionConversationId(conversation.getConversationId());
                        questionAndRecallInfoResult.add(questionAndRecallInfo);
                    }

                    long addTime = 0;
                    if (conversation.getUpdateTime() != null) {
                        addTime = conversation.getUpdateTime().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
                    }
                    
                    AppStatusDTO.Question questionObj = new AppStatusDTO.Question(question, conversation.getParentMessageId(), conversation.getConversationId(), addTime);
                    questionList.add(questionObj);
                    questionMap.put(conversation.getParentMessageId(), questionObj);
                    
                    // 添加问答对
                    long answerTime = 0;
                    if (conversation.getUpdateTime() != null) {
                        answerTime = conversation.getUpdateTime().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
                    }
                    
                    QuestionAnswerPairDTO questionAnswerPair = QuestionAnswerPairDTO.builder()
                            .question(questionObj)
                            .answer(conversation.getMessage())
                            .answerMessageId(conversation.getMessageId())
                            .answerTime(answerTime)
                            .conversationId(conversation.getConversationId())
                            .build();
                    questionAnswerPairs.add(questionAnswerPair);

                    List<FridayConversationRecallInfoDTO.DocRecallItem> docRecallItems = recallResponse.getDocQaResponse()
                            .getDocRecallItems();
                    if (CollectionUtils.isEmpty(docRecallItems)) {
                        log.warn(
                                "#queryStatByAppIdAndDate#warn,message recall_info doc_recall_items is null,recall_info:{}",
                                recallInfo);
                        continue;
                    }
                    for (FridayConversationRecallInfoDTO.DocRecallItem docRecallItem : docRecallItems) {
                        RecalledCorpusChunkInfo recalledCorpusChunkInfo = convertToRecalledCorpusChunkInfo(docRecallItem);
                        if (recalledCorpusChunkInfo == null) {
                            log.error(
                                    "#queryStatByAppIdAndDate#error,convertToRecalledCorpusChunkInfo failed,docRecallItem:{}",
                                    docRecallItem);
                            continue;
                        }
                        questionAndRecallInfo.getRecalledCorpusChunkInfoList().add(recalledCorpusChunkInfo);
                    }
                } 
                // 处理用户提问
                else if (FridayConversationUserTypeEnum.USER.getType().equals(conversation.getRole())) {
                    // 用户消息不需要收集recall_info，但需要记录提问
                    if (StringUtils.isNotBlank(conversation.getMessage())) {
                        AppStatusDTO.Question question = new AppStatusDTO.Question(
                            conversation.getMessage(), 
                            conversation.getMessageId(), 
                            conversation.getConversationId(), 
                            conversation.getUpdateTime() != null ? 
                                conversation.getUpdateTime().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli() : 0
                        );
                        questionMap.put(conversation.getMessageId(), question);
                    }
                }
            }
        }
        
        // 将收集到的问答对按对话时间排序
        questionAnswerPairs = questionAnswerPairs.stream()
                .sorted((o1, o2) -> Long.compare(o1.getAnswerTime(), o2.getAnswerTime()))
                .collect(Collectors.toList());
        
        log.info("#queryStatByAppIdAndDate#收集到的问答对数量: {}", questionAnswerPairs.size());
        
        appStatusDTO.setUserMessageCount(queryCount);
        appStatusDTO.setQuestionAndRecallInfos(questionAndRecallInfoResult);
        appStatusDTO.setConversationCount(conversationIdSet.size());

        Map<String, FridayAppInfo> appInfoMap = mtConfigService.getFridayAppInfoMap();
        FridayAppInfo appInfo = appInfoMap.get(appId);
        if (appInfo == null) {
            log.warn("#StatisticsServiceImpl.queryStatByAppIdAndDate#warn,appInfo is null,appId:{}", appId);
            appStatusDTO.setAppName("未定义");
            appStatusDTO.setRelatedRgList(Lists.newArrayList());
            appStatusDTO.setTimeRangeTicketIdList(Lists.newArrayList());
            return appStatusDTO;
        }

        // 并行执行问题解决状态检查和问题聚类两个长耗时任务
        int timeout = mtConfigService.getStatsDataProcessAsyncTaskTimeout();
        List<AppStatusDTO.Question> processedQuestionList;
        List<QuestionCluster> questionClusters;
        
        // 创建最终版本的问答对列表（effectively final）
        final List<QuestionAnswerPairDTO> finalQuestionAnswerPairs = questionAnswerPairs;
        
        try {
            // 创建两个异步任务
            CompletableFuture<List<AppStatusDTO.Question>> solvedCheckFuture = 
                AsyncTaskUtils.supplyAsyncWithThreadLocal(
                    AsyncTaskUtils.getStatisticsReportThreadPool(),
                    () -> checkQuestionSolvedOrNot(questionList, finalQuestionAnswerPairs)
                );
            
            CompletableFuture<List<QuestionCluster>> clusterFuture = 
                AsyncTaskUtils.supplyAsyncWithThreadLocal(
                    AsyncTaskUtils.getStatisticsReportThreadPool(),
                    () -> {
                        try {
                            return getQuestionCluster(questionList);
                        } catch (LlmCorpusException e) {
                            throw new RuntimeException(e);
                        }
                    }
                );
            
            // 等待两个任务完成
            processedQuestionList = solvedCheckFuture.get(timeout, TimeUnit.MILLISECONDS);
            questionClusters = clusterFuture.get(timeout, TimeUnit.MILLISECONDS);
            
            log.info("#queryStatByAppIdAndDate#并行处理完成, 问题数量:{}, 聚类数量:{}", 
                    processedQuestionList.size(), questionClusters.size());
        } catch (Exception e) {
            log.error("#queryStatByAppIdAndDate#并行处理任务异常", e);
            if (e.getCause() instanceof LlmCorpusException) {
                throw (LlmCorpusException) e.getCause();
            }
            throw LlmCorpusException.buildWithMsg(BizCode.SERVER_INTERNAL_ERROR, "数据处理失败: " + e.getMessage());
        }
        
        // 更新聚类中问题的解决状态
        Map<String, Boolean> solvedStatusMap = new HashMap<>();
        for (AppStatusDTO.Question q : processedQuestionList) {
            solvedStatusMap.put(q.getQuestionMessageId(), q.getSolved());
        }
        
        for (QuestionCluster cluster : questionClusters) {
            if (cluster.getQuestionList() != null) {
                for (AppStatusDTO.Question q : cluster.getQuestionList()) {
                    Boolean solved = solvedStatusMap.get(q.getQuestionMessageId());
                    if (solved != null) {
                        q.setSolved(solved);
                    }
                }
            }
        }

        appStatusDTO.setAppName(appInfo.getAppName());
        appStatusDTO.setRelatedRgList(appInfo.getOrgIds());
        appStatusDTO.setTimeRangeTicketIdList(getTicketInfoListByRgIdList(appInfo.getOrgIds(),beginDate,endDate));
        appStatusDTO.setQuestionList(processedQuestionList);
        appStatusDTO.setQuestionClusters(questionClusters);
        
        // 不管是否强制生成，都将结果存入数据库
        try {
            // 将计算结果直接存入数据库
            String reportData = JSON.toJSONString(appStatusDTO);
            
            // 构建报表实体
            StatisticsReportEntity reportEntity = StatisticsReportEntity.builder()
                    .appId(appId)
                    .startTime(beginDate)
                    .endTime(endDate)
                    .reportData(reportData)
                    .ctime(new Timestamp(System.currentTimeMillis()))
                    .build();
            
            // 查询是否已存在报表
            StatisticsReportEntity existingReport = statisticsReportMapper.selectByAppIdAndTimeRange(appId, beginDate, endDate);
            
            if (existingReport != null) {
                // 更新已有报表
                reportEntity.setId(existingReport.getId());
                statisticsReportMapper.updateByAppIdAndTimeRange(reportEntity);
                log.info("更新应用报表成功: appId={}, reportId={}", appId, existingReport.getId());
            } else {
                // 插入新报表
                statisticsReportMapper.insert(reportEntity);
                log.info("插入应用报表成功: appId={}, reportId={}", appId, reportEntity.getId());
            }
        } catch (Exception e) {
            log.warn("存储计算结果到数据库失败: appId={}, error={}", appId, e.getMessage(), e);
        }
        
        return appStatusDTO;
    }
    
    /**
     * 调用大模型 输入问题列表，获取问题聚类
     * @param questions 问题列表
     * @return 问题聚类列表
     */
    private List<QuestionCluster> getQuestionCluster(List<AppStatusDTO.Question> questions) throws LlmCorpusException {
        if (CollectionUtils.isEmpty(questions)) {
            return Lists.newArrayList();
        }
        
        // 使用MtConfigService的专门方法获取配置
        Boolean useQuestionTypeAllocation = mtConfigService.getUseQuestionTypeAllocation();
        
        if (useQuestionTypeAllocation) {
            log.info("使用问题类型分配方法");
            return getQuestionClusterWithTypeAllocation(questions);
        }
        
        List<QuestionCluster> questionClusters = Lists.newArrayList();
        
        // 第一步：根据配置选择聚类算法对问题进行聚类
        try {
            // 1. 获取聚类算法配置参数
            ClusteringAlgorithmType algorithmType = mtConfigService.getClusteringAlgorithmType();
            KMeansConfig kMeansConfig = mtConfigService.getKMeansConfig();
            HDBSCANConfig hdbscanConfig = mtConfigService.getHDBSCANConfig();
            
            if (algorithmType == ClusteringAlgorithmType.KMEANS) {
                if (kMeansConfig.getK() <= 0) {
                    log.info("使用K-means聚类, 将自动确定最佳簇数量, maxIterations={}, 允许极小簇={}, 评估方法={}", 
                            kMeansConfig.getMaxIterations(), kMeansConfig.getAllowSmallClusters(),
                            getValidationMethodDescription(kMeansConfig.getClusterValidationMethod()));
                } else {
                    log.info("使用K-means聚类, 参数: k={}, maxIterations={}, 允许极小簇={}, 评估方法={}", 
                            kMeansConfig.getK(), kMeansConfig.getMaxIterations(), 
                            kMeansConfig.getAllowSmallClusters(), 
                            getValidationMethodDescription(kMeansConfig.getClusterValidationMethod()));
                }
            } else if (algorithmType == ClusteringAlgorithmType.HDBSCAN) {
                log.info("使用HDBSCAN*聚类, 参数: minPoints={}, minClusterSize={}, alpha={}, 算法={}",
                        hdbscanConfig.getMinPoints(), hdbscanConfig.getMinClusterSize(),
                        hdbscanConfig.getAlpha(), hdbscanConfig.getAlgorithm());
            }
            
            // 2. 将问题文本转换为向量
            // 使用embedding模型进行向量化，效果优于TF-IDF
            // 获取默认的misId用于调用embedding模型
            String misId = "daili07";
            User user = UserUtils.getUser();
            if (user != null) {
                misId = user.getLogin();
            }
            Map<String, double[]> questionIdToVector = TextVectorizationUtil.convertToVectors(questions, fridayService, misId);
            
            if (questionIdToVector.isEmpty()) {
                log.warn("问题向量化失败，回退到使用Friday聚类服务");
                return fallbackToFridayClusteringService(questions);
            }
            
            log.info("问题向量化完成，开始进行聚类, 问题数量: {}, 向量数量: {}", questions.size(), questionIdToVector.size());
            
            // 3. 使用选定的聚类算法进行聚类
            // 将Map转换为List，同时保存索引到问题ID的映射
            List<double[]> vectorsList = new ArrayList<>();
            Map<Integer, String> indexToQuestionId = new HashMap<>();
            int index = 0;
            for (Map.Entry<String, double[]> entry : questionIdToVector.entrySet()) {
                vectorsList.add(entry.getValue());
                indexToQuestionId.put(index, entry.getKey());
                index++;
            }
            
            // 使用工厂类根据配置选择聚类算法
            Map<Integer, List<Integer>> clusters = ClusteringFactory.cluster(
                    vectorsList, algorithmType, kMeansConfig, hdbscanConfig);
            
            if (clusters.isEmpty()) {
                log.warn("聚类失败，回退到使用Friday聚类服务");
                return fallbackToFridayClusteringService(questions);
            }
            
            log.info("聚类完成，簇数量: {}", clusters.size());
            
            // 4. 为每个簇构建问题集合
            // 构建问题ID到问题对象的映射
            Map<String, AppStatusDTO.Question> questionIdToQuestion = new HashMap<>();
            for (AppStatusDTO.Question question : questions) {
                questionIdToQuestion.put(question.getQuestionMessageId(), question);
            }
            
            Map<Integer, List<AppStatusDTO.Question>> clusterQuestions = new HashMap<>();
            for (Map.Entry<Integer, List<Integer>> entry : clusters.entrySet()) {
                int clusterId = entry.getKey();
                List<Integer> indices = entry.getValue();
                
                List<AppStatusDTO.Question> clusterQuestionList = new ArrayList<>();
                for (Integer i : indices) {
                    String questionId = indexToQuestionId.get(i);
                    AppStatusDTO.Question question = questionIdToQuestion.get(questionId);
                    if (question != null) {
                        clusterQuestionList.add(question);
                    }
                }
                
                clusterQuestions.put(clusterId, clusterQuestionList);
            }
            
            // 5. 通过Friday大模型为每个簇生成类别描述 - 并行处理
            List<CompletableFuture<QuestionCluster>> clusterFutures = new ArrayList<>();
            ExecutorService clusterNamingExecutor = AsyncTaskUtils.getClusterNamingThreadPool();
            
            for (Map.Entry<Integer, List<AppStatusDTO.Question>> entry : clusterQuestions.entrySet()) {
                final int clusterId = entry.getKey();
                final List<AppStatusDTO.Question> clusterQuestionList = entry.getValue();
                
                // 为每个聚类异步生成名称
                CompletableFuture<QuestionCluster> future = AsyncTaskUtils.supplyAsyncWithThreadLocal(
                    clusterNamingExecutor,
                    () -> {
                        // 构建QuestionCluster对象
                        QuestionCluster questionCluster = new QuestionCluster();
                        questionCluster.setQuestionList(clusterQuestionList);
                        
                        // 为簇生成描述
                        String description = generateClusterDescription(clusterQuestionList, clusterId);
                        questionCluster.setQuestionPattern(description);
                        
                        return questionCluster;
                    }
                );
                
                clusterFutures.add(future);
            }
            
            // 收集所有异步任务的结果
            int timeout = mtConfigService.getStatsDataProcessAsyncTaskTimeout();
            for (CompletableFuture<QuestionCluster> future : clusterFutures) {
                try {
                    QuestionCluster cluster = future.get(timeout, TimeUnit.MILLISECONDS);
                    questionClusters.add(cluster);
                } catch (Exception e) {
                    log.error("生成聚类描述异步任务执行失败: {}", e.getMessage(), e);
                }
            }
            
            log.info("并行生成聚类描述完成，共处理 {} 个聚类", questionClusters.size());
            
            return questionClusters;
            
        } catch (Exception e) {
            log.error("聚类算法执行失败，回退到Friday聚类服务: {}", e.getMessage(), e);
            return fallbackToFridayClusteringService(questions);
        }
    }
    
    /**
     * 使用问题类型分配的方式获取问题聚类
     * 1. 先通过Friday服务获取问题类型列表
     * 2. 再多线程将问题分批分配到这些类型中
     * 3. 最后合并结果
     * 
     * @param questions 问题列表
     * @return 问题聚类列表
     * @throws LlmCorpusException 如果处理过程中出错
     */
    private List<QuestionCluster> getQuestionClusterWithTypeAllocation(List<AppStatusDTO.Question> questions) throws LlmCorpusException {
        if (CollectionUtils.isEmpty(questions)) {
            return Lists.newArrayList();
        }
        
        // 结果列表
        List<QuestionCluster> questionClusters = Lists.newArrayList();
        
        try {
            // 1. 获取所有问题内容
            List<String> questionContents = questions.stream()
                .map(AppStatusDTO.Question::getQuestion)
                .collect(Collectors.toList());
            
            // 2. 调用Friday服务总结问题类型
            log.info("开始调用Friday服务总结问题类型，问题数量: {}", questionContents.size());
            List<String> questionTypes = fridayService.summarizeQuestionsIntoTypeDefinition(questionContents);
            if (CollectionUtils.isEmpty(questionTypes)) {
                log.warn("Friday服务未返回问题类型，回退到使用原始聚类方法");
                return fallbackToFridayClusteringService(questions);
            }
            log.info("获取到问题类型: {}，开始将问题分配到类型中", questionTypes);
            
            // 3. 准备分批处理
            int batchSize = mtConfigService.getQuestionAllocationBatchSize();
            int totalSize = questions.size();
            int batchCount = (totalSize + batchSize - 1) / batchSize;
            
            // 4. 使用CompletableFuture并行处理每个批次
            List<CompletableFuture<List<FridayQuestionClassficationItemDTO>>> futures = new ArrayList<>();
            ExecutorService executor = AsyncTaskUtils.getClusterNamingThreadPool();
            
            // 分批提交任务
            for (int i = 0; i < batchCount; i++) {
                int startIndex = i * batchSize;
                int endIndex = Math.min((i + 1) * batchSize, totalSize);
                List<AppStatusDTO.Question> batchQuestions = questions.subList(startIndex, endIndex);
                
                // 创建任务
                final int batchNum = i + 1;
                // 必须使用final或effective final保证类型一致，避免Lambda表达式中类型推断不一致
                List<AllocateQuestionsToClustersRequestParam.ClusterTypeDefinition> finalQuestionTypes = new ArrayList<>();
                for (int j = 0; j < questionTypes.size(); j++) {
                    AllocateQuestionsToClustersRequestParam.ClusterTypeDefinition clusterTypeDefinition = new AllocateQuestionsToClustersRequestParam.ClusterTypeDefinition();
                    clusterTypeDefinition.setClusterId(j);
                    clusterTypeDefinition.setClusterType(questionTypes.get(j));
                    finalQuestionTypes.add(clusterTypeDefinition);
                }
                
                
                CompletableFuture<List<FridayQuestionClassficationItemDTO>> future = AsyncTaskUtils.supplyAsyncWithThreadLocal(
                    executor,
                    () -> {
                        try {
                            log.info("处理第 {}/{} 批问题，数量: {}", batchNum, batchCount, batchQuestions.size());
                            // 构建请求参数
                            AllocateQuestionsToClustersRequestParam requestParam = new AllocateQuestionsToClustersRequestParam();
                            requestParam.setQuestions(batchQuestions);
                            // 使用finalQuestionTypes，确保类型一致性
                            requestParam.setClusterTypeDefinitions(finalQuestionTypes);
                            
                            // 调用Friday服务分配问题
                            return fridayService.allocateQuestionsToClusters(requestParam);
                        } catch (Exception e) {
                            log.error("处理第 {}/{} 批问题时出错: {}", batchNum, batchCount, e.getMessage(), e);
                            return Lists.newArrayList();
                        }
                    }
                );
                
                futures.add(future);
            }
            
            // 5. 等待所有任务完成并收集结果
            int timeout = mtConfigService.getQuestionAllocationTaskTimeout();
            // Key the map by Cluster ID (Integer) now, to use it as an index for questionTypes
            Map<Integer, List<AppStatusDTO.Question>> clusterIdToQuestionsMap = new HashMap<>();
            
            for (CompletableFuture<List<FridayQuestionClassficationItemDTO>> future : futures) {
                try {
                    List<FridayQuestionClassficationItemDTO> batchResult = future.get(timeout, TimeUnit.MILLISECONDS);
                    if (CollectionUtils.isEmpty(batchResult)) {
                        continue;
                    }
                    
                    // 合并结果 - 将每个批次中的分类汇总
                    for (FridayQuestionClassficationItemDTO item : batchResult) {
                        Integer clusterId = item.getClusterId(); // Use clusterId from the item
                        
                        if (clusterId == null) {
                            log.warn("#getQuestionClusterWithTypeAllocation#warn, received null clusterId for item: {}. Skipping.", item);
                            continue;
                        }
                        
                        // Add questions to the map keyed by clusterId.
                        List<AppStatusDTO.Question> questionsForThisClusterId = clusterIdToQuestionsMap.computeIfAbsent(
                            clusterId, k -> new ArrayList<>()
                        );
                        
                        if (CollectionUtils.isNotEmpty(item.getQuestionMessageIdList())) {
                            for (String questionId : item.getQuestionMessageIdList()) {
                                // 查找原始问题
                                for (AppStatusDTO.Question originalQuestion : questions) { // Iterate through the original full list 'questions'
                                    if (questionId.equals(originalQuestion.getQuestionMessageId())) {
                                        questionsForThisClusterId.add(originalQuestion);
                                        break;
                                    }
                                }
                            }
                        }
                    }
                    
                } catch (Exception e) {
                    log.error("等待批处理任务结果时出错: {}", e.getMessage(), e);
                }
            }
            
            // 6. 构建最终结果
            for (Map.Entry<Integer, List<AppStatusDTO.Question>> entry : clusterIdToQuestionsMap.entrySet()) {
                Integer clusterId = entry.getKey();
                List<AppStatusDTO.Question> typeQuestions = entry.getValue();
                
                if (CollectionUtils.isEmpty(typeQuestions)) {
                    continue;
                }

                String questionPatternToSet;
                // Use clusterId to get the type from the original questionTypes list
                if (clusterId >= 0 && clusterId < questionTypes.size()) {
                    questionPatternToSet = questionTypes.get(clusterId);
                } else {
                    log.warn("#getQuestionClusterWithTypeAllocation#warn, clusterId {} is out of bounds for questionTypes list (size {}). Using a default pattern.",
                             clusterId, questionTypes.size());
                    // Fallback to a generic name if clusterId is invalid index
                    questionPatternToSet = "未定义类型 (ID: " + clusterId + ")";
                }
                
                // 创建问题聚类对象
                QuestionCluster cluster = new QuestionCluster();
                cluster.setQuestionPattern(questionPatternToSet); // Set the looked-up type pattern
                cluster.setQuestionList(typeQuestions);
                questionClusters.add(cluster);
            }
            
            log.info("问题分配处理完成，共 {} 个问题类型 (通过ID映射)", questionClusters.size());
            return questionClusters;
            
        } catch (Exception e) {
            log.error("使用问题类型分配方式处理时出错: {}", e.getMessage(), e);
            return fallbackToFridayClusteringService(questions);
        }
    }

    /**
     * 为聚类生成描述
     * @param questions 簇中的问题列表
     * @param clusterId 簇ID
     * @return 簇的描述
     */
    private String generateClusterDescription(List<AppStatusDTO.Question> questions, int clusterId) {
        try {
            FridayQuestionClusterNamingResDTO res = fridayService.questionClusterNaming(questions);
            if (res == null) {
                log.error("为聚类生成描述失败: {}", "res is null");
                return "未命名聚类#" + clusterId;
            }
            return res.getClusterName();
        } catch (Exception e) {
            log.error("为聚类生成描述失败: {}", e.getMessage(), e);
            return "未命名聚类#" + clusterId;
        }
    }
    
    /**
     * 回退到使用Friday聚类服务
     */
    private List<QuestionCluster> fallbackToFridayClusteringService(List<AppStatusDTO.Question> questions) throws LlmCorpusException {
        List<QuestionCluster> questionClusters = Lists.newArrayList();
        List<FridayQuestionClusteringItemDTO> questionClusteringItems = fridayService.questionClustering(questions);
        if (CollectionUtils.isEmpty(questionClusteringItems)) {
            log.warn("#StatisticsServiceImpl.fallbackToFridayClusteringService#error,questionClusteringItemDTOS is null,questions:{}",questions);
            return questionClusters;
        }
        Map<String, AppStatusDTO.Question> questionMap = Maps.newHashMap();
        for (AppStatusDTO.Question question : questions) {
            questionMap.put(question.getQuestionMessageId(), question);
        }
        for (FridayQuestionClusteringItemDTO questionClusteringItem : questionClusteringItems) {
            QuestionCluster questionCluster = new QuestionCluster();
            questionCluster.setQuestionPattern(questionClusteringItem.getQuestionPattern());
            List<AppStatusDTO.Question> questionList = Lists.newArrayList();
            for (String questionMessageId : questionClusteringItem.getQuestionMessageIdList()) {
                AppStatusDTO.Question questionItem = questionMap.get(questionMessageId);
                if (questionItem == null) {
                    log.warn("#StatisticsServiceImpl.fallbackToFridayClusteringService#warn,questionItem is null,questionMessageId:{}",questionMessageId);
                    AppStatusDTO.Question questionResult = new AppStatusDTO.Question("模型返回的问题ID错误", questionMessageId,"0",0L);
                    questionList.add(questionResult);
                    continue;
                }
                questionList.add(questionItem);
            }
            questionCluster.setQuestionList(questionList);
            questionClusters.add(questionCluster);
        }
        return questionClusters;
    }

    /**
     * 检查问题是否已解决，并更新问题的solved字段
     * @param questionList 问题列表
     * @param questionAnswerPairs 问题答案对列表
     * @return 更新后的问题列表
     */
    private List<AppStatusDTO.Question> checkQuestionSolvedOrNot(List<AppStatusDTO.Question> questionList, List<QuestionAnswerPairDTO> questionAnswerPairs) {
        if (CollectionUtils.isEmpty(questionList) || CollectionUtils.isEmpty(questionAnswerPairs)) {
            return questionList;
        }
        
        try {
            // 调用Friday服务获取问题解决状态
            List<FridayQuestionResolveStateItem> resolveStateItems = fridayService.judgeQuestionResolveState(questionAnswerPairs);
            if (CollectionUtils.isEmpty(resolveStateItems)) {
                log.warn("#checkQuestionSolvedOrNot#warn, resolveStateItems为空");
                return questionList;
            }
            
            // 构建问题ID到解决状态的映射
            Map<String, Boolean> questionSolvedMap = new HashMap<>();
            for (FridayQuestionResolveStateItem item : resolveStateItems) {
                questionSolvedMap.put(item.getQuestionMessageId(), item.isState());
            }
            
            // 更新问题列表中的solved字段
            for (AppStatusDTO.Question question : questionList) {
                Boolean solved = questionSolvedMap.get(question.getQuestionMessageId());
                if (solved != null) {
                    question.setSolved(solved);
                }
            }
        } catch (Exception e) {
            log.error("#checkQuestionSolvedOrNot#error", e);
        }
        
        return questionList;
    }

    /**
     * 从JSON字符串中移除chatRequest字段内容，保留基本结构
     */
    private static String removeChatRequest(String jsonStr) {
        int startIndex = jsonStr.indexOf("\"chatRequest\":");
        if (startIndex == -1) {
            return jsonStr; // 字段不存在
        }

        // 找到字段开始位置后的第一个 {
        int objectStartIndex = jsonStr.indexOf("{", startIndex);
        if (objectStartIndex == -1) {
            return jsonStr; // 格式不符合预期
        }

        // 跟踪花括号嵌套
        int nestLevel = 1;
        int endIndex = objectStartIndex + 1;

        while (nestLevel > 0 && endIndex < jsonStr.length()) {
            char c = jsonStr.charAt(endIndex);
            if (c == '{') {
                nestLevel++;
            } else if (c == '}') {
                nestLevel--;
            }
            endIndex++;
        }

        if (nestLevel != 0) {
            return jsonStr; // JSON 格式不正确
        }

        // 构造新的 JSON 字符串，用简化结构替换chatRequest内容
        StringBuilder result = new StringBuilder();
        result.append(jsonStr.substring(0, objectStartIndex + 1));
        result.append("\"simplified\":true");
        result.append(jsonStr.substring(endIndex - 1));

        return result.toString();
    }

    /**
     * 从JSON字符串中移除llmHitDetail字段内容，保留基本结构
     */
    private static String removeLlmHitDetail(String jsonStr) {
        int startIndex = jsonStr.indexOf("\"llmHitDetail\":");
        if (startIndex == -1) {
            return jsonStr; // 字段不存在
        }

        // 找到字段开始位置后的第一个 {
        int objectStartIndex = jsonStr.indexOf("{", startIndex);
        if (objectStartIndex == -1) {
            return jsonStr; // 格式不符合预期
        }

        // 跟踪花括号嵌套
        int nestLevel = 1;
        int endIndex = objectStartIndex + 1;

        while (nestLevel > 0 && endIndex < jsonStr.length()) {
            char c = jsonStr.charAt(endIndex);
            if (c == '{') {
                nestLevel++;
            } else if (c == '}') {
                nestLevel--;
            }
            endIndex++;
        }

        if (nestLevel != 0) {
            return jsonStr; // JSON 格式不正确
        }

        // 构造新的 JSON 字符串，用简化结构替换llmHitDetail内容
        StringBuilder result = new StringBuilder();
        result.append(jsonStr.substring(0, objectStartIndex + 1));
        result.append("\"records\":[],\"lastLLMReturn\":\"\",\"reasoningContent\":\"\"");
        result.append(jsonStr.substring(endIndex - 1));

        return result.toString();
    }

    /**
     * 从JSON字符串中移除llmExecuteDetails字段内容，保留基本结构
     */
    private static String removeLlmExecuteDetails(String jsonStr) {
        int startIndex = jsonStr.indexOf("\"llmExecuteDetails\":");
        if (startIndex == -1) {
            return jsonStr; // 字段不存在
        }

        // 找到字段开始位置后的第一个 {
        int objectStartIndex = jsonStr.indexOf("{", startIndex);
        if (objectStartIndex == -1) {
            return jsonStr; // 格式不符合预期
        }

        // 跟踪花括号嵌套
        int nestLevel = 1;
        int endIndex = objectStartIndex + 1;

        while (nestLevel > 0 && endIndex < jsonStr.length()) {
            char c = jsonStr.charAt(endIndex);
            if (c == '{') {
                nestLevel++;
            } else if (c == '}') {
                nestLevel--;
            }
            endIndex++;
        }

        if (nestLevel != 0) {
            return jsonStr; // JSON 格式不正确
        }

        // 构造新的 JSON 字符串，用简化结构替换llmExecuteDetails内容
        StringBuilder result = new StringBuilder();
        result.append(jsonStr.substring(0, objectStartIndex + 1));
        result.append("\"chatCompletionRequests\":[],\"usage\":{\"prompt_tokens\":0,\"completion_tokens\":0,\"total_tokens\":0}");
        result.append(jsonStr.substring(endIndex - 1));

        return result.toString();
    }

    private static String removeAllLargeFields(String jsonStr) {
        String result = jsonStr;
        result = removeFunctionQaResponse(result);
        result = removeLlmExecuteDetails(result);
        result = removeChatRequest(result);
        result = removeLlmHitDetail(result);
        result = removeLlmExecuteDetails(result);
        return result;
    }

    /**
     * 从JSON字符串中移除functionQaResponse字段的详细内容，保留基本结构
     * @param jsonStr 输入的JSON字符串
     * @return 处理后的JSON字符串
     */
    public static String removeFunctionQaResponse(String jsonStr) {
        int startIndex = jsonStr.indexOf("\"functionQaResponse\":");
        if (startIndex == -1) {
            return jsonStr; // 字段不存在
        }

        // 找到字段开始位置后的第一个 {
        int objectStartIndex = jsonStr.indexOf("{", startIndex);
        if (objectStartIndex == -1) {
            return jsonStr; // 格式不符合预期
        }

        // 跟踪花括号嵌套
        int nestLevel = 1;
        int endIndex = objectStartIndex + 1;

        while (nestLevel > 0 && endIndex < jsonStr.length()) {
            char c = jsonStr.charAt(endIndex);
            if (c == '{') {
                nestLevel++;
            } else if (c == '}') {
                nestLevel--;
            }
            endIndex++;
        }

        if (nestLevel != 0) {
            return jsonStr; // JSON 格式不正确
        }

        // 构造新的 JSON 字符串，用最简结构替换functionQaResponse内容
        StringBuilder result = new StringBuilder();
        result.append(jsonStr.substring(0, objectStartIndex + 1));
        result.append("\"responseCode\":\"\",\"message\":\"\",\"functionRecallItems\":[]");
        result.append(jsonStr.substring(endIndex - 1));

        return result.toString();
    }

    private List<TicketDetailDTO> getTicketInfoListByRgIdList(List<Long> rgIdList, Long beginDate, Long endDate) {
        if (CollectionUtils.isEmpty(rgIdList)) {
            return Lists.newArrayList();
        }
        List<TicketDetailDTO> ticketInfoList = Lists.newArrayList();
        for (Long rgId : rgIdList) {
            List<RgUserInfo> rgAdminUserList = ticketQueryService.queryRgAdminUserInfoByRgId("daili07", rgId);
            List<RgUserInfo> rgUserList = ticketQueryService.queryRgNormalUserInfoByRgId("daili07", rgId);
            rgAdminUserList.addAll(rgUserList);
            String misId = "daili07";
            if (CollectionUtils.isEmpty(rgAdminUserList)) {
                log.error("#StatisticsServiceImpl.getTicketInfoListByRgIdList#error,rgAdminUserList is empty,rgId:{}", rgId);
            }else {
                misId = rgAdminUserList.get(0).getIdentify();
            }

            List<TicketDetailDTO> ticketDetailDTOList = ticketQueryService.queryTicketListWithDate(misId, rgId, beginDate, endDate);
            if (CollectionUtils.isEmpty(ticketDetailDTOList)) {
                log.warn("#StatisticsServiceImpl.getTicketInfoListByRgIdList#warn,ticketDetailDTOList is empty,rgId:{}", rgId);
                continue;
            }
            // 移除所有rg管理员提的tt （rgAdminUserList的任意getIdentify == TicketDetailDTO.reporter）
            ticketDetailDTOList.removeIf(ticketDetailDTO -> rgAdminUserList.stream().anyMatch(rgUserInfo -> rgUserInfo.getIdentify().equals(ticketDetailDTO.getReporter())));
            ticketInfoList.addAll(ticketDetailDTOList);
        }
        return ticketInfoList;
    }

    private RecalledCorpusChunkInfo
            convertToRecalledCorpusChunkInfo(FridayConversationRecallInfoDTO.DocRecallItem docRecallItem) {
        if (docRecallItem == null || StringUtils.isBlank(docRecallItem.getText())) {
            log.warn(
                    "#StatisticsServiceImpl.convertToRecalledCorpusChunkInfo#warn,docRecallItem is null or text is blank,docItem:{}",
                    docRecallItem);
            return null;
        }

        RecalledCorpusChunkInfo recalledCorpusChunkInfo = new RecalledCorpusChunkInfo();
        recalledCorpusChunkInfo.setCorpusId(docRecallItem.getTextId());
        recalledCorpusChunkInfo.setTitle(docRecallItem.getText());
        recalledCorpusChunkInfo.setSource(CorpusChunkSourceEnum.OTHER.getCode());
        try {
            FridayConversationRecallInfoDTO.RichData richData = docRecallItem.getRichData();
            if (richData != null && richData.getSource() != null && richData.getSource().getDocId() != null) {
                recalledCorpusChunkInfo.setParentId(String.valueOf(richData.getSource().getDocId()));
                recalledCorpusChunkInfo.setTitle(richData.getSource().getDocName());
                recalledCorpusChunkInfo.setSource(CorpusChunkSourceEnum.KM_IMPORT_FRIDAY.getCode());
            }
        } catch (Exception e) {
            log.error("#StatisticsServiceImpl.convertToRecalledCorpusChunkInfo#error,docId转换失败,docRecallItem:{}",
                    docRecallItem, e);
        }

        String text = docRecallItem.getText();
        recalledCorpusChunkInfo.setContent(text);
        // 判断text是否以【XXX】模式开头
        if (StringUtils.isNotBlank(text) && text.startsWith("【")) {
            // 如果是，则使用正则提取【XXX】内的XXX
            try {
                Matcher matcher = corpusIdPattern.matcher(text);
                if (matcher.find()) {
                    String corpusId = matcher.group(1);
                    recalledCorpusChunkInfo.setCorpusId(corpusId);
                    recalledCorpusChunkInfo.setSource(CorpusChunkSourceEnum.CORPUS_SERVICE_CONVERT.getCode());
                    // 提取标题：从首个】到首个\n之间的内容
                    Matcher titleMatcher = titlePattern.matcher(text);
                    if (titleMatcher.find()) {
                        String title = titleMatcher.group(1);
                        recalledCorpusChunkInfo.setTitle(title);
                    } else {
                        log.warn("#StatisticsServiceImpl.convertToRecalledCorpusChunkInfo#warn,提取标题失败,text:{}", text);
                    }
                } else {
                    log.error(
                            "#StatisticsServiceImpl.convertToRecalledCorpusChunkInfo#error,text is not start with 【XXX】,text:{}",
                            text);
                }
            } catch (Exception e) {
                log.error("#StatisticsServiceImpl.convertToRecalledCorpusChunkInfo#error,正则提取corpusId失败,text:{}", text,
                        e);
            }
        }

        return recalledCorpusChunkInfo;
    }

    @Override
    public List<FridayAppInfo> queryFridayAppInfoList() {
        List<FridayAppInfo> fridayAppInfoList = Lists.newArrayList();
        Map<String,FridayAppInfo> fridayAppInfoMap = mtConfigService.getFridayAppInfoMap();
        if (MapUtils.isEmpty(fridayAppInfoMap)) {
            return fridayAppInfoList;
        }
        fridayAppInfoList.addAll(fridayAppInfoMap.values());
        return fridayAppInfoList;
    }

    @Override
    public List<FridayAppInfo> queryFridayAppInfoListWithValidate(String misId) {
        List<FridayAppInfo> fridayAppInfoList = Lists.newArrayList();
        Map<String,FridayAppInfo> fridayAppInfoMap = mtConfigService.getFridayAppInfoMap();
        if (MapUtils.isEmpty(fridayAppInfoMap)) {
            return fridayAppInfoList;
        }

        // 0. 白名单能力检查
        List<String> whitelist = mtConfigService.getFridayAppInfoWhitelist();
        if (CollectionUtils.isNotEmpty(whitelist) && whitelist.contains(misId)) {
            log.info("#queryFridayAppInfoListWithValidate# 用户在白名单中，返回全部应用信息: misId={}", misId);
            fridayAppInfoList.addAll(fridayAppInfoMap.values());
            return fridayAppInfoList;
        }

        // 权限过滤
        for (FridayAppInfo appInfo : fridayAppInfoMap.values()) {
            if (hasPermissionForApp(misId, appInfo)) {
                fridayAppInfoList.add(appInfo);
            }
        }

        log.info("#queryFridayAppInfoListWithValidate# 用户有权限的应用数量: misId={}, count={}", misId, fridayAppInfoList.size());
        return fridayAppInfoList;
    }

    @Override
    public List<FridayConversationMessageDTO> queryConversationMessages(String conversationId) throws LlmCorpusException {
        if (StringUtils.isBlank(conversationId)) {
            log.error("#StatisticsServiceImpl.queryConversationMessages#error,参数错误：conversationId为空");
            throw LlmCorpusException.build(BizCode.ILLEGAL_ARGUMENT, "参数错误, 会话ID不能为空");
        }
        
        try {
            List<FridayBotConversationEntity> conversations = fridayBotConversationMapper.queryByConversationId(conversationId);
            if (CollectionUtils.isEmpty(conversations)) {
                log.warn("#StatisticsServiceImpl.queryConversationMessages#warn,未查询到会话记录,conversationId:{}", conversationId);
                return Lists.newArrayList();
            }
            // 转换为DTO列表返回
            return FridayConversationMessageDTO.fromEntities(conversations);
        } catch (Exception e) {
            log.error("#StatisticsServiceImpl.queryConversationMessages#error,查询会话记录失败,conversationId:{},异常原因:{}",
                    conversationId, e.getMessage(), e);
            throw LlmCorpusException.build(BizCode.QUERY_CONVERSATION_ERROR, "查询会话记录失败: " + e.getMessage());
        }
    }

    /**
     * 获取聚类验证方法的详细描述
     * @param method 验证方法枚举
     * @return 验证方法的详细描述
     */
    private String getValidationMethodDescription(ClusterValidationMethod method) {
        switch (method) {
            case SILHOUETTE:
                return "轮廓系数法 - 评估点与自身簇的相似度与其他簇的差异度，适合形状规则、密度均匀的数据集";
            case DB_INDEX:
                return "Davies-Bouldin指数法 - 关注簇内相似性和簇间差异性的平衡，适合存在噪声的数据集";
            case COMBINED:
                return "组合评估法 - 结合轮廓系数和Davies-Bouldin指数，取其平均结果，适合一般数据场景";
            case CH_INDEX:
                return "Calinski-Harabasz指数法 - 关注簇间和簇内方差比率，适合密度均匀、分布规则的数据集";
            case GAP_STATISTIC:
                return "Gap统计量法 - 比较观察数据与随机参考分布的聚类分散度差异，适合非球形、不规则形状簇";
            case COMPREHENSIVE:
                return "综合评估法 - 采用多种方法投票机制，适合复杂、多样化的数据集，提供稳健可靠的估计";
            default:
                return "未知验证方法";
        }
    }

    @Crane("generate-statistics-report-task")
    @Override
    public void generateStatisticsReportTask() {
        log.info("开始执行统计报表生成定时任务");
        try {
            // 获取所有需要生成报表的应用
            List<FridayAppInfo> appInfoList = queryFridayAppInfoList();
            if (appInfoList.isEmpty()) {
                log.warn("没有需要生成报表的应用配置");
                return;
            }

            // 计算时间范围：使用LocalDate精确计算
            LocalDate today = LocalDate.now();
            LocalDate yesterday = today.minusDays(1);
            LocalDate startDay = today.minusDays(DEFAULT_DAYS_BEFORE);
            
            // 转换为毫秒时间戳 - 精确计算
            // 开始时间：7天前的00:00:00.000
            long startTime = startDay.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
            // 结束时间：昨天的23:59:59.999
            long endTime = yesterday.atTime(LocalTime.MAX).atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();

            log.info("生成报表时间范围: startTime={} ({}), endTime={} ({})", 
                    startTime, startDay, endTime, yesterday);

            // 为每个应用生成报表
            int total = appInfoList.size();
            for (int i = 0; i < total; i++) {
                FridayAppInfo appInfo = appInfoList.get(i);
                try {
                    log.info("开始处理应用[{}/{}]: appId={}, appName={}", 
                            (i+1), total, appInfo.getAppId(), appInfo.getAppName());
                    generateReportForApp(appInfo.getAppId(), startTime, endTime);
                    log.info("完成处理应用[{}/{}]: appId={}", (i+1), total, appInfo.getAppId());
                } catch (Exception e) {
                    log.error("生成应用报表失败[{}/{}]: appId={}, error={}", 
                            (i+1), total, appInfo.getAppId(), e.getMessage(), e);
                }
                
                // 不是最后一个应用，则休眠30秒
                if (i < total - 1) {
                    try {
                        log.info("休眠30秒后继续处理下一个应用...");
                        Thread.sleep(30000);
                    } catch (InterruptedException e) {
                        log.warn("休眠被中断: {}", e.getMessage());
                        Thread.currentThread().interrupt();
                    }
                }
            }
            
            log.info("统计报表生成定时任务执行完成,共处理{}个应用", total);
        } catch (Exception e) {
            log.error("统计报表生成定时任务执行异常: {}", e.getMessage(), e);
        }
    }

    @Override
    public boolean regenerateStatisticsReport(String appId, Long beginDate, Long endDate) throws LlmCorpusException {
        if (StringUtils.isBlank(appId) || beginDate == null || endDate == null) {
            throw LlmCorpusException.buildWithMsg(BizCode.ILLEGAL_ARGUMENT, "参数错误：应用ID和时间范围不能为空");
        }

        try {
            return generateReportForApp(appId, beginDate, endDate);
        } catch (Exception e) {
            log.error("手动重新生成报表失败: appId={}, beginDate={}, endDate={}, error={}", 
                    appId, beginDate, endDate, e.getMessage(), e);
            throw LlmCorpusException.buildWithMsg(BizCode.SERVER_INTERNAL_ERROR, "重新生成报表失败: " + e.getMessage());
        }
    }

    /**
     * 为指定应用生成报表并存储
     * @param appId 应用ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 是否生成成功
     */
    private boolean generateReportForApp(String appId, long startTime, long endTime) throws LlmCorpusException {
        log.info("开始生成应用报表: appId={}, startTime={}, endTime={}", appId, startTime, endTime);
        
        // 直接调用queryStatByAppIdAndDate方法，并强制重新生成数据
        AppStatusDTO appStatusDTO = queryStatByAppIdAndDate(appId, startTime, endTime, true);
        if (appStatusDTO == null) {
            log.warn("生成报表数据为空: appId={}", appId);
            return false;
        }

        // 转换为JSON
        String reportData = JSON.toJSONString(appStatusDTO);
        
        // 构建报表实体
        StatisticsReportEntity reportEntity = StatisticsReportEntity.builder()
                .appId(appId)
                .startTime(startTime)
                .endTime(endTime)
                .reportData(reportData)
                .ctime(new Timestamp(System.currentTimeMillis()))
                .build();

        try {
            // 查询是否已存在报表
            StatisticsReportEntity existingReport = statisticsReportMapper.selectByAppIdAndTimeRange(appId, startTime, endTime);
            
            if (existingReport != null) {
                // 更新已有报表
                reportEntity.setId(existingReport.getId());
                int updated = statisticsReportMapper.updateByAppIdAndTimeRange(reportEntity);
                log.info("更新应用报表成功: appId={}, reportId={}", appId, existingReport.getId());
                return updated > 0;
            } else {
                // 插入新报表
                int inserted = statisticsReportMapper.insert(reportEntity);
                log.info("插入应用报表成功: appId={}, reportId={}", appId, reportEntity.getId());
                return inserted > 0;
            }
        } catch (Exception e) {
            log.error("存储报表数据失败: appId={}, error={}", appId, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public AppStatusDTO queryStatByAppIdAndDate(String appId, Long beginDate, Long endDate) throws LlmCorpusException {
        return queryStatByAppIdAndDate(appId, beginDate, endDate, false);
    }

    /**
     * 生成上一自然周（周一至周日）的统计报表定时任务
     */
    @Crane("generate-weekly-statistics-report-task")
    @Override
    public void generateWeeklyStatisticsReportTask() {
        log.info("开始执行上一自然周统计报表生成定时任务");
        try {
            // 获取所有需要生成报表的应用
            List<FridayAppInfo> appInfoList = queryFridayAppInfoList();
            if (appInfoList.isEmpty()) {
                log.warn("没有需要生成报表的应用配置");
                return;
            }

            // 计算上一自然周的时间范围（上周一00:00:00到上周日23:59:59）
            LocalDate today = LocalDate.now();
            // 计算上周一的日期
            LocalDate lastMonday = today.minusWeeks(1).with(java.time.DayOfWeek.MONDAY);
            // 计算上周日的日期
            LocalDate lastSunday = today.minusWeeks(1).with(java.time.DayOfWeek.SUNDAY);
            
            // 转换为毫秒时间戳
            long startTime = lastMonday.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
            long endTime = lastSunday.atTime(LocalTime.MAX).atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();

            log.info("生成上一自然周报表时间范围: startTime={} ({}), endTime={} ({})", 
                    startTime, lastMonday, endTime, lastSunday);

            // 为每个应用生成报表
            int total = appInfoList.size();
            for (int i = 0; i < total; i++) {
                FridayAppInfo appInfo = appInfoList.get(i);
                try {
                    log.info("开始处理应用[{}/{}]: appId={}, appName={}", 
                            (i+1), total, appInfo.getAppId(), appInfo.getAppName());
                    boolean success = generateReportForApp(appInfo.getAppId(), startTime, endTime);
                    log.info("完成处理应用[{}/{}]: appId={}, 结果={}", 
                            (i+1), total, appInfo.getAppId(), success ? "成功" : "失败");
                } catch (Exception e) {
                    log.error("生成应用上一自然周报表失败[{}/{}]: appId={}, error={}", 
                            (i+1), total, appInfo.getAppId(), e.getMessage(), e);
                }
                
                // 不是最后一个应用，则休眠30秒
                if (i < total - 1) {
                    try {
                        log.info("休眠30秒后继续处理下一个应用...");
                        Thread.sleep(30000);
                    } catch (InterruptedException e) {
                        log.warn("休眠被中断: {}", e.getMessage());
                        Thread.currentThread().interrupt();
                    }
                }
            }
            
            log.info("上一自然周统计报表生成定时任务执行完成，共处理{}个应用", total);
        } catch (Exception e) {
            log.error("上一自然周统计报表生成定时任务执行异常: {}", e.getMessage(), e);
        }
    }


    @Override
    public CorpusOperationStatusDTO getCorpusOperationStatus(Long beginDate, Long endTime, Boolean forceGenerate)
            throws LlmCorpusException {
        // 使用固定的appId=100来标识语料运营状态报表
        String appId = "100";
        
        if (beginDate == null || endTime == null) {
            log.error("#StatisticsServiceImpl.getCorpusOperationStatus#error,参数错误：beginDate:{},endTime:{}",
                    beginDate, endTime);
            throw LlmCorpusException.build(BizCode.ILLEGAL_ARGUMENT, "参数错误, 时间范围不能为空。");
        }
        
        // 如果不是强制生成，先尝试从数据库读取
        if (!forceGenerate) {
            try {
                StatisticsReportEntity reportEntity = statisticsReportMapper.selectByAppIdAndTimeRange(appId, beginDate, endTime);
                if (reportEntity != null && StringUtils.isNotBlank(reportEntity.getReportData())) {
                    log.info("从数据库读取到预计算的语料运营状态报表数据: appId={}, reportId={}", appId, reportEntity.getId());
                    return JSON.parseObject(reportEntity.getReportData(), CorpusOperationStatusDTO.class);
                }
            } catch (Exception e) {
                log.warn("读取数据库语料运营状态报表失败，将进行实时计算: appId={}, error={}", appId, e.getMessage());
            }
        }
        
        // 时间范围检查：不超过30天
        if (endTime - beginDate > 30 * 24 * 60 * 60 * 1000L) {
            log.error("#StatisticsServiceImpl.getCorpusOperationStatus#error,时间间隔超过30天：beginDate:{},endTime:{}",
                    beginDate, endTime);
            throw LlmCorpusException.buildWithMsg(BizCode.ILLEGAL_ARGUMENT, "时间间隔超过30天, 查询失败。");
        }
        
        // 实时计算统计数据
        CorpusOperationStatusDTO statusDTO = generateCorpusOperationStatus(beginDate, endTime);
        
        // 不管是否强制生成，都将结果存入数据库
        try {
            // 将计算结果直接存入数据库
            String reportData = JSON.toJSONString(statusDTO);
            
            // 构建报表实体
            StatisticsReportEntity reportEntity = StatisticsReportEntity.builder()
                    .appId(appId)
                    .startTime(beginDate)
                    .endTime(endTime)
                    .reportData(reportData)
                    .ctime(new Timestamp(System.currentTimeMillis()))
                    .build();
            
            // 查询是否已存在报表
            StatisticsReportEntity existingReport = statisticsReportMapper.selectByAppIdAndTimeRange(appId, beginDate, endTime);
            
            if (existingReport != null) {
                // 更新已有报表
                reportEntity.setId(existingReport.getId());
                statisticsReportMapper.updateByAppIdAndTimeRange(reportEntity);
                log.info("更新语料运营状态报表成功: appId={}, reportId={}", appId, existingReport.getId());
            } else {
                // 插入新报表
                statisticsReportMapper.insert(reportEntity);
                log.info("插入语料运营状态报表成功: appId={}, reportId={}", appId, reportEntity.getId());
            }
        } catch (Exception e) {
            log.warn("存储语料运营状态计算结果到数据库失败: appId={}, error={}", appId, e.getMessage(), e);
        }
        
        return statusDTO;
    }

    /**
     * 生成语料运营状态数据
     * 
     * @param beginDate 开始时间
     * @param endTime 结束时间
     * @return 语料运营状态DTO
     */
    private CorpusOperationStatusDTO generateCorpusOperationStatus(Long beginDate, Long endTime) {
        // 周期内语料助手机器人在区间内被@以及私聊消息列表
        List<CorpusBotMessageItem> corpusBotMessages = getCorpusBotMessages(beginDate, endTime);
        // 周期内语料生成任务列表
        List<CorpusGenerateTaskItemForStats> dailyCorpusGenerateTaskCountList = getDailyCorpusGenerateTaskCountList(beginDate, endTime);
        // 接入的所有值班组信息
        List<CorpusStatsRgInfoItem> corpusStatsRgInfoList = getCorpusStatsRgInfoList(beginDate, endTime);
        // 接入的所有Friday空间信息
        List<CorpusStatsFridaySpaceItem> corpusStatsFridaySpaceList = getCorpusStatsFridaySpaceList();

        CorpusOperationStatusDTO statusDTO = new CorpusOperationStatusDTO();
        statusDTO.setCorpusBotMessages(corpusBotMessages);
        statusDTO.setDailyCorpusGenerateTaskCountList(dailyCorpusGenerateTaskCountList);
        statusDTO.setCorpusStatsRgInfoList(corpusStatsRgInfoList);
        statusDTO.setCorpusStatsFridaySpaceList(corpusStatsFridaySpaceList);
        
        return statusDTO;
    }

    private List<CorpusBotMessageItem> getCorpusBotMessages(Long beginDate, Long endTime) {
        List<CorpusBotChatMessageEntity> corpusBotMessages = corpusBotChatMessageMapper.selectByTimeRange(beginDate, endTime);
        if (corpusBotMessages == null || corpusBotMessages.isEmpty()) {
            return Collections.emptyList();
        }
        
        // 转换为CorpusBotMessageItem列表
        return corpusBotMessages.stream()
                .map(entity -> {
                    CorpusBotMessageItem item = new CorpusBotMessageItem();
                    item.setMsgId(entity.getMsgId());
                    item.setFromUid(entity.getFromUid());
                    item.setFromPubId(entity.getFromPubId());
                    item.setGid(entity.getGid());
                    item.setCts(entity.getCts());
                    item.setType(entity.getType());
                    item.setMessage(entity.getMessage());
                    item.setMsgExt(entity.getMsgExt());
                    item.setFromMis(entity.getFromMis());
                    item.setFromName(entity.getFromName());
                    item.setUserOrgId(entity.getUserOrgId());
                    item.setUserOrgName(entity.getUserOrgName());
                    item.setUserOrgPath(entity.getUserOrgPath());
                    return item;
                })
                .collect(Collectors.toList());
    }

    private List<CorpusGenerateTaskItemForStats> getDailyCorpusGenerateTaskCountList(Long beginDate, Long endTime) {
        // 使用过滤DX_MONITORING的方法
        List<ModelOutputTaskEntity> modelOutputTasks = modelOutputMapper.selectByTimeRangeExcludeMonitoring(beginDate, endTime);
        if (modelOutputTasks == null || modelOutputTasks.isEmpty()) {
            return Collections.emptyList();
        }
        
        // 1. 先收集所有非空的taskId
        List<String> taskIds = modelOutputTasks.stream()
                .map(ModelOutputTaskEntity::getTaskId)
                .filter(StringUtils::isNotBlank)
                .distinct()
                .collect(Collectors.toList());
        
        // 2. 批量查询已落库的taskId
        Set<String> existingTaskIds = Collections.emptySet();
        if (!taskIds.isEmpty()) {
            try {
                List<String> existingTaskIdList = reviewMapper.findExistingTaskIds(taskIds);
                existingTaskIds = new HashSet<>(existingTaskIdList);
                log.debug("批量查询taskId落库状态完成，总任务数: {}, 已落库任务数: {}", taskIds.size(), existingTaskIds.size());
            } catch (Exception e) {
                log.warn("批量查询taskId是否已落库失败: error={}", e.getMessage(), e);
            }
        }
        
        // 3. 收集所有创建者的misId
        Set<String> allCreatorMisIds = modelOutputTasks.stream()
                .map(ModelOutputTaskEntity::getCreatorMisId)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toSet());
        
        // 4. 构建misId到对应snapshot的映射
        Map<String, Date> misIdToSnapshotMap = new HashMap<>();
        for (ModelOutputTaskEntity task : modelOutputTasks) {
            if (StringUtils.isNotBlank(task.getCreatorMisId()) && task.getCreateTime() != null) {
                String misId = task.getCreatorMisId();
                Date currentSnapshot = misIdToSnapshotMap.get(misId);
                Date taskCreateTime = new Date(task.getCreateTime().getTime());
                
                // 如果该misId还没有对应的snapshot，或者当前任务的创建时间更早，则更新
                if (currentSnapshot == null || taskCreateTime.before(currentSnapshot)) {
                    misIdToSnapshotMap.put(misId, taskCreateTime);
                }
            }
        }
        
        // 5. 批量获取所有创建者的员工信息，使用对应的snapshot
        Map<String, com.sankuai.meituan.org.opensdk.model.domain.Emp> creatorEmpInfoMap = new HashMap<>();
        Map<String, com.sankuai.meituan.org.opensdk.model.domain.Org> creatorOrgInfoMap = new HashMap<>();
        
        if (!allCreatorMisIds.isEmpty()) {
            try {
                log.info("批量获取{}个任务创建者的员工信息", allCreatorMisIds.size());
                
                // 对于每个misId，使用其对应的snapshot时间查询emp信息
                for (String misId : allCreatorMisIds) {
                    Date snapshot = misIdToSnapshotMap.get(misId);
                    try {
                        Map<String, com.sankuai.meituan.org.opensdk.model.domain.Emp> empInfo = 
                            empQueryService.getEmpInfoByMisId(Collections.singletonList(misId), snapshot);
                        if (empInfo != null && !empInfo.isEmpty()) {
                            creatorEmpInfoMap.putAll(empInfo);
                        }
                    } catch (Exception e) {
                        log.warn("获取用户{}的组织信息失败: error={}", misId, e.getMessage());
                    }
                }
                
                log.info("成功批量获取{}个任务创建者的员工信息", creatorEmpInfoMap.size());
                
                // 6. 从员工信息中提取所有的orgPath，解析出所有orgId，然后批量获取组织信息
                Map<String, Date> orgIdToSnapshotMap = new HashMap<>();
                Set<String> allOrgIds = new HashSet<>();
                
                for (Map.Entry<String, com.sankuai.meituan.org.opensdk.model.domain.Emp> entry : creatorEmpInfoMap.entrySet()) {
                    String misId = entry.getKey();
                    com.sankuai.meituan.org.opensdk.model.domain.Emp emp = entry.getValue();
                    Date snapshot = misIdToSnapshotMap.get(misId);
                    
                    if (emp != null && StringUtils.isNotBlank(emp.getOrgPath())) {
                        // 解析orgPath，例如"0-123-1233"，分割并添加到set中
                        String[] orgIdArray = emp.getOrgPath().split("-");
                        for (String orgId : orgIdArray) {
                            if (StringUtils.isNotBlank(orgId) && !"0".equals(orgId)) {
                                allOrgIds.add(orgId);
                                
                                // 记录orgId对应的snapshot
                                Date currentSnapshot = orgIdToSnapshotMap.get(orgId);
                                if (currentSnapshot == null || (snapshot != null && snapshot.before(currentSnapshot))) {
                                    orgIdToSnapshotMap.put(orgId, snapshot);
                                }
                            }
                        }
                    }
                }
                
                // 批量获取组织信息，每个orgId使用对应的snapshot
                if (!allOrgIds.isEmpty()) {
                    try {
                        log.info("批量获取{}个组织的详细信息", allOrgIds.size());
                        
                        for (String orgId : allOrgIds) {
                            Date snapshot = orgIdToSnapshotMap.get(orgId);
                            try {
                                List<com.sankuai.meituan.org.opensdk.model.domain.Org> orgList = 
                                    orgService.batchQuery(Collections.singletonList(orgId), snapshot);
                                if (CollectionUtils.isNotEmpty(orgList)) {
                                    for (com.sankuai.meituan.org.opensdk.model.domain.Org org : orgList) {
                                        if (org != null && StringUtils.isNotBlank(org.getOrgId())) {
                                            creatorOrgInfoMap.put(org.getOrgId(), org);
                                        }
                                    }
                                }
                            } catch (Exception e) {
                                log.warn("获取组织{}的详细信息失败: error={}", orgId, e.getMessage());
                            }
                        }
                        
                        log.info("成功批量获取{}个组织的详细信息", creatorOrgInfoMap.size());
                    } catch (Exception e) {
                        log.error("批量获取组织详细信息失败: error={}", e.getMessage(), e);
                    }
                }
            } catch (Exception e) {
                log.error("批量获取任务创建者员工信息失败: creatorMisIds={}, error={}", allCreatorMisIds, e.getMessage(), e);
            }
        }
        
        // 7. 转换为CorpusGenerateTaskItemForStats列表，并填充组织信息
        final Set<String> finalExistingTaskIds = existingTaskIds;
        
        return modelOutputTasks.stream()
                .map(entity -> {
                    CorpusGenerateTaskItemForStats item = new CorpusGenerateTaskItemForStats();
                    item.setTaskId(entity.getTaskId());
                    item.setTicketId(entity.getTicketId());
                    item.setDxGroupId(entity.getDxGroupId());
                    item.setTaskStatus(entity.getTaskStatus());
                    item.setCreatorUserDxId(entity.getCreatorDxId());
                    item.setCreatorMisId(entity.getCreatorMisId());
                    item.setCreatorUserName(entity.getCreatorUserName());
                    item.setPlatformId(entity.getPlatformId());
                    item.setTaskMessage(entity.getTaskMessage());
                    item.setCreateTime(entity.getCreateTime());
                    item.setUpdateTime(entity.getUpdateTime());
                    
                    // 通过Set快速判断taskId是否已落库
                    boolean taskSaved = StringUtils.isNotBlank(entity.getTaskId()) && 
                                       finalExistingTaskIds.contains(entity.getTaskId());
                    item.setTaskSaved(taskSaved);
                    
                    // 填充创建者的组织信息
                    if (StringUtils.isNotBlank(entity.getCreatorMisId())) {
                        com.sankuai.meituan.org.opensdk.model.domain.Emp empInfo = creatorEmpInfoMap.get(entity.getCreatorMisId());
                        if (empInfo != null) {
                            item.setCreatorOrgId(empInfo.getOrgId());
                            item.setCreatorOrgName(empInfo.getOrgName());
                            
                            // 构建组织名称路径
                            String orgNamePath = buildOrgNamePath(empInfo.getOrgPath(), creatorOrgInfoMap);
                            item.setCreatorOrgPath(orgNamePath);
                        }
                    }
                    
                    return item;
                })
                .collect(Collectors.toList());
    }

    private List<CorpusStatsRgInfoItem> getCorpusStatsRgInfoList(Long beginDate, Long endTime) {
        try {
            // 1. 从modified_output表查询所有不重复的rgid
            List<Long> allRgIds = reviewMapper.findAllDistinctRgIds();
            if (CollectionUtils.isEmpty(allRgIds)) {
                log.info("未找到任何值班组数据，beginDate:{}, endTime:{}", beginDate, endTime);
                return Collections.emptyList();
            }
            
            log.info("查询到{}个值班组ID: {}", allRgIds.size(), allRgIds);
            
            // 2. 先获取所有值班组的基本信息，收集所有owner的misId
            Map<Long, RgListItemDTO> rgInfoMap = new HashMap<>();
            Set<String> allOwnerMisIds = new HashSet<>();
            
            // 2.1 收集所有进行中任务的创建者misId
            Set<String> allTaskCreatorMisIds = new HashSet<>();
            Map<Long, List<ModelOutputTaskEntity>> rgIdToRunningTasksMap = new HashMap<>();
            
            for (Long rgId : allRgIds) {
                try {
                    // 获取值班组基本信息
                    RgListItemDTO rgInfo = ticketQueryService.queryRgInfoByRgId("daili07", rgId);
                    if (rgInfo != null) {
                        rgInfoMap.put(rgId, rgInfo);
                        if (StringUtils.isNotBlank(rgInfo.getOwner())) {
                            allOwnerMisIds.add(rgInfo.getOwner());
                        }
                    }
                    
                    // 获取值班组所有进行中任务，排除DX_MONITORING平台类型
                    List<ModelOutputTaskEntity> runningTasks = modelOutputMapper.findAllRunningTasksExcludeMonitoringByRgId(rgId);
                    if (CollectionUtils.isNotEmpty(runningTasks)) {
                        rgIdToRunningTasksMap.put(rgId, runningTasks);
                        
                        // 收集所有任务创建者的misId
                        for (ModelOutputTaskEntity task : runningTasks) {
                            if (StringUtils.isNotBlank(task.getCreatorMisId())) {
                                allTaskCreatorMisIds.add(task.getCreatorMisId());
                            }
                        }
                    }
                } catch (Exception e) {
                    log.warn("获取值班组信息或进行中任务失败: rgId={}, error={}", rgId, e.getMessage());
                }
            }
            
            // 3. 合并所有需要查询的misId
            allOwnerMisIds.addAll(allTaskCreatorMisIds);
            
            // 4. 批量获取所有owner和任务创建者的组织信息
            Map<String, com.sankuai.meituan.org.opensdk.model.domain.Emp> allEmpInfoMap = new HashMap<>();
            Map<String, com.sankuai.meituan.org.opensdk.model.domain.Org> allOrgInfoMap = new HashMap<>();
            Map<String, Date> misIdToSnapshotMap = new HashMap<>(); // 记录每个misId对应的snapshot时间
            
            if (!allOwnerMisIds.isEmpty()) {
                try {
                    log.info("批量获取{}个用户(值班组owner和任务创建者)的组织信息: {}", allOwnerMisIds.size(), allOwnerMisIds);
                    
                    // 对于每个misId，可能有多个任务，我们需要找到最早的任务时间作为snapshot
                    for (Map.Entry<Long, List<ModelOutputTaskEntity>> entry : rgIdToRunningTasksMap.entrySet()) {
                        for (ModelOutputTaskEntity task : entry.getValue()) {
                            if (StringUtils.isNotBlank(task.getCreatorMisId()) && task.getCreateTime() != null) {
                                String misId = task.getCreatorMisId();
                                Date currentSnapshot = misIdToSnapshotMap.get(misId);
                                Date taskCreateTime = new Date(task.getCreateTime().getTime());
                                
                                // 如果该misId还没有对应的snapshot，或者当前任务的创建时间更早，则更新
                                if (currentSnapshot == null || taskCreateTime.before(currentSnapshot)) {
                                    misIdToSnapshotMap.put(misId, taskCreateTime);
                                }
                            }
                        }
                    }
                    
                    // 对于每个misId，使用其对应的snapshot时间查询emp信息
                    for (String misId : allOwnerMisIds) {
                        Date snapshot = misIdToSnapshotMap.get(misId);
                        try {
                            Map<String, com.sankuai.meituan.org.opensdk.model.domain.Emp> empInfo = 
                                empQueryService.getEmpInfoByMisId(Collections.singletonList(misId), snapshot);
                            if (empInfo != null && !empInfo.isEmpty()) {
                                allEmpInfoMap.putAll(empInfo);
                            }
                        } catch (Exception e) {
                            log.warn("获取用户{}的组织信息失败: error={}", misId, e.getMessage());
                        }
                    }
                    
                    log.info("成功批量获取{}个用户的组织信息", allEmpInfoMap.size());
                    
                    // 5. 从员工信息中提取所有的orgPath，解析出所有orgId，然后批量获取组织信息
                    Map<String, Date> orgIdToSnapshotMap = new HashMap<>(); // 记录每个orgId对应的snapshot时间
                    Set<String> allOrgIds = new HashSet<>();
                    
                    for (Map.Entry<String, com.sankuai.meituan.org.opensdk.model.domain.Emp> entry : allEmpInfoMap.entrySet()) {
                        String misId = entry.getKey();
                        com.sankuai.meituan.org.opensdk.model.domain.Emp emp = entry.getValue();
                        Date snapshot = misIdToSnapshotMap.get(misId); // 使用emp对应的misId的snapshot
                        
                        if (emp != null && StringUtils.isNotBlank(emp.getOrgPath())) {
                            // 解析orgPath，例如"0-123-1233"，分割并添加到set中
                            String[] orgIdArray = emp.getOrgPath().split("-");
                            for (String orgId : orgIdArray) {
                                if (StringUtils.isNotBlank(orgId) && !orgId.equals("0")) {
                                    allOrgIds.add(orgId);
                                    
                                    // 记录orgId对应的snapshot
                                    Date currentSnapshot = orgIdToSnapshotMap.get(orgId);
                                    if (currentSnapshot == null || (snapshot != null && snapshot.before(currentSnapshot))) {
                                        orgIdToSnapshotMap.put(orgId, snapshot);
                                    }
                                }
                            }
                        }
                    }
                    
                    // 批量获取组织信息，每个orgId使用对应的snapshot
                    if (!allOrgIds.isEmpty()) {
                        try {
                            log.info("批量获取{}个组织的详细信息", allOrgIds.size());
                            
                            for (String orgId : allOrgIds) {
                                Date snapshot = orgIdToSnapshotMap.get(orgId);
                                try {
                                    List<com.sankuai.meituan.org.opensdk.model.domain.Org> orgList = 
                                        orgService.batchQuery(Collections.singletonList(orgId), snapshot);
                                    if (CollectionUtils.isNotEmpty(orgList)) {
                                        for (com.sankuai.meituan.org.opensdk.model.domain.Org org : orgList) {
                                            if (org != null && StringUtils.isNotBlank(org.getOrgId())) {
                                                allOrgInfoMap.put(org.getOrgId(), org);
                                            }
                                        }
                                    }
                                } catch (Exception e) {
                                    log.warn("获取组织{}的详细信息失败: error={}", orgId, e.getMessage());
                                }
                            }
                            
                            log.info("成功批量获取{}个组织的详细信息", allOrgInfoMap.size());
                        } catch (Exception e) {
                            log.error("批量获取组织详细信息失败: error={}", e.getMessage(), e);
                        }
                    }
                } catch (Exception e) {
                    log.error("批量获取用户组织信息失败: misIds={}, error={}", allOwnerMisIds, e.getMessage(), e);
                }
            }
            
            // 6. 并行处理每个值班组的统计信息
            List<CompletableFuture<CorpusStatsRgInfoItem>> futures = new ArrayList<>();
            ExecutorService executor = AsyncTaskUtils.getStatisticsReportThreadPool();
            
            final Map<String, com.sankuai.meituan.org.opensdk.model.domain.Emp> finalEmpInfoMap = allEmpInfoMap;
            final Map<String, com.sankuai.meituan.org.opensdk.model.domain.Org> finalOrgInfoMap = allOrgInfoMap;
            final Map<Long, List<ModelOutputTaskEntity>> finalRgIdToRunningTasksMap = rgIdToRunningTasksMap;
            
            for (Long rgId : allRgIds) {
                CompletableFuture<CorpusStatsRgInfoItem> future = AsyncTaskUtils.supplyAsyncWithThreadLocal(
                    executor,
                    () -> {
                        try {
                            // 获取该值班组的进行中任务
                            List<ModelOutputTaskEntity> runningTasks = finalRgIdToRunningTasksMap.getOrDefault(rgId, Collections.emptyList());
                            return buildCorpusStatsRgInfoItemWithEmpInfo(rgId, beginDate, endTime, rgInfoMap.get(rgId), 
                                    finalEmpInfoMap, finalOrgInfoMap, runningTasks);
                        } catch (Exception e) {
                            log.error("处理值班组信息失败: rgId={}, error={}", rgId, e.getMessage(), e);
                            return null;
                        }
                    }
                );
                futures.add(future);
            }
            
            // 7. 收集所有结果
            List<CorpusStatsRgInfoItem> result = new ArrayList<>();
            int timeout = mtConfigService.getStatsDataProcessAsyncTaskTimeout();
            
            for (CompletableFuture<CorpusStatsRgInfoItem> future : futures) {
                try {
                    CorpusStatsRgInfoItem item = future.get(timeout, TimeUnit.MILLISECONDS);
                    if (item != null) {
                        result.add(item);
                    }
                } catch (Exception e) {
                    log.error("获取值班组信息异步任务失败: {}", e.getMessage(), e);
                }
            }
            
            log.info("成功获取{}个值班组信息", result.size());
            return result;
        } catch (Exception e) {
            log.error("获取值班组信息列表失败: {}", e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    /**
     * 构建单个值班组的统计信息（使用已获取的值班组信息和员工信息）
     * 
     * @param rgId 值班组ID
     * @param beginDate 开始时间
     * @param endTime 结束时间
     * @param rgInfo 已获取的值班组信息
     * @param empInfoMap 已获取的员工信息映射
     * @param orgInfoMap 已获取的组织信息映射
     * @param runningTasks 该值班组的所有进行中任务
     * @return 值班组统计信息
     */
    private CorpusStatsRgInfoItem buildCorpusStatsRgInfoItemWithEmpInfo(Long rgId, Long beginDate, Long endTime, 
                                                                        RgListItemDTO rgInfo, 
                                                                        Map<String, com.sankuai.meituan.org.opensdk.model.domain.Emp> empInfoMap,
                                                                        Map<String, com.sankuai.meituan.org.opensdk.model.domain.Org> orgInfoMap,
                                                                        List<ModelOutputTaskEntity> runningTasks) {
        try {
            // 1. 检查值班组基本信息
            if (rgInfo == null) {
                log.warn("未找到值班组信息: rgId={}", rgId);
                return null;
            }
            
            // 2. 统计指定时间范围内的语料数量
            // 将时间戳转换为日期字符串格式进行查询
            String startTimeStr = null;
            String endTimeStr = null;
            if (beginDate != null) {
                startTimeStr = DatetimeUtils.timestampToDateStr(beginDate);
            }
            if (endTime != null) {
                endTimeStr = DatetimeUtils.timestampToDateStr(endTime);
            }
            
            int timeRangeCorpusCount = reviewMapper.countCorpusByCondition(
                rgId, null, null, null, null, null, startTimeStr, endTimeStr, null, null);
            
            // 3. 统计该值班组的历史语料总数（不限时间范围）
            int totalCorpusCount = reviewMapper.countCorpusByCondition(
                rgId, null, null, null, null, null, null, null, null, null);
            
            // 4. 处理该值班组所有进行中的任务（状态为0）
            List<CorpusGenerateTaskItemForStats> runningTaskItems = new ArrayList<>();
            
            // 任务创建者misId到snapshot的映射
            Map<String, Date> taskMisIdToSnapshotMap = new HashMap<>();
            
            if (CollectionUtils.isNotEmpty(runningTasks)) {
                // 4.1 为每个任务的创建者准备snapshot
                for (ModelOutputTaskEntity task : runningTasks) {
                    if (StringUtils.isNotBlank(task.getCreatorMisId()) && task.getCreateTime() != null) {
                        taskMisIdToSnapshotMap.put(task.getCreatorMisId(), new Date(task.getCreateTime().getTime()));
                    }
                }
                
                // 4.2 处理每个任务
                for (ModelOutputTaskEntity task : runningTasks) {
                    CorpusGenerateTaskItemForStats taskItem = new CorpusGenerateTaskItemForStats();
                    taskItem.setTaskId(task.getTaskId());
                    taskItem.setTicketId(task.getTicketId());
                    taskItem.setDxGroupId(task.getDxGroupId());
                    taskItem.setTaskStatus(task.getTaskStatus());
                    taskItem.setCreatorUserDxId(task.getCreatorDxId());
                    taskItem.setCreatorMisId(task.getCreatorMisId());
                    taskItem.setCreatorUserName(task.getCreatorUserName());
                    taskItem.setPlatformId(task.getPlatformId());
                    taskItem.setTaskMessage(task.getTaskMessage());
                    taskItem.setCreateTime(task.getCreateTime());
                    taskItem.setUpdateTime(task.getUpdateTime());
                    taskItem.setTaskSaved(false); // 进行中的任务尚未保存
                    
                    // 如果有员工信息，设置组织信息
                    if (StringUtils.isNotBlank(task.getCreatorMisId()) && empInfoMap != null) {
                        com.sankuai.meituan.org.opensdk.model.domain.Emp empInfo = empInfoMap.get(task.getCreatorMisId());
                        if (empInfo != null) {
                            taskItem.setCreatorOrgId(empInfo.getOrgId());
                            taskItem.setCreatorOrgName(empInfo.getOrgName());
                            
                            // 构建组织名称路径
                            String orgNamePath = buildOrgNamePath(empInfo.getOrgPath(), orgInfoMap);
                            taskItem.setCreatorOrgPath(orgNamePath);
                        }
                    }
                    
                    runningTaskItems.add(taskItem);
                }
            }
            
            // 5. 构建基本信息
            CorpusStatsRgInfoItem.CorpusStatsRgInfoItemBuilder builder = CorpusStatsRgInfoItem.builder()
                    .rgId(String.valueOf(rgId))
                    .rgName(rgInfo.getName())
                    .rgDesc(rgInfo.getDescription())
                    .rgOwner(rgInfo.getOwner())
                    .timeRangeCorpusCount((long) timeRangeCorpusCount)
                    .totalCorpusCount((long) totalCorpusCount)
                    .allRunningStatusTasks(runningTaskItems);
            
            // 6. 从已获取的员工信息中设置owner的组织信息
            if (StringUtils.isNotBlank(rgInfo.getOwner()) && empInfoMap != null) {
                com.sankuai.meituan.org.opensdk.model.domain.Emp empInfo = empInfoMap.get(rgInfo.getOwner());
                if (empInfo != null) {
                    builder.rgOrgId(empInfo.getOrgId())
                           .rgOrgName(empInfo.getOrgName());
                    
                    // 构建组织名称路径
                    String orgNamePath = buildOrgNamePath(empInfo.getOrgPath(), orgInfoMap);
                    builder.rgOrgPath(orgNamePath);
                } else {
                    log.warn("未找到owner的组织信息: rgId={}, owner={}", rgId, rgInfo.getOwner());
                }
            }
            
            return builder.build();
            
        } catch (Exception e) {
            log.error("构建值班组统计信息失败: rgId={}, error={}", rgId, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 根据orgPath和组织信息映射构建组织名称路径
     * 
     * @param orgPath 组织路径，例如"0-123-1233"
     * @param orgInfoMap 组织ID到组织信息的映射
     * @return 组织名称路径，例如"美团/技术部/后端组"
     */
    private String buildOrgNamePath(String orgPath, Map<String, com.sankuai.meituan.org.opensdk.model.domain.Org> orgInfoMap) {
        if (StringUtils.isBlank(orgPath) || orgInfoMap == null || orgInfoMap.isEmpty()) {
            return orgPath; // 如果没有信息，返回原始路径
        }
        
        try {
            String[] orgIds = orgPath.split("-");
            List<String> orgNames = new ArrayList<>();
            
            for (String orgId : orgIds) {
                if (StringUtils.isNotBlank(orgId) && !"0".equals(orgId)) {
                    com.sankuai.meituan.org.opensdk.model.domain.Org org = orgInfoMap.get(orgId);
                    if (org != null && StringUtils.isNotBlank(org.getName())) {
                        orgNames.add(org.getName());
                    } else {
                        // 如果找不到组织名称，保留orgId
                        orgNames.add(orgId);
                    }
                }
            }
            
            if (orgNames.isEmpty()) {
                return orgPath;
            }
            
            return String.join("/", orgNames);
        } catch (Exception e) {
            log.warn("构建组织名称路径失败: orgPath={}, error={}", orgPath, e.getMessage());
            return orgPath;
        }
    }

    private List<CorpusStatsFridaySpaceItem> getCorpusStatsFridaySpaceList() {
        try {
            // 1. 查询所有不重复的Friday空间信息
            List<RgDatasetDocumentEntity> allSpaces = rgDatasetDocumentMapper.findAllDistinctSpaces();
            if (CollectionUtils.isEmpty(allSpaces)) {
                log.info("未找到任何Friday空间数据");
                return Collections.emptyList();
            }
            
            log.info("查询到{}个Friday空间", allSpaces.size());
            
            // 2. 为每个空间查询绑定的rgId列表
            List<CorpusStatsFridaySpaceItem> result = new ArrayList<>();
            
            for (RgDatasetDocumentEntity spaceEntity : allSpaces) {
                try {
                    String spaceId = spaceEntity.getSpaceId();
                    if (StringUtils.isBlank(spaceId)) {
                        continue;
                    }
                    
                    // 查询绑定该空间的所有rgId
                    List<Long> rgIds = rgDatasetDocumentMapper.findRgIdsBySpaceId(spaceId);
                    List<String> spaceRgIds = rgIds.stream()
                            .map(String::valueOf)
                            .collect(Collectors.toList());
                    
                    // 构建CorpusStatsFridaySpaceItem
                    CorpusStatsFridaySpaceItem spaceItem = CorpusStatsFridaySpaceItem.builder()
                            .spaceId(spaceId)
                            .spaceName(spaceEntity.getSpaceName())
                            .spaceDesc(null) // 当前数据库表中没有描述字段
                            .ctime(spaceEntity.getTimestamp() != null ? 
                                   spaceEntity.getTimestamp().getTime() : null)
                            .spaceRgIds(spaceRgIds)
                            .build();
                    
                    result.add(spaceItem);
                    
                    log.debug("Friday空间: spaceId={}, spaceName={}, 绑定值班组数量={}", 
                             spaceId, spaceEntity.getSpaceName(), spaceRgIds.size());
                    
                } catch (Exception e) {
                    log.error("处理Friday空间信息失败: spaceId={}, error={}", 
                             spaceEntity.getSpaceId(), e.getMessage(), e);
                }
            }
            
            log.info("成功获取{}个Friday空间信息", result.size());
            return result;
            
        } catch (Exception e) {
            log.error("获取Friday空间信息列表失败: {}", e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    /**
     * 生成语料运营状态报表定时任务
     * 每天自动生成最近7天的语料运营状态报表
     */
    @Crane("generate-corpus-operation-statistics-report-task")
    @Override
    public void generateCorpusOperationStatisticsReportTask() {
        log.info("开始执行语料运营状态报表生成定时任务");
        try {
            // 计算时间范围：使用LocalDate精确计算
            LocalDate today = LocalDate.now();
            LocalDate yesterday = today.minusDays(1);
            LocalDate startDay = today.minusDays(DEFAULT_DAYS_BEFORE);
            
            // 转换为毫秒时间戳 - 精确计算
            // 开始时间：7天前的00:00:00.000
            long startTime = startDay.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
            // 结束时间：昨天的23:59:59.999
            long endTime = yesterday.atTime(LocalTime.MAX).atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();

            log.info("生成语料运营状态报表时间范围: startTime={} ({}), endTime={} ({})", 
                    startTime, startDay, endTime, yesterday);

            // 生成语料运营状态报表
            boolean success = generateCorpusOperationReportForTimeRange(startTime, endTime);
            log.info("语料运营状态报表生成完成，结果: {}", success ? "成功" : "失败");
            
        } catch (Exception e) {
            log.error("语料运营状态报表生成定时任务执行异常: {}", e.getMessage(), e);
        }
    }

    /**
     * 生成上一自然周的语料运营状态报表定时任务
     */
    @Crane("generate-weekly-corpus-operation-statistics-report-task")
    @Override
    public void generateWeeklyCorpusOperationStatisticsReportTask() {
        log.info("开始执行上一自然周语料运营状态报表生成定时任务");
        try {
            // 计算上一自然周的时间范围（上周一00:00:00到上周日23:59:59）
            LocalDate today = LocalDate.now();
            // 计算上周一的日期
            LocalDate lastMonday = today.minusWeeks(1).with(java.time.DayOfWeek.MONDAY);
            // 计算上周日的日期
            LocalDate lastSunday = today.minusWeeks(1).with(java.time.DayOfWeek.SUNDAY);
            
            // 转换为毫秒时间戳
            long startTime = lastMonday.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
            long endTime = lastSunday.atTime(LocalTime.MAX).atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();

            log.info("生成上一自然周语料运营状态报表时间范围: startTime={} ({}), endTime={} ({})", 
                    startTime, lastMonday, endTime, lastSunday);

            // 生成语料运营状态报表
            boolean success = generateCorpusOperationReportForTimeRange(startTime, endTime);
            log.info("上一自然周语料运营状态报表生成完成，结果: {}", success ? "成功" : "失败");
            
        } catch (Exception e) {
            log.error("上一自然周语料运营状态报表生成定时任务执行异常: {}", e.getMessage(), e);
        }
    }

    /**
     * 为指定时间范围生成语料运营状态报表并存储
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 是否生成成功
     */
    private boolean generateCorpusOperationReportForTimeRange(long startTime, long endTime) throws LlmCorpusException {
        String appId = "100";
        log.info("开始生成语料运营状态报表: appId={}, startTime={}, endTime={}", appId, startTime, endTime);
        
        // 直接调用generateCorpusOperationStatus方法生成数据
        CorpusOperationStatusDTO statusDTO = generateCorpusOperationStatus(startTime, endTime);
        if (statusDTO == null) {
            log.warn("生成语料运营状态报表数据为空: appId={}", appId);
            return false;
        }

        // 转换为JSON
        String reportData = JSON.toJSONString(statusDTO);
        
        // 构建报表实体
        StatisticsReportEntity reportEntity = StatisticsReportEntity.builder()
                .appId(appId)
                .startTime(startTime)
                .endTime(endTime)
                .reportData(reportData)
                .ctime(new Timestamp(System.currentTimeMillis()))
                .build();

        try {
            // 查询是否已存在报表
            StatisticsReportEntity existingReport = statisticsReportMapper.selectByAppIdAndTimeRange(appId, startTime, endTime);
            
            if (existingReport != null) {
                // 更新已有报表
                reportEntity.setId(existingReport.getId());
                int updated = statisticsReportMapper.updateByAppIdAndTimeRange(reportEntity);
                log.info("更新语料运营状态报表成功: appId={}, reportId={}", appId, existingReport.getId());
                return updated > 0;
            } else {
                // 插入新报表
                int inserted = statisticsReportMapper.insert(reportEntity);
                log.info("插入语料运营状态报表成功: appId={}, reportId={}", appId, reportEntity.getId());
                return inserted > 0;
            }
        } catch (Exception e) {
            log.error("存储语料运营状态报表数据失败: appId={}, error={}", appId, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 检查用户是否对指定的Friday应用有权限
     * @param misId 用户ID
     * @param appInfo Friday应用信息
     * @return 是否有权限
     */
    private boolean hasPermissionForApp(String misId, FridayAppInfo appInfo) {
        if (StringUtils.isBlank(misId) || appInfo == null) {
            return false;
        }

        // 0. 检查是否是公共应用
        if (appInfo.getPublicApp()) {
            log.debug("#hasPermissionForApp# 用户是公共应用: misId={}, appId={}", misId, appInfo.getAppId());
            return true;
        }

        // 1. 检查是否是应用的owner
        if (CollectionUtils.isNotEmpty(appInfo.getOwner()) && appInfo.getOwner().contains(misId)) {
            log.debug("#hasPermissionForApp# 用户是应用owner: misId={}, appId={}", misId, appInfo.getAppId());
            return true;
        }

        // 2. 检查是否是任意orgId的值班组成员
        if (CollectionUtils.isNotEmpty(appInfo.getOrgIds())) {
            for (Long orgId : appInfo.getOrgIds()) {
                if (validateUserPermission(misId, orgId)) {
                    log.debug("#hasPermissionForApp# 用户是值班组成员: misId={}, appId={}, orgId={}", misId, appInfo.getAppId(), orgId);
                    return true;
                }
            }
        }

        log.debug("#hasPermissionForApp# 用户无权限: misId={}, appId={}", misId, appInfo.getAppId());
        return false;
    }

    /**
     * 验证用户是否有对应的值班组权限
     * 复用 KnowledgeBaseServiceImpl 的逻辑
     * @param misId 用户ID
     * @param rgId 值班组ID
     * @return 是否有权限
     */
    private boolean validateUserPermission(String misId, Long rgId) {
        if (StringUtils.isBlank(misId) || rgId == null) {
            return false;
        }
        try {
            List<RgInfoDTO> rgInfoDTOS = getRgInfoDTOList(misId);
            return rgInfoDTOS.stream().anyMatch(rgInfoDTO -> rgInfoDTO.getId() == rgId);
        } catch (Exception e) {
            log.error("#validateUserPermission# 验证用户权限异常: misId={}, rgId={}", misId, rgId, e);
            return false;
        }
    }

    /**
     * 获取用户的值班组列表
     * @param misId 用户ID
     * @return 值班组列表
     */
    private List<RgInfoDTO> getRgInfoDTOList(String misId) {
        String rgInfoDTOListJSONString = rgQueryUtil.queryMyRgList(misId, 1, 200);
        if (StringUtils.isBlank(rgInfoDTOListJSONString)) {
            return Collections.emptyList();
        }
        return parseJsonToRgInfoDTOList(rgInfoDTOListJSONString);
    }

    /**
     * 验证用户是否有查看指定应用统计数据的权限
     * @param misId 用户ID
     * @param appId 应用ID
     * @return 是否有权限
     */
    public boolean validateUserPermissionForApp(String misId, String appId) {
        if (StringUtils.isBlank(misId) || StringUtils.isBlank(appId)) {
            return false;
        }

        // 0. 白名单能力检查
        List<String> whitelist = mtConfigService.getFridayAppInfoWhitelist();
        if (CollectionUtils.isNotEmpty(whitelist) && whitelist.contains(misId)) {
            log.debug("#validateUserPermissionForApp# 用户在白名单中: misId={}, appId={}", misId, appId);
            return true;
        }

        // 获取应用信息
        Map<String, FridayAppInfo> fridayAppInfoMap = mtConfigService.getFridayAppInfoMap();
        FridayAppInfo appInfo = fridayAppInfoMap.get(appId);
        if (appInfo == null) {
            log.warn("#validateUserPermissionForApp# 应用信息不存在: appId={}", appId);
            return false;
        }

        // 检查权限
        return hasPermissionForApp(misId, appInfo);
    }
}