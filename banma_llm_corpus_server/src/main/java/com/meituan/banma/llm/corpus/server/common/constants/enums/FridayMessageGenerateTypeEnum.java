package com.meituan.banma.llm.corpus.server.common.constants.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum FridayMessageGenerateTypeEnum {
    WELCOME("WELCOME", "欢迎语"),
    HUMAN("<PERSON>UM<PERSON>", "人工"),
    LLM("LLM", "大模型")
    ;
    private final String type;
    private final String desc;

    public static FridayMessageGenerateTypeEnum fromType(String type){

        for (FridayMessageGenerateTypeEnum value : FridayMessageGenerateTypeEnum.values()) {
            if (value.getType().equals(type)) {
                return value;
            }
        }
        return null;
    }
}
