package com.meituan.banma.llm.corpus.server.rpc.talos2.impl;

import com.meituan.banma.llm.corpus.server.common.MtConfigService;
import com.meituan.banma.llm.corpus.server.rpc.talos2.TalosTwoRpcService;
import com.meituan.talos.commons.exception.TalosException;
import com.sankuai.data.talos.AsyncTalosClient;
import com.sankuai.data.talos.QueryRequest;
import com.sankuai.data.talos.model.Engine;
import com.sankuai.data.talos.model.QueryResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class TalosTwoRpcServiceImpl implements TalosTwoRpcService {
    @Autowired
    private AsyncTalosClient asyncTalosClient;

    @Autowired
    private MtConfigService mtConfigService;

    private static final String  engine = Engine.Hive.getName();

    @Override
    public List<List<Object>> queryFromHive(String querySql) throws TalosException {
        long start = System.currentTimeMillis();
        QueryRequest queryRequest = new QueryRequest.Builder()
                .engine(engine)
                .statement(querySql)
                .build();

        String qid = asyncTalosClient.submit(queryRequest);
        int resCount = asyncTalosClient.waitForFinished(qid, mtConfigService.getQueryHiveTimeout(), true);
        List<List<Object>> lists = new ArrayList<>();
        if (resCount == 1) {
            // 查询成功，获取查询结果
            QueryResult queryResult = asyncTalosClient.getQueryResult(qid);
            while (queryResult.hasNext()) {
                lists = queryResult.fetchAll();
                break;
            }
            log.info("#TalosTwoRpcServiceImpl.queryFromHive# query success, querySql = {} hiveResult = {}", querySql, mtConfigService.getLoggingHiveResultSwitch() ? lists : "logging_hive_result_switch未开启，不展示结果");
        } else {
            log.error("#TalosTwoRpcServiceImpl.queryFromHive# query error, querySql = {} , resCount = {}, hiveResult = {}", querySql, resCount, lists);
        }
        return lists;
    }
}
