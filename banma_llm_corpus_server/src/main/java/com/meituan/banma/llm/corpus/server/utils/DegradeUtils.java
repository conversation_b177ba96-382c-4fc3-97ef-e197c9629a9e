package com.meituan.banma.llm.corpus.server.utils;

import com.sankuai.meituan.banma.qingniu.degrade.utils.QingniuDegradeUtils;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class DegradeUtils {
    public static boolean degradeLlmCorpusConvertTaskCallbackMessage() {
        return QingniuDegradeUtils.isDegrade("degrade.llm.corpus.convert.callback.message", "模型处理结果回调消息降级");
    }
    public static boolean degradeImageToText() {
        return QingniuDegradeUtils.isDegrade("degrade.llm.corpus.image.to.text", "图片转文字接口降级");
    }
}
