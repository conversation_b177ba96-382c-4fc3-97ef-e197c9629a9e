package com.meituan.banma.llm.corpus.server.controller;

import com.google.common.base.Preconditions;
import com.meituan.banma.llm.corpus.api.exception.LlmCorpusException;
import com.meituan.banma.llm.corpus.server.common.CommonResult;
import com.meituan.banma.llm.corpus.server.common.domain.dto.PageDTO;
import com.meituan.banma.llm.corpus.server.controller.request.monitoring.MonitoringGroupRequest;
import com.meituan.banma.llm.corpus.server.controller.request.monitoring.MonitoringTimeRangeRequest;
import com.meituan.banma.llm.corpus.server.dal.entity.InquiryMonitoringEntity;
import com.meituan.banma.llm.corpus.server.dal.entity.MonitoringGroupEntity;
import com.meituan.banma.llm.corpus.server.dal.entity.QuestionTypeEntity;
import com.meituan.banma.llm.corpus.server.service.IInquiryMonitoringService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 问询监控Controller
 */
@Slf4j
@RestController
@RequestMapping("/inquiry/monitoring")
public class InquiryMonitoringController {

    @Resource
    private IInquiryMonitoringService inquiryMonitoringService;

    /**
     * 根据时间范围查询监控数据
     *
     * @param request 包含时间范围的请求参数
     * @return 监控数据列表
     */
    @PostMapping("/queryByTimeRange")
    public CommonResult queryByTimeRange(@RequestBody MonitoringTimeRangeRequest request) {
        try {
            Preconditions.checkArgument(request != null, "请求参数不能为空");
            Preconditions.checkArgument(request.getStartTime() != null, "开始时间不能为空");
            Preconditions.checkArgument(request.getEndTime() != null, "结束时间不能为空");
            Preconditions.checkArgument(request.getMonitorGroupIds() != null, "监控组id不能为空");
            Preconditions.checkArgument(request.getStartTime() <= request.getEndTime(), "开始时间不能大于结束时间");


            int pageNum = request.getPageNum() != null ? request.getPageNum() : 1;
            int pageSize = request.getPageSize() != null ? request.getPageSize() : 10;
            
            // 调用服务查询数据
            PageDTO<InquiryMonitoringEntity> pageData = inquiryMonitoringService.queryMonitoringData(
                    request.getStartTime(), request.getEndTime(), pageNum, pageSize, request.getMonitorGroupIds());
            
            // 生成Excel并上传到S3
            String excelDownloadLink = inquiryMonitoringService.generateExcelAndUploadToS3(
                    request.getStartTime(), request.getEndTime(), request.getMonitorGroupIds());

            Map<String, Object> result = new HashMap<>();
            result.put("currentPage", pageData.getPageNum());
            result.put("pageSize", pageData.getPageSize());
            result.put("total", pageData.getTotalCount());
            result.put("totalPage", pageData.getTotalPage());
            result.put("list", pageData.getData());
            result.put("excelDownloadLink", excelDownloadLink);
            
            log.info("查询监控数据成功，共返回{}条记录，当前第{}页，每页{}条，Excel下载链接：{}", 
                    pageData.getTotalCount(), pageData.getPageNum(), pageData.getPageSize(), excelDownloadLink);
            return CommonResult.success("查询成功", result);
        } catch (IllegalArgumentException e) {
            log.warn("查询监控数据参数校验失败: request={}, error={}", request, e.getMessage());
            return CommonResult.error(e.getMessage());
        } catch (LlmCorpusException e) {
            log.warn("#queryByTimeRange 查询监控数据业务异常: request={}, code={}, message={}", 
                    request, e.getCode(), e.getMessage());
            return CommonResult.error(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("查询监控数据异常: request={}, error={}", request, e.getMessage(), e);
            return CommonResult.error(String.format("查询监控数据异常，原因=%s", e.getMessage()));
        }
    }

    /**
     * 根据时间范围查询监控数据（不生成Excel文件）
     *
     * @param request 包含时间范围的请求参数
     * @return 监控数据列表
     */
    @PostMapping("/queryByTimeRangeWithoutExcel")
    public CommonResult queryByTimeRangeWithoutExcel(@RequestBody MonitoringTimeRangeRequest request) {
        try {
            Preconditions.checkArgument(request != null, "请求参数不能为空");
            Preconditions.checkArgument(request.getStartTime() != null, "开始时间不能为空");
            Preconditions.checkArgument(request.getEndTime() != null, "结束时间不能为空");
            Preconditions.checkArgument(request.getMonitorGroupIds() != null, "监控组id不能为空");
            Preconditions.checkArgument(request.getStartTime() <= request.getEndTime(), "开始时间不能大于结束时间");

            int pageNum = request.getPageNum() != null ? request.getPageNum() : 1;
            int pageSize = request.getPageSize() != null ? request.getPageSize() : 10;
            
            // 调用服务查询数据
            PageDTO<InquiryMonitoringEntity> pageData = inquiryMonitoringService.queryMonitoringData(
                    request.getStartTime(), request.getEndTime(), pageNum, pageSize, request.getMonitorGroupIds());

            Map<String, Object> result = new HashMap<>();
            result.put("currentPage", pageData.getPageNum());
            result.put("pageSize", pageData.getPageSize());
            result.put("total", pageData.getTotalCount());
            result.put("totalPage", pageData.getTotalPage());
            result.put("list", pageData.getData());
            
            log.info("查询监控数据成功（不生成Excel），共返回{}条记录，当前第{}页，每页{}条", 
                    pageData.getTotalCount(), pageData.getPageNum(), pageData.getPageSize());
            return CommonResult.success("查询成功", result);
        } catch (IllegalArgumentException e) {
            log.warn("查询监控数据参数校验失败: request={}, error={}", request, e.getMessage());
            return CommonResult.error(e.getMessage());
        } catch (LlmCorpusException e) {
            log.warn("#queryByTimeRangeWithoutExcel 查询监控数据业务异常: request={}, code={}, message={}", 
                    request, e.getCode(), e.getMessage());
            return CommonResult.error(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("查询监控数据异常: request={}, error={}", request, e.getMessage(), e);
            return CommonResult.error(String.format("查询监控数据异常，原因=%s", e.getMessage()));
        }
    }

    /**
     * 验证监控任务是否可行
     * 检查小助手和所有监控组成员是否在指定的群聊中
     *
     * @param request 监控组请求参数
     * @return 验证结果
     */
    @PostMapping("/validateMonitoringTask")
    public CommonResult validateMonitoringTask(@RequestBody MonitoringGroupRequest request) {
        try {
            Preconditions.checkArgument(request != null, "请求参数不能为空");
            
            // 将请求对象转换为实体对象
            MonitoringGroupEntity entity = inquiryMonitoringService.convertRequestToEntity(request);
            
            // 调用服务验证监控任务
            inquiryMonitoringService.validateMonitoringTask(entity);
            
            log.info("验证监控任务成功: request={}", request);
            return CommonResult.success("验证成功");
        } catch (IllegalArgumentException e) {
            log.warn("验证监控任务参数校验失败: request={}, error={}", request, e.getMessage());
            return CommonResult.error(e.getMessage());
        } catch (LlmCorpusException e) {
            log.warn("#validateMonitoringTask 验证监控任务业务异常: request={}, code={}, message={}", 
                    request, e.getCode(), e.getMessage());
            return CommonResult.error(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("验证监控任务异常: request={}, error={}", request, e.getMessage(), e);
            return CommonResult.error(String.format("验证监控任务异常，原因=%s", e.getMessage()));
        }
    }

    /**
     * 新增监控组
     *
     * @param request 监控组请求参数
     * @return 创建结果
     */
    @PostMapping("/addMonitoringGroup")
    public CommonResult addMonitoringGroup(@RequestBody MonitoringGroupRequest request) {
        try {
            // 参数校验
            Preconditions.checkArgument(request != null, "请求参数不能为空");
            Preconditions.checkArgument(request.getMonitoringGroupName() != null && !request.getMonitoringGroupName().isEmpty(), "监控组名称不能为空");
            Preconditions.checkArgument(request.getDxGroupIds() != null && !request.getDxGroupIds().isEmpty(), "DX群组ID不能为空");
            Preconditions.checkArgument(request.getMonitoringGroupOwner() != null && !request.getMonitoringGroupOwner().isEmpty(), "监控组所有者不能为空");
            Preconditions.checkArgument(request.getMonitoringTimeRangeType() != null, "监控时间范围类型不能为空");
            
            // 转换为实体对象
            MonitoringGroupEntity entity = inquiryMonitoringService.convertRequestToEntity(request);
            
            try {
                // 先验证监控任务是否可行
                inquiryMonitoringService.validateMonitoringTask(entity);
            } catch (LlmCorpusException e) {
                // 验证失败，直接返回错误
                log.warn("#addMonitoringGroup 验证监控任务失败: request={}, code={}, message={}",
                        request, e.getCode(), e.getMessage());
                return CommonResult.error(e.getCode(), e.getMessage());
            }
            
            // 调用服务添加监控组
            Long monitoringGroupId = inquiryMonitoringService.addMonitoringGroup(entity);
            
            // 保存问题类型
            if (monitoringGroupId != null && request.getQuestionTypes() != null && !request.getQuestionTypes().isEmpty()) {
                inquiryMonitoringService.saveQuestionTypes(monitoringGroupId, request.getQuestionTypes());
            }
            
            // 查询完整的监控组信息（包含问题类型）
            MonitoringGroupEntity newEntity = inquiryMonitoringService.getMonitoringGroupById(monitoringGroupId);
            if (newEntity != null) {
                List<QuestionTypeEntity> questionTypes = inquiryMonitoringService.getQuestionTypesByMonitoringGroupId(monitoringGroupId);
                newEntity.setQuestionTypes(questionTypes);
            }
            
            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("monitoringGroup", newEntity);
            result.put("monitoringGroupId", monitoringGroupId);
            
            log.info("新增监控组成功: monitoringGroupId={}, name={}", monitoringGroupId, request.getMonitoringGroupName());
            return CommonResult.success("新增成功", result);
        } catch (IllegalArgumentException e) {
            log.warn("新增监控组参数校验失败: request={}, error={}", request, e.getMessage());
            return CommonResult.error(e.getMessage());
        } catch (LlmCorpusException e) {
            log.warn("#addMonitoringGroup 新增监控组业务异常: request={}, code={}, message={}", 
                    request, e.getCode(), e.getMessage());
            return CommonResult.error(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("新增监控组异常: request={}, error={}", request, e.getMessage(), e);
            return CommonResult.error(String.format("新增监控组异常，原因=%s", e.getMessage()));
        }
    }

    /**
     * 获取监控组的问题类型列表
     *
     * @param monitoringGroupId 监控组ID
     * @return 问题类型列表
     */
    @PostMapping("/getQuestionTypes")
    public CommonResult getQuestionTypes(@RequestParam("monitoringGroupId") Long monitoringGroupId) {
        try {
            Preconditions.checkArgument(monitoringGroupId != null, "监控组ID不能为空");
            
            // 获取问题类型列表
            List<QuestionTypeEntity> questionTypes = inquiryMonitoringService.getQuestionTypesByMonitoringGroupId(monitoringGroupId);
            
            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("questionTypes", questionTypes);
            
            log.info("获取问题类型列表成功: monitoringGroupId={}, count={}", 
                    monitoringGroupId, questionTypes.size());
            return CommonResult.success("获取成功", result);
        } catch (IllegalArgumentException e) {
            log.warn("获取问题类型列表参数校验失败: monitoringGroupId={}, error={}", monitoringGroupId, e.getMessage());
            return CommonResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("获取问题类型列表异常: monitoringGroupId={}, error={}", monitoringGroupId, e.getMessage(), e);
            return CommonResult.error(String.format("获取问题类型列表异常，原因=%s", e.getMessage()));
        }
    }

    /**
     * 根据MIS ID查询该用户参与的所有监控组
     *
     * @param misId 用户MIS ID
     * @return 监控组列表
     */
    @PostMapping("/queryMonitoringGroupsByMisId")
    public CommonResult queryMonitoringGroupsByMisId(@RequestParam("misId") String misId) {
        try {
            Preconditions.checkArgument(misId != null && !misId.isEmpty(), "MIS ID不能为空");
            
            // 调用服务查询数据
            List<MonitoringGroupEntity> monitoringGroups = inquiryMonitoringService.getMonitoringGroupsByMisId(misId);
            
            // 为每个监控组查询并设置问题类型
            if (!monitoringGroups.isEmpty()) {
                for (MonitoringGroupEntity group : monitoringGroups) {
                    List<QuestionTypeEntity> questionTypes = inquiryMonitoringService.getQuestionTypesByMonitoringGroupId(group.getMonitoringGroupId());
                    group.setQuestionTypes(questionTypes);
                }
            }
            
            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("monitoringGroups", monitoringGroups);
            result.put("total", monitoringGroups.size());
            
            log.info("查询用户监控组成功: misId={}, count={}", misId, monitoringGroups.size());
            return CommonResult.success("查询成功", result);
        } catch (IllegalArgumentException e) {
            log.warn("查询用户监控组参数校验失败: misId={}, error={}", misId, e.getMessage());
            return CommonResult.error(e.getMessage());
        } catch (LlmCorpusException e) {
            log.warn("#queryMonitoringGroupsByMisId 查询用户监控组业务异常: misId={}, code={}, message={}", 
                    misId, e.getCode(), e.getMessage());
            return CommonResult.error(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("查询用户监控组异常: misId={}, error={}", misId, e.getMessage(), e);
            return CommonResult.error(String.format("查询用户监控组异常，原因=%s", e.getMessage()));
        }
    }

    /**
     * 修改监控组信息
     *
     * @param request 监控组请求参数
     * @return 修改结果
     */
    @PostMapping("/updateMonitoringGroup")
    public CommonResult updateMonitoringGroup(@RequestBody MonitoringGroupRequest request) {
        try {
            Preconditions.checkArgument(request != null, "请求参数不能为空");
            Preconditions.checkArgument(request.getMonitoringGroupId() != null, "监控组ID不能为空");
            Preconditions.checkArgument(request.getMonitoringGroupName() != null && !request.getMonitoringGroupName().isEmpty(), "监控组名称不能为空");
            Preconditions.checkArgument(request.getDxGroupIds() != null && !request.getDxGroupIds().isEmpty(), "DX群组ID不能为空");
            Preconditions.checkArgument(request.getMonitoringGroupOwner() != null && !request.getMonitoringGroupOwner().isEmpty(), "监控组所有者不能为空");
            Preconditions.checkArgument(request.getMonitoringTimeRangeType() != null, "监控时间范围类型不能为空");
            Preconditions.checkArgument(request.getOperatorMisId() != null, "操作人不能为空");

            Map<String, Object> result = inquiryMonitoringService.updateMonitoringGroupWithValidation(request);
            log.info("修改监控组成功: monitoringGroupId={}, name={}", 
                    request.getMonitoringGroupId(), request.getMonitoringGroupName());
            return CommonResult.success("修改成功", result);
        } catch (IllegalArgumentException e) {
            log.warn("修改监控组参数校验失败: request={}, error={}", request, e.getMessage());
            return CommonResult.error(e.getMessage());
        } catch (LlmCorpusException e) {
            log.warn("#updateMonitoringGroup 修改监控组业务异常: request={}, code={}, message={}", 
                    request, e.getCode(), e.getMessage());
            return CommonResult.error(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("修改监控组异常: request={}, error={}", request, e.getMessage(), e);
            return CommonResult.error(String.format("修改监控组异常，原因=%s", e.getMessage()));
        }
    }

    /**
     * 删除监控组
     *
     * @param monitoringGroupId 监控组ID
     * @return 删除结果
     */
    @PostMapping("/deleteMonitoringGroup")
    public CommonResult deleteMonitoringGroup(@RequestParam("monitoringGroupId") Long monitoringGroupId) {
        try {
            Preconditions.checkArgument(monitoringGroupId != null, "监控组ID不能为空");
            
            // 先查询原监控组信息，确认存在
            MonitoringGroupEntity existingEntity = inquiryMonitoringService.getMonitoringGroupById(monitoringGroupId);
            if (existingEntity == null) {
                return CommonResult.error("监控组不存在，ID: " + monitoringGroupId);
            }
            
            // 调用服务删除监控组
            boolean deleted = inquiryMonitoringService.deleteMonitoringGroup(monitoringGroupId);
            
            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("monitoringGroupId", monitoringGroupId);
            result.put("deleted", deleted);
            
            log.info("删除监控组成功: monitoringGroupId={}", monitoringGroupId);
            return CommonResult.success("删除成功", result);
        } catch (IllegalArgumentException e) {
            log.warn("删除监控组参数校验失败: monitoringGroupId={}, error={}", monitoringGroupId, e.getMessage());
            return CommonResult.error(e.getMessage());
        } catch (LlmCorpusException e) {
            log.warn("#deleteMonitoringGroup 删除监控组业务异常: monitoringGroupId={}, code={}, message={}", 
                    monitoringGroupId, e.getCode(), e.getMessage());
            return CommonResult.error(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("删除监控组异常: monitoringGroupId={}, error={}", monitoringGroupId, e.getMessage(), e);
            return CommonResult.error(String.format("删除监控组异常，原因=%s", e.getMessage()));
        }
    }

    @PostMapping("/unansweredInquiries")
    public CommonResult getUnansweredInquiries(@RequestBody MonitoringTimeRangeRequest request) {
        try {
            Preconditions.checkNotNull(request, "请求参数不能为空");
            Preconditions.checkNotNull(request.getMonitorGroupIds(), "监控组ID不能为空");
            Preconditions.checkArgument(request.getStartTime() != null && request.getEndTime() != null, "时间范围不能为空");
            Preconditions.checkArgument(request.getStartTime() <= request.getEndTime(), "开始时间不能大于结束时间");

            int pageNum = request.getPageNum() != null ? request.getPageNum() : 1;
            int pageSize = request.getPageSize() != null ? request.getPageSize() : 10;

            // 查询所有数据（不分页）
            List<InquiryMonitoringEntity> allData = inquiryMonitoringService.queryAllMonitoringData(request.getStartTime(), request.getEndTime(), request.getMonitorGroupIds());

            // 只保留未回应的
            List<InquiryMonitoringEntity> unansweredList = allData.stream()
                    .filter(entity -> entity.getReplyStatus() != null && entity.getReplyStatus() == 0)
                    .collect(Collectors.toList());

            int total = unansweredList.size();
            int totalPage = (total + pageSize - 1) / pageSize;
            int fromIndex = (pageNum - 1) * pageSize;
            int toIndex = Math.min(fromIndex + pageSize, total);
            List<InquiryMonitoringEntity> pageList = fromIndex < total ? unansweredList.subList(fromIndex, toIndex) : java.util.Collections.emptyList();

            Map<String, Object> result = new HashMap<>();
            result.put("currentPage", pageNum);
            result.put("pageSize", pageSize);
            result.put("total", total); // 所有未回应的总数
            result.put("totalPage", totalPage);
            result.put("list", pageList);
            result.put("unansweredCount", pageList.size());

            log.info("查询未回应咨询统计成功，共返回{}条记录，当前第{}页，每页{}条", 
                    pageList.size(), pageNum, pageSize);
            return CommonResult.success("查询成功", result);
        } catch (IllegalArgumentException e) {
            log.warn("查询未回应咨询统计参数校验失败: request={}, error={}", request, e.getMessage());
            return CommonResult.error(e.getMessage());
        } catch (LlmCorpusException e) {
            log.warn("#getUnansweredInquiries 查询未回应咨询统计业务异常: request={}, code={}, message={}", 
                    request, e.getCode(), e.getMessage());
            return CommonResult.error(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("查询未回应咨询统计异常: request={}, error={}", request, e.getMessage(), e);
            return CommonResult.error(String.format("查询未回应咨询统计异常，原因=%s", e.getMessage()));
        }
    }
} 