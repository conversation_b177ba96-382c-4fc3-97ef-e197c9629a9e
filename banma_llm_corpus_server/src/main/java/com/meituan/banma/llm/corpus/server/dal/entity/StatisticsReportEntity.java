package com.meituan.banma.llm.corpus.server.dal.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

/**
 * 统计报表实体类，对应statistics_report表
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StatisticsReportEntity {
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 应用ID
     */
    private String appId;
    
    /**
     * 报表起始时间戳（毫秒）
     */
    private Long startTime;
    
    /**
     * 报表结束时间戳（毫秒）
     */
    private Long endTime;
    
    /**
     * 报表数据（JSON格式）
     */
    private String reportData;
    
    /**
     * 报表生成时间
     */
    private Timestamp ctime;
    
    /**
     * 报表更新时间
     */
    private Timestamp utime;
} 