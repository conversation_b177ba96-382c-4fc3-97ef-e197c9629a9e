package com.meituan.banma.llm.corpus.server.service.impl;

import com.meituan.banma.llm.corpus.server.common.MtConfigService;
import com.meituan.banma.llm.corpus.server.dal.entity.RgTagsEntity;
import com.meituan.banma.llm.corpus.server.dal.entity.KnowledgeBaseVersionEntity;
import com.meituan.banma.llm.corpus.server.dal.entity.ModelOutputTaskEntity;
import com.meituan.banma.llm.corpus.server.dal.mapper.RgTagsMapper;
import com.meituan.banma.llm.corpus.server.dal.mapper.ReviewMapper;
import com.meituan.banma.llm.corpus.server.dal.mapper.ModelOutputMapper;
import com.meituan.banma.llm.corpus.server.service.IRgTagsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class RgTagsServiceImpl implements IRgTagsService {

    @Autowired
    private RgTagsMapper rgTagsMapper;
    
    @Autowired
    private MtConfigService mtConfigService;

    @Autowired
    private ReviewMapper reviewMapper;

    @Autowired
    private ModelOutputMapper modelOutputMapper;

    @Override
    public RgTagsEntity addTag(Long rgId, String tagName, String tagDesc, String misId) {
        try {
            // 检查标签是否已存在
            RgTagsEntity existingTag = rgTagsMapper.findByRgIdAndTagName(rgId, tagName.trim());
            if (existingTag != null) {
                log.warn("#RgTagsServiceImpl.addTag 标签已存在: rgId={}, tagName={}", rgId, tagName);
                return null;
            }
            
            // 创建新标签
            RgTagsEntity entity = new RgTagsEntity();
            entity.setRgId(rgId);
            entity.setTagName(tagName.trim());
            entity.setTagDesc(tagDesc != null ? tagDesc.trim() : "");
            entity.setMisId(misId.trim());
            
            int result = rgTagsMapper.insert(entity);
            if (result > 0) {
                log.info("#RgTagsServiceImpl.addTag 成功创建标签: rgId={}, tagName={}, misId={}, id={}", 
                        rgId, tagName, misId, entity.getId());
                return entity;
            } else {
                log.error("#RgTagsServiceImpl.addTag 创建标签失败: rgId={}, tagName={}, misId={}", 
                        rgId, tagName, misId);
                return null;
            }
        } catch (Exception e) {
            log.error("#RgTagsServiceImpl.addTag 创建标签过程中发生异常: rgId={}, tagName={}, misId={}", 
                    rgId, tagName, misId, e);
            return null;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteTagById(Long id) {
        try {
            // 1. 首先检查标签是否存在
            RgTagsEntity tag = rgTagsMapper.findById(id);
            if (tag == null) {
                log.warn("#RgTagsServiceImpl.deleteTagById 标签不存在: id={}", id);
                return false;
            }

            // 2. 更新modified_output表中的tags_ids字段，移除被删除的标签ID
            updateModifiedOutputTagsIds(id);

            // 3. 更新model_output_task表中的tags_ids字段，移除被删除的标签ID
            updateModelOutputTaskTagsIds(id);

            // 4. 删除标签
            int result = rgTagsMapper.deleteById(id);
            if (result > 0) {
                log.info("#RgTagsServiceImpl.deleteTagById 成功删除标签并更新相关表: id={}, tagName={}", id, tag.getTagName());
                return true;
            } else {
                log.warn("#RgTagsServiceImpl.deleteTagById 标签删除失败: id={}", id);
                return false;
            }
        } catch (Exception e) {
            log.error("#RgTagsServiceImpl.deleteTagById 删除标签过程中发生异常: id={}", id, e);
            return false;
        }
    }

    /**
     * 更新modified_output表中的tags_ids字段，移除指定的标签ID
     */
    private void updateModifiedOutputTagsIds(Long tagId) {
        try {
            // 获取默认标签ID
            String defaultTagId = getDefaultTagId();
            
            // 查询所有包含该标签ID的记录
            List<KnowledgeBaseVersionEntity> recordsWithTag = reviewMapper.findRecordsWithTagId(tagId.toString());
            
            if (CollectionUtils.isEmpty(recordsWithTag)) {
                log.info("#updateModifiedOutputTagsIds 没有找到包含标签ID {}的记录", tagId);
                return;
            }

            // 批量更新记录
            for (KnowledgeBaseVersionEntity record : recordsWithTag) {
                String newTagsIds = removeTagIdFromString(record.getTagsIds(), tagId.toString());
                
                // 如果删除后tags_ids为空，则设置为默认标签ID
                if (StringUtils.isBlank(newTagsIds) && StringUtils.isNotBlank(defaultTagId)) {
                    newTagsIds = defaultTagId;
                }
                
                reviewMapper.updateTagsIds(record.getId(), newTagsIds);
            }
            
            log.info("#updateModifiedOutputTagsIds 成功更新modified_output表中{}条记录的tags_ids字段", recordsWithTag.size());
        } catch (Exception e) {
            log.error("#updateModifiedOutputTagsIds 更新modified_output表tags_ids字段时发生异常: tagId={}", tagId, e);
            throw e;
        }
    }

    /**
     * 更新model_output_task表中的tags_ids字段，移除指定的标签ID
     */
    private void updateModelOutputTaskTagsIds(Long tagId) {
        try {
            // 获取默认标签ID
            String defaultTagId = getDefaultTagId();
            
            // 查询所有包含该标签ID的记录
            List<ModelOutputTaskEntity> recordsWithTag = modelOutputMapper.findRecordsWithTagId(tagId.toString());
            
            if (CollectionUtils.isEmpty(recordsWithTag)) {
                log.info("#updateModelOutputTaskTagsIds 没有找到包含标签ID {}的记录", tagId);
                return;
            }

            // 批量更新记录
            for (ModelOutputTaskEntity record : recordsWithTag) {
                String newTagsIds = removeTagIdFromString(record.getTagsIds(), tagId.toString());
                
                // 如果删除后tags_ids为空，则设置为默认标签ID
                if (StringUtils.isBlank(newTagsIds) && StringUtils.isNotBlank(defaultTagId)) {
                    newTagsIds = defaultTagId;
                }
                
                modelOutputMapper.updateTagsIds(record.getId(), newTagsIds);
            }
            
            log.info("#updateModelOutputTaskTagsIds 成功更新model_output_task表中{}条记录的tags_ids字段", recordsWithTag.size());
        } catch (Exception e) {
            log.error("#updateModelOutputTaskTagsIds 更新model_output_task表tags_ids字段时发生异常: tagId={}", tagId, e);
            throw e;
        }
    }

    /**
     * 获取默认标签ID
     * @return 默认标签ID字符串，如果没有默认标签则返回null
     */
    private String getDefaultTagId() {
        try {
            // 获取默认标签的rgId
            Long defaultTagRgId = mtConfigService.getDefaultTagRgId();
            
            // 查询默认标签
            List<RgTagsEntity> defaultTags = rgTagsMapper.findByRgId(defaultTagRgId);
            
            if (!CollectionUtils.isEmpty(defaultTags)) {
                // 返回第一个默认标签的ID
                Long defaultTagId = defaultTags.get(0).getId();
                log.debug("#getDefaultTagId 获取到默认标签ID: {}", defaultTagId);
                return defaultTagId.toString();
            } else {
                log.warn("#getDefaultTagId 未找到默认标签, defaultTagRgId: {}", defaultTagRgId);
                return null;
            }
        } catch (Exception e) {
            log.error("#getDefaultTagId 获取默认标签ID时发生异常", e);
            return null;
        }
    }

    /**
     * 从逗号分隔的标签ID字符串中移除指定的标签ID
     * 
     * @param tagsIds 原始标签ID字符串，如 "1,2,3,4"
     * @param tagIdToRemove 要移除的标签ID，如 "2"
     * @return 移除指定标签ID后的字符串，如 "1,3,4"
     */
    private String removeTagIdFromString(String tagsIds, String tagIdToRemove) {
        if (StringUtils.isBlank(tagsIds) || StringUtils.isBlank(tagIdToRemove)) {
            return tagsIds;
        }
        
        try {
            // 分割标签ID字符串
            List<String> tagIdList = Arrays.stream(tagsIds.split(","))
                    .map(String::trim)
                    .filter(StringUtils::isNotBlank)
                    .filter(tagId -> !tagId.equals(tagIdToRemove.trim()))
                    .collect(Collectors.toList());
            
            // 重新组合成字符串
            return tagIdList.isEmpty() ? "" : String.join(",", tagIdList);
        } catch (Exception e) {
            log.warn("#removeTagIdFromString 处理标签ID字符串时发生异常: tagsIds={}, tagIdToRemove={}", tagsIds, tagIdToRemove, e);
            return tagsIds; // 出现异常时返回原字符串
        }
    }

    @Override
    public boolean deleteTagByRgIdAndTagName(Long rgId, String tagName) {
        try {
            int result = rgTagsMapper.deleteByRgIdAndTagName(rgId, tagName.trim());
            if (result > 0) {
                log.info("#RgTagsServiceImpl.deleteTagByRgIdAndTagName 成功删除标签: rgId={}, tagName={}", rgId, tagName);
                return true;
            } else {
                log.warn("#RgTagsServiceImpl.deleteTagByRgIdAndTagName 标签不存在或删除失败: rgId={}, tagName={}", rgId, tagName);
                return false;
            }
        } catch (Exception e) {
            log.error("#RgTagsServiceImpl.deleteTagByRgIdAndTagName 删除标签过程中发生异常: rgId={}, tagName={}", rgId, tagName, e);
            return false;
        }
    }

    @Override
    public boolean updateTagDesc(Long id, String tagDesc, String misId) {
        try {
            // 检查标签是否存在
            RgTagsEntity existingTag = rgTagsMapper.findById(id);
            if (existingTag == null) {
                log.warn("#RgTagsServiceImpl.updateTagDesc 标签不存在: id={}", id);
                return false;
            }

            // 更新标签描述和操作人misId，保持原标签名不变
            RgTagsEntity updateEntity = new RgTagsEntity();
            updateEntity.setId(id);
            updateEntity.setTagName(existingTag.getTagName());
            updateEntity.setTagDesc(tagDesc != null ? tagDesc.trim() : "");
            updateEntity.setMisId(misId != null ? misId.trim() : "");
            
            int result = rgTagsMapper.updateById(updateEntity);
            if (result > 0) {
                log.info("#RgTagsServiceImpl.updateTagDesc 成功更新标签描述: id={}, tagDesc={}, misId={}", id, tagDesc, misId);
                return true;
            } else {
                log.error("#RgTagsServiceImpl.updateTagDesc 更新标签描述失败: id={}, tagDesc={}, misId={}", id, tagDesc, misId);
                return false;
            }
        } catch (Exception e) {
            log.error("#RgTagsServiceImpl.updateTagDesc 更新标签描述过程中发生异常: id={}, tagDesc={}, misId={}", id, tagDesc, misId, e);
            return false;
        }
    }

    @Override
    public List<RgTagsEntity> getTagsByRgId(Long rgId) {
        try {
            // 查询指定rgId的标签
            List<RgTagsEntity> tags = rgTagsMapper.findByRgId(rgId);
            
            // 获取默认标签的rgId
            Long defaultTagRgId = mtConfigService.getDefaultTagRgId();
            
            // 查询默认标签
            List<RgTagsEntity> defaultTags = rgTagsMapper.findByRgId(defaultTagRgId);

            // 对默认标签进行字段隐藏处理
            for (RgTagsEntity defaultTag : defaultTags) {
                defaultTag.setId(null);
                defaultTag.setRgId(null);
                defaultTag.setMisId(null);
                defaultTag.setCtime(null);
                defaultTag.setUtime(null);
            }
            
            // 将默认标签放在最前面
            defaultTags.addAll(tags);
            
            log.info("#RgTagsServiceImpl.getTagsByRgId 查询到标签列表: rgId={}, 普通标签数量={}, 默认标签数量={}, 总数量={}, 默认标签rgId={}", 
                    rgId, tags.size(), defaultTags.size() - tags.size(), defaultTags.size(), defaultTagRgId);
            return defaultTags;
        } catch (Exception e) {
            log.error("#RgTagsServiceImpl.getTagsByRgId 查询标签列表过程中发生异常: rgId={}", rgId, e);
            return null;
        }
    }

    @Override
    public List<RgTagsEntity> getTagsByIds(List<Long> ids) {
        try {
            if (CollectionUtils.isEmpty(ids)) {
                log.warn("#RgTagsServiceImpl.getTagsByIds 参数无效: ids为空");
                return null;
            }
            
            List<RgTagsEntity> tags = rgTagsMapper.findByIds(ids);
            log.info("#RgTagsServiceImpl.getTagsByIds 查询到标签列表: ids={}, 数量={}", ids, tags.size());
            return tags;
        } catch (Exception e) {
            log.error("#RgTagsServiceImpl.getTagsByIds 查询标签列表过程中发生异常: ids={}", ids, e);
            return null;
        }
    }

    @Override
    public Map<String, String> getTagsMapByRgId(Long rgId) {
        try {
            List<RgTagsEntity> tags = rgTagsMapper.findByRgId(rgId);
            Map<String, String> tagMap = new HashMap<>();
            
            for (RgTagsEntity tag : tags) {
                // 格式：id|description，如果id为null则用空字符串
                String value = (tag.getId() != null ? tag.getId().toString() : "") + "|" + 
                              (tag.getTagDesc() != null ? tag.getTagDesc() : "");
                tagMap.put(tag.getTagName(), value);
            }
            
            log.info("#RgTagsServiceImpl.getTagsMapByRgId 查询到标签Map: rgId={}, 数量={}", rgId, tagMap.size());
            return tagMap;
        } catch (Exception e) {
            log.error("#RgTagsServiceImpl.getTagsMapByRgId 查询标签Map过程中发生异常: rgId={}", rgId, e);
            return new HashMap<>();
        }
    }
} 