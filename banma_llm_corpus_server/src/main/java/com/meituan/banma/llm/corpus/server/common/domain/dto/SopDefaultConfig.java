package com.meituan.banma.llm.corpus.server.common.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * SOP默认配置DTO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SopDefaultConfig {
    /**
     * 默认值班组ID
     */
    private Long defaultRgId;
    
    /**
     * 默认SOP模板
     */
    private String defaultTemplate;
    
    /**
     * 创建默认配置
     * @return 默认配置对象
     */
    public static SopDefaultConfig createDefault() {
        return new SopDefaultConfig(-1L, 
            "**用户反馈问题**：XXXXX\n\n**现象描述**：XXXXX\n\n**分析过程**：1.XXXXX\n2.XXXX\n\n**结论**：XXXX\n\n**处理方案**：1.XXX\n\n2.XXX");
    }
} 