package com.meituan.banma.llm.corpus.server.controller;

import com.dianping.lion.shade.org.apache.curator.shaded.com.google.common.base.Preconditions;
import com.meituan.banma.llm.corpus.server.common.CommonResult;
import com.meituan.banma.llm.corpus.server.common.domain.dto.PageDTO;
import com.meituan.banma.llm.corpus.server.controller.request.km.AddDocumentForm;
import com.meituan.banma.llm.corpus.server.controller.request.km.BodyToSimplify;
import com.meituan.banma.llm.corpus.server.controller.request.km.SplitContentForm;
import com.meituan.banma.llm.corpus.server.dal.entity.RgDocumentUrlEntity;
import com.meituan.banma.llm.corpus.server.service.IKmService;
import com.meituan.banma.llm.corpus.server.service.impl.DxGroupChatServiceImpl;
import com.meituan.banma.llm.corpus.server.utils.AsyncTaskUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

/**
 * <AUTHOR>
 */
@RestController
@Slf4j
@RequestMapping("/km")
@ResponseBody
public class KmController {


    @Autowired
    private IKmService kmService;

    /**
     * 根据URL获取学城文档基本信息，检查是否能获取基本信息与内容获取
     * @param url
     * @param rgId
     * @param misId
     * @return
     */
    @GetMapping("/getKmMetaByUrl")
    public Object getKmMetaByUrl(@RequestParam(value = "url", defaultValue = "") String url,
                                   @RequestParam(value = "rgId", defaultValue = "0")  long rgId,
                                   @RequestParam(value = "misId", defaultValue = "") String misId) {
        try {
            Preconditions.checkArgument(rgId > 0,"rgId 必须大于0");
            Preconditions.checkArgument(misId != null && !misId.isEmpty(),"misId 不能为空");
            Preconditions.checkArgument(url != null && !url.isEmpty(), "url 不能为空");
            
            // 使用异步处理URL检查和元数据获取
            CompletableFuture<Map<String, Object>> resultFuture = kmService.checkUrlAndGetMetaAsync(url, rgId, misId);
            
            // 添加超时处理，使用可传递ThreadLocal值的方式
            CompletableFuture<Map<String, Object>> timeoutFuture = new CompletableFuture<>();
            
            // 使用支持ThreadLocal传递的异步任务
            AsyncTaskUtils.submitWithThreadLocal(AsyncTaskUtils.getCheckUrlThreadPool(), () -> {
                try {
                    Map<String, Object> result = resultFuture.get(10, TimeUnit.SECONDS);
                    timeoutFuture.complete(result);
                } catch (Exception e) {
                    log.error("getKmMetaByUrl 异步执行超时或异常, rgId:{}, misId:{}, url:{}, 异常:{}", 
                              rgId, misId, url, e.getMessage());
                    Map<String, Object> errorResult = new HashMap<>();
                    errorResult.put("canAdd", Boolean.FALSE);
                    if (e instanceof TimeoutException) {
                        errorResult.put("reason", "获取文档信息超时，请稍后重试");
                    } else {
                        errorResult.put("reason", "获取文档信息异常: " + e.getMessage());
                    }
                    timeoutFuture.complete(errorResult);
                }
            });
            
            // 获取异步结果
            Map<String, Object> result = timeoutFuture.join();
            
            // 根据结果构建响应
            if (Boolean.TRUE.equals(result.get("canAdd"))) {
                return CommonResult.success("解析学城文档基本信息成功", result);
            } else {
                String reason = (String) result.getOrDefault("reason", "解析学城文档基本信息失败");
                return CommonResult.success(reason, result);
            }
        } catch (Exception e) {
            log.error("getKmMetaByUrl 执行失败, rgId:{}, misId:{}, url:{}, 异常原因:{}", rgId, misId, url, e.getMessage());
            return CommonResult.error(e.getMessage());
        }
    }

    /**
     * 根据学城文档ID获取切分后的内容，先获取body中的JSON格式-》转换成HTML-〉根据标题切分
     * @param contentId
     * @param rgId
     * @param misId
     * @return
     */
    @GetMapping("/getKmSplitByContentId")
    public Object getKmSplitByContentId(@RequestParam(value = "contentId", defaultValue = "0") long contentId,
                                       @RequestParam(value = "isTable", defaultValue = "0")  int isTable,
                                       @RequestParam(value = "rgId", defaultValue = "0")  long rgId,
                                       @RequestParam(value = "misId", defaultValue = "") String misId) {
        try {
            Preconditions.checkArgument(rgId > 0,"rgId 必须大于0");
            Preconditions.checkArgument(misId != null && !misId.isEmpty(),"misId 不能为空");
            Preconditions.checkArgument(contentId > 0, "contentId 必须大于0");
            Object kmSplitContent = kmService.getKmSplitByContentId(contentId, rgId, misId, isTable);
            Map<String,Object> result= new HashMap<>();
            result.put("contentId",contentId);
            result.put("data",kmSplitContent);
            if (kmSplitContent != null) {
                return CommonResult.success("学城文档切分成功",result);
            }else {
                return CommonResult.error("学城文档切分失败");
            }
        } catch (Exception e){
            log.error("getKmByContentId 执行失败, rgId:{}, misId:{}, contentId:{}, 异常原因:{}", rgId, misId, contentId, e.getMessage());
            return CommonResult.error(String.format("执行失败,异常原因=%s", e.getMessage()));
        }
    }

    @GetMapping("/getKmHtmlByContentId")
    public Object getKmHtmlByContentId(@RequestParam(value = "contentId", defaultValue = "0") long contentId,
                                        @RequestParam(value = "rgId", defaultValue = "0")  long rgId,
                                        @RequestParam(value = "misId", defaultValue = "") String misId) {
        try {
            Preconditions.checkArgument(rgId > 0,"rgId 必须大于0");
            Preconditions.checkArgument(misId != null && !misId.isEmpty(),"misId 不能为空");
            Preconditions.checkArgument(contentId > 0, "contentId 必须大于0");
            Object kmContent = kmService.getKmHtmlByContentId(contentId, rgId, misId);
            if (kmContent != null) {
                return CommonResult.success("获取学城文档内容信息成功",kmContent);
            }else {
                return CommonResult.error("获取学城文档内容信息失败");
            }
        } catch (Exception e){
            log.error("getKmHtmlByContentId 执行失败, rgId:{}, misId:{}, contentId:{}, 异常原因:{}", rgId, misId, contentId, e.getMessage());
            return CommonResult.error(String.format("执行失败,异常原因=%s", e.getMessage()));
        }
    }


    @PostMapping("/testSimplifyBody")
    public Object testSimplifyBody(@RequestBody BodyToSimplify bodyToSimplify) {
        try {
            String simplifyBody = kmService.testSimplfyBody(bodyToSimplify.getBody());
            return CommonResult.success("测试成功",simplifyBody);
        } catch (Exception e) {
            log.error("testSimplifyBody 执行失败, 异常原因:{}", e.getMessage());
            return CommonResult.error(String.format("执行失败,异常原因=%s", e.getMessage()));
        }
    }

    @PostMapping("/kmToCorpusByContentId")
    public Object kmToCorpusByContentId(@RequestParam(value = "contentId", defaultValue = "0") long contentId,
                               @RequestParam(value = "rgId", defaultValue = "0")  long rgId,
                               @RequestParam(value = "misId", defaultValue = "") String misId) {
        try {
            Preconditions.checkArgument(rgId > 0,"rgId 必须大于0");
            Preconditions.checkArgument(misId != null && !misId.isEmpty(),"misId 不能为空");
            Preconditions.checkArgument(contentId > 0, "contentId 必须大于0");
            String taskId = kmService.kmToCorpusByContentId(contentId, rgId, misId);
            return CommonResult.success("任务提交成功",taskId);
        } catch (Exception e) {
            log.error("kmToCorpusByContentId 执行失败, rgId:{}, misId:{}, contentId:{}, 异常原因:{}", rgId, misId, contentId, e.getMessage());
            return CommonResult.error(String.format("执行失败,异常原因=%s", e.getMessage()));
        }
    }

    @PostMapping("/splitContentToCorpus")
    public Object splitContentToCorpus(@RequestBody SplitContentForm splitContentForm,
                                       @RequestParam(value = "rgId", defaultValue = "0")  long rgId,
                                       @RequestParam(value = "misId", defaultValue = "") String misId) {
        try {
            Preconditions.checkArgument(rgId > 0,"rgId 必须大于0");
            Preconditions.checkArgument(misId != null && !misId.isEmpty(),"misId 不能为空");
            Preconditions.checkArgument(splitContentForm != null, "参数不能为空");
            Preconditions.checkArgument(splitContentForm.getContentId() > 0, "contentId 必须大于0");
            String taskId = kmService.splitContentToCorpus(splitContentForm, rgId, misId);
            return CommonResult.success("任务提交成功",taskId);
        } catch (Exception e) {
            log.error("splitContentToCorpus 执行失败, rgId:{}, misId:{}, contentId:{}, 异常原因:{}", rgId, misId, splitContentForm.getContentId(), e.getMessage());
            return CommonResult.error(String.format("执行失败,异常原因=%s", e.getMessage()));
        }
    }

    @GetMapping("/queryDocumentByRgId")
    public Object queryDocumentByRgId(@RequestParam(value = "rgId", defaultValue = "0")  long rgId,
                                       @RequestParam(value = "strMatch", defaultValue = "") String strMatch,
                                       @RequestParam(value = "misId", defaultValue = "") String misId,
                                       @RequestParam(value = "pageNum", defaultValue = "1") int pageNum,
                                       @RequestParam(value = "pageSize", defaultValue = "100" ) int pageSize) {
        try {
            Preconditions.checkArgument(rgId > 0,"rgId 必须大于0");
            Preconditions.checkArgument(misId != null && !misId.isEmpty(),"misId 不能为空");

            // 调用服务获取分页文档数据
            PageDTO<RgDocumentUrlEntity> pageDTO = kmService.queryDocumentByRgId(rgId, misId, strMatch, pageNum, pageSize);
            Map<String,Object> result = new HashMap<>();
            result.put("pageNum",pageDTO.getPageNum());
            result.put("pageSize",pageDTO.getPageSize());
            result.put("total",pageDTO.getTotalCount());
            result.put("totalPage",pageDTO.getTotalPage());
            result.put("list",pageDTO.getData());
            return CommonResult.success("获取文档列表成功", result);
        } catch (Exception e) {
            log.error("queryDocumentByRgId 执行失败, rgId:{}, misId:{}, 异常原因:{}", rgId, misId,  e.getMessage());
            return CommonResult.error(String.format("执行失败,异常原因=%s", e.getMessage()));
        }
    }

    @GetMapping("/queryAddCheck")
    public Object queryAddCheck(@RequestParam(value = "rgId", defaultValue = "0")  long rgId,
                                @RequestParam(value = "url", defaultValue = "") String url,
                                @RequestParam(value = "name", defaultValue = "") String name,
                                @RequestParam(value = "misId", defaultValue = "") String misId) {
        try {
            Preconditions.checkArgument(rgId > 0,"rgId 必须大于0");
            Preconditions.checkArgument(misId != null && !misId.isEmpty(),"misId 不能为空");
            Preconditions.checkArgument(url != null && !url.isEmpty(),"url 不能为空");
            Preconditions.checkArgument(name != null && !name.isEmpty(),"name 不能为空");
            
            // 调用服务进行检查
            Map<String, Object> checkResult = kmService.checkDocumentCanAdd(rgId, url, name, misId);
            boolean canAdd = (boolean) checkResult.get("canAdd");
            
            if (canAdd) {
                return CommonResult.success("可以添加");
            } else {
                String reason = (String) checkResult.get("reason");
                return CommonResult.success(reason, false);
            }
        } catch (Exception e) {
            log.error("queryAddCheck 执行失败, rgId:{}, misId:{}, url:{}, name:{}, 异常原因:{}", rgId, misId, url, name,  e.getMessage());
            return CommonResult.error(String.format("执行失败,异常原因=%s", e.getMessage()));
        }
    }
    
    @GetMapping("/checkUrlExists")
    public Object checkUrlExists(@RequestParam(value = "rgId", defaultValue = "0")  long rgId,
                                @RequestParam(value = "url", defaultValue = "") String url,
                                @RequestParam(value = "misId", defaultValue = "") String misId) {
        try {
            Preconditions.checkArgument(rgId > 0,"rgId 必须大于0");
            Preconditions.checkArgument(misId != null && !misId.isEmpty(),"misId 不能为空");
            Preconditions.checkArgument(url != null && !url.isEmpty(),"url 不能为空");
            
            // 使用异步处理URL检查和元数据获取
            CompletableFuture<Map<String, Object>> resultFuture = kmService.checkUrlExistsAsync(url, rgId, misId);
            
            // 添加超时处理，使用可传递ThreadLocal值的方式
            CompletableFuture<Map<String, Object>> timeoutFuture = new CompletableFuture<>();
            
            // 使用支持ThreadLocal传递的异步任务
            AsyncTaskUtils.submitWithThreadLocal(AsyncTaskUtils.getCheckUrlThreadPool(), () -> {
                try {
                    Map<String, Object> result = resultFuture.get(10, TimeUnit.SECONDS);
                    timeoutFuture.complete(result);
                } catch (Exception e) {
                    log.error("checkUrlExists 异步执行超时或异常, rgId:{}, misId:{}, url:{}, 异常:{}", 
                              rgId, misId, url, e.getMessage());
                    Map<String, Object> errorResult = new HashMap<>();
                    errorResult.put("exists", Boolean.FALSE);
                    errorResult.put("canAdd", Boolean.FALSE);
                    if (e instanceof TimeoutException) {
                        errorResult.put("reason", "检查URL存在状态超时，请稍后重试");
                    } else {
                        errorResult.put("reason", "检查URL存在状态异常: " + e.getMessage());
                    }
                    timeoutFuture.complete(errorResult);
                }
            });
            
            // 获取异步结果
            Map<String, Object> result = timeoutFuture.join();
            
            // 根据结果构建响应
            String reason = (String) result.getOrDefault("reason", "检查URL存在状态");
            return CommonResult.success(reason, result);
        } catch (Exception e) {
            log.error("checkUrlExists 执行失败, rgId:{}, misId:{}, url:{}, 异常原因:{}", rgId, misId, url, e.getMessage());
            return CommonResult.error(String.format("执行失败,异常原因=%s", e.getMessage()));
        }
    }
    
    @GetMapping("/checkNameExists")
    public Object checkNameExists(@RequestParam(value = "rgId", defaultValue = "0")  long rgId,
                                 @RequestParam(value = "name", defaultValue = "") String name,
                                 @RequestParam(value = "misId", defaultValue = "") String misId) {
        try {
            Preconditions.checkArgument(rgId > 0,"rgId 必须大于0");
            Preconditions.checkArgument(misId != null && !misId.isEmpty(),"misId 不能为空");
            Preconditions.checkArgument(name != null && !name.isEmpty(),"name 不能为空");
            
            // 调用服务检查名称是否存在
            boolean exists = kmService.checkNameExists(rgId, name, misId);
            
            if (exists) {
                return CommonResult.success("文档名称已存在", true);
            } else {
                return CommonResult.success("文档名称不存在，可以添加", false);
            }
        } catch (Exception e) {
            log.error("checkNameExists 执行失败, rgId:{}, misId:{}, name:{}, 异常原因:{}", rgId, misId, name, e.getMessage());
            return CommonResult.error(String.format("执行失败,异常原因=%s", e.getMessage()));
        }
    }

    @PostMapping("/deleteByDocumentId")
    public Object deleteByDocumentId(@RequestBody List<String> documentIds,
                                     @RequestParam(value = "rgId", defaultValue = "0")  long rgId,
                                     @RequestParam(value = "misId", defaultValue = "") String misId) {
        try {
            Preconditions.checkArgument(rgId > 0,"rgId 必须大于0");
            Preconditions.checkArgument(misId != null && !misId.isEmpty(),"misId 不能为空");
            Preconditions.checkArgument(documentIds != null && !documentIds.isEmpty(),"documentIds 不能为空");
            
            // 调用服务批量删除文档，使用事务保证全部成功或全部失败
            List<String> failDoc = kmService.batchDeleteByDocumentIds(rgId, documentIds, misId);
            Map<String,Object> result = new HashMap<>();
            result.put("total",documentIds.size());
            result.put("success",documentIds.size() - failDoc.size());

            if (failDoc.isEmpty()) {
                return CommonResult.success("全部删除成功",result);
            } else {
                result.put("failDoc",failDoc);
                return CommonResult.success("部分删除成功",result);
            }
        } catch (Exception e) {
            log.error("deleteByDocumentId 执行失败, rgId:{}, misId:{}, documentIds:{}, 异常原因:{}", 
                    rgId, misId, documentIds, e.getMessage());
            return CommonResult.error(String.format("执行失败,异常原因=%s", e.getMessage()));
        }
    }

    @PostMapping("/addBatchDocumentByNameAndUrl")
    public Object addBatchDocumentByNameAndUrl(@RequestBody AddDocumentForm addDocumentForm,
                                    @RequestParam(value = "rgId", defaultValue = "0")  long rgId,
                                    @RequestParam(value = "misId", defaultValue = "") String misId) {
        try {
            Preconditions.checkArgument(rgId > 0,"rgId 必须大于0");
            Preconditions.checkArgument(misId != null && !misId.isEmpty(),"misId 不能为空");
            Preconditions.checkArgument(addDocumentForm != null, "参数不能为空");
            Preconditions.checkArgument(addDocumentForm.getNameList() != null && !addDocumentForm.getNameList().isEmpty(), "nameList 不能为空");
            Preconditions.checkArgument(addDocumentForm.getUrlList() != null && !addDocumentForm.getUrlList().isEmpty(), "urlList 不能为空");
            Preconditions.checkArgument(addDocumentForm.getNameList().size() == addDocumentForm.getUrlList().size(), "nameList 和 urlList 长度必须一致");
            
            List<Integer> autoUpdateList = addDocumentForm.getAutoUpdateList();
            Preconditions.checkArgument(autoUpdateList.size() == addDocumentForm.getNameList().size(), "autoUpdateList 和 nameList 长度必须一致");
            
            // 调用服务批量添加文档
            List<String> failUrls = kmService.addBatchDocumentByNameAndUrl(rgId, addDocumentForm.getNameList(),
                                                                 addDocumentForm.getUrlList(),
                                                                 autoUpdateList, misId);

            Map<String, Object> result = new HashMap<>();
            result.put("totalCount", addDocumentForm.getNameList().size());
            result.put("successCount", addDocumentForm.getNameList().size() - failUrls.size());
            if (failUrls.isEmpty()) {
                return CommonResult.success("文档批量添加成功", result);
            }else {
                result.put("failCount", failUrls.size());
                result.put("failUrlList", failUrls);
                return CommonResult.success("以下文档添加失败", result);
            }
        } catch (Exception e) {
            log.error("addBatchDocumentByNameAndUrl 执行失败, rgId:{}, misId:{}, 异常原因:{}", 
                    rgId, misId, e.getMessage());
            return CommonResult.error(String.format("执行失败,异常原因=%s", e.getMessage()));
        }
    }

    @PostMapping("/changeRefreshByDocumentId")
    public Object changeRefreshByDocumentId(@RequestParam(value = "url", defaultValue = "") String url,
                                            @RequestParam(value = "autoUpdate", defaultValue = "0") int autoUpdate,
                                            @RequestParam(value = "rgId", defaultValue = "0")  long rgId,
                                            @RequestParam(value = "misId", defaultValue = "") String misId) {
        try {
            Preconditions.checkArgument(rgId > 0,"rgId 必须大于0");
            Preconditions.checkArgument(misId != null && !misId.isEmpty(),"misId 不能为空");
            Preconditions.checkArgument(url != null && !url.isEmpty(),"documentId 不能为空");
            Preconditions.checkArgument(autoUpdate == 0 || autoUpdate == 1, "autoUpdate 只能为0或1");

            // 调用服务修改文档的自动更新设置
            boolean change = kmService.changeRefreshByDocumentId(rgId, url, autoUpdate, misId);
            if (change) {
                return CommonResult.success("修改成功");
            } else {
                return CommonResult.error("修改失败");
            }
        } catch (Exception e) {
            log.error("changeRefreshByDocumentId 执行失败, rgId:{}, misId:{}, url:{}, autoUpdate:{}, 异常原因:{}",
                    rgId, misId, url, autoUpdate, e.getMessage());
            return CommonResult.error(String.format("执行失败,异常原因=%s", e.getMessage()));
        }
    }


    @Autowired
    private DxGroupChatServiceImpl dxGroupChatService;
    @GetMapping("/testGetMsg")
    public Object testGetMsg(@RequestParam(value = "msgId", defaultValue = "0")  long msgId,
                             @RequestParam(value = "chatType", defaultValue = "0")  int chatType) {
        try {
            Object result = dxGroupChatService.testGetMsg(msgId,chatType);
            return CommonResult.success("testGetMsg success",result);
        } catch (Exception e) {
            log.error("testGetMsg 执行失败, 异常原因:{}", e.getMessage());
            return CommonResult.error(String.format("执行失败,异常原因=%s", e.getMessage()));
        }
    }
}
