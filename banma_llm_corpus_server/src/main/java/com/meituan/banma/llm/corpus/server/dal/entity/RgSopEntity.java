package com.meituan.banma.llm.corpus.server.dal.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RgSopEntity {

    private Long id;

    private Long rgId;

    //sop模板内容
    private String sop;

    //sop版本
    private int version;

    //操作时间
    private Timestamp updateTime;

    //操作人misId
    private String misId;
}
