package com.meituan.banma.llm.corpus.server.service;

import java.util.Map;
import java.util.List;

import com.meituan.banma.llm.corpus.api.exception.LlmCorpusException;
import com.meituan.banma.llm.corpus.server.dal.entity.AccessKeyEntity;

/**
 * AccessKey服务接口，负责生成和管理AccessKey
 */
public interface IAccessKeyService {

    /**
     * 生成新的AccessKey
     * 
     * @return 生成的AccessKey
     */
    String generateAccessKey();
    
    /**
     * 保存默认AccessKey到数据库
     * 
     * @param ak 生成的AccessKey
     * @param rgId 值班组ID
     */
    void saveDefaultAccessKey(String ak, Long rgId);
    
    /**
     * 根据rgId查询默认ak
     * 
     * @param rgId 值班组ID
     * @return 默认AccessKey，如果不存在则返回null
     */
    String getDefaultAccessKey(String rgId);
    
    /**
     * 批量清空所有记录的document_id字段
     * 在清空前调用Friday RPC服务删除实际文档，只有删除成功才清空字段
     *
     * @return 操作结果Map，包含总体成功/失败状态和所有失败记录的详细数据
     */
    Map<String, Object> clearDocumentIdByRgIdAndSpaceId();
    
    /**
     * 根据rgId和misId查询AccessKey列表
     * 返回结果包含该值班组的默认AccessKey
     *
     * @param rgId 值班组ID
     * @param misId 用户MIS ID
     * @return AccessKey实体列表
     */
    List<AccessKeyEntity> findAccessKeysByRgIdAndMisId(Long rgId, String misId);
    
    /**
     * 生成并保存自定义AccessKey
     * 
     * @param misId 用户MIS ID
     * @param akName AccessKey名称
     * @param rgIds 值班组ID列表（逗号分隔）
     * @return 生成并保存的AccessKey实体
     */
    AccessKeyEntity generateAndSaveAccessKey(String misId, String akName, String rgIds);
    
    /**
     * 修改AccessKey名称
     * 修改前会验证ak与misId是否匹配，只有匹配才能修改
     *
     * @param ak AccessKey
     * @param misId 用户MIS ID
     * @param newAkName 新的AccessKey名称
     * @return 修改结果，true表示成功，false表示失败
     */
    boolean updateAccessKeyName(String ak, String misId, String newAkName);
    
    /**
     * 根据ak和misId删除AccessKey
     * 删除前会验证ak与misId是否匹配，只有匹配才能删除
     * 注意：默认AccessKey不允许删除
     *
     * @param ak AccessKey
     * @param misId 用户MIS ID
     * @return 删除结果，true表示成功，false表示失败
     */
    boolean deleteAccessKey(String ak, String misId) throws LlmCorpusException;

    /**
     * 获取指定值班组的最新修改人MIS ID
     * 如果找不到则返回null
     *
     * @param rgId 值班组ID
     * @return 最新修改人MIS ID，如果找不到返回null
     */
    String getLatestMisIdByRgId(Long rgId);
} 