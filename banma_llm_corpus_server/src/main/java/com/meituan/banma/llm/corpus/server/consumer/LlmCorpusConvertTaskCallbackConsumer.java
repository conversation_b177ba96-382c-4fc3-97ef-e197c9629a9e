package com.meituan.banma.llm.corpus.server.consumer;

import com.alibaba.fastjson.JSON;
import com.meituan.banma.llm.corpus.server.common.domain.dto.LlmCorpusConvertTaskMessageDTO;
import com.meituan.banma.llm.corpus.server.utils.DegradeUtils;
import com.meituan.mafka.client.consumer.ConsumerConstants;
import com.meituan.mafka.client.consumer.IConsumerProcessor;
import com.meituan.mafka.client.consumer.IMessageListener;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.MafkaClient;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.mafka.client.message.MessagetContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.Properties;

@Slf4j
@Component
public class LlmCorpusConvertTaskCallbackConsumer {
    @Autowired
    @Qualifier("llmCorpusConvertTaskCallbackProcessorImpl")
    private MessageProcessor<LlmCorpusConvertTaskMessageDTO> messageProcessor;

    /**
     * 注意：服务端对单ip创建相同主题相同队列的消费者实例数有限制，超过100个拒绝创建.
     */
    private IConsumerProcessor consumer;


    @PostConstruct
    public void init() throws Exception {
        log.info("#LlmCorpusConvertTaskCallbackConsumer.init#start");
        Properties properties = new Properties();
        properties.setProperty(ConsumerConstants.MafkaBGNamespace, "com.sankuai.mafka.castle.daojialvyue");
        properties.setProperty(ConsumerConstants.MafkaClientAppkey, "com.sankuai.deliverypaotui.llm.corpus");
        properties.setProperty(ConsumerConstants.SubscribeGroup, "llm_corpus_convert_task_callback");

        consumer = MafkaClient.buildConsumerFactory(properties, "llm_corpus_convert_task_topic");

        consumer.recvMessageWithParallel(String.class, new IMessageListener() {
            @Override
            public ConsumeStatus recvMessage(MafkaMessage message, MessagetContext context) {
                try {
                    if (DegradeUtils.degradeLlmCorpusConvertTaskCallbackMessage()){
                        log.info("#LlmCorpusConvertTaskCallbackConsumer.recvMessage#degrade,message:{}",message);
                        return ConsumeStatus.CONSUME_SUCCESS;
                    }
                    if (message.getBody() == null) {
                        return ConsumeStatus.CONSUME_SUCCESS;
                    }
                    LlmCorpusConvertTaskMessageDTO llmCorpusConvertTaskMessageDTO = JSON.parseObject(message.getBody().toString(), LlmCorpusConvertTaskMessageDTO.class);
                    if (llmCorpusConvertTaskMessageDTO == null) {
                        log.warn("#LlmCorpusConvertTaskCallbackConsumer.recvMessage#message:{}", message);
                        return ConsumeStatus.CONSUME_SUCCESS;
                    }
                    messageProcessor.process(llmCorpusConvertTaskMessageDTO);
                } catch (Exception e) {
                    log.error("#LlmCorpusConvertTaskCallbackConsumer.recvMessage#error,message:{}", message, e);
                }
                return ConsumeStatus.CONSUME_SUCCESS;
            }
        });
        log.info("#LlmCorpusConvertTaskCallbackConsumer.init#end");
    }

    @PreDestroy
    public void destroy() {
        try {
            if (consumer != null) {
                consumer.close();
                log.info("#LlmCorpusConvertTaskCallbackConsumer.destroy#end");
            }
        } catch (Exception e) {
            log.error("#LlmCorpusConvertTaskCallbackConsumer.destroy#error", e);
        }
    }
}