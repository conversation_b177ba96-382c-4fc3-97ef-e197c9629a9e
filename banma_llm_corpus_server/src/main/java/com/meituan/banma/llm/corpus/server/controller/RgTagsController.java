package com.meituan.banma.llm.corpus.server.controller;

import com.google.common.base.Preconditions;
import com.meituan.banma.llm.corpus.server.common.CommonResult;
import com.meituan.banma.llm.corpus.server.controller.request.RgTagsRequest;
import com.meituan.banma.llm.corpus.server.dal.entity.RgTagsEntity;
import com.meituan.banma.llm.corpus.server.service.IRgTagsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 语料标签控制器
 * 提供标签相关的接口
 */
@Slf4j
@RestController
@RequestMapping("/rgTags")
public class RgTagsController {

    @Autowired
    private IRgTagsService rgTagsService;

    /**
     * 新增标签
     *
     * @param request 标签请求参数
     * @return 创建的标签信息
     */
    @PostMapping("/addTag")
    public Object addTag(@RequestBody RgTagsRequest request) {
        try {
            // 参数校验
            Preconditions.checkArgument(request != null, "请求参数不能为空");
            Preconditions.checkArgument(request.getRgId() != null, "值班组ID不能为空");
            Preconditions.checkArgument(request.getRgId() > 0, "值班组ID必须大于0");
            Preconditions.checkArgument(StringUtils.isNotBlank(request.getTagName()), "标签名不能为空");
            Preconditions.checkArgument(StringUtils.isNotBlank(request.getTagDesc()), "标签描述不能为空");
            Preconditions.checkArgument(StringUtils.isNotBlank(request.getMisId()), "操作人misID不能为空");
            
            log.info("addTag# 开始新增标签, rgId:{}, tagName:{}, tagDesc:{}, misId:{}", 
                    request.getRgId(), request.getTagName(), request.getTagDesc(), request.getMisId());
            
            RgTagsEntity result = rgTagsService.addTag(
                    request.getRgId(), 
                    request.getTagName(), 
                    request.getTagDesc(), 
                    request.getMisId()
            );
            
            if (result != null) {
                log.info("addTag# 新增标签成功, rgId:{}, tagName:{}, misId:{}, tagId:{}", 
                        request.getRgId(), request.getTagName(), request.getMisId(), result.getId());
                return CommonResult.success(result);
            } else {
                log.warn("addTag# 新增标签失败, rgId:{}, tagName:{}, misId:{}",
                        request.getRgId(), request.getTagName(), request.getMisId());
                return CommonResult.error("新增标签失败");
            }
        } catch (Exception e) {
            log.error("addTag# 新增标签异常, rgId:{}, tagName:{}, misId:{}, 异常原因:{}", 
                    request.getRgId(), request.getTagName(), request.getMisId(), e.getMessage(), e);
            return CommonResult.error(String.format("新增标签失败，异常原因=%s", e.getMessage()));
        }
    }

    /**
     * 根据rgId查询标签列表
     *
     * @param rgId 值班组ID
     * @return 标签列表
     */
    @GetMapping("/listTags")
    public Object getTagsByRgId(@RequestParam("rgId") Long rgId) {
        try {
            Preconditions.checkArgument(rgId != null && rgId > 0, "rgId不能为空且必须大于0");
            
            List<RgTagsEntity> tags = rgTagsService.getTagsByRgId(rgId);
            log.info("getTagsByRgId# 查询标签列表成功, rgId:{}, 结果数量:{}", rgId, tags.size());
            return CommonResult.success(tags);
        } catch (Exception e) {
            log.error("getTagsByRgId# 查询标签列表异常, rgId:{}, 异常原因:{}", rgId, e.getMessage(), e);
            return CommonResult.error(String.format("查询标签列表失败，异常原因=%s", e.getMessage()));
        }
    }

    /**
     * 根据id删除标签
     *
     * @param id 标签ID
     * @return 删除结果
     */
    @PostMapping("/deleteTag")
    public Object deleteTagById(@RequestParam("id") Long id) {
        try {
            Preconditions.checkArgument(id != null && id > 0, "id不能为空且必须大于0");
            
            boolean result = rgTagsService.deleteTagById(id);
            if (result) {
                log.info("deleteTagById# 删除标签成功, id:{}", id);
                
                Map<String, Object> data = new HashMap<>();
                data.put("success", true);
                data.put("message", "删除成功");
                return CommonResult.success(data);
            } else {
                log.warn("deleteTagById# 删除标签失败, id:{}", id);
                return CommonResult.error("删除标签失败，标签可能不存在");
            }
        } catch (Exception e) {
            log.error("deleteTagById# 删除标签异常, id:{}, 异常原因:{}", id, e.getMessage(), e);
            return CommonResult.error(String.format("删除标签失败，异常原因=%s", e.getMessage()));
        }
    }

    /**
     * 根据rgId和tagName删除标签
     *
     * @param rgId 值班组ID
     * @param tagName 标签名
     * @return 删除结果
     */
    @PostMapping("/deleteTagByRgIdAndTagName")
    public Object deleteTagByRgIdAndTagName(@RequestParam("rgId") Long rgId,
                                            @RequestParam("tagName") String tagName) {
        try {
            Preconditions.checkArgument(rgId != null && rgId > 0, "rgId不能为空且必须大于0");
            Preconditions.checkArgument(StringUtils.isNotBlank(tagName), "tagName不能为空");
            
            boolean result = rgTagsService.deleteTagByRgIdAndTagName(rgId, tagName);
            if (result) {
                log.info("deleteTagByRgIdAndTagName# 删除标签成功, rgId:{}, tagName:{}", rgId, tagName);
                
                Map<String, Object> data = new HashMap<>();
                data.put("success", true);
                data.put("message", "删除成功");
                return CommonResult.success(data);
            } else {
                log.warn("deleteTagByRgIdAndTagName# 删除标签失败, rgId:{}, tagName:{}", rgId, tagName);
                return CommonResult.error("删除标签失败，标签可能不存在");
            }
        } catch (Exception e) {
            log.error("deleteTagByRgIdAndTagName# 删除标签异常, rgId:{}, tagName:{}, 异常原因:{}", 
                    rgId, tagName, e.getMessage(), e);
            return CommonResult.error(String.format("删除标签失败，异常原因=%s", e.getMessage()));
        }
    }

    /**
     * 修改标签信息（仅支持修改标签描述）
     *
     * @param id 标签ID
     * @param tagDesc 新的标签描述
     * @param misId 操作人misID
     * @return 修改结果
     */
    @PostMapping("/updateTag")
    public Object updateTag(@RequestParam("id") Long id,
                            @RequestParam(value = "tagDesc", required = false) String tagDesc,
                            @RequestParam("misId") String misId) {
        try {
            Preconditions.checkArgument(id != null && id > 0, "id不能为空且必须大于0");
            Preconditions.checkArgument(StringUtils.isNotBlank(misId), "操作人misID不能为空");
            
            boolean result = rgTagsService.updateTagDesc(id, tagDesc, misId);
            if (result) {
                log.info("updateTag# 修改标签描述成功, id:{}, tagDesc:{}, misId:{}", id, tagDesc, misId);
                
                Map<String, Object> data = new HashMap<>();
                data.put("success", true);
                data.put("message", "修改成功");
                return CommonResult.success(data);
            } else {
                log.warn("updateTag# 修改标签描述失败, id:{}, tagDesc:{}, misId:{}", id, tagDesc, misId);
                return CommonResult.error("修改标签描述失败，标签可能不存在");
            }
        } catch (Exception e) {
            log.error("updateTag# 修改标签描述异常, id:{}, tagDesc:{}, misId:{}, 异常原因:{}", id, tagDesc, misId, e.getMessage(), e);
            return CommonResult.error(String.format("修改标签描述失败，异常原因=%s", e.getMessage()));
        }
    }

    /**
     * 根据id列表批量查询标签
     *
     * @param ids 标签ID列表
     * @return 标签列表
     */
    @GetMapping("/getTagsByIds")
    public Object getTagsByIds(@RequestParam("ids") List<Long> ids) {
        try {
            Preconditions.checkArgument(ids != null && !ids.isEmpty(), "ids不能为空");
            
            // 校验每个id的有效性
            for (Long id : ids) {
                Preconditions.checkArgument(id != null && id > 0, "id必须大于0，无效id: " + id);
            }
            
            List<RgTagsEntity> tags = rgTagsService.getTagsByIds(ids);
            if (tags != null && !tags.isEmpty()) {
                log.info("getTagsByIds# 批量查询标签成功, ids:{}, 查询到数量:{}", ids, tags.size());
                return CommonResult.success(tags);
            } else {
                log.warn("getTagsByIds# 未查询到任何标签, ids:{}", ids);
                return CommonResult.success(new ArrayList<>());
            }
        } catch (Exception e) {
            log.error("getTagsByIds# 批量查询标签异常, ids:{}, 异常原因:{}", ids, e.getMessage(), e);
            return CommonResult.error(String.format("批量查询标签失败，异常原因=%s", e.getMessage()));
        }
    }
} 