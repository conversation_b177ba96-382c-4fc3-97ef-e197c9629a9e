package com.meituan.banma.llm.corpus.server.controller;

import com.google.common.base.Preconditions;
import com.meituan.banma.llm.corpus.api.exception.LlmCorpusException;
import com.meituan.banma.llm.corpus.server.common.CommonResult;
import com.meituan.banma.llm.corpus.server.controller.request.monitoring.MonitoringStatisticsRequest;
import com.meituan.banma.llm.corpus.server.service.IInquiryMonitoringService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Map;

/**
 * 问询监控统计数据Controller
 */
@Slf4j
@RestController
@RequestMapping("/inquiry/statistics")
public class InquiryStatisticsController {

    @Resource
    private IInquiryMonitoringService inquiryMonitoringService;

    /**
     * 查询统计数据
     *
     * @param request 包含时间范围的请求参数
     * @return 统计数据
     */
    @PostMapping("/queryStatistics")
    public CommonResult queryStatistics(@RequestBody MonitoringStatisticsRequest request) {
        try {
            Preconditions.checkArgument(request != null, "请求参数不能为空");
            Preconditions.checkArgument(request.getStartTime() != null, "开始时间不能为空");
            Preconditions.checkArgument(request.getEndTime() != null, "结束时间不能为空");
            Preconditions.checkArgument(request.getMonitorGroupIds() != null, "监控组id不能为空");
            Preconditions.checkArgument(request.getStartTime() <= request.getEndTime(), "开始时间不能大于结束时间");

            // 调用服务查询统计数据
            Map<String, Object> statistics = inquiryMonitoringService.getInquiryStatistics(
                    request.getStartTime(), request.getEndTime(), request.getMonitorGroupIds());

            return CommonResult.success("查询成功", statistics);
        } catch (IllegalArgumentException e) {
            log.warn("查询统计数据参数校验失败: request={}, error={}", request, e.getMessage());
            return CommonResult.error(e.getMessage());
        } catch (LlmCorpusException e) {
            log.warn("#queryStatistics 查询统计数据业务异常: request={}, code={}, message={}", 
                    request, e.getCode(), e.getMessage());
            return CommonResult.error(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("查询统计数据异常: request={}, error={}", request, e.getMessage(), e);
            return CommonResult.error(String.format("查询统计数据异常，原因=%s", e.getMessage()));
        }
    }
} 