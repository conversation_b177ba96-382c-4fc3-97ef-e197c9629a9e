package com.meituan.banma.llm.corpus.server.common.config;

import com.sankuai.dxenterprise.open.gateway.service.dx.api.DxService;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2024/12/26
 */

@Configuration
public class DxServiceConfig {

    @Bean
    public DxService dxService() {
        return new DxService();
    }
}
