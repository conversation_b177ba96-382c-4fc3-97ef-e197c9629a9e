package com.meituan.banma.llm.corpus.server.service.impl;

import com.alibaba.fastjson.JSON;
import com.cip.crane.client.spring.annotation.Crane;
import com.google.common.collect.Lists;
import com.meituan.banma.llm.corpus.api.constants.BizCode;
import com.meituan.banma.llm.corpus.api.exception.LlmCorpusException;
import com.meituan.banma.llm.corpus.server.common.constants.Constant;
import com.meituan.banma.llm.corpus.server.common.constants.enums.ConvertTaskPlatformId;
import com.meituan.banma.llm.corpus.server.common.constants.enums.ModelOutputTaskStatusEnum;
import com.meituan.banma.llm.corpus.server.common.constants.enums.MonitoringTimeRangeTypeEnum;
import com.meituan.banma.llm.corpus.server.common.domain.dto.CreateQuestionSummaryLlmTaskParam;
import com.meituan.banma.llm.corpus.server.common.domain.dto.DxChatMessageRecord;
import com.meituan.banma.llm.corpus.server.common.domain.dto.LlmDxGroupMonitoringTaskMessageDTO;
import com.meituan.banma.llm.corpus.server.common.domain.dto.LlmDxGroupMonitoringTaskResultDTO;
import com.meituan.banma.llm.corpus.server.common.domain.dto.MonitoringGroupConfig;
import com.meituan.banma.llm.corpus.server.common.domain.dto.MonitoringTaskParam;
import com.meituan.banma.llm.corpus.server.common.domain.dto.QuestionType;
import com.meituan.banma.llm.corpus.server.dal.entity.InquiryMonitoringEntity;
import com.meituan.banma.llm.corpus.server.dal.entity.ModelOutputTaskEntity;
import com.meituan.banma.llm.corpus.server.dal.entity.MonitoringGroupEntity;
import com.meituan.banma.llm.corpus.server.dal.entity.QuestionTypeEntity;
import com.meituan.banma.llm.corpus.server.dal.mapper.InquiryMonitoringMapper;
import com.meituan.banma.llm.corpus.server.dal.mapper.ModelOutputMapper;
import com.meituan.banma.llm.corpus.server.dal.mapper.MonitoringGroupMapper;
import com.meituan.banma.llm.corpus.server.dal.mapper.QuestionTypeMapper;
import com.meituan.banma.llm.corpus.server.service.IDxMonitoringService;
import com.meituan.banma.llm.corpus.server.service.IFridayService;
import com.meituan.banma.llm.corpus.server.utils.AsyncTaskUtils;
import com.meituan.banma.llm.corpus.server.utils.DatetimeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;
import javax.annotation.Resource;

@Slf4j
@Service
public class DxMonitoringServiceImpl implements IDxMonitoringService {

    @Autowired
    private DxGroupChatServiceImpl dxGroupChatService;

    @Autowired
    private IFridayService fridayService;

    @Autowired
    private ModelOutputMapper modelOutputMapper;

    @Autowired
    private InquiryMonitoringMapper inquiryMonitoringMapper;
    
    @Resource
    private MonitoringGroupMapper monitoringGroupMapper;
    
    @Resource
    private QuestionTypeMapper questionTypeMapper;

    /**
     * 这里是一个任务，查询并调度配置好的监控项目
     * 1. 查询监控配置
     * 2. 获取指定群聊聊天记录数据
     * 3. 增强消息（获取发言人名称、mis、组织）
     * 4. 构建大模型输入消息
     * 5. 调用大模型
     * 6. 解析大模型输出
     * 7. 查询数据库去除重复
     * 8. 写入数据库
     */
    @Crane("dx-question-monitor-task")
    @Override
    public void dxMonitoringTask() {
        // 从数据库中获取状态正常(status=0)的监控组
        List<MonitoringGroupEntity> monitoringGroupEntities = monitoringGroupMapper.selectByStatus(0);
        if (CollectionUtils.isEmpty(monitoringGroupEntities)) {
            log.warn("#dxMonitoringTask#warn, 数据库中没有状态正常的监控组配置");
            return;
        }

        // 获取所有监控组ID
        List<Long> monitoringGroupIds = monitoringGroupEntities.stream()
                .map(MonitoringGroupEntity::getMonitoringGroupId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        
        // 批量查询所有监控组的问题类型
        if (!monitoringGroupIds.isEmpty()) {
            List<QuestionTypeEntity> allQuestionTypes = questionTypeMapper.selectByMonitoringGroupIds(monitoringGroupIds);
            
            // 按监控组ID分组
            Map<Long, List<QuestionTypeEntity>> questionTypesMap = new HashMap<>();
            for (QuestionTypeEntity questionType : allQuestionTypes) {
                if (questionType.getMonitoringGroupId() != null) {
                    questionTypesMap.computeIfAbsent(questionType.getMonitoringGroupId(), k -> new ArrayList<>())
                            .add(questionType);
                }
            }
            
            // 将问题类型设置到对应的监控组实体中
            for (MonitoringGroupEntity entity : monitoringGroupEntities) {
                if (entity.getMonitoringGroupId() != null) {
                    List<QuestionTypeEntity> questionTypes = questionTypesMap.getOrDefault(entity.getMonitoringGroupId(), Collections.emptyList());
                    entity.setQuestionTypes(questionTypes);
                }
            }
        }

        // 将实体转换为配置格式，确保与配置中心获取的格式一致
        List<MonitoringGroupConfig> monitoringGroupConfigList = convertEntitiesToConfigs(monitoringGroupEntities);
        if (CollectionUtils.isEmpty(monitoringGroupConfigList)) {
            log.warn("#dxMonitoringTask#warn, monitoringGroupConfigList is empty");
            return;
        }
        
        for (MonitoringGroupConfig monitoringGroupConfig : monitoringGroupConfigList) {
            List<String> taskIds = processSingleMonitorGroup(monitoringGroupConfig);
            log.info("#dxMonitoringTask#running,monitoringGroupConfig:{},taskIds:{}",monitoringGroupConfig, taskIds);
        }
    }

    /**
     * 将监控组实体列表转换为监控组配置列表
     * 
     * @param entities 监控组实体列表
     * @return 监控组配置列表
     */
    private List<MonitoringGroupConfig> convertEntitiesToConfigs(List<MonitoringGroupEntity> entities) {
        if (CollectionUtils.isEmpty(entities)) {
            return Collections.emptyList();
        }
        
        List<MonitoringGroupConfig> configList = new ArrayList<>(entities.size());
        
        for (MonitoringGroupEntity entity : entities) {
            if (entity == null) {
                continue;
            }

            List<QuestionType> questionTypeList = new ArrayList<>();
            try {
                // 直接使用实体中已设置的问题类型
                List<QuestionTypeEntity> questionTypeEntities = entity.getQuestionTypes();
                if (questionTypeEntities != null) {
                    // 转换为QuestionType对象
                    for (QuestionTypeEntity typeEntity : questionTypeEntities) {
                        QuestionType questionType = new QuestionType();
                        questionType.setQuestionTypeId(typeEntity.getQuestionTypeId().longValue());
                        questionType.setQuestionTypeName(typeEntity.getTypeName());
                        questionType.setQuestionTypeDesc(typeEntity.getTypeDesc());
                        questionTypeList.add(questionType);
                    }
                }
            } catch (Exception e) {
                log.warn("#convertEntitiesToConfigs#warn, 处理问题类型失败: {}", e.getMessage());
            }
            
            MonitoringGroupConfig config = MonitoringGroupConfig.builder()
                    .monitoringGroupId(entity.getMonitoringGroupId())
                    .monitoringGroupName(entity.getMonitoringGroupName())
                    .monitoringGroupDesc(entity.getMonitoringGroupDesc())
                    .monitoringGroupOwner(Arrays.asList(entity.getMonitoringGroupOwnerArray()))
                    .dxGroupIds(Arrays.asList(entity.getDxGroupIdArray()))
                    .monitoredOrgIds(Arrays.asList(entity.getMonitoredOrgIdArray()))
                    .monitoredMisIds(Arrays.asList(entity.getMonitoredMisIdArray()))
                    .keywords(Arrays.asList(entity.getKeywordsArray()))
                    .questionTypes(questionTypeList)
                    .monitoringTimeRangeType(entity.getMonitoringTimeRangeType())
                    .build();
            
            configList.add(config);
        }
        
        return configList;
    }

    private List<String> processSingleMonitorGroup(MonitoringGroupConfig monitoringGroupConfig) {
        if(monitoringGroupConfig == null) {
            log.warn("#processSingleMonitorGroup#warn, monitoringGroupConfig is empty,monitoringGroupConfig:{}",monitoringGroupConfig);
            return null;
        }
        List<Long> dxGroupIdList = monitoringGroupConfig.getDxGroupIds();
        if (CollectionUtils.isEmpty(dxGroupIdList)) {
            log.warn("#processSingleMonitorGroup#warn, dxGroupIdList is empty,monitoringGroupConfig:{}",monitoringGroupConfig);
            return null;
        }
        List<String> taskIdList = Lists.newArrayList();

        // 默认获取过去7天的所有消息 startTime=7天前00:00 , endTime = 昨天23:59:59
        Long startTime = DatetimeUtils.getLastSevenDaysStartTime();
        Long endTime = DatetimeUtils.getLastSevenDaysEndTime();
        if (monitoringGroupConfig.getMonitoringTimeRangeType() == MonitoringTimeRangeTypeEnum.LAST_FOURTEEN_DAYS.getCode()) {
            startTime = DatetimeUtils.getLastFourteenDaysStartTime();
            endTime = DatetimeUtils.getLastFourteenDaysEndTime();
        } else if (monitoringGroupConfig.getMonitoringTimeRangeType() == MonitoringTimeRangeTypeEnum.LAST_THIRTY_DAYS.getCode()) {
            startTime = DatetimeUtils.getLastThirtyDaysStartTime();
            endTime = DatetimeUtils.getLastThirtyDaysEndTime();
        }

        for (Long dxGroupId : dxGroupIdList) {
            String taskId = processSingleMonitoringTask(MonitoringTaskParam.builder()
                    .dxGroupId(dxGroupId)
                    .startTime(startTime)
                    .endTime(endTime)
                    .monitoringGroupConfig(monitoringGroupConfig)
                    .build());
            if (StringUtils.isNotBlank(taskId)) {
                taskIdList.add(taskId);
            }else {
                log.warn("#processSingleMonitorGroup#warn, taskId is null,dxGroupId:{}",dxGroupId);
            }
        }
        return taskIdList;
    }
    private String processSingleMonitoringTask(MonitoringTaskParam monitoringTaskParam) {
        if (monitoringTaskParam == null || monitoringTaskParam.getDxGroupId() == null || monitoringTaskParam.getStartTime() == null || monitoringTaskParam.getEndTime() == null) {
            log.warn("#processSingleMonitoringTask#warn, param is null,monitoringTaskParam:{}",monitoringTaskParam);
            return null;
        }
        List<DxChatMessageRecord> dxChatMessageRecords = dxGroupChatService.getTtChatInfoByTime(monitoringTaskParam.getDxGroupId(), monitoringTaskParam.getStartTime(), 
                monitoringTaskParam.getEndTime());
        if (CollectionUtils.isEmpty(dxChatMessageRecords)) {
            log.warn("#processSingleMonitoringTask#warn, dxChatMessageRecords is empty,dxGroupId:{},startTime:{},endTime:{}",monitoringTaskParam.getDxGroupId(), monitoringTaskParam.getStartTime(),
                    monitoringTaskParam.getEndTime());
            return null;
        }
        MonitoringGroupConfig monitoringGroupConfig = monitoringTaskParam.getMonitoringGroupConfig();
        List<String> importantOrgList = monitoringGroupConfig.getMonitoredOrgIds();
        List<String> importantMisList = monitoringGroupConfig.getMonitoredMisIds();
        // 扩充数据
        for (DxChatMessageRecord dxChatMessageRecord : dxChatMessageRecords) {
            if (dxChatMessageRecord == null) {
                log.warn("dxChatMessageRecord is null,dxChatMessageRecords:{}", dxChatMessageRecords);
                continue;
            }
            String fromMis = dxChatMessageRecord.getFromMis();
            String userOrgId = dxChatMessageRecord.getUserOrgId();

            if (StringUtils.isBlank(fromMis)) {
                log.warn("#processSingleMonitoringTask#warn, MIS或者orgId为空,dxChatMessageRecord:{}",dxChatMessageRecord);
                continue;
            }
            if (CollectionUtils.isNotEmpty(importantMisList) && importantMisList.contains(fromMis)) {
                log.info("#processSingleMonitoringTask#info, 命中重点监控mis,dxChatMessageRecord:{}",dxChatMessageRecord);
                dxChatMessageRecord.setMarkAsImportantMessage(true);
            }
            if (CollectionUtils.isNotEmpty(importantOrgList) && importantOrgList.contains(userOrgId)) {
                log.info("#processSingleMonitoringTask#info, 命中重点监控org,dxChatMessageRecord:{}",dxChatMessageRecord);
                dxChatMessageRecord.setMarkAsImportantMessage(true);
            }
        }
        // 构建消息
        LlmDxGroupMonitoringTaskMessageDTO messageDTO = buildLlmTaskMessage(monitoringGroupConfig, dxChatMessageRecords);
        if (messageDTO == null) {
            log.warn("#processSingleMonitoringTask#warn, messageDTO is null,monitoringTaskParam:{}",monitoringTaskParam);
            return null;
        }
        String taskId = createQuestionSummaryLlmTask(CreateQuestionSummaryLlmTaskParam.builder()
                .monitoringGroupId(monitoringGroupConfig.getMonitoringGroupId())
                .monitoringGroupOwner(monitoringGroupConfig.getMonitoringGroupOwner())
                .dxGroupId(monitoringTaskParam.getDxGroupId())
                .startTime(monitoringTaskParam.getStartTime())
                .endTime(monitoringTaskParam.getEndTime())
                .messageDTO(messageDTO)
                .build());

        if (StringUtils.isBlank(taskId)) {
            log.warn("#processSingleMonitoringTask#warn, taskId is null,monitoringTaskParam:{}",monitoringTaskParam);
            return null;
        }
        return taskId;
    }

    private LlmDxGroupMonitoringTaskMessageDTO buildLlmTaskMessage(MonitoringGroupConfig monitoringGroupConfig, List<DxChatMessageRecord> dxChatMessageRecords) {
        LlmDxGroupMonitoringTaskMessageDTO messageDTO = LlmDxGroupMonitoringTaskMessageDTO.builder()
                .importantMisIdList(monitoringGroupConfig.getMonitoredMisIds())
                .importantOrgIdList(monitoringGroupConfig.getMonitoredOrgIds())
                .importantKeyWordList(monitoringGroupConfig.getKeywords())
                .questionTypeList(monitoringGroupConfig.getQuestionTypes())
                .chatMessageRecordList(dxChatMessageRecords)
                .build();
        return messageDTO;
    }

    public String createQuestionSummaryLlmTask(CreateQuestionSummaryLlmTaskParam param){
        if (param == null || param.getMessageDTO() == null) {
            log.warn("#createQuestionSummaryLlmTask#warn, messageDTO is null");
            return null;
        }
        String taskId = UUID.randomUUID().toString();
        ExecutorService executor = AsyncTaskUtils.getDxMonitoringThreadPool();
        String executorMis;
        if (CollectionUtils.isNotEmpty(param.getMonitoringGroupOwner())){
            executorMis = param.getMonitoringGroupOwner().get(0);
        } else {
            executorMis = "daili07";
        }
        initTaskRecord(taskId, param);
        executor.submit(() -> {
            try{
                LlmDxGroupMonitoringTaskResultDTO result = fridayService.summarizeDxGroupChatQuestions(executorMis, param.getMessageDTO());
                if (result == null) {
                    log.error("#createQuestionSummaryLlmTask#error, result is null,param:{}",param);
                    throw LlmCorpusException.buildWithMsg(BizCode.DX_GROUP_QUESTION_SUMMARY_ERROR,"总结大象群问题失败,返回结果为空");
                }
                if (CollectionUtils.isEmpty(result.getSummaryQuestionList())){
                    log.error("#createQuestionSummaryLlmTask#error, result is null,param:{}",param);
                    throw LlmCorpusException.buildWithMsg(BizCode.DX_GROUP_QUESTION_SUMMARY_ERROR,"总结大象群问题失败,返回结果列表为空");
                }
                llmTaskSuccess(taskId, result, param);
            } catch (LlmCorpusException e) {
                log.error("#createQuestionSummaryLlmTask#error, param:{}",param,e);
                llmTaskFail(taskId, e.getMessage(), param);
            } catch (Exception e){
                log.error("#createQuestionSummaryLlmTask#internal_error, param:{}",param,e);
                llmTaskFail(taskId, e.getMessage(), param);
            }
        });
        return taskId;
    }
    private void initTaskRecord(String taskId, CreateQuestionSummaryLlmTaskParam param) {
        ModelOutputTaskEntity entity = new ModelOutputTaskEntity();
        entity.setTaskId(taskId);
        entity.setTaskStatus(ModelOutputTaskStatusEnum.PROCESSING.getCode());
        entity.setTicketId(Constant.MONITORING_ID_PREFIX + param.getMonitoringGroupId() + "_" + param.getDxGroupId() + "_" + param.getStartTime() + "_" + param.getEndTime());
//        entity.setCreatorDxId(param.getCreatorDxUserId());
        entity.setDxGroupId(param.getDxGroupId());
        entity.setCreatorMisId("daili07");
        entity.setCreatorUserName("daili07");
        
        if (CollectionUtils.isNotEmpty(param.getMonitoringGroupOwner())){
            entity.setCreatorMisId(param.getMonitoringGroupOwner().get(0));
            entity.setCreatorUserName(param.getMonitoringGroupOwner().get(0));
        }

        entity.setRgId(-1L);
        entity.setTitle(String.format("【%d】监控组【%d】大象群问题总结-%s~%s",
                param.getMonitoringGroupId(),
                param.getDxGroupId(),
                DatetimeUtils.timestampToDateStr(param.getStartTime()),
                DatetimeUtils.timestampToDateStr(param.getEndTime())));
        entity.setContent("");
        entity.setPlatformId(ConvertTaskPlatformId.DX_MONITORING.getCode());
        entity.setCreatorDxId(-1L);
        entity.setTagsIds("");
        modelOutputMapper.insertModelOutputTask(entity);
    }
    public void llmTaskSuccess(String taskId, LlmDxGroupMonitoringTaskResultDTO resultDTO,CreateQuestionSummaryLlmTaskParam param) {
        if (StringUtils.isBlank(taskId) || resultDTO == null) {
            log.warn("#llmTaskSuccess#warn, param is null");
            return;
        }
        if (CollectionUtils.isEmpty(resultDTO.getSummaryQuestionList())) {
            log.warn("#llmTaskSuccess#warn, resultDTO.getSummaryQuestionList() is empty");
            return;
        }
        
        // 获取原始消息记录，用于填充rawQuestionContent
        List<DxChatMessageRecord> chatMessageRecords = null;
        if (param != null && param.getMessageDTO() != null) {
            chatMessageRecords = param.getMessageDTO().getChatMessageRecordList();
        }
        
        // 插入识别到的问题
        List<LlmDxGroupMonitoringTaskResultDTO.LlmDxGroupMonitoringTaskResultItem> resultItemList = resultDTO.getSummaryQuestionList();
        List<InquiryMonitoringEntity> inquiryMonitoringEntityList = Lists.newArrayList();
        for (LlmDxGroupMonitoringTaskResultDTO.LlmDxGroupMonitoringTaskResultItem resultItem : resultItemList) {
            if (resultItem == null) {
                log.warn("#llmTaskSuccess#warn, resultItem is null,resultItemList:{}",resultItemList);
                continue;
            }
            
            // 根据消息ID查找原始消息内容
            if (chatMessageRecords != null && !chatMessageRecords.isEmpty() && resultItem.getQuestionFromMsgId() != null) {
                // 查找匹配的消息记录
                for (DxChatMessageRecord record : chatMessageRecords) {
                    if (record != null && 
                            record.getMsgId() == resultItem.getQuestionFromMsgId()) {
                        // 找到匹配的消息，设置原始消息内容
                        String rawContent = "";
                        if (record.getMessage() != null) {
                            if (record.getMessage().containsKey("text")) {
                                rawContent = record.getMessage().getString("text");
                            } else if (record.getMessage().containsKey("imageUrl")) {
                                rawContent = "[图片消息]" + record.getMessage().getString("imageUrl");
                            } else {
                                rawContent = record.getMessage().toJSONString();
                            }
                        }
                        
                        // 更新resultItem的原始消息内容
                        resultItem.setRawQuestionContent(rawContent);
                        log.info("#llmTaskSuccess#info, 填充原始消息内容成功, msgId:{}, rawContent:{}", resultItem.getQuestionFromMsgId(), rawContent);
                        break;
                    }
                }
            }
            
            inquiryMonitoringEntityList.add(buildInquiryMonitoringEntity(resultItem, param));
        }
        int res = inquiryMonitoringMapper.batchInsert(inquiryMonitoringEntityList);
        if (res != inquiryMonitoringEntityList.size()) {
            log.warn("#llmTaskSuccess#error, 影响条数不一致,res:{},inquiryMonitoringEntityList:{}", res, inquiryMonitoringEntityList);
        }
        if (res <= 0){
            log.warn("#llmTaskSuccess#warn, 插入数据库失败,res:{},inquiryMonitoringEntityList:{}", res, inquiryMonitoringEntityList);
        }
        // 更新任务
        ModelOutputTaskEntity entity = new ModelOutputTaskEntity();
        try {
            entity.setTaskId(taskId);
            entity.setTaskStatus(ModelOutputTaskStatusEnum.SUCCESS.getCode());
            entity.setPlatformId(ConvertTaskPlatformId.DX_MONITORING.getCode());
            entity.setTitle(String.format("【%d】监控组【%d】大象群问题总结-%s~%s",
                    param.getMonitoringGroupId(),
                    param.getDxGroupId(),
                    DatetimeUtils.timestampToDateStr(param.getStartTime()),
                    DatetimeUtils.timestampToDateStr(param.getEndTime())));
            entity.setContent(JSON.toJSONString(resultDTO));
            modelOutputMapper.updateModelOutputTaskStatusByTaskId(entity);
        } catch (Exception e) {
            log.warn("#llmTaskSuccess#error,更新任务状态失败，entity:{}", entity,e);
        }
    }
    private InquiryMonitoringEntity buildInquiryMonitoringEntity(LlmDxGroupMonitoringTaskResultDTO.LlmDxGroupMonitoringTaskResultItem resultItem, CreateQuestionSummaryLlmTaskParam param){
        if (resultItem == null || param == null) {
            return null;
        }
        InquiryMonitoringEntity entity = InquiryMonitoringEntity.builder()
                .monitorGroupId(param.getMonitoringGroupId())
                .fromMessageId(resultItem.getQuestionFromMsgId())
                .questionerMisId(resultItem.getQuestionFromMisId())
                .messageCts(resultItem.getQuestionFromMsgCts())
                .questionerOrgId(resultItem.getQuestionFromOrgId())
                .dxGroupId(param.getDxGroupId())
                .rawQuestionMsg(resultItem.getRawQuestionContent())
                .summarizedQuestion(resultItem.getSummaryQuestionContent())
                .questionType(resultItem.getQuestionTypeId())
                .replyStatus(resultItem.getReplyStatus())
                .ctime(System.currentTimeMillis())
                .utime(System.currentTimeMillis())
                .build();
        return entity;
    }

    public void llmTaskFail(String taskId, String message, CreateQuestionSummaryLlmTaskParam param){
        if (StringUtils.isBlank(taskId) || param == null) {
            log.warn("#llmTaskFail#warn, param is null");
            return;
        }
        // 更新任务
        ModelOutputTaskEntity entity = new ModelOutputTaskEntity();
        try {
            entity.setTaskId(taskId);
            entity.setTaskStatus(ModelOutputTaskStatusEnum.FAIL.getCode());
            entity.setPlatformId(ConvertTaskPlatformId.DX_MONITORING.getCode());
            entity.setTaskMessage(message);
            modelOutputMapper.updateModelOutputTaskStatusByTaskId(entity);
        } catch (Exception e) {
            log.warn("#llmTaskSuccess#error,更新任务状态失败，entity:{}", entity,e);
        }
    }
}
