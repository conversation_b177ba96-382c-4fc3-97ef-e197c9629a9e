package com.meituan.banma.llm.corpus.server.service;

import com.meituan.banma.llm.corpus.api.exception.LlmCorpusException;
import com.meituan.banma.llm.corpus.server.common.domain.dto.DxChatMessageRecord;
import com.meituan.banma.llm.corpus.server.common.domain.dto.ImageToTextResultDTO;
import com.meituan.banma.llm.corpus.server.common.domain.dto.KnowledgeSimilarityRecord;
import com.meituan.banma.llm.corpus.server.common.domain.dto.LlmDxGroupMonitoringTaskMessageDTO;
import com.meituan.banma.llm.corpus.server.common.domain.dto.LlmDxGroupMonitoringTaskResultDTO;
import com.meituan.banma.llm.corpus.server.common.domain.dto.TTInfoDTO;
import com.meituan.banma.llm.corpus.server.common.domain.dto.TtChatCorpusDTO;
import com.meituan.banma.llm.corpus.server.common.domain.dto.*;
import com.meituan.banma.llm.corpus.server.common.domain.dto.stat.AppStatusDTO;
import com.meituan.banma.llm.corpus.server.rpc.friday.dto.LlmKmToCorpusDTO;

import java.util.List;

public interface IFridayService {
    TtChatCorpusDTO convertTtChatToCorups(String misId, TTInfoDTO ttInfo, List<DxChatMessageRecord> chatRecord) throws LlmCorpusException;
    List<KnowledgeSimilarityRecord> getSimilarityRecord(Long rgId, String query) throws LlmCorpusException;
    List<KnowledgeSimilarityRecordWithScore> getSimilarityRecordWithScore(Long rgId, String query) throws LlmCorpusException;
    String extractTtId(String text) throws LlmCorpusException;

    ImageToTextResultDTO convertImageToText(String misId, String message, String imageUrl) throws LlmCorpusException;
    List<ImageToTextResultDTO> convertImageToTextBatch(String misId, String message, List<String> imageUrls) throws LlmCorpusException;
    TtChatCorpusDTO mergeCorpus(FridayMergeCorpusParam fridayMergeCorpusParam) throws LlmCorpusException;
    LlmDxGroupMonitoringTaskResultDTO summarizeDxGroupChatQuestions(String misId, LlmDxGroupMonitoringTaskMessageDTO messageDTO) throws LlmCorpusException;

    /**
     * 拉群自动回复
     * @param fridayAutoReplyConfig 自动回复配置
     * @param misId 值班misId
     * @param ttInfoDTO tt信息
     * @return 回复结果
     * @throws LlmCorpusException 异常
     */
    FridayAutoReplyResultDto autoReply(FridayAutoReplyConfig fridayAutoReplyConfig, String misId, TTInfoDTO ttInfoDTO) throws LlmCorpusException;

    List<FridayQuestionClusteringItemDTO> questionClustering(List<AppStatusDTO.Question> questionList) throws LlmCorpusException;

    /**
     * 获取语料质量评估结果
     *
     * @param query 待评估的语料内容
     * @return 语料质量评估结果
     * @throws LlmCorpusException 异常信息
     */
    ContentQualityDTO getContentQualityAssessment(String query) throws LlmCorpusException;

    List<FridayQuestionResolveStateItem> judgeQuestionResolveState(List<QuestionAnswerPairDTO> questionAnswerPairs) throws LlmCorpusException;

    /**
     * Aiops事件群自动回复
     *
     * @param aiopsAutoReplyConfig Aiops自动回复配置
     * @param misId 用户MIS ID
     * @param message 消息内容
     * @return 回复结果
     * @throws LlmCorpusException 异常
     */
    AiopsAutoReplyResultDto aiopsAutoReply(AiopsAutoReplyConfig aiopsAutoReplyConfig, String misId, String message) throws LlmCorpusException;

    /**
     * 获取文本的embedding
     * @param misId
     * @param input
     * @return
     * @throws LlmCorpusException
     */
    List<FridayEmbeddingResDTO> getEmbedding(String misId, List<String> input) throws LlmCorpusException;

    FridayQuestionClusterNamingResDTO questionClusterNaming(List<AppStatusDTO.Question> questionList) throws LlmCorpusException;
    LlmKmToCorpusDTO.LlmKmToCorpusTaskResultDTO convertKmToCorups(String body, String misId,boolean flag, long rgId)throws LlmCorpusException;
    /**
     * 将问题列表总结为类型定义
     * @param questionList 问题列表
     * @return 类型定义
     * @throws LlmCorpusException 异常
     */
    List<String> summarizeQuestionsIntoTypeDefinition(List<String> questionList) throws LlmCorpusException;

    
    /**
     * 将问题列表分配到聚类中
     * @param allocateQuestionsToClustersRequestParam 请求参数
     * @return 聚类
     * @throws LlmCorpusException 异常
     */
    List<FridayQuestionClassficationItemDTO> allocateQuestionsToClusters(AllocateQuestionsToClustersRequestParam allocateQuestionsToClustersRequestParam) throws LlmCorpusException;
}
