package com.meituan.banma.llm.corpus.server.utils;

import java.lang.reflect.Field;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * Utility class to convert Java Beans to Markdown format.
 */
public class MarkdownConverterUtil {

    private static final String H1 = "# ";
    private static final String H2 = "## ";
    private static final String H3 = "### ";
    private static final String H4 = "#### ";
    private static final String H5 = "##### ";
    private static final String H6 = "###### ";
    private static final String BULLET_POINT = "- ";
    private static final String INDENT = "  "; // For nested structures if needed

    /**
     * Converts a JavaBean object to a Markdown formatted string.
     *
     * @param obj The JavaBean object to convert.
     * @return Markdown formatted string representation of the object.
     */
    public static String toMarkdown(Object obj) {
        if (obj == null) {
            return "null";
        }
        StringBuilder markdownBuilder = new StringBuilder();
        convertObjectToMarkdown(obj, markdownBuilder, 1);
        return markdownBuilder.toString();
    }

    private static void convertObjectToMarkdown(Object obj, StringBuilder markdownBuilder, int level) {
        if (obj == null) {
            markdownBuilder.append("null").append("\n");
            return;
        }

        Class<?> clazz = obj.getClass();

        // Add class name as header only for the top-level object (level 1)
        if (level == 1) {
            markdownBuilder.append(getHeader(level)).append(clazz.getSimpleName()).append("\n");
        }

        Field[] fields = clazz.getDeclaredFields();

        for (Field field : fields) {
            // Skip static fields, etc. if needed
            if (java.lang.reflect.Modifier.isStatic(field.getModifiers())) {
                continue;
            }

            field.setAccessible(true); // Allow access to private fields
            try {
                Object value = field.get(obj);
                String fieldName = field.getName();

                // Add field name as header
                markdownBuilder.append(getHeader(level + 1)).append(fieldName).append(":\n");

                appendValue(value, markdownBuilder, level + 1);

            } catch (IllegalAccessException e) {
                // Handle exception appropriately, maybe log it
                markdownBuilder.append(getHeader(level + 1)).append(field.getName()).append(":\n");
                markdownBuilder.append("Error accessing field: ").append(e.getMessage()).append("\n");
            }
        }
    }

    private static void appendValue(Object value, StringBuilder markdownBuilder, int level) {
        if (value == null) {
            markdownBuilder.append("null\n");
        } else if (value instanceof Collection) {
            Collection<?> collection = (Collection<?>) value;
            if (collection.isEmpty()) {
                markdownBuilder.append("(empty list)\n");
            } else {
                for (Object item : collection) {
                    markdownBuilder.append(BULLET_POINT);
                    // Check if item is a complex object or simple type
                    if (isSimpleType(item.getClass())) {
                        markdownBuilder.append(item).append("\n");
                    } else {
                        // Handle nested objects within lists - start a new block without a header
                        StringBuilder nestedBuilder = new StringBuilder();
                        convertObjectToMarkdown(item, nestedBuilder, level); // Increase level for indentation in nested objects if needed
                        // Indent the nested object representation
                        String[] lines = nestedBuilder.toString().split("\n");
                        if (lines.length > 0) {
                            // Append the first line directly after bullet
                             markdownBuilder.append(lines[0].trim()).append("\n"); // Assuming first line is nested object's "header" or simple value
                             for (int i = 1; i < lines.length; i++) {
                                 markdownBuilder.append(INDENT).append(lines[i]).append("\n");
                             }
                         }
                    }
                }
            }
        } else if (value instanceof Map) {
            // Basic Map handling - could be improved
            Map<?, ?> map = (Map<?, ?>) value;
             if (map.isEmpty()) {
                 markdownBuilder.append("(empty map)\n");
             } else {
                 map.forEach((k, v) -> {
                     markdownBuilder.append(BULLET_POINT).append(k).append(": ");
                     appendValue(v, markdownBuilder, level); // Recursive call for map values
                 });
             }
        } else if (isSimpleType(value.getClass())) {
            markdownBuilder.append(value).append("\n");
        } else {
            // Nested object: recursively call conversion, increasing header level
            convertObjectToMarkdown(value, markdownBuilder, level);
        }
    }

    private static String getHeader(int level) {
        switch (level) {
            case 1: return H1;
            case 2: return H2;
            case 3: return H3;
            case 4: return H4;
            case 5: return H5;
            default: return H6; // Max out at H6
        }
    }

    private static boolean isSimpleType(Class<?> clazz) {
        return clazz.isPrimitive() ||
               clazz.equals(String.class) ||
               clazz.equals(Integer.class) ||
               clazz.equals(Long.class) ||
               clazz.equals(Double.class) ||
               clazz.equals(Float.class) ||
               clazz.equals(Boolean.class) ||
               clazz.equals(Character.class) ||
               clazz.equals(Byte.class) ||
               clazz.equals(Short.class) ||
               clazz.isEnum();
    }

    // Example Usage (optional - can be removed or placed in a test class)
    /*
    public static class NestedDTO {
        private String detail = "Nested Detail";
        private int count = 10;
        public NestedDTO() {} // Default constructor
    }

    public static class ExampleBean {
        private Long id = 12345L;
        private String name = "Example Name";
        private List<String> tags = List.of("tag1", "tag2", "tag3");
        private NestedDTO nested = new NestedDTO();
        private List<NestedDTO> dtoList = List.of(new NestedDTO(), new NestedDTO());
        private Map<String, Integer> scores = Map.of("Math", 95, "Science", 88);
         private String nullField = null;
         private List<String> emptyList = List.of();


        public ExampleBean() {} // Default constructor
    }

    public static void main(String[] args) {
        ExampleBean bean = new ExampleBean();
        String markdown = MarkdownConverterUtil.toMarkdown(bean);
        System.out.println(markdown);

        System.out.println("
--- Null Object ---");
        System.out.println(MarkdownConverterUtil.toMarkdown(null));

        System.out.println("
--- Simple String ---");
         System.out.println(MarkdownConverterUtil.toMarkdown("Just a simple string")); // Will treat String as simple type

         System.out.println("
--- Nested DTO direct call ---");
         System.out.println(MarkdownConverterUtil.toMarkdown(new NestedDTO()));
    }
    */
} 