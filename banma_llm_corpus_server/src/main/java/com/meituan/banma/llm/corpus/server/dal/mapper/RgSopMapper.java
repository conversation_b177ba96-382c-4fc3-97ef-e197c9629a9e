package com.meituan.banma.llm.corpus.server.dal.mapper;


import com.meituan.banma.llm.corpus.server.dal.entity.RgSopEntity;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface RgSopMapper {
    @Select("SELECT COUNT(*) FROM rg_corpus_sop WHERE rg_id=#{rgId}")
    int countSopByRgId(long rgId);

    //根据rgId分页查询历史sop
    @Select("SELECT * FROM rg_corpus_sop WHERE rg_id=#{rgId} ORDER BY version DESC LIMIT #{pageSize} OFFSET #{offset}")
    List<RgSopEntity> queryRgSopByRgId(@Param("rgId")long rgId, @Param("pageSize")int pageSize, @Param("offset")int offset);

    @Insert("INSERT INTO rg_corpus_sop (rg_id, sop, version, update_time, mis_id) " +
            "SELECT #{rgId}, #{sop}, #{version}, #{updateTime}, #{misId}" +
            "WHERE NOT EXISTS (SELECT 1 FROM rg_corpus_sop WHERE rg_id=#{rgId} AND version>=#{version})"
    )
    int insertSopWithVersionCheck(RgSopEntity rgSopEntity);

    // 查询最新一条SOP
    @Select("SELECT * FROM rg_corpus_sop WHERE rg_id=#{rgId} ORDER BY version DESC LIMIT 1")
    RgSopEntity queryLatestByRgId(@Param("rgId") long rgId);

    // 根据ID查询单条SOP记录
    @Select("SELECT * FROM rg_corpus_sop WHERE id=#{id}")
    RgSopEntity queryById(@Param("id") Long id);
}
