package com.meituan.banma.llm.corpus.server.dal.mapper;

import com.meituan.banma.llm.corpus.server.dal.entity.TicketRangeTaskRecordEntity;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 任务记录表Mapper
 */
@Mapper
public interface TicketRangeTaskRecordMapper {
    
    /**
     * 插入任务记录
     * 
     * @param entity 任务记录实体
     * @return 受影响的行数
     */
    @Insert("INSERT INTO ticket_range_task_records(task_id, tt_ids, created_at) " +
            "VALUES(#{taskId}, #{ttIds}, #{createdAt})")
    int insert(TicketRangeTaskRecordEntity entity);
    
    /**
     * 更新任务记录
     * 
     * @param entity 任务记录实体
     * @return 受影响的行数
     */
    @Update("UPDATE ticket_range_task_records SET " +
            "tt_ids = #{ttIds} " +
            "WHERE task_id = #{taskId}")
    int update(TicketRangeTaskRecordEntity entity);
    
    /**
     * 插入或更新任务记录
     * 当任务ID已存在时更新，不存在时插入
     * 
     * @param entity 任务记录实体
     * @return 受影响的行数
     */
    @Insert("INSERT INTO ticket_range_task_records(task_id, tt_ids, created_at) " +
            "VALUES(#{taskId}, #{ttIds}, #{createdAt}) " +
            "ON DUPLICATE KEY UPDATE " +
            "tt_ids = VALUES(tt_ids)")
    int insertOrUpdate(TicketRangeTaskRecordEntity entity);
    
    /**
     * 根据任务ID查询任务记录
     * 
     * @param taskId 任务ID
     * @return 任务记录实体
     */
    @Select("SELECT * FROM ticket_range_task_records WHERE task_id = #{taskId}")
    TicketRangeTaskRecordEntity findByTaskId(@Param("taskId") String taskId);
    
    /**
     * 查询所有任务记录
     * 
     * @return 任务记录列表
     */
    @Select("SELECT * FROM ticket_range_task_records ORDER BY created_at DESC")
    List<TicketRangeTaskRecordEntity> findAll();
    
    /**
     * 根据创建时间查询任务记录
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 任务记录列表
     */
    @Select("SELECT * FROM ticket_range_task_records WHERE created_at BETWEEN #{startTime} AND #{endTime} " +
            "ORDER BY created_at DESC")
    List<TicketRangeTaskRecordEntity> findByTimeRange(@Param("startTime") String startTime, @Param("endTime") String endTime);
} 