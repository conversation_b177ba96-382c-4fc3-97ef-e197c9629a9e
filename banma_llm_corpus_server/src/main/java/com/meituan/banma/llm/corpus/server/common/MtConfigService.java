package com.meituan.banma.llm.corpus.server.common;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.meituan.banma.llm.corpus.server.common.domain.dto.*;
import com.meituan.banma.llm.corpus.server.common.domain.dto.stat.StatQueryPageSettings;
import com.meituan.banma.llm.corpus.server.common.domain.dto.stat.KMeansConfig;
import com.meituan.banma.llm.corpus.server.common.domain.dto.stat.HDBSCANConfig;
import com.meituan.banma.llm.corpus.server.common.domain.dto.TemplateConfigList;
import com.meituan.banma.llm.corpus.server.common.domain.dto.ContentLengthLimitConfig;
import com.meituan.mdp.boot.starter.config.client.MdpConfigClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import com.meituan.banma.llm.corpus.server.common.constants.enums.ClusteringAlgorithmType;
import com.meituan.banma.llm.corpus.server.common.domain.dto.DeliveryTypeConfig;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.text.MessageFormat;

@Slf4j
@Service
public class MtConfigService {
    @Resource
    private MdpConfigClient mdpConfigClient;

    /**
     * 默认示例值
     */
    private static final String DEFAULT_EXAMPLE_VALUE = "Hello World";

    /**
     * 默认friday会话接口地址
     */
    private static final String DEFAULT_FRIDAY_CONVERSATION_URL = "https://aigc.sankuai.com/conversation/v2/openapi";

    /**
     * 默认friday异步会话接口地址
     */
    private static final String DEFAULT_FRIDAY_ASYNC_CONVERSATION_URL = "https://aigc.sankuai.com/conversation/async/v1/openapi";

    private static final String DEFAULT_FRIDAY_ASYNC_GET_CONVERSATION_RESULT_URL = "https://aigc.sankuai.com/conversation/async/v1/query";

    private static final int DEFAULT_FRIDAY_POLL_INTERVAL = 3000;
    /**
     * 默认friday会话超时时间
     */
    private static final Integer DEFAULT_FRIDAY_CONVERSATION_TIMEOUT = 60000;

    /**
     * 默认图片转文本线程超时时间
     */
    private static final Integer DEFAULT_IMAGE_TO_TEXT_FEATURE_TIMEOUT = 60000;
    /**
     * 示例配置
     */
    private static final String EXAMPLE_LION_KEY = "example_lion_key";

    /**
     * friday接口鉴权配置
     */
    private static final String FRIDAY_ACCESS_TOKEN_CONFIG = "friday_access_token_config";

    /**
     * friday会话配置
     */
    private static final String FRIDAY_CONVERSATION_CONFIG = "friday_conversation_config";

    /**
     * friday会话接口地址
     */
    private static final  String FRIDAY_CONVERSATION_URL = "friday_conversation_url";

    /**
     * friday异步会话接口地址
     */
    private static final String FRIDAY_ASYNC_CONVERSATION_URL = "friday_async_conversation_url";

    /**
     * friday异步获取会话结果接口地址
     */
    private static final String FRIDAY_ASYNC_GET_CONVERSATION_RESULT_URL = "friday_async_get_conversation_result_url";

    /**
     * friday异步轮询间隔
     */
    private static final String FRIDAY_ASYNC_POLL_INTERVAL = "friday_async_poll_interval";
    /**
     * friday会话超时时间
     */
    private static final String FRIDAY_CONVERSATION_TIMEOUT = "friday_conversation_timeout";

    /**
     * friday相似度匹配配置
     */
    private static final String FRIDAY_SIMILARITY_CONVERSATION_CONFIG = "friday_similarity_conversation_config";

    /**
     * friday相似度匹配配置
     */
    private static final String FRIDAY_MERGE_CORPUS_CONVERSATION_CONFIG = "friday_merge_corpus_conversation_config";

    /**
     * oms接入指引智能化配置
     */
    private static final String OMS_ACCESS_GUIDE_INTELLIGENCE_CONFIG = "firday_oms_access_guide_intelligence_configuration";

    /**
     * friday语料质量评估配置
     */
    private static final String FRIDAY_CONTENT_QUALITY_ASSESSMENT = "friday_content_quality_assessment";

    /**
     * friday大象聊天记录问题监控配置
     */
    private static final String FRIDAY_DX_MONITORING_CONVERSATION_CONFIG = "friday_dx_monitoring_conversation_config";

    /**
     * 大象监控配置
     */
    private static final String DX_MONITORING_CONFIG = "dx_monitoring_config";

    /**
     * friday学城问答转语料配置
     */
    private static final String FRIDAY_KM_SPLIT_CONVERSATION_CONFIG = "friday_km_split_conversation_config";
    private static final String FRIDAY_QA_SPLIT_CONVERSATION_CONFIG = "friday_qa_split_conversation_config";
    /**
     * 组织appkey
     */
    private static final String ORG_APP_KEY = "org_app_key";

    /**
     * 默认TT查询线上域名
     */
    private static final String DEFAULT_TT_QUERY_ONLINE_DOMAIN = "https://ticket.vip.sankuai.com";

    /**
     * TT查询域名
     */
    private static final String TT_QUERY_DOMAIN = "tt_query_domain";

    /**
     * BA 鉴权配置
     */
    private static final String BA_AUTH_CONFIG = "ticket_ba_auth";

    /**
     * 大象开放平台AppKey
     */
    private static final String OPEN_APP_KEY = "open_app_key";

    /**
     * 大象开放平台AppSecret
     */
    private static final String OPEN_APP_SECRET = "open_app_secret";

    /**
     * "此处" 跳转url
     */
    private static final String REVIEW_URL = "review_url";

    /**
     * Venus配置
     */
    private static final String VENUS_CONFIG = "venus_config";

    /**
     * 无意义回复关键词,会被过滤
     */
    private static final String NONSENSE_MESSAGE_KEYWORDS = "nonsense_message_keywords";

    /**
     * 机器人id
     */
    private static final String CHAT_BOT_ID = "chatbot_id";

    /**
     * friday图片识别会话配置
     */
    private static final String FRIDAY_TEXT_TO_IMAGE_CONVERSATION_CONFIG = "friday_text_to_image_conversation_config";

    /**
     * 并发图片转文本线程超时时间
     */
    private static final String IMAGE_TO_TEXT_FEATURE_TIMEOUT = "text_to_image_feature_timeout";

    /**
     * 合并语料最大限制
     */
    private static final String  MERGE_CORPUS_MAX_LIMIT = "merge_corpus_max_limit";

    /**
     * friday拉群自动回答配置
     */
    private static final String FRIDAY_AUTO_REPLY_CONVERSATION_CONFIG = "friday_auto_reply_conversation_config";

    /**
     * friday拉群自动回答前, sleep时间, ms
     */
    private static final String FRIDAY_AUTO_REPLY_SLEEP_TIME = "friday_auto_reply_sleep_time";

    /**
     * 批量处理TT最大限制
     */
    private static final String BATCH_PROCESS_TT_MAX_LIMIT = "batch_process_tt_max_limit";

    /**
     * 批量处理TT超时时间(毫秒)
     */
    private static final String BATCH_PROCESS_TT_TIMEOUT = "batch_process_tt_timeout";

    /**
     * 批量处理TT默认超时时间(毫秒)
     */
    private static final Integer DEFAULT_BATCH_PROCESS_TT_TIMEOUT = 30000;

    /**
     * SSO测试账号用户名
     */
    private static final String SSO_LOGIN_NAME = "sso_login_name";

    /**
     * 测试账号用户名默认值
     */
    private static final String DEFAULT_SSO_LOGIN_NAME = "test_tt_backlog2";

    /**
     * SSO测试账号密码
     */
    private static final String SSO_LOGIN_PASSWORD = "sso_login_password";

    /**
     * 测试账号密码默认值
     */
    private static final String DEFAULT_SSO_LOGIN_PASSWORD = "WYhniv?658";

    /**
     * 测试账号员工ID
     */
    private static final String TEST_ACCOUNT_EMP_ID = "test_account_emp_id";

    /**
     * 测试账号员工ID默认值
     */
    private static final Long DEFAULT_TEST_ACCOUNT_EMP_ID = 75402822L;

    /**
     * SSO测试账号大象clientId
     */
    private static final String SSO_DX_CLIENT_ID = "sso_dx_client_id";

    /**
     * 测试账号大象clientId默认值
     */
    private static final String DEFAULT_SSO_DX_CLIENT_ID = "xm-xai";

    /**
     * SSO测试账号学城clientId
     */
    private static final String SSO_KM_CLIENT_ID = "sso_km_client_id";

    /**
     * 测试账号学城clientId默认值
     */
    private static final String DEFAULT_SSO_KM_CLIENT_ID = "com.sankuai.it.ead.citadel";

    /**
     * 学城url正则
     */
    private static final String KM_URL_REGEX = "km_url_regex";

    private static final String FRIDAY_QUESTION_RESOLVE_STATE_CONFIG = "friday_question_resolve_state_config";

    /**
     * 学城url正则默认值
     */
    private static final String DEFAULT_KM_URL_REGEX = "https://km.it.test.sankuai.com/collabpage/";


    /**
     * SSO相关配置
     */
    private static final String SSO_AUTH_URI = "sso_auth_uri";
    private static final String SSO_BASE_URL = "sso_base_url";
    private static final String SSO_CLIENT_ID = "sso_client_id";
    private static final String SSO_CLIENT_SECRET = "sso_client_secret";

    /**
     * SSO相关默认值
     */
    private static final String DEFAULT_SSO_AUTH_URI = "/sson/api/auth";
    private static final String DEFAULT_SSO_BASE_URL = "http://ssosv.it.test.sankuai.com";
    private static final String DEFAULT_SSO_CLIENT_ID = "20c01d5264";
    private static final String DEFAULT_SSO_CLIENT_SECRET = "33e28dd324964cb3bdb37028a44b1b7b";

    /**
     * talos配置
     */
    private static final String TALOS_MCC_CONFIG = "talos_mcc_config";

    /**
     * 查询hive超时时间
     */
    private static final String QUERY_HIVE_TIMEOUT = "query_hive_timeout";

    /**
     * 日志打印hive查询结果开关
     */
    private static final String LOGGING_HIVE_RESULT_SWITCH = "logging_hive_result_switch";

    private static final String STAT_QUERY_PAGE_SETTINGS = "stat_query_page_settings";

    /**
     * friday appId 信息映射
     */
    private static final String FRIDAY_APP_ID_INFO_MAPPING = "friday_app_id_info_mapping";

    /**
     * friday 应用信息查询白名单
     */
    private static final String FRIDAY_APP_INFO_WHITELIST = "friday_app_info_whitelist";

    /**
     * friday 问题聚类会话配置
     */
    private static final String FRIDAY_QUESTION_CLUSTERING_CONVERSATION_CONFIG = "friday_question_clustering_conversation_config";

    /**
     * 历史消息查询年限配置key
     */
    private static final String HISTORY_MESSAGE_QUERY_YEARS = "history_message_query_years";

    /**
     * 历史消息查询年限默认值
     */
    private static final Integer DEFAULT_HISTORY_MESSAGE_QUERY_YEARS = 3;

    /**
     * 工单范围查询配置
     */
    private static final String TICKET_RANGE_QUERY_CONFIG = "ticket_range_query_config";

    /**
     * 模板查询配置
     */
    private static final String TICKET_RANGE_QUERY_TEMPLATE = "ticket_range_query_template";

    /**
     * Aiops事件群自动回复配置
     */
    private static final String AIOPS_AUTO_REPLY_CONFIG = "aiops_auto_reply_config";

    /**
     * 统计数据处理异步任务超时时间
     */
    private static final String GET_STATS_DATA_PROCESS_ASYNC_TASK_TIMEOUT = "get_stats_data_process_async_task_timeout";

    /**
     * 获取统计数据处理异步任务超时时间默认值
     */
    private static final Integer DEFAULT_GET_STATS_DATA_PROCESS_ASYNC_TASK_TIMEOUT = 300000;

    /**
     * 问题解决状态判断批处理大小配置
     */
    private static final String QUESTION_RESOLVE_STATE_BATCH_SIZE = "question_resolve_state_batch_size";

    /**
     * 问题解决状态判断批处理大小默认值
     */
    private static final Integer DEFAULT_QUESTION_RESOLVE_STATE_BATCH_SIZE = 100;

    /**
     * 问题解决状态判断任务超时配置
     */
    private static final String QUESTION_RESOLVE_STATE_TASK_TIMEOUT = "question_resolve_state_task_timeout";

    /**
     * 问题解决状态判断任务超时默认值(毫秒)
     */
    private static final Integer DEFAULT_QUESTION_RESOLVE_STATE_TASK_TIMEOUT = 280000;

    /**
     * k-means聚类算法配置
     */
    private static final String KMEANS_CLUSTERING_CONFIG = "kmeans_clustering_config";

    /**
     * 聚类算法类型配置键
     */
    private static final String CLUSTERING_ALGORITHM_TYPE_CONFIG = "clustering_algorithm_type";

    /**
     * HDBSCAN*聚类算法配置键
     */
    private static final String HDBSCAN_CLUSTERING_CONFIG = "hdbscan_clustering_config";

    /**
     * Friday模型工厂appId
     */
    private static final String FRIDAY_MODEL_FACTORY_APP_ID = "friday_model_factory_app_id";

    /**
     * Friday模型工厂embedding模型名称
     */
    private static final String FRIDAY_EMBEDDING_MODEL = "friday_embedding_model";

    /**
     * friday问题模式聚类名称总结会话配置
     */
    private static final String FRIDAY_QUESTION_PATTERN_SUM_CONVERSATION_CONFIG = "friday_question_pattern_sum_conversation_config";

    /**
     * friday问题模式聚类名称总结会话配置
     */
    private static final String FRIDAY_SUMMARIZE_QUESTIONS_INTO_TYPE_DEFINITION_CONVERSATION_CONFIG = "friday_summarize_questions_into_type_definition_conversation_config";

    /**
     * friday问题分类配置
     */
    private static final String FRIDAY_ALLOCATE_QUESTIONS_TO_CLUSTERS_CONVERSATION_CONFIG = "friday_allocate_questions_to_clusters_conversation_config";

    /**
     * 问题分配批处理大小配置键
     */
    private static final String QUESTION_ALLOCATION_BATCH_SIZE = "question_allocation_batch_size";

    /**
     * 自动回复过期时间配置键
     */
    private static final String AUTO_REPLY_EXPIRE_TERM = "auto_reply_expire_term";

    /**
     * 问题分配批处理大小默认值
     */
    private static final Integer DEFAULT_QUESTION_ALLOCATION_BATCH_SIZE = 10;

    /**
     * 问题类型分配任务超时配置键
     */
    private static final String QUESTION_ALLOCATION_TASK_TIMEOUT = "question_allocation_task_timeout";

    /**
     * 问题类型分配任务超时默认值(毫秒)
     */
    private static final Integer DEFAULT_QUESTION_ALLOCATION_TASK_TIMEOUT = 280000;

    /**
     * 是否使用问题类型分配方法的配置键
     */
    private static final String USE_QUESTION_TYPE_ALLOCATION = "use_question_type_allocation";

    /**
     * 是否使用问题类型分配方法的默认值
     */
    private static final Boolean DEFAULT_USE_QUESTION_TYPE_ALLOCATION = true;

    /**
     * 学城文档图片前缀
     */
    private static final String KM_IMAGE_PREFIX = "km_image_prefix";
    private static final String DEFAULT_KM_IMAGE_PREFIX = "https://km.it.test.sankuai.com/api/file/cdn/";

    /**
     * 问询监控组ID配置键
     */
    private static final String INQUIRY_MONITORING_GROUP_IDS = "inquiry_monitoring_group_ids";

    /**
     * 默认的问询监控组ID
     */
    private static final String DEFAULT_INQUIRY_MONITORING_GROUP_IDS = "9";

    /**
     * 自动回复过期时间默认值 7天
     */
    private static final Integer DEFAULT_AUTO_REPLY_EXPIRE_TERM = 7;

    /**
     * 灰度配置键
     */
    private static final String GRAYSCALE_CONFIG = "grayscale_config";

    /**
     * 获取雷达拉群自动总结群聊sleep时间
     *
     */
    private static final String RADAR_AUTO_SUMMARY_SLEEP_TIME = "radar_auto_summary_sleep_time";

    /**
     * 配送类型配置键
     */
    private static final String DELIVERY_TYPE_CONFIG = "delivery_type_config";

    /**
     * 默认配送类型配置JSON
     */
    private static final String DEFAULT_DELIVERY_TYPE_CONFIG = "[{\"name\":\"新专送\",\"typeId\":\"1006\"},{\"name\":\"快送\",\"typeId\":\"2002\"}]";

    /**
     * 获取雷达拉群自动总结群聊sleep时间
     *
     * @return 拉群自动回答配置sleep时间
     */
    public int getRadarAutoSummarySleepTime() {
        return getObject(RADAR_AUTO_SUMMARY_SLEEP_TIME, new TypeReference<Integer>(){}, 0);
    }

    /**
     * 获取配送类型配置
     * @return 配送类型配置列表
     */
    public List<DeliveryTypeConfig> getDeliveryTypeConfig() {
        try {
            String value = mdpConfigClient.get(DELIVERY_TYPE_CONFIG);
            if (StringUtils.isBlank(value)) {
                log.warn("#MtConfigService.getDeliveryTypeConfig#配置为空，使用默认配置");
                value = DEFAULT_DELIVERY_TYPE_CONFIG;
            }
            return JSON.parseArray(value, DeliveryTypeConfig.class);
        } catch (Exception e) {
            log.error("#MtConfigService.getDeliveryTypeConfig#解析配送类型配置异常", e);
            // 解析失败时返回默认配置
            try {
                return JSON.parseArray(DEFAULT_DELIVERY_TYPE_CONFIG, DeliveryTypeConfig.class);
            } catch (Exception ex) {
                log.error("#MtConfigService.getDeliveryTypeConfig#解析默认配送类型配置异常", ex);
                return new ArrayList<>();
            }
        }
    }

    /**
     * 获取灰度配置
     *
     * @return 灰度配置JSON字符串
     */
    public String getGrayscaleConfig() {
        return mdpConfigClient.get(GRAYSCALE_CONFIG);
    }

    /**
     * 获取问询监控组ID配置字符串
     *
     * @return 监控组ID配置字符串
     */
    public String getInquiryMonitoringGroupIdsStr() {
        return getStringConfig(INQUIRY_MONITORING_GROUP_IDS, DEFAULT_INQUIRY_MONITORING_GROUP_IDS);
    }

    /**
     * 未识别关键字消息模板
     */
    private static final String UNRECOGNIZED_KEYWORD_MESSAGE_TEMPLATE = "unrecognized_keyword_message_template";

    /**
     * 未识别关键字消息模版(不包含输入框)
     */
    private static final String UNRECOGNIZED_KEYWORD_MESSAGE_TEMPLATE_NOIN = "unrecognized_keyword_message_template_noin";

    /**
     * 默认未识别关键字消息模板
     */
    private static final String DEFAULT_UNRECOGNIZED_KEYWORD_MESSAGE_TEMPLATE = "嗨~ 我是知识库小助手，很高兴为您服务！\n" +
            "\n" +
            "当您需要将TT对话添加到知识库时：\n" +
            "1. 直接在群里@我 + 输入\"加知识库\"\n" +
            "例如：“[@知识小助手test|mtdaxiang://www.meituan.com/pub/profile?pubid=137439654420&isAt=true] 加知识库”\n" +
            "\uD83D\uDC49 我会自动抓取本群全部历史消息进行处理\n" +
            "2. 合并转发至公众号「知识库语料小助手」\n" +
            "\uD83D\uDC49 保存关键对话片段\n" +
            "\n" +
            "更多强大功能请前往[大模型语料处理平台|https://oms-banma.sankuai.com/#/corpus/index]｜[使用手册|https://km.sankuai.com/collabpage/2708110417]";

    /**
     * 知识库添加回调URL
     */
    private static final String KNOWLEDGE_ADD_CALLBACK_URL = "knowledge_add_callback_url";

    /**
     * 默认知识库添加回调URL
     */
    private static final String DEFAULT_KNOWLEDGE_ADD_CALLBACK_URL = "http://oms.banma.test.sankuai.com/api/llm/corpus/callback/knowledge/add";

    /**
     * SOP默认配置键
     */
    private static final String SOP_DEFAULT_CONFIG = "sop_default_config";

    /**
     * friday文档分片大小配置键
     */
    private static final String FRIDAY_DOCUMENT_CHUNK_SIZE = "friday_document_chunk_size";

    /**
     * friday文档分片大小默认值
     */
    private static final Integer DEFAULT_FRIDAY_DOCUMENT_CHUNK_SIZE = 1000;

    /**
     * 内容长度限制配置
     */
    private static final String CONTENT_LENGTH_LIMIT_CONFIG = "content_length_limit_config";

    /**
     * 通用方法 获取对象 未取到则返回默认值
     *
     * @param key          key
     * @param type         类型
     * @param defaultValue 默认值
     * @return {@link T}
     */
    public <T> T getObject(String key, TypeReference<T> type, T defaultValue) {
        String value = mdpConfigClient.get(key);
        if (StringUtils.isBlank(value)) {
            log.warn("#MtConfigService.getObject#config_is_null, key:{}, type:{}", key,
                    type.getType());
            return defaultValue;
        }
        try {
            return JSON.parseObject(value, type.getType());
        } catch (Exception e) {
            log.error("#MtConfigService.getObject#parse_error,key:{}, value:{}, type:{}", key,
                    value, type.getType(), e);
        }
        return defaultValue;
    }

    /**
     * 通用方法 获取对象 未取到则返回null并打印error
     *
     * @param key  key
     * @param type 类型
     * @return {@link T}
     */
    public <T> T getObject(String key, TypeReference<T> type) {
        String value = mdpConfigClient.get(key);
        if (StringUtils.isBlank(value)) {
            log.error("#MtConfigService.getObject#config_is_null, key:{}, type:{}", key,
                    type.getType());
            return null;
        }
        try {
            return JSON.parseObject(value, type.getType());
        } catch (Exception e) {
            log.error("#MtConfigService.getObject#parse_error,key:{}, value:{}, type:{}", key,
                    value, type.getType(), e);
        }
        return null;
    }


    /**
     * 获取字符串类型配置
     *
     * @param key          key
     * @param defaultValue 默认值
     * @return {@link String}
     */
    public String getStringConfig(String key, String defaultValue) {
        String value = mdpConfigClient.get(key);
        if (StringUtils.isBlank(value)) {
            log.error(
                    "#MtConfigService.getStringConfig#config_error,value_is_blank,key:{},defaultValue:{}",
                    key, defaultValue);
            return defaultValue;
        }
        return value;
    }

    /**
     * 返回配置列表类型，此接口不会返回null
     *
     * @param key key
     * @param cls cls
     * @return {@link List <T>}
     */
    public <T> List<T> getObjectList(String key, Class<T> cls) {
        String value = mdpConfigClient.get(key);
        if (StringUtils.isBlank(value)) {
            log.warn("#config_is_null, key:{}, class:{}", key, cls.getName());
            return Lists.newArrayList();
        }
        try {
            return JSON.parseArray(value, cls);
        } catch (Exception e) {
            log.error("#MTConfigService.getObjectList#parse_error,key:{}, value:{}, class:{}", key,
                    value, cls.getName(), e);
        }
        return Lists.newArrayList();
    }

    /**
     * 获取示例配置
     * @return 示例配置
     */
    public String getExampleConfig() {
        return getStringConfig(EXAMPLE_LION_KEY, DEFAULT_EXAMPLE_VALUE);
    }

    /**
     * 获取friday接口鉴权配置
     * @return 接口鉴权配置
     */
    public FridayAccessTokenMccDTO getFridayAccessTokenConfig() {
        return getObject(FRIDAY_ACCESS_TOKEN_CONFIG, new TypeReference<FridayAccessTokenMccDTO>(){}, new FridayAccessTokenMccDTO());
    }

    /**
     * 获取friday会话配置
     * @return 会话配置
     */
    public FridayConversationConfig getFridayConversationConfig() {
        return getObject(FRIDAY_CONVERSATION_CONFIG, new TypeReference<FridayConversationConfig>(){}, new FridayConversationConfig());
    }

    /**
     * 获取friday会话接口地址
     * @return 会话接口地址
     */
    public String getFridayConversationUrl() {
        return getStringConfig(FRIDAY_CONVERSATION_URL, DEFAULT_FRIDAY_CONVERSATION_URL);
    }

    /**
     * 获取friday异步会话接口地址
     * @return 异步会话接口地址
     */
    public String getFridayAsyncConversationUrl() {
        return getStringConfig(FRIDAY_ASYNC_CONVERSATION_URL, DEFAULT_FRIDAY_ASYNC_CONVERSATION_URL);
    }

    /**
     * 获取friday异步获取会话结果接口地址
     * @return
     */
    public String getFridayAsyncGetConversationResultUrl() {
        return getStringConfig(FRIDAY_ASYNC_GET_CONVERSATION_RESULT_URL, DEFAULT_FRIDAY_ASYNC_GET_CONVERSATION_RESULT_URL);
    }

    /**
     * 获取friday会话超时时间
     * @return 超时时间（ms）
     */
    public Integer getFridayConversationTimeout() {
        String value = mdpConfigClient.get(FRIDAY_CONVERSATION_TIMEOUT);
        if (StringUtils.isBlank(value)) {
            return DEFAULT_FRIDAY_CONVERSATION_TIMEOUT;
        }
        return Integer.parseInt(value);
    }

    /**
     * 获取friday相似度匹配配置
     * @return 相似度匹配配置
     */
    public FridayConversationConfig getFridaySimilarityConversationConfig() {
        return getObject(FRIDAY_SIMILARITY_CONVERSATION_CONFIG, new TypeReference<FridayConversationConfig>(){}, new FridayConversationConfig());
    }

    /**
     * 获取oms接入指引智能化配置
     * @return
     */
    public FridayConversationConfig getOmsAccessGuideIntelligenceConfiguration() {
        return getObject(OMS_ACCESS_GUIDE_INTELLIGENCE_CONFIG, new TypeReference<FridayConversationConfig>(){}, new FridayConversationConfig());
    }

    /**
     * 获取TT查询域名
     */
    public String getTtQueryDomain() {
        return getStringConfig(TT_QUERY_DOMAIN, DEFAULT_TT_QUERY_ONLINE_DOMAIN);
    }

    /**
     * 获取BA 鉴权配置
     */
    public String getTicketBaAuth() {
        return getStringConfig(BA_AUTH_CONFIG, null);
    }

    /**
     * 获取大象开放平台AppKey
     */
    public String getOpenPlatformAppKey() {
        return getStringConfig(OPEN_APP_KEY, null);
    }

    /**
     * 获取大象开放平台AppSecret
     */
    public String getOpenPlatformAppSecret() {
        return getStringConfig(OPEN_APP_SECRET, null);
    }

    /**
     * 获取review页面url
     */
    public String getReviewUrl() {
        return getStringConfig(REVIEW_URL, null);
    }

    /**
     * 获取fridayApi页面链接
     */
    public String getFridayApiUrl() {
        return getStringConfig("friday_api_url", "https://aigc.sankuai.com/");
    }

    /**
     * 获取friday空间ID
     */
    public String getFridaySpaceId() {
        return getStringConfig("friday_space_id", "1304772");
    }

    /**
     * 获取delivery openapi链接
     */
    public String getDeliveryOpenApiUrl() {
        return getStringConfig("delivery_openapi_url", "https://deliveryopenapi.meituan.com/llm/corpus/");
    }

    /**
     * 查询当前用户参与的rg调用链接
     */
    public String getRgQueryUrl() {
        return getStringConfig("rg_query_url", "https://cti.vip.sankuai.com");
    }

    /**
     * 三方群调用卡片模板id
     */
    public long getThirdPartyGroupCardTemplateId() {
        return Long.parseLong(getStringConfig("third_party_group_card_template_id", "18302"));
    }

    /**
     * 单聊调用卡片模板id
     */
    public long getSingleChatCardTemplateId() {
        return Long.parseLong(getStringConfig("single_chat_card_template_id", "20412"));
    }

    public VenusMccConfig getVenusConfig() {
        return getObject(VENUS_CONFIG, new TypeReference<VenusMccConfig>(){}, null);
    }

    /**
     * 获取无意义回复关键词
     * @return
     */
    public List<String> getNonsenseMessageKeywords() {
        return getObjectList(NONSENSE_MESSAGE_KEYWORDS, String.class);
    }

    /**
     * 获取机器人id
     * @return
     */
    public Long getChatBotId() {
        return getObject(CHAT_BOT_ID, new TypeReference<Long>(){}, 137979484915L);
    }

    /**
     * 获取friday图片识别会话配置
     * @return 图片识别会话配置
     */
    public FridayConversationConfig getFridayImageToTextConversationConfig() {
        return getObject(FRIDAY_TEXT_TO_IMAGE_CONVERSATION_CONFIG, new TypeReference<FridayConversationConfig>(){}, new FridayConversationConfig());
    }

    /**
     * 获取friday异步会话轮询时间间隔
     * @return
     */
    public int getDefaultFridayPollInterval() {
        return getObject(FRIDAY_ASYNC_POLL_INTERVAL, new TypeReference<Integer>(){}, DEFAULT_FRIDAY_POLL_INTERVAL);
    }

    public int getImageToTextFeatureTimeout() {
        return getObject(IMAGE_TO_TEXT_FEATURE_TIMEOUT, new TypeReference<Integer>(){}, DEFAULT_IMAGE_TO_TEXT_FEATURE_TIMEOUT);
    }

    public FridayConversationConfig getFridayMergeCorpusConversationConfig() {
        return getObject(FRIDAY_MERGE_CORPUS_CONVERSATION_CONFIG, new TypeReference<FridayConversationConfig>(){}, new FridayConversationConfig());
    }

    /**
     * 获取friday语料质量评估配置
     * @return 语料质量评估配置
     */
    public FridayConversationConfig getFridayContentQualityAssessmentConfig() {
        return getObject(FRIDAY_CONTENT_QUALITY_ASSESSMENT, new TypeReference<FridayConversationConfig>(){}, new FridayConversationConfig());
    }

    public int getMergeCorpusMaxLimit() {
        return getObject(MERGE_CORPUS_MAX_LIMIT, new TypeReference<Integer>(){}, 10);
    }

    /**
     * 获取批量处理TT的最大限制
     * @return 批量处理TT的最大限制数
     */
    public int getBatchProcessTtMaxLimit() {
        return getObject(BATCH_PROCESS_TT_MAX_LIMIT, new TypeReference<Integer>() {}, 10);
    }

    /**
     * 获取批量处理TT的超时时间(毫秒)
     * @return 超时时间，默认30000毫秒(0.5分钟)
     */
    public int getBatchProcessTtTimeout() {
        return getObject(BATCH_PROCESS_TT_TIMEOUT, new TypeReference<Integer>() {}, DEFAULT_BATCH_PROCESS_TT_TIMEOUT);
    }

    public List<MonitoringGroupConfig> getDxMonitoringGroupConfig() {
        return getObject(DX_MONITORING_CONFIG, new TypeReference<List<MonitoringGroupConfig>>() {}, new ArrayList<>());
    }

    public FridayConversationConfig getFridayDxMonitoringConversationConfig() {
        return getObject(FRIDAY_DX_MONITORING_CONVERSATION_CONFIG, new TypeReference<FridayConversationConfig>(){}, new FridayConversationConfig());
    }

    public String getOrgAppKey(){
        return getStringConfig(ORG_APP_KEY, "f380943305");
    }

    /**
     * 获取friday拉群自动回答配置
     *
     * @return 拉群自动回答配置
     */
    public Map<String, FridayAutoReplyConfig> getFridayAutoReplyConversationConfig() {
        return getObject(FRIDAY_AUTO_REPLY_CONVERSATION_CONFIG, new TypeReference<Map<String, FridayAutoReplyConfig>>(){}, new HashMap<>());
    }

    /**
     * 获取friday拉群自动回答sleep时间
     *
     * @return 拉群自动回答配置sleep时间
     */
    public int getFridayAutoReplySleepTime() {
        return getObject(FRIDAY_AUTO_REPLY_SLEEP_TIME, new TypeReference<Integer>(){}, 0);
    }

    /**
     * 获取SSO测试账号用户名
     */
    public String getSsoLoginName() {
        return getStringConfig(SSO_LOGIN_NAME, DEFAULT_SSO_LOGIN_NAME);
    }

    /**
     * 获取SSO测试账号密码
     */
    public String getSsoLoginPassword() {
        return getStringConfig(SSO_LOGIN_PASSWORD, DEFAULT_SSO_LOGIN_PASSWORD);
    }

    /**
     * 获取SSO测试账号使用的大象clientId
     */
    public String getSsoDxClientId() {
        return getStringConfig(SSO_DX_CLIENT_ID, DEFAULT_SSO_DX_CLIENT_ID);
    }

    public String getSsoKmClientId() {
        return getStringConfig(SSO_KM_CLIENT_ID, DEFAULT_SSO_KM_CLIENT_ID);
    }

    /**
     * 获取测试账号员工ID
     */
    public Long getTestAccountEmpId() {
        return getObject(TEST_ACCOUNT_EMP_ID, new TypeReference<Long>() {}, DEFAULT_TEST_ACCOUNT_EMP_ID);
    }

    /**
     * 获取SSO认证URI
     */
    public String getSsoAuthUri() {
        return getStringConfig(SSO_AUTH_URI, DEFAULT_SSO_AUTH_URI);
    }

    /**
     * 获取SSO基础URL
     */
    public String getSsoBaseUrl() {
        return getStringConfig(SSO_BASE_URL, DEFAULT_SSO_BASE_URL);
    }

    /**
     * 获取SSO客户端ID
     */
    public String getSsoClientId() {
        return getStringConfig(SSO_CLIENT_ID, DEFAULT_SSO_CLIENT_ID);
    }

    /**
     * 获取SSO客户端密钥
     */
    public String getSsoClientSecret() {
        return getStringConfig(SSO_CLIENT_SECRET, DEFAULT_SSO_CLIENT_SECRET);
    }

    public FridayConversationConfig getFridayKmToCorpusConversationConfig() {
        return getObject(FRIDAY_KM_SPLIT_CONVERSATION_CONFIG, new TypeReference<FridayConversationConfig>(){}, new FridayConversationConfig());
    }

    public FridayConversationConfig getFridayQaToCorpusConversationConfig() {
        return getObject(FRIDAY_QA_SPLIT_CONVERSATION_CONFIG, new TypeReference<FridayConversationConfig>(){}, new FridayConversationConfig());
    }

    public TalosMccConfig getTalosMccConfig() {
        return getObject(TALOS_MCC_CONFIG, new TypeReference<TalosMccConfig>(){}, null);
    }

    public int getQueryHiveTimeout() {
        return getObject(QUERY_HIVE_TIMEOUT, new TypeReference<Integer>(){}, 600000);
    }

    public boolean getLoggingHiveResultSwitch() {
        return getObject(LOGGING_HIVE_RESULT_SWITCH, new TypeReference<Boolean>(){}, false);
    }

    public StatQueryPageSettings getStatQueryPageSettings() {
        return getObject(STAT_QUERY_PAGE_SETTINGS, new TypeReference<StatQueryPageSettings>(){}, new StatQueryPageSettings());
    }

    public Map<String,FridayAppInfo> getFridayAppInfoMap() {
        return getObject(FRIDAY_APP_ID_INFO_MAPPING, new TypeReference<Map<String,FridayAppInfo>>(){}, new HashMap<>());
    }

    /**
     * 获取Friday应用信息查询白名单用户列表
     * @return 白名单用户misId列表
     */
    public List<String> getFridayAppInfoWhitelist() {
        return getObject(FRIDAY_APP_INFO_WHITELIST, new TypeReference<List<String>>(){}, new ArrayList<>());
    }

    public String getKmUrlRegex() {
        return getStringConfig(KM_URL_REGEX, DEFAULT_KM_URL_REGEX);
    }

    public FridayConversationConfig getFridayQuestionClusteringConversationConfig() {
        return getObject(FRIDAY_QUESTION_CLUSTERING_CONVERSATION_CONFIG, new TypeReference<FridayConversationConfig>(){}, new FridayConversationConfig());
    }

    /**
     * 获取历史消息查询年限
     * @return 历史年限
     */
    public Integer getHistoryMessageQueryYears() {
        return getObject(HISTORY_MESSAGE_QUERY_YEARS, new TypeReference<Integer>(){}, DEFAULT_HISTORY_MESSAGE_QUERY_YEARS);
    }

    /**
     * 获取Aiops事件群自动回复配置
     *
     * @return Aiops事件群自动回复配置
     */
    public AiopsAutoReplyConfig getAiopsAutoReplyConfig() {
        return getObject(AIOPS_AUTO_REPLY_CONFIG, new TypeReference<AiopsAutoReplyConfig>(){}, new AiopsAutoReplyConfig());
    }

    public FridayConversationConfig getFridayQuestionResolveStateConfig() {
        return getObject(FRIDAY_QUESTION_RESOLVE_STATE_CONFIG, new TypeReference<FridayConversationConfig>(){}, new FridayConversationConfig());
    }

    /**
     * 获取问题解决状态判断批处理大小
     * @return 批处理大小，默认100
     */
    public Integer getQuestionResolveStateBatchSize() {
        return getObject(QUESTION_RESOLVE_STATE_BATCH_SIZE, new TypeReference<Integer>(){}, DEFAULT_QUESTION_RESOLVE_STATE_BATCH_SIZE);
    }

    /**
     * 获取问题解决状态判断任务超时时间
     * @return 超时时间，单位毫秒，默认280000毫秒（280秒）
     */
    public Integer getQuestionResolveStateTaskTimeout() {
        return getObject(QUESTION_RESOLVE_STATE_TASK_TIMEOUT, new TypeReference<Integer>(){}, DEFAULT_QUESTION_RESOLVE_STATE_TASK_TIMEOUT);
    }

    /**
     * 获取统计数据处理异步任务超时时间
     * @return 超时时间，单位毫秒，默认300000毫秒（300秒）
     */
    public Integer getStatsDataProcessAsyncTaskTimeout() {
        return getObject(GET_STATS_DATA_PROCESS_ASYNC_TASK_TIMEOUT, new TypeReference<Integer>(){}, DEFAULT_GET_STATS_DATA_PROCESS_ASYNC_TASK_TIMEOUT);
    }

    /**
     * 获取服务域名前缀
     *
     * @return 服务域名前缀，例如 "https://example.meituan.com"
     */
    public String getServiceDomain() {
        // 从配置中心获取域名前缀，如不存在则使用默认值
        return "";
    }

    /**
     * 获取工单范围查询配置
     * @return 工单范围查询配置
     */
    public TicketRangeQueryConfig getTicketRangeQueryConfig() {
        return getObject(TICKET_RANGE_QUERY_CONFIG, new TypeReference<TicketRangeQueryConfig>() {}, new TicketRangeQueryConfig());
    }

    /**
     * 获取k-means聚类算法配置
     */
    public KMeansConfig getKMeansConfig() {
        return getObject(KMEANS_CLUSTERING_CONFIG, new TypeReference<KMeansConfig>(){}, new KMeansConfig());
    }

    public String getFridayModelFactoryAppId() {
        return getStringConfig(FRIDAY_MODEL_FACTORY_APP_ID, "1851528974225326171");
    }

    public FridayEmbeddingConfig getFridayEmbeddingModel() {
        return getObject(FRIDAY_EMBEDDING_MODEL, new TypeReference<FridayEmbeddingConfig>(){},new FridayEmbeddingConfig());
    }

    public FridayConversationConfig getFridayQuestionPatternSumConversationConfig() {
        return getObject(FRIDAY_QUESTION_PATTERN_SUM_CONVERSATION_CONFIG, new TypeReference<FridayConversationConfig>(){}, new FridayConversationConfig());
    }

    public String getKmImagePrefix() {
        return getStringConfig(KM_IMAGE_PREFIX, DEFAULT_KM_IMAGE_PREFIX);
    }

    /**
     * 获取工单范围查询模板配置
     * @return 工单范围查询模板配置
     */
    public TemplateConfigList getTicketRangeQueryTemplate() {
        return getObject(TICKET_RANGE_QUERY_TEMPLATE, new TypeReference<TemplateConfigList>() {}, new TemplateConfigList());
    }

    /**
     * 获取聚类算法类型
     * @return 聚类算法类型
     */
    public ClusteringAlgorithmType getClusteringAlgorithmType() {
        String algorithmTypeName = getStringConfig(CLUSTERING_ALGORITHM_TYPE_CONFIG, ClusteringAlgorithmType.KMEANS.name());
        try {
            return ClusteringAlgorithmType.valueOf(algorithmTypeName.toUpperCase());
        } catch (IllegalArgumentException e) {
            log.error("#MtConfigService.getClusteringAlgorithmType#error,聚类算法类型配置错误,value:{}", algorithmTypeName, e);
            return ClusteringAlgorithmType.KMEANS;
        }
    }

    /**
     * 获取HDBSCAN配置
     * @return HDBSCAN配置
     */
    public HDBSCANConfig getHDBSCANConfig() {
        return getObject(HDBSCAN_CLUSTERING_CONFIG, new TypeReference<HDBSCANConfig>(){}, new HDBSCANConfig());
    }

    /**
     * 工单预处理消息模板
     */
    private static final String TICKET_PREPROCESS_MESSAGE_TEMPLATE = "ticket_preprocess_message_template";

    /**
     * 默认工单预处理消息模板
     */
    private static final String DEFAULT_TICKET_PREPROCESS_MESSAGE_TEMPLATE = "已经将此群群聊记录预处理，点击 [此处|{0}] 人工整理保存到小助手语料库中。";

    /**
     * 获取工单预处理消息模板
     * @param url 替换模板中的URL占位符
     * @return 格式化后的消息
     */
    public String getTicketPreprocessMessageTemplate(String url) {
        String template = getStringConfig(TICKET_PREPROCESS_MESSAGE_TEMPLATE, DEFAULT_TICKET_PREPROCESS_MESSAGE_TEMPLATE);
        return MessageFormat.format(template, url);
    }

    /**
     * 获取未识别关键词消息模板
     * @return 消息模板
     */
    public String getUnrecognizedKeywordMessageTemplate() {
        return getStringConfig(UNRECOGNIZED_KEYWORD_MESSAGE_TEMPLATE_NOIN, DEFAULT_UNRECOGNIZED_KEYWORD_MESSAGE_TEMPLATE);
    }

    /**
     * 获取未识别关键词消息模板并格式化
     * @param interactiveContent 替换模板中的交互内容占位符
     * @return 格式化后的消息
     */
    public String getUnrecognizedKeywordMessageTemplate(String interactiveContent) {
        String template = getStringConfig(UNRECOGNIZED_KEYWORD_MESSAGE_TEMPLATE, DEFAULT_UNRECOGNIZED_KEYWORD_MESSAGE_TEMPLATE);
        return MessageFormat.format(template, interactiveContent);
    }

    /**
     * 获取知识库添加回调URL
     * @return 回调URL
     */
    public String getKnowledgeAddCallbackUrl() {
        return getStringConfig(KNOWLEDGE_ADD_CALLBACK_URL, DEFAULT_KNOWLEDGE_ADD_CALLBACK_URL);
    }

    /**
     * 获取问题模式聚类名称总结会话配置
     * @return 问题模式聚类名称总结会话配置
     */
    public FridayConversationConfig getFridaySummarizeQuestionsIntoTypeDefinitionConversationConfig() {
        return getObject(FRIDAY_SUMMARIZE_QUESTIONS_INTO_TYPE_DEFINITION_CONVERSATION_CONFIG, new TypeReference<FridayConversationConfig>(){}, new FridayConversationConfig());
    }

    /**
     * 获取问题分类配置
     * @return 问题分类配置
     */
    public FridayConversationConfig getFridayAllocateQuestionsToClustersConversationConfig() {
        return getObject(FRIDAY_ALLOCATE_QUESTIONS_TO_CLUSTERS_CONVERSATION_CONFIG, new TypeReference<FridayConversationConfig>(){}, new FridayConversationConfig());
    }


    /**
     * 获取问题分配批处理大小
     * @return 批处理大小，默认10
     */
    public Integer getQuestionAllocationBatchSize() {
        return getObject(QUESTION_ALLOCATION_BATCH_SIZE, new TypeReference<Integer>(){}, DEFAULT_QUESTION_ALLOCATION_BATCH_SIZE);
    }

    /**
     * 获取问题类型分配任务超时时间
     * @return 超时时间，单位毫秒，默认280000毫秒（280秒）
     */
    public Integer getQuestionAllocationTaskTimeout() {
        return getObject(QUESTION_ALLOCATION_TASK_TIMEOUT, new TypeReference<Integer>(){}, DEFAULT_QUESTION_ALLOCATION_TASK_TIMEOUT);
    }

    /**
     * 获取是否使用问题类型分配方法的配置
     * @return 是否启用，默认true
     */
    public Boolean getUseQuestionTypeAllocation() {
        return getObject(USE_QUESTION_TYPE_ALLOCATION, new TypeReference<Boolean>(){}, DEFAULT_USE_QUESTION_TYPE_ALLOCATION);
    }

    /**
     * 获取自动回复过期时间
     * @return 自动回复过期时间，默认7天
     */
    public Integer getAutoReplyExpireTerm() {
        return getObject(AUTO_REPLY_EXPIRE_TERM, new TypeReference<Integer>(){}, DEFAULT_AUTO_REPLY_EXPIRE_TERM);
    }

    /**
     * AccessKey白名单配置键
     */
    private static final String ACCESS_KEY_WHITELIST_RGIDS = "access_key_whitelist_rgids";
    
    /**
     * AccessKey无效或缺失时返回内容
     */
    private static final String FORBIDDEN_ACCESS_KEY_MESSAGE = "forbidden_access_key_message";
    
    /**
     * 默认的AccessKey无效或缺失时返回内容
     */
    private static final String DEFAULT_FORBIDDEN_ACCESS_KEY_MESSAGE = "<html><head><title>403 Forbidden</title></head><body><h1>403 Forbidden</h1><p>Access denied: Invalid or missing access key.</p></body></html>";
    
    public List<Long> getAccessKeyWhitelistRgIds() {
        String value = getStringConfig(ACCESS_KEY_WHITELIST_RGIDS, "");
        if (StringUtils.isBlank(value)) {
            return new ArrayList<>();
        }
        
        List<Long> rgIds = new ArrayList<>();
        try {
            String[] ids = value.split(",");
            for (String id : ids) {
                if (StringUtils.isNotBlank(id)) {
                    rgIds.add(Long.parseLong(id.trim()));
                }
            }
        } catch (Exception e) {
            log.error("#MtConfigService.getAccessKeyWhitelistRgIds 解析rgIds失败, value={}", value, e);
        }
        return rgIds;
    }
    
    /**
     * 获取AccessKey无效或缺失时返回的403错误页面
     * 
     * @return 403错误页面HTML内容
     */
    public String getForbiddenAccessKeyMessage() {
        return getStringConfig(FORBIDDEN_ACCESS_KEY_MESSAGE, DEFAULT_FORBIDDEN_ACCESS_KEY_MESSAGE);
    }

    /**
     * 获取SOP默认配置
     * @return SOP默认配置对象
     */
    public SopDefaultConfig getSopDefaultConfig() {
        return getObject(SOP_DEFAULT_CONFIG, new TypeReference<SopDefaultConfig>(){}, SopDefaultConfig.createDefault());
    }

    /**
     * 获取friday文档分片大小
     * @return 文档分片大小
     */
    public Integer getFridayDocumentChunkSize() {
        return getObject(FRIDAY_DOCUMENT_CHUNK_SIZE, new TypeReference<Integer>(){}, DEFAULT_FRIDAY_DOCUMENT_CHUNK_SIZE);
    }

    /**
     * 默认标签rgId默认值
     */
    private static final Long DEFAULT_TAG_RG_ID_VALUE = -1L;

    /**
     * 默认标签rgId配置键
     */
    private static final String DEFAULT_TAG_RG_ID = "default_tag_rg_id";

    /**
     * 获取默认标签rgId
     * @return 默认标签rgId，默认值为-1
     */
    public Long getDefaultTagRgId() {
        return getObject(DEFAULT_TAG_RG_ID, new TypeReference<Long>(){}, DEFAULT_TAG_RG_ID_VALUE);
    }

    /**
     * 获取内容长度限制配置
     */
    public ContentLengthLimitConfig getContentLengthLimitConfig() {
        return getObject(CONTENT_LENGTH_LIMIT_CONFIG, new TypeReference<ContentLengthLimitConfig>(){}, ContentLengthLimitConfig.createDefault());
    }
}
