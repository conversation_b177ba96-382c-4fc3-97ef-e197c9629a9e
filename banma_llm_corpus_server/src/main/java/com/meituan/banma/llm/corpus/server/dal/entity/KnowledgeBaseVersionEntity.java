package com.meituan.banma.llm.corpus.server.dal.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

/**
 * 自建知识库版本信息
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class KnowledgeBaseVersionEntity {

    private Long id;

    //TT id
    private String ticketId;

    private String title;

    //知识库内容
    private String content;

    //知识库版本
    private int version;

    //操作人misId
    private String misId;

    //TT值班组Id
    private Long rgId;

    //操作时间
    private Timestamp timestamp;

    // 来源 CorpusSourceEnum
    private Integer source;

    // 状态 CorpusStatusEnum
    private Integer corpusStatus;

    // 如果已经合并则指向合并到到新语料id
    private String mergedToId;

    // 问题类型id
    private Integer type;

    /**
     * 关联的任务ID，用于关联ModelOutputTask（允许为空）
     */
    private String taskId;

    private long contentId = 0L;

    // 关联rg_background_knowledge表的ID
    private Long backgroundKnowledgeId;

    // 关联rg_corpus_sop表的ID  
    private Long corpusSopId;

    // 关联rg_rules表的ID
    private Long ruleId;

    /**
     * 标签id列表，英文逗号分隔，如1,2,3
     */
    private String tagsIds;
}

