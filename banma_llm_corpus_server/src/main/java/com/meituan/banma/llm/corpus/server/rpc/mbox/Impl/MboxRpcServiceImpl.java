package com.meituan.banma.llm.corpus.server.rpc.mbox.Impl;

import com.meituan.banma.llm.corpus.server.common.SimpleRetryTemplate;
import com.meituan.banma.llm.corpus.server.rpc.dx.XmAuthRpcService;
import com.meituan.banma.llm.corpus.server.rpc.mbox.MboxRpcService;
import com.meituan.mx.mbox.thrift.service.download.IFileDownloadService;
import com.sankuai.dxenterprise.open.gateway.service.mbox.api.MboxService;
import com.sankuai.dxenterprise.open.gateway.service.mbox.api.req.ChangeDownloadTempUrlReq;
import com.sankuai.dxenterprise.open.gateway.service.mbox.api.resp.ChangeDownloadTempUrlResp;
import com.sankuai.xm.openplatform.common.enums.ResCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
public class MboxRpcServiceImpl implements MboxRpcService{
    @Resource
    private IFileDownloadService.Iface fileDownloadService;

    @Resource
    private MboxService mboxService;

    @Resource
    private XmAuthRpcService xmAuthRpcService;

    @Override
    public String getNoAuthUrl(String xmImageUrl, String imageToken) {
        try{
            return new SimpleRetryTemplate<String>(){
                @Override
                public String invoke() throws Exception {
                    return fileDownloadService.signForAuthFreeUrl(xmImageUrl, imageToken);
                }
            }.retryWithException(Exception.class, 3).executeWithRetry();
        }catch (TException e){
            log.warn("#MboxRpcServiceImpl.getNoAuthUrl#warn 超时, xmImageUrl:{}, imageToken:{}",xmImageUrl, imageToken, e);
        }catch (Exception e){
            log.error("#MboxRpcServiceImpl.getNoAuthUrl# error, xmImageUrl:{}, imageToken:{}", xmImageUrl, imageToken, e);
        }
        return null;
    }

    @Override
    public String changeDownloadTempUrl(String imageUrl){
        ChangeDownloadTempUrlReq request = new ChangeDownloadTempUrlReq();
        ChangeDownloadTempUrlReq.Request req = new ChangeDownloadTempUrlReq.Request();
        req.setUrl(imageUrl);
        request.setRequest(req);
        try{
            return new SimpleRetryTemplate<String>(){
                @Override
                public String invoke() throws Exception {
                    ChangeDownloadTempUrlResp resp = mboxService.changeDownloadTempUrl(xmAuthRpcService.getToken(),request);
                    if(resp == null || resp.getData() == null || resp.getStatus() == null){
                        log.error("#MboxRpcServiceImpl.changeDownloadTempUrl# error, resp:{}", resp);
                        return null;
                    }
                    if(resp.getStatus().getCode() != ResCodeEnum.SUCCESS.getCode()){
                        log.error("#MboxRpcServiceImpl.changeDownloadTempUrl# error,出错，返回错误码：{}, message:{},resp:{}",resp.getStatus().getCode(), resp.getStatus().getMsg(),resp);
                        return null;
                    }
                    return resp.getData().getUrl();
                }
            }.retryWithException(Exception.class, 3).executeWithRetry();
        }catch (TException e){
            log.warn("#MboxRpcServiceImpl.changeDownloadTempUrl# warn, imageUrl:{}", imageUrl, e);
        } catch (Exception e){
            log.error("#MboxRpcServiceImpl.changeDownloadTempUrl# warn, imageUrl:{}", imageUrl, e);
        }
        return null;
    }
}
