package com.meituan.banma.llm.corpus.server.dal.entity;

import lombok.Data;

import java.util.Date;

/**
 * 问题类型实体类
 */
@Data
public class QuestionTypeEntity {
    
    /**
     * 自增主键ID
     */
    private Long id;
    
    /**
     * 问题类型业务ID
     */
    private Integer questionTypeId;
    
    /**
     * 关联的监控组ID
     */
    private Long monitoringGroupId;
    
    /**
     * 前端展示序号
     */
    private Integer sortOrder;
    
    /**
     * 类型名称
     */
    private String typeName;
    
    /**
     * 类型描述
     */
    private String typeDesc;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
} 