package com.meituan.banma.llm.corpus.server.service;

import com.alibaba.fastjson.JSONObject;
import com.meituan.banma.llm.corpus.api.exception.LlmCorpusException;
import com.meituan.banma.llm.corpus.server.common.domain.dto.SaveCorpusBotChatMessageParam;
import com.sankuai.xm.openplatform.callback.constant.CallBackEventTypeEnum;

/**
 * 语料机器人对话服务
 * <AUTHOR>
 */
public interface ICorpusBotChatService {

    /**
     * 保存对话消息
     * @param param 消息参数
     */
    void saveChatMessage(SaveCorpusBotChatMessageParam param) throws LlmCorpusException;
}
