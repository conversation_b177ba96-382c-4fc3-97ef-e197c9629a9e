package com.meituan.banma.llm.corpus.server.controller;

import com.meituan.banma.llm.corpus.server.common.domain.dto.ContentQualityDTO;
import com.meituan.banma.llm.corpus.server.common.domain.dto.KmToQaCorpusDTO;
import com.meituan.banma.llm.corpus.server.common.domain.dto.KnowledgeSimilarityRecord;
import com.meituan.banma.llm.corpus.server.common.domain.dto.KnowledgeSimilarityRecordWithScore;
import com.meituan.banma.llm.corpus.server.common.domain.dto.PageDTO;
import com.meituan.banma.llm.corpus.server.common.domain.dto.TtToCorpusTaskInfo;
import com.meituan.banma.llm.corpus.server.controller.request.review.GetContentQualityAssessmentRequest;
import com.meituan.banma.llm.corpus.server.controller.request.review.QuerySimilarContentRequest;
import com.meituan.banma.llm.corpus.server.service.IKnowledgeBaseService;
import com.meituan.banma.llm.corpus.server.service.IReviewService;
import com.meituan.banma.llm.corpus.server.common.CommonResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.alibaba.fastjson.JSON;

@RestController
@Slf4j
@RequestMapping("/review")
public class ReviewController {

    @Autowired
    private IReviewService reviewService;

    @Autowired
    private IKnowledgeBaseService knowledgeBaseService;

    @GetMapping("/queryModelOutputByTicketId")
    public Object queryModelOutputByTicketId(@RequestParam String ticketId) {
        try {
            if (StringUtils.isBlank(ticketId)) {
                return CommonResult.error("ticketId不能为空");
            }
            Object result = reviewService.queryModelOutputByTicketId(ticketId);
            return CommonResult.success(result);
        } catch (Exception e) {
            log.error("queryModelOutputByTicketId# 执行失败, ticketId:{}, 异常原因:{}", ticketId, e);
            return CommonResult.error(String.format("执行失败,异常原因=%s", e.getMessage()));
        }
    }

    @GetMapping("/queryModelOutputByTaskId")
    public Object queryModelOutputByTaskId(@RequestParam(value = "taskId", defaultValue = "") String taskId) {
        try {
            if (StringUtils.isBlank(taskId)) {
                return CommonResult.error("taskId不能为空");
            }
            Object result = knowledgeBaseService.queryConvertTtToKnowledgeTask(taskId);
            return CommonResult.success(result);
        } catch (Exception e) {
            log.error("queryModelOutputByTicketId# 执行失败, taskId:{}, 异常原因:{}", taskId, e);
            return CommonResult.error(String.format("执行失败,异常原因=%s", e.getMessage()));
        }
    }

    @GetMapping("/queryKmQaByTaskId")
    public Object queryKmQaByTaskId(@RequestParam String taskId) {
        try {
            if (StringUtils.isBlank(taskId)) {
                return CommonResult.error("taskId不能为空");
            }
            List<KmToQaCorpusDTO> kmToQaCorpusDTOS = knowledgeBaseService.queryConvertKmToQaAsList(taskId);
            Map<String, Object> result = new HashMap<>();
            result.put("data", kmToQaCorpusDTOS);
            result.put("total", kmToQaCorpusDTOS.size());
            return CommonResult.success(result);
        } catch (Exception e) {
            log.error("queryKmQaByTaskId# 执行失败, taskId:{}, 异常原因:{}", taskId, e);
            return CommonResult.error(String.format("执行失败,异常原因=%s", e.getMessage()));
        }
    }

    /**
     * 根据rgId查询所有相关的模型输出任务
     *
     * @param rgId 值班组ID
     * @param pageNum 页码，默认1
     * @param pageSize 每页大小，默认10
     * @return 转换任务信息列表
     */
    @GetMapping("/queryModelOutputsByRgId")
    public Object queryModelOutputsByRgId(
            @RequestParam(value = "rgId") Long rgId,
            @RequestParam(value = "pageNum", defaultValue = "1") int pageNum,
            @RequestParam(value = "pageSize", defaultValue = "10") int pageSize) {
        try {
            if (rgId == null || rgId <= 0) {
                return CommonResult.error("rgId不能为空且必须大于0");
            }

            // 直接使用分页查询方法
            PageDTO<TtToCorpusTaskInfo> pageData = knowledgeBaseService.queryConvertTtToKnowledgeTasksByRgId(rgId, pageNum, pageSize);

            // 构建结果
            Map<String, Object> result = new HashMap<>();
            result.put("currentPage", pageData.getPageNum());
            result.put("pageSize", pageSize);
            result.put("total", pageData.getTotalCount());
            result.put("totalPage", pageData.getTotalPage());
            result.put("list", pageData.getData());

            return CommonResult.success("success", result);
        } catch (Exception e) {
            log.error("queryModelOutputsByRgId# 执行失败, rgId:{}, pageNum:{}, pageSize:{}, 异常原因:{}",
                    rgId, pageNum, pageSize, e);
            return CommonResult.error(String.format("执行失败,异常原因=%s", e.getMessage()));
        }
    }

    /**
     * 根据rgId和misId查询所有相关的模型输出任务
     *
     * @param rgId 值班组ID
     * @param misId 用户MIS ID
     * @param pageNum 页码，默认1
     * @param pageSize 每页大小，默认10
     * @return 转换任务信息列表
     */
    @GetMapping("/queryModelOutputsByRgIdAndMisId")
    public Object queryModelOutputsByRgIdAndMisId(
            @RequestParam(value = "rgId") Long rgId,
            @RequestParam(value = "misId") String misId,
            @RequestParam(value = "pageNum", defaultValue = "1") int pageNum,
            @RequestParam(value = "pageSize", defaultValue = "10") int pageSize) {
        try {
            if (rgId == null || rgId <= 0) {
                return CommonResult.error("rgId不能为空且必须大于0");
            }
            if (StringUtils.isBlank(misId)) {
                return CommonResult.error("misId不能为空");
            }

            // 直接使用分页查询方法
            PageDTO<TtToCorpusTaskInfo> pageData = knowledgeBaseService.queryConvertTtToKnowledgeTasksByRgIdAndMisId(rgId, misId, pageNum, pageSize);

            // 构建结果
            Map<String, Object> result = new HashMap<>();
            result.put("currentPage", pageData.getPageNum());
            result.put("pageSize", pageSize);
            result.put("total", pageData.getTotalCount());
            result.put("totalPage", pageData.getTotalPage());
            result.put("list", pageData.getData());

            return CommonResult.success("success", result);
        } catch (Exception e) {
            log.error("queryModelOutputsByRgIdAndMisId# 执行失败, rgId:{}, misId:{}, pageNum:{}, pageSize:{}, 异常原因:{}",
                    rgId, misId, pageNum, pageSize, e);
            return CommonResult.error(String.format("执行失败,异常原因=%s", e.getMessage()));
        }
    }

    @PostMapping("/saveModifiedOutput")
    public Object saveModifiedOutput(@RequestParam String ticketId, @RequestParam String title, @RequestParam String modifiedContent, @RequestParam String misId, @RequestParam Long rgId, @RequestParam String taskId, @RequestParam(required = false) String tagsIds) {
        try {
            if (StringUtils.isBlank(ticketId)) {
                return CommonResult.error("ticketId不能为空");
            }
            if (StringUtils.isBlank(modifiedContent)) {
                return CommonResult.error("modifiedContent不能为空");
            }
            if (StringUtils.isBlank(misId)) {
                return CommonResult.error("misId不能为空");
            }
            if (rgId == null) {
                return CommonResult.error("rgId不能为空");
            }

            reviewService.saveModifiedOutput(ticketId, title, modifiedContent, misId, rgId, taskId, tagsIds);
            return CommonResult.success("保存成功");
        } catch (Exception e) {
            log.error("saveModifiedOutput# 执行失败, ticketId:{}, 异常原因:{}", ticketId, e);
            return CommonResult.error(String.format("执行失败,异常原因=%s", e.getMessage()));
        }
    }

    @GetMapping("/queryLatestContentByRgId")
    public Object queryLatestContentByRgId(@RequestParam Long rgId, @RequestParam(required = false) String ak) {
        try {
            if (rgId == null) {
                return CommonResult.error("rgId不能为空");
            }
            String result = reviewService.queryLatestContentByRgId(rgId, ak);
            return CommonResult.success(result);
        } catch (Exception e) {
            log.error("queryLatestContentByRgId# 执行失败, rgId:{}, 异常原因:{}", rgId, e);
            return CommonResult.error(String.format("执行失败,异常原因=%s", e.getMessage()));
        }
    }

    /**
     * 统计指定值班组和用户下进行中的任务量
     *
     * @param rgId 值班组ID
     * @param misId 用户MIS ID
     * @return 统计结果
     */
    @GetMapping("/countModelOutputsWithAskStatusZero")
    public Object countModelOutputsWithAskStatusZero(@RequestParam Long rgId, @RequestParam String misId) {
        try {
            if (rgId == null) {
                return CommonResult.error("rgId不能为空");
            }
            if (StringUtils.isBlank(misId)) {
                return CommonResult.error("misId不能为空");
            }
            int count = knowledgeBaseService.countModelOutputsWithAskStatusZero(rgId, misId);
            return CommonResult.success(count);
        } catch (Exception e) {
            log.error("countModelOutputsWithAskStatusZero# 执行失败, rgId:{}, misId:{}, 异常原因:{}", rgId, misId, e);
            return CommonResult.error(String.format("执行失败,异常原因=%s", e.getMessage()));
        }
    }


    @GetMapping("/testString")
    public Object testString() {
        try {
            String result = reviewService.testString();
            return CommonResult.success(result);
        } catch (Exception e) {
            log.error("testString# 执行失败, 异常原因:{}", e.getMessage());
            return CommonResult.error(String.format("执行失败,异常原因=%s", e.getMessage()));
        }
    }

    @GetMapping("/testRandomString")
    public Object testRandomString() {
        try {
            String result = reviewService.testRandomString();
            return CommonResult.success(result);
        } catch (Exception e) {
            log.error("testRandomString# 执行失败, 异常原因:{}", e.getMessage());
            return CommonResult.error(String.format("执行失败,异常原因=%s", e.getMessage()));
        }
    }

    @PostMapping("/querySimilarContent")
    public Object querySimilarContent(@RequestBody QuerySimilarContentRequest request) {
        try {
            List<KnowledgeSimilarityRecord> knowledgeSimilarityRecordList = knowledgeBaseService.queryKnowledgeSimilarity(request.getRgId(), request.getQuery());
            Map<String, Object> result = new HashMap<>();
            result.put("data", knowledgeSimilarityRecordList);
            result.put("total", knowledgeSimilarityRecordList.size());
            result.put("pageSize", knowledgeSimilarityRecordList.size());
            result.put("currentPage", 1);
            return CommonResult.success(result);
        } catch (Exception e) {
            log.error("querySimilarContent# 执行失败, 异常原因:", e);
            return CommonResult.error(String.format("执行失败,异常原因=%s", e.getMessage()));
        }
    }

    @PostMapping("/querySimilarContentWithScore")
    public Object querySimilarContentWithScore(@RequestBody QuerySimilarContentRequest request) {
        try {
            List<KnowledgeSimilarityRecordWithScore> knowledgeSimilarityRecordList = knowledgeBaseService.queryKnowledgeSimilarityWithScore(request.getRgId(), request.getQuery());
            Map<String, Object> result = new HashMap<>();
            result.put("data", knowledgeSimilarityRecordList);
            result.put("total", knowledgeSimilarityRecordList.size());
            result.put("pageSize", knowledgeSimilarityRecordList.size());
            result.put("currentPage", 1);
            return CommonResult.success(result);
        } catch (Exception e) {
            log.error("querySimilarContent# 执行失败, 异常原因:", e);
            return CommonResult.error(String.format("执行失败,异常原因=%s", e.getMessage()));
        }
    }

    /**
     * 获取语料质量评估结果
     *
     * @param request 请求参数，包含query字段
     * @return 语料质量评估结果
     */
    @PostMapping("/getContentQualityAssessment")
    public Object getContentQualityAssessment(@RequestBody GetContentQualityAssessmentRequest request) {
        try {
            log.info("getContentQualityAssessment# 接收请求参数: {}", JSON.toJSONString(request));
            ContentQualityDTO result = knowledgeBaseService.getContentQualityAssessment(request.getQuery());
            Map<String, Object> response = new HashMap<>();
            response.put("result", result);
            response.put("query", request.getQuery());
            return CommonResult.success(response);
        } catch (Exception e) {
            log.error("getContentQualityAssessment# 执行失败, 异常原因:", e);
            return CommonResult.error(String.format("执行失败,异常原因=%s", e.getMessage()));
        }
    }
}
