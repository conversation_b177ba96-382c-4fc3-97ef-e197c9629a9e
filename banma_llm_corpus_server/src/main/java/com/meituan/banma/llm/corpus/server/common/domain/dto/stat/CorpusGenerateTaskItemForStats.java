package com.meituan.banma.llm.corpus.server.common.domain.dto.stat;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CorpusGenerateTaskItemForStats {

    private String taskId;
    private String ticketId;
    private Long dxGroupId;
    /**
     * 任务状态 ModelOutputTaskStatusEnum
     */
    private Integer taskStatus;
    private Long creatorUserDxId;
    private String creatorMisId;
    private String creatorUserName;
    private String creatorOrgId;
    private String creatorOrgName;
    private String creatorOrgPath;

    /**
     * 任务来源平台ID, ConvertTaskPlatformEnum
     */
    private Integer platformId;
    private String taskMessage;

    /**
     * 任务对应数据是否落库
     */
    private Boolean taskSaved;

    private Timestamp createTime;
    private Timestamp updateTime;
}
