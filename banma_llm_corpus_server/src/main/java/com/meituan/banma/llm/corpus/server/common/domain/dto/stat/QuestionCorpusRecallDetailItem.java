package com.meituan.banma.llm.corpus.server.common.domain.dto.stat;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class QuestionCorpusRecallDetailItem {
    private String questionContent;
    private String questionMessageId;
    private String questionConversationId;
    private List<RecalledCorpusChunkInfo> recalledCorpusChunkInfoList = new ArrayList<>();
}
