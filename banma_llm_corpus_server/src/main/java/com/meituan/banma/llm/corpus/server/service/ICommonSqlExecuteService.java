package com.meituan.banma.llm.corpus.server.service;

import com.meituan.banma.llm.corpus.server.common.domain.vo.TableSchema;

import java.util.List;
import java.util.Map;

public interface ICommonSqlExecuteService {
    /**
     * 获取所有表结构
     */
    List<TableSchema> getAllTableSchema();

    /**
     * select sql
     */
    List<Map<String, Object>> query(String sql, String jdbcRef);

    /**
     * explain sql
     */
    String explain(String sql, String jdbcRef);
}
