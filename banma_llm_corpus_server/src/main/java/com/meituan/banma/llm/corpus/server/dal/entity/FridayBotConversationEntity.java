package com.meituan.banma.llm.corpus.server.dal.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Date;
import java.time.LocalDateTime;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class FridayBotConversationEntity {
    private String recordId;
    private Long id;
    private String conversationId;
    private String userId;
    private String userType;
    private String appId;
    private String requestId;
    private String messageId;
    private String parentMessageId;
    private String role;
    private String generateType;
    private String message;
    private String status;
    private LocalDateTime addTime;
    private LocalDateTime updateTime;
    private String recallInfo;
    private String assistantRecommendation;
    private String conversationName;
    private Integer isNew;
    private Integer deleted;
    private String accessChannel;
    private LocalDateTime lastChatTime;
    private LocalDateTime recordAddTime;
    private LocalDateTime recordUpdateTime;
    private Long dt;
}
