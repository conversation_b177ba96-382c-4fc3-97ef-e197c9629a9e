package com.meituan.banma.llm.corpus.server.service.impl;

import com.alibaba.fastjson.JSON;
import com.meituan.banma.llm.corpus.server.common.domain.dto.TicketRangeTaskRecordDTO;
import com.meituan.banma.llm.corpus.server.dal.entity.TicketRangeTaskRecordEntity;
import com.meituan.banma.llm.corpus.server.dal.mapper.TicketRangeTaskRecordMapper;
import com.meituan.banma.llm.corpus.server.service.ITicketRangeTaskRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 工单范围任务记录服务实现类
 */
@Slf4j
@Service
public class TicketRangeTaskRecordServiceImpl implements ITicketRangeTaskRecordService {
    
    @Resource
    private TicketRangeTaskRecordMapper taskRecordMapper;
    
    @Override
    public TicketRangeTaskRecordDTO createTaskRecord(List<String> ticketIds) {
        if (ticketIds == null) {
            ticketIds = new ArrayList<>();
        }
        
        // 创建任务记录实体
        TicketRangeTaskRecordEntity entity = new TicketRangeTaskRecordEntity();
        
        // 生成UUID作为任务ID
        String taskId = UUID.randomUUID().toString();
        entity.setTaskId(taskId);
        
        // 将工单ID列表转换为JSON字符串
        entity.setTtIds(JSON.toJSONString(ticketIds));
        
        // 设置创建时间
        entity.setCreatedAt(Timestamp.from(Instant.now()));

        log.warn("#TicketRangeTaskRecordServiceImpl#createTaskRecord 创建任务记录, taskId={}", taskId);
        return convertToDTO(entity);
//        try {
//            // 保存到数据库
//            int rows = taskRecordMapper.insert(entity);
//            if (rows > 0) {
//                return convertToDTO(entity);
//            } else {
//                log.warn("#TicketRangeTaskRecordServiceImpl#createTaskRecord 创建任务记录失败, taskId={}", taskId);
//                return null;
//            }
//        } catch (Exception e) {
//            log.error("#TicketRangeTaskRecordServiceImpl#createTaskRecord 创建任务记录异常, taskId={}, error={}", taskId, e.getMessage(), e);
//            return null;
//        }
    }
    
    @Override
    public boolean updateTaskRecord(String taskId, List<String> ticketIds) {
        if (taskId == null) {
            log.warn("#TicketRangeTaskRecordServiceImpl#updateTaskRecord 更新任务记录失败，任务ID为空");
            return false;
        }
        
        if (ticketIds == null) {
            ticketIds = new ArrayList<>();
        }
        
        try {
            TicketRangeTaskRecordEntity existingEntity = taskRecordMapper.findByTaskId(taskId);
            if (existingEntity == null) {
                log.warn("#TicketRangeTaskRecordServiceImpl#updateTaskRecord 更新任务记录失败，任务不存在: taskId={}", taskId);
                return false;
            }

            existingEntity.setTtIds(JSON.toJSONString(ticketIds));

            int rows = taskRecordMapper.update(existingEntity);
            if (rows > 0) {
                return true;
            } else {
                log.warn("#TicketRangeTaskRecordServiceImpl#updateTaskRecord 更新任务记录失败, taskId={}", taskId);
                return false;
            }
        } catch (Exception e) {
            log.error("#TicketRangeTaskRecordServiceImpl#updateTaskRecord 更新任务记录异常, taskId={}, error={}", taskId, e.getMessage(), e);
            return false;
        }
    }
    
    @Override
    public TicketRangeTaskRecordDTO getTaskRecord(String taskId) {
        if (taskId == null) {
            return null;
        }
        
        try {
            TicketRangeTaskRecordEntity entity = taskRecordMapper.findByTaskId(taskId);
            return convertToDTO(entity);
        } catch (Exception e) {
            log.error("#TicketRangeTaskRecordServiceImpl#getTaskRecord 查询任务记录异常, taskId={}, error={}", taskId, e.getMessage(), e);
            return null;
        }
    }
    
    @Override
    public List<TicketRangeTaskRecordDTO> getAllTaskRecords() {
        try {
            List<TicketRangeTaskRecordEntity> entities = taskRecordMapper.findAll();
            if (entities == null || entities.isEmpty()) {
                return Collections.emptyList();
            }
            
            return entities.stream()
                    .map(this::convertToDTO)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("#TicketRangeTaskRecordServiceImpl#getAllTaskRecords 查询所有任务记录异常, error={}", e.getMessage(), e);
            return Collections.emptyList();
        }
    }
    
    @Override
    public List<TicketRangeTaskRecordDTO> getTaskRecordsByTimeRange(String startTime, String endTime) {
        try {
            List<TicketRangeTaskRecordEntity> entities = taskRecordMapper.findByTimeRange(startTime, endTime);
            if (entities == null || entities.isEmpty()) {
                return Collections.emptyList();
            }
            
            return entities.stream()
                    .map(this::convertToDTO)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("#TicketRangeTaskRecordServiceImpl#getTaskRecordsByTimeRange 根据时间范围查询任务记录异常, startTime={}, endTime={}, error={}",
                    startTime, endTime, e.getMessage(), e);
            return Collections.emptyList();
        }
    }
    
    /**
     * 将实体对象转换为DTO
     */
    private TicketRangeTaskRecordDTO convertToDTO(TicketRangeTaskRecordEntity entity) {
        if (entity == null) {
            return null;
        }
        
        TicketRangeTaskRecordDTO dto = new TicketRangeTaskRecordDTO();
        BeanUtils.copyProperties(entity, dto);
        
        // 将JSON字符串转换为List<String>
        if (entity.getTtIds() != null) {
            dto.setTtIds(JSON.parseArray(entity.getTtIds(), String.class));
        } else {
            dto.setTtIds(new ArrayList<>());
        }
        
        return dto;
    }
} 