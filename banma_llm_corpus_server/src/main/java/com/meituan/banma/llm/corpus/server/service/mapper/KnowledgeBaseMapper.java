package com.meituan.banma.llm.corpus.server.service.mapper;


import com.alibaba.fastjson.JSON;
import com.meituan.banma.llm.corpus.server.common.domain.dto.ConvertTtToKnowledgeParam;
import com.meituan.banma.llm.corpus.server.common.domain.dto.KnowledgeMergeParam;
import com.meituan.banma.llm.corpus.server.common.domain.dto.LlmCorpusConvertTaskMessageDTO;
import com.meituan.banma.llm.corpus.server.common.domain.dto.SaveMergeCorpusParam;
import com.meituan.banma.llm.corpus.server.common.domain.dto.TTInfoDTO;
import com.meituan.banma.llm.corpus.server.common.domain.dto.TicketDetailDTO;
import com.meituan.banma.llm.corpus.server.common.domain.dto.TtToCorpusTaskInfo;
import com.meituan.banma.llm.corpus.server.controller.request.corpus.MergeCorpusRequest;
import com.meituan.banma.llm.corpus.server.controller.request.corpus.SaveKnowledgeCorpusRequest;
import com.meituan.banma.llm.corpus.server.dal.entity.ModelOutputTaskEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.Collections;
import java.util.List;

@Mapper()
@Slf4j
public abstract class KnowledgeBaseMapper {
    private static final KnowledgeBaseMapper KNOWLEDGE_BASE_MAPPER = Mappers.getMapper(KnowledgeBaseMapper.class);

    public static KnowledgeBaseMapper get() {
        return KNOWLEDGE_BASE_MAPPER;
    }

    public static TTInfoDTO trans2TtInfoDTO(Long dxGroupId, Long orgId, TicketDetailDTO ticketDetailDTO) {
        TTInfoDTO ttInfoDTO = new TTInfoDTO();
        ttInfoDTO.setGroupId(dxGroupId);
        ttInfoDTO.setRgId(orgId);
        ttInfoDTO.setTicketId(ticketDetailDTO.getId());
        ttInfoDTO.setAssigned(ticketDetailDTO.getAssigned());
        ttInfoDTO.setItemName(ticketDetailDTO.getItemName());
        ttInfoDTO.setSla(ticketDetailDTO.getSla());
        ttInfoDTO.setTicketType(ticketDetailDTO.getTicketType());
        ttInfoDTO.setState(ticketDetailDTO.getState());
        ttInfoDTO.setTypeName(ticketDetailDTO.getTypeName());
        ttInfoDTO.setCategoryName(ticketDetailDTO.getCategoryName());
        ttInfoDTO.setName(ticketDetailDTO.getName());
        ttInfoDTO.setDesc(ticketDetailDTO.getDesc());
        ttInfoDTO.setCreatedAt(ticketDetailDTO.getCreatedAt());
        ttInfoDTO.setUpdatedAt(ticketDetailDTO.getUpdatedAt());
        ttInfoDTO.setUpdatedBy(ticketDetailDTO.getUpdatedBy());
        return ttInfoDTO;
    }

    @Mappings({
            @Mapping(target = "taskId", source = "taskId"),
            @Mapping(target = "ticketId", source = "param.ttId"),
            @Mapping(target = "dxGroupId", source = "param.dxGroupId"),
            @Mapping(target = "creatorUserName", source = "param.creatorUserName"),
            @Mapping(target = "creatorUserDxId", source = "param.creatorDxUserId"),
            @Mapping(target = "creatorMisId", source = "param.misId"),
            @Mapping(target = "taskStatus", source = "taskStatus"),
            @Mapping(target = "platformId", expression = "java(param.getPlatformId().getCode())"),
            @Mapping(target = "taskMessage", source = "taskMessage")
    })
    public abstract LlmCorpusConvertTaskMessageDTO trans2LlmCorpusConvertTaskMessageDTO(String taskId,Integer taskStatus,ConvertTtToKnowledgeParam param, String taskMessage);

    @Mappings({
            @Mapping(target = "taskMissingInfo", expression = "java(getTaskMissingInfo(entity.getTaskMissingInfo()))")
    })
    public abstract TtToCorpusTaskInfo trans(ModelOutputTaskEntity entity);

    @Mappings({
            @Mapping(target = "corpusList", source = "corpusTextList")
    })
    public abstract KnowledgeMergeParam trans(MergeCorpusRequest request);
    public List<String> getTaskMissingInfo(String missingInfo) {
        if (StringUtils.isBlank(missingInfo)) {
            return Collections.emptyList();
        }
        return JSON.parseArray(missingInfo, String.class);
    }

    public abstract SaveMergeCorpusParam trans(SaveKnowledgeCorpusRequest request);
}
