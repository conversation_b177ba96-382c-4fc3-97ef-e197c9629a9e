package com.meituan.banma.llm.corpus.server.utils;

import com.dianping.rhino.Rhino;
import com.dianping.rhino.threadpool.DefaultThreadPoolProperties;
import com.dianping.rhino.threadpool.component.RhinoRejectedExecutionHandler;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.RejectedExecutionHandler;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@Slf4j
public class ThreadPoolUtils {
    /**
     * 语料转换任务线程池
     */
    public static ThreadPoolExecutor getLlmCorpusConvertTaskThreadPool() {
        return Rhino.newThreadPool("getLlmCorpusConvertTaskThreadPool", DefaultThreadPoolProperties.Setter()
                .withCoreSize(8)
                .withMaxSize(32)
                .withKeepAliveTimeMinutes(1000)
                .withKeepAliveTimeUnit(TimeUnit.MILLISECONDS)
                .withMaxQueueSize(10)
                .withTraceable(true)
                .withRejectHandler(new RhinoRejectedExecutionHandler("getLlmCorpusConvertTaskThreadPool", new RejectedExecutionHandler() {
                    @Override
                    public void rejectedExecution(Runnable r, ThreadPoolExecutor executor) {
                        log.warn("getLlmCorpusConvertTaskThreadPool queue is full,main-thread run");
                        //当线程池满时,由调用者线程执行任务(该场景下即为由主线程调用)
                        if (!executor.isShutdown()) {
                            r.run();
                        }
                    }
                }))).getExecutor();
    }

    /**
     * friday接口调用线程池
     * @return
     */
    public static ThreadPoolExecutor getFridayConversationThreadPool() {
        return Rhino.newThreadPool("fridayConversationThreadPool", DefaultThreadPoolProperties.Setter()
                .withCoreSize(8)
                .withMaxSize(32)
                .withKeepAliveTimeMinutes(1000)
                .withKeepAliveTimeUnit(TimeUnit.MILLISECONDS)
                .withMaxQueueSize(10)
                .withTraceable(true)
                .withRejectHandler(new RhinoRejectedExecutionHandler("fridayConversationThreadPool", new RejectedExecutionHandler() {
                    @Override
                    public void rejectedExecution(Runnable r, ThreadPoolExecutor executor) {
                        log.warn("fridayConversationThreadPool queue is full,main-thread run");
                        //当线程池满时,由调用者线程执行任务(该场景下即为由主线程调用)
                        if (!executor.isShutdown()) {
                            r.run();
                        }
                    }
                }))).getExecutor();
    }

    /**
     * 批量图片转文字线程池
     * @return
     */
    public static ThreadPoolExecutor getBatchImageToTextThreadPool() {
        return Rhino.newThreadPool("getBatchImageToTextThreadPool", DefaultThreadPoolProperties.Setter()
                .withCoreSize(8)
                .withMaxSize(32)
                .withKeepAliveTimeMinutes(1000)
                .withKeepAliveTimeUnit(TimeUnit.MILLISECONDS)
                .withMaxQueueSize(10)
                .withTraceable(true)
                .withRejectHandler(new RhinoRejectedExecutionHandler("getBatchImageToTextThreadPool", new RejectedExecutionHandler() {
                    @Override
                    public void rejectedExecution(Runnable r, ThreadPoolExecutor executor) {
                        log.warn("getBatchImageToTextThreadPool queue is full,main-thread run");
                        //当线程池满时,由调用者线程执行任务(该场景下即为由主线程调用)
                        if (!executor.isShutdown()) {
                            r.run();
                        }
                    }
                }))).getExecutor();
    }

    public static ThreadPoolExecutor getRefreshDocumentThreadPool() {
        return Rhino.newThreadPool("getRefreshDocumentThreadPool", DefaultThreadPoolProperties.Setter()
                .withCoreSize(8)
                .withMaxSize(32)
                .withKeepAliveTimeMinutes(1000)
                .withKeepAliveTimeUnit(TimeUnit.MILLISECONDS)
                .withMaxQueueSize(10)
                .withTraceable(true)
                .withRejectHandler(new RhinoRejectedExecutionHandler("getRefreshDocumentThreadPool", new RejectedExecutionHandler() {
                    @Override
                    public void rejectedExecution(Runnable r, ThreadPoolExecutor executor) {
                        log.warn("getRefreshDocumentThreadPool queue is full,main-thread run");
                        //当线程池满时,由调用者线程执行任务(该场景下即为由主线程调用)
                        if (!executor.isShutdown()) {
                            r.run();
                        }
                    }
                }))).getExecutor();
    }

    public static ThreadPoolExecutor getStatisticsReportThreadPool() {
        return Rhino.newThreadPool("getStatisticsReportThreadPool", DefaultThreadPoolProperties.Setter()
                .withCoreSize(8)
                .withMaxSize(32)
                .withKeepAliveTimeMinutes(1000)
                .withKeepAliveTimeUnit(TimeUnit.MILLISECONDS)
                .withMaxQueueSize(10)
                .withTraceable(true)
                .withRejectHandler(new RhinoRejectedExecutionHandler("getStatisticsReportThreadPool", new RejectedExecutionHandler() {
                    @Override
                    public void rejectedExecution(Runnable r, ThreadPoolExecutor executor) {
                        log.warn("getStatisticsReportThreadPool queue is full,main-thread run");
                        //当线程池满时,由调用者线程执行任务(该场景下即为由主线程调用)
                        if (!executor.isShutdown()) {
                            r.run();
                        }
                    }
                }))).getExecutor();
    }

    public static ThreadPoolExecutor getMergeCorpusThreadPool() {
        return Rhino.newThreadPool("getMergeCorpusThreadPool", DefaultThreadPoolProperties.Setter()
                .withCoreSize(8)
                .withMaxSize(32)
                .withKeepAliveTimeMinutes(1000)
                .withKeepAliveTimeUnit(TimeUnit.MILLISECONDS)
                .withMaxQueueSize(10)
                .withTraceable(true)
                .withRejectHandler(new RhinoRejectedExecutionHandler("getMergeCorpusThreadPool", new RejectedExecutionHandler() {
                    @Override
                    public void rejectedExecution(Runnable r, ThreadPoolExecutor executor) {
                        log.warn("getMergeCorpusThreadPool queue is full,main-thread run");
                        //当线程池满时,由调用者线程执行任务(该场景下即为由主线程调用)
                        if (!executor.isShutdown()) {
                            r.run();
                        }
                    }
                }))).getExecutor();
    }


    public static ThreadPoolExecutor getDxMonitoringThreadPool() {
        return Rhino.newThreadPool("getDxMonitoringThreadPool", DefaultThreadPoolProperties.Setter()
                .withCoreSize(6)
                .withMaxSize(32)
                .withKeepAliveTimeMinutes(1000)
                .withKeepAliveTimeUnit(TimeUnit.MILLISECONDS)
                .withMaxQueueSize(10)
                .withTraceable(true)
                .withRejectHandler(new RhinoRejectedExecutionHandler("getDxMonitoringThreadPool", new RejectedExecutionHandler() {
                    @Override
                    public void rejectedExecution(Runnable r, ThreadPoolExecutor executor) {
                        log.warn("getDxMonitoringThreadPool queue is full,main-thread run");
                        //当线程池满时,由调用者线程执行任务(该场景下即为由主线程调用)
                        if (!executor.isShutdown()) {
                            r.run();
                        }
                    }
                }))).getExecutor();
    }

    /**
     * 批量处理TT内容的线程池
     * @return 线程池实例
     */
    public static ThreadPoolExecutor getBatchProcessTtThreadPool() {
        return Rhino.newThreadPool("getBatchProcessTtThreadPool", DefaultThreadPoolProperties.Setter()
                .withCoreSize(8)
                .withMaxSize(32)
                .withKeepAliveTimeMinutes(1000)
                .withKeepAliveTimeUnit(TimeUnit.MILLISECONDS)
                .withMaxQueueSize(10)
                .withTraceable(true)
                .withRejectHandler(new RhinoRejectedExecutionHandler("getBatchProcessTtThreadPool", new RejectedExecutionHandler() {
                    @Override
                    public void rejectedExecution(Runnable r, ThreadPoolExecutor executor) {
                        log.warn("getBatchProcessTtThreadPool queue is full, main-thread run");
                        //当线程池满时,由调用者线程执行任务
                        if (!executor.isShutdown()) {
                            r.run();
                        }
                    }
                }))).getExecutor();
    }

    public static ThreadPoolExecutor getCheckUrlThreadPool() {
        return Rhino.newThreadPool("getCheckUrlThreadPool", DefaultThreadPoolProperties.Setter()
                .withCoreSize(8)
                .withMaxSize(32)
                .withKeepAliveTimeMinutes(1000)
                .withKeepAliveTimeUnit(TimeUnit.MILLISECONDS)
                .withMaxQueueSize(10)
                .withTraceable(true)
                .withRejectHandler(new RhinoRejectedExecutionHandler("getCheckUrlThreadPool", new RejectedExecutionHandler() {
                    @Override
                    public void rejectedExecution(Runnable r, ThreadPoolExecutor executor) {
                        log.warn("getCheckUrlThreadPool queue is full, main-thread run");
                        //当线程池满时,由调用者线程执行任务
                        if (!executor.isShutdown()) {
                            r.run();
                        }
                    }
                }))).getExecutor();
    }

    /**
     * 问题解决状态判断线程池
     * @return 线程池实例
     */
    public static ThreadPoolExecutor getQuestionResolveStateThreadPool() {
        return Rhino.newThreadPool("getQuestionResolveStateThreadPool", DefaultThreadPoolProperties.Setter()
                .withCoreSize(8)
                .withMaxSize(32)
                .withKeepAliveTimeMinutes(1000)
                .withKeepAliveTimeUnit(TimeUnit.MILLISECONDS)
                .withMaxQueueSize(10)
                .withTraceable(true)
                .withRejectHandler(new RhinoRejectedExecutionHandler("getQuestionResolveStateThreadPool", new RejectedExecutionHandler() {
                    @Override
                    public void rejectedExecution(Runnable r, ThreadPoolExecutor executor) {
                        log.warn("getQuestionResolveStateThreadPool queue is full, main-thread run");
                        //当线程池满时,由调用者线程执行任务
                        if (!executor.isShutdown()) {
                            r.run();
                        }
                    }
                }))).getExecutor();
    }

    /**
     * Embedding任务线程池
     * @return 线程池实例
     */
    public static ThreadPoolExecutor getEmbeddingThreadPool() {
        return Rhino.newThreadPool("getEmbeddingThreadPool", DefaultThreadPoolProperties.Setter()
                .withCoreSize(8)
                .withMaxSize(32)
                .withKeepAliveTimeMinutes(1000)
                .withKeepAliveTimeUnit(TimeUnit.MILLISECONDS)
                .withMaxQueueSize(10)
                .withTraceable(true)
                .withRejectHandler(new RhinoRejectedExecutionHandler("getEmbeddingThreadPool", new RejectedExecutionHandler() {
                    @Override
                    public void rejectedExecution(Runnable r, ThreadPoolExecutor executor) {
                        log.warn("getEmbeddingThreadPool queue is full, main-thread run");
                        //当线程池满时,由调用者线程执行任务
                        if (!executor.isShutdown()) {
                            r.run();
                        }
                    }
                }))).getExecutor();
    }

    /**
     * 聚类命名任务线程池
     * @return 线程池实例
     */
    public static ThreadPoolExecutor getClusterNamingThreadPool() {
        return Rhino.newThreadPool("getClusterNamingThreadPool", DefaultThreadPoolProperties.Setter()
                .withCoreSize(8)
                .withMaxSize(16)
                .withKeepAliveTimeMinutes(1000)
                .withKeepAliveTimeUnit(TimeUnit.MILLISECONDS)
                .withMaxQueueSize(20)
                .withTraceable(true)
                .withRejectHandler(new RhinoRejectedExecutionHandler("getClusterNamingThreadPool", new RejectedExecutionHandler() {
                    @Override
                    public void rejectedExecution(Runnable r, ThreadPoolExecutor executor) {
                        log.warn("getClusterNamingThreadPool queue is full, main-thread run");
                        //当线程池满时,由调用者线程执行任务
                        if (!executor.isShutdown()) {
                            r.run();
                        }
                    }
                }))).getExecutor();
    }
}
