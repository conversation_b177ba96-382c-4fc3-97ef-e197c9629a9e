package com.meituan.banma.llm.corpus.server.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

@Slf4j
public class DatetimeUtils {
    public static final String DEFAULT_DATE_TIME_FORMAT = "yyyy-MM-dd HH:mm:ss";
    public static final String DEFAULT_DATE_FORMAT = "yyyy-MM-dd";

    public static String formatDateTime(Date date) {
        if (date == null) {
            return null;
        }
        return new SimpleDateFormat(DEFAULT_DATE_TIME_FORMAT).format(date);
    }

    public static String formatDate(Date date) {
        if (date == null) {
            return null;
        }
        return new SimpleDateFormat(DEFAULT_DATE_FORMAT).format(date);
    }
    public static String formatDate(Date date,String format) {
        if (date == null) {
            return null;
        }
        return new SimpleDateFormat(format).format(date);
    }
    public static Date parseDateTime(String dateStr) {
        if (StringUtils.isBlank(dateStr)) {
            return null;
        }
        try {
            return new SimpleDateFormat(DEFAULT_DATE_TIME_FORMAT).parse(dateStr);
        } catch (ParseException e) {
            return null;
        }
    }

    public static Date parseDate(String dateStr) {
        if (StringUtils.isBlank(dateStr)) {
            return null;
        }
        try {
            return new SimpleDateFormat(DEFAULT_DATE_FORMAT).parse(dateStr);
        } catch (ParseException e) {
            return null;
        }
    }

    public static Date getCurrentDate() {
        return new Date();
    }

    public static String getCurrentDateTimeStr() {
        return formatDateTime(new Date());
    }

    public static String getCurrentDateStr() {
        return formatDate(new Date());
    }

    public static boolean isSameDay(Date date1, Date date2) {
        if (date1 == null || date2 == null) {
            return false;
        }
        Calendar cal1 = Calendar.getInstance();
        cal1.setTime(date1);
        Calendar cal2 = Calendar.getInstance();
        cal2.setTime(date2);
        return cal1.get(Calendar.YEAR) == cal2.get(Calendar.YEAR) &&
                cal1.get(Calendar.MONTH) == cal2.get(Calendar.MONTH) &&
                cal1.get(Calendar.DAY_OF_MONTH) == cal2.get(Calendar.DAY_OF_MONTH);
    }

    public static Date addDays(Date date, int days) {
        if (date == null) {
            return null;
        }
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.DAY_OF_MONTH, days);
        return cal.getTime();
    }

    public static Date addMonths(Date date, int months) {
        if (date == null) {
            return null;
        }
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.MONTH, months);
        return cal.getTime();
    }

    public static Date addYears(Date date, int years) {
        if (date == null) {
            return null;
        }
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.YEAR, years);
        return cal.getTime();
    }

    public static String timestampToDateStr(long timestamp) {
        return formatDateTime(new Date(timestamp));
    }

    public static String timestampToDateTimeStr(long timestamp) {
        return formatDate(new Date(timestamp));
    }
    public static long getTodayStartTime() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTimeInMillis();
    }

    public static long getTodayEndTime() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        return calendar.getTimeInMillis();
    }
    public static long getYesterdayStartTime() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_YEAR, -1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTimeInMillis();
    }
    public static long getYesterdayEndTime() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_YEAR, -1);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        
        // 确保结束时间大于开始时间
        long endTime = calendar.getTimeInMillis();
        long yesterdayStartTime = getYesterdayStartTime();
        
        if (endTime <= yesterdayStartTime) {
            // 时区或日期计算问题导致结束时间早于开始时间，强制设置结束时间为开始时间+23小时59分59秒999毫秒    // 1天减1毫秒
            endTime = yesterdayStartTime + 86399999L;
            log.warn("警告: 修正了昨天的结束时间，确保大于开始时间: {}",new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS").format(new Date(endTime)));
        }
        
        return endTime;
    }
    public static long getLastWeekStartTime() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.WEEK_OF_YEAR, -1);
        calendar.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTimeInMillis();
    }
    public static long getLastWeekEndTime() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.WEEK_OF_YEAR, -1);
        calendar.set(Calendar.DAY_OF_WEEK, Calendar.SUNDAY);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        
        // 确保结束时间大于开始时间
        long endTime = calendar.getTimeInMillis();
        long lastWeekStartTime = getLastWeekStartTime();
        
        if (endTime <= lastWeekStartTime) {
            // 时区或日期计算问题导致结束时间早于开始时间，强制设置结束时间为开始时间+6天23小时59分59秒999毫秒  // 7天减1毫秒
            endTime = lastWeekStartTime + 604799999L;
            log.warn("警告: 修正了上周的结束时间，确保大于开始时间:  {}",new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS").format(new Date(endTime)));
        }
        
        return endTime;
    }

    public static long getLastSevenDaysStartTime() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_YEAR, -7);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTimeInMillis();
    }
    public static long getLastSevenDaysEndTime() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_YEAR, -1);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        
        // 确保结束时间大于开始时间
        long endTime = calendar.getTimeInMillis();
        long lastSevenDaysStartTime = getLastSevenDaysStartTime();
        
        if (endTime <= lastSevenDaysStartTime) {
            // 时区或日期计算问题导致结束时间早于开始时间，强制设置结束时间为开始时间+6天23小时59分59秒999毫秒  // 7天减1毫秒
            endTime = lastSevenDaysStartTime + 604799999L;
            log.warn("警告: 修正了过去七天的结束时间，确保大于开始时间:  {}",new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS").format(new Date(endTime)));
        }
        
        return endTime;
    }
    public static long getFromDateString(String dateStr) {
        if (StringUtils.isBlank(dateStr)) {
            return 0;
        }
        return parseDateTime(dateStr).getTime();
    }
    /**
     * 输入long timestamp （毫秒） 输出yyyyMMdd格式的long数字
     */
    public static long getDateKey(long timestamp) {
        return Long.parseLong(formatDate(new Date(timestamp),"yyyyMMdd"));
    }

    public static long getLastFourteenDaysStartTime() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_YEAR, -14);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTimeInMillis();
    }
    
    public static long getLastFourteenDaysEndTime() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_YEAR, -1);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        
        // 确保结束时间大于开始时间
        long endTime = calendar.getTimeInMillis();
        long lastFourteenDaysStartTime = getLastFourteenDaysStartTime();
        
        if (endTime <= lastFourteenDaysStartTime) {
            // 时区或日期计算问题导致结束时间早于开始时间，强制设置结束时间为开始时间+13天23小时59分59秒999毫秒   // 14天减1毫秒
            endTime = lastFourteenDaysStartTime + 1209599999L;
            log.warn("警告: 修正了过去十四天的结束时间，确保大于开始时间: {}",new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS").format(new Date(endTime)));
        }
        
        return endTime;
    }
    
    public static long getLastThirtyDaysStartTime() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_YEAR, -30);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTimeInMillis();
    }
    
    public static long getLastThirtyDaysEndTime() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_YEAR, -1);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        
        // 确保结束时间大于开始时间
        long endTime = calendar.getTimeInMillis();
        long lastThirtyDaysStartTime = getLastThirtyDaysStartTime();
        
        if (endTime <= lastThirtyDaysStartTime) {
            // 时区或日期计算问题导致结束时间早于开始时间，强制设置结束时间为开始时间+29天23小时59分59秒999毫秒   // 30天减1毫秒
            endTime = lastThirtyDaysStartTime + 2591999999L;
            log.warn("警告: 修正了过去三十天的结束时间，确保大于开始时间: {}", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS").format(new Date(endTime)));
        }
        
        return endTime;
    }
}
