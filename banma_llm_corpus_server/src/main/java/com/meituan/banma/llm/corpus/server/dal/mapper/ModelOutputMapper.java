package com.meituan.banma.llm.corpus.server.dal.mapper;

import com.meituan.banma.llm.corpus.server.dal.entity.KnowledgeBaseVersionEntity;
import com.meituan.banma.llm.corpus.server.dal.entity.ModelOutputTaskEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.List;

@Mapper
public interface ModelOutputMapper {

    // 根据ticketId查询模型总结输出，用于给修改提供原本
    @Select("SELECT * FROM model_output_task WHERE ticket_id = #{ticketId} AND task_status = 1")
    List<ModelOutputTaskEntity> queryModelOutputByTicketId(String ticketId);

    @Select("SELECT * FROM model_output_task WHERE task_id = #{taskId}")
    ModelOutputTaskEntity queryModelOutputByTaskId(String taskId);

    /**
     * 根据rgId查询所有相关的ModelOutputTask记录
     * 
     * @param rgId 值班组ID
     * @param limit 每页大小
     * @param offset 偏移量
     * @return 相关的ModelOutputTask记录列表
     */
    @Select("SELECT * FROM model_output_task WHERE rg_id = #{rgId} AND create_time >= DATE_SUB(NOW(), INTERVAL 1 MONTH) AND platform_id IN (1, 2) ORDER BY update_time DESC LIMIT #{limit} OFFSET #{offset}")
    List<ModelOutputTaskEntity> queryModelOutputsByRgId(@Param("rgId") Long rgId, @Param("limit") int limit, @Param("offset") int offset);

    /**
     * 根据rgId统计ModelOutputTask记录数
     * 
     * @param rgId 值班组ID
     * @return 记录数量
     */
    @Select("SELECT COUNT(*) FROM model_output_task WHERE rg_id = #{rgId} AND create_time >= DATE_SUB(NOW(), INTERVAL 1 MONTH) AND platform_id IN (1, 2)")
    int countModelOutputsByRgId(@Param("rgId") Long rgId);

    /**
     * 根据rgId和misId查询所有相关的ModelOutputTask记录
     * 
     * @param rgId 值班组ID
     * @param misId 用户MIS ID
     * @param limit 每页大小
     * @param offset 偏移量
     * @return 相关的ModelOutputTask记录列表
     */
    @Select("SELECT * FROM model_output_task WHERE rg_id = #{rgId} AND creator_mis_id = #{misId} AND create_time >= DATE_SUB(NOW(), INTERVAL 1 MONTH) AND platform_id IN (1, 2) ORDER BY update_time DESC LIMIT #{limit} OFFSET #{offset}")
    List<ModelOutputTaskEntity> queryModelOutputsByRgIdAndMisId(@Param("rgId") Long rgId, @Param("misId") String misId, @Param("limit") int limit, @Param("offset") int offset);

    /**
     * 根据rgId和misId统计ModelOutputTask记录数
     * 
     * @param rgId 值班组ID
     * @param misId 用户MIS ID
     * @return 记录数量
     */
    @Select("SELECT COUNT(*) FROM model_output_task WHERE rg_id = #{rgId} AND creator_mis_id = #{misId} AND create_time >= DATE_SUB(NOW(), INTERVAL 1 MONTH) AND platform_id IN (1, 2)")
    int countModelOutputsByRgIdAndMisId(@Param("rgId") Long rgId, @Param("misId") String misId);

    /**
     * 根据rgId和misId统计进行中的任务数
     * 
     * @param rgId 值班组ID
     * @param misId 用户MIS ID
     * @return 记录数量
     */
    @Select("SELECT COUNT(*) FROM model_output_task WHERE rg_id = #{rgId} AND creator_mis_id = #{misId} AND task_status = 0")
    int countModelOutputsWithAskStatusZero(@Param("rgId") Long rgId, @Param("misId") String misId);

    /**
     * 根据时间区间查询ModelOutputTask记录
     * 
     * @param startTime 开始时间（毫秒时间戳）
     * @param endTime 结束时间（毫秒时间戳）
     * @return ModelOutputTask记录列表
     */
    List<ModelOutputTaskEntity> selectByTimeRange(@Param("startTime") Long startTime, @Param("endTime") Long endTime);

    /**
     * 根据时间区间查询ModelOutputTask记录，排除平台类型为DX_MONITORING(4)的任务
     * 
     * @param startTime 开始时间（毫秒时间戳）
     * @param endTime 结束时间（毫秒时间戳）
     * @return 非DX_MONITORING平台的ModelOutputTask记录列表
     */
    List<ModelOutputTaskEntity> selectByTimeRangeExcludeMonitoring(@Param("startTime") Long startTime, @Param("endTime") Long endTime);

    void insertModelOutputTask(ModelOutputTaskEntity modelOutputTaskEntity);
    void updateModelOutputTaskStatusByTaskId(ModelOutputTaskEntity modelOutputTaskEntity);

    /**
     * 根据rgId查询所有进行中的任务（状态为0）
     * 
     * @param rgId 值班组ID
     * @return 进行中的任务列表
     */
    @Select("SELECT * FROM model_output_task WHERE rg_id = #{rgId} AND task_status = 0 ORDER BY create_time DESC")
    List<ModelOutputTaskEntity> findAllRunningTasksByRgId(@Param("rgId") Long rgId);

    /**
     * 根据rgId查询所有进行中的任务（状态为0），排除平台类型为DX_MONITORING(4)的任务
     * 
     * @param rgId 值班组ID
     * @return 进行中且非DX_MONITORING平台的任务列表
     */
    @Select("SELECT * FROM model_output_task WHERE rg_id = #{rgId} AND task_status = 0 AND platform_id != 4 ORDER BY create_time DESC")
    List<ModelOutputTaskEntity> findAllRunningTasksExcludeMonitoringByRgId(@Param("rgId") Long rgId);

    /**
     * 查询包含指定标签ID的记录
     * @param tagId 标签ID
     * @return 包含该标签ID的记录列表
     */
    @Select("SELECT * FROM model_output_task WHERE FIND_IN_SET(#{tagId}, tags_ids) > 0")
    List<ModelOutputTaskEntity> findRecordsWithTagId(@Param("tagId") String tagId);

    /**
     * 更新指定记录的tags_ids字段
     * @param id 记录ID
     * @param tagsIds 新的标签ID字符串
     * @return 影响的行数
     */
    @Update("UPDATE model_output_task SET tags_ids = #{tagsIds} WHERE id = #{id}")
    int updateTagsIds(@Param("id") Long id, @Param("tagsIds") String tagsIds);
}
