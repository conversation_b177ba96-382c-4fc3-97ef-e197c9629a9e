package com.meituan.banma.llm.corpus.server.dal.mapper;

import com.meituan.banma.llm.corpus.server.dal.entity.QuestionTypeEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 问题类型数据访问接口
 */
public interface QuestionTypeMapper {
    
    /**
     * 插入单条问题类型记录
     * 
     * @param record 问题类型实体
     * @return 影响的行数
     */
    int insert(QuestionTypeEntity record);
    
    /**
     * 批量插入问题类型记录
     * 
     * @param records 问题类型实体列表
     * @return 影响的行数
     */
    int batchInsert(List<QuestionTypeEntity> records);
    
    /**
     * 根据ID更新问题类型记录
     * 
     * @param record 问题类型实体
     * @return 影响的行数
     */
    int updateById(QuestionTypeEntity record);
    
    /**
     * 根据ID删除问题类型记录
     * 
     * @param id 问题类型ID
     * @return 影响的行数
     */
    int deleteById(Long id);
    
    /**
     * 根据监控组ID删除所有问题类型记录
     * 
     * @param monitoringGroupId 监控组ID
     * @return 影响的行数
     */
    int deleteByMonitoringGroupId(Long monitoringGroupId);
    
    /**
     * 根据ID查询问题类型记录
     * 
     * @param id 问题类型ID
     * @return 问题类型实体
     */
    QuestionTypeEntity selectById(Long id);
    
    /**
     * 根据监控组ID查询问题类型记录列表
     * 
     * @param monitoringGroupId 监控组ID
     * @return 问题类型实体列表
     */
    List<QuestionTypeEntity> selectByMonitoringGroupId(Long monitoringGroupId);
    
    /**
     * 根据监控组ID列表查询问题类型记录列表
     * 
     * @param monitoringGroupIds 监控组ID列表
     * @return 问题类型实体列表
     */
    List<QuestionTypeEntity> selectByMonitoringGroupIds(@Param("monitoringGroupIds") List<Long> monitoringGroupIds);
} 