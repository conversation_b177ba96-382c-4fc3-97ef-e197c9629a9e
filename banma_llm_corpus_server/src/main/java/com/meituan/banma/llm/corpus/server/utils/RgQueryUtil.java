package com.meituan.banma.llm.corpus.server.utils;

import com.meituan.banma.llm.corpus.server.common.MtConfigService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/12/10
 * 查询值班组相关信息
 */

@Service
public class RgQueryUtil {
    @Resource
    private TicketQueryUtil ticketQueryUtil;

    @Resource
    private MtConfigService mtConfigService;

    private static final String MY_RG_QUERY_METHOD = "/api/1.0/rg/my";

    private static final String RG_QUERY_BY_ID = "/api/1.0/rg/list";

    private static final String RG_QUERY_RG_USERS = "/api/1.0/rg/user/list";
    /**
     * 查询当前用户参与的rg
     */
    public String queryMyRgList(String misId, int pageNum, int pageSize) {
        // 拼接URL参数
        Map<String, String> params = new HashMap<>();
        params.put("cn", String.valueOf(pageNum));
        params.put("sn", String.valueOf(pageSize));
        String urlWithParams = ticketQueryUtil.buildUrlWithParams(mtConfigService.getRgQueryUrl() + MY_RG_QUERY_METHOD, params);

        return HttpUtil.doGet(urlWithParams, ticketQueryUtil.getCommonHeaders(misId));
    }

    public String queryRgInfoById(String misId, Long rgId){
        Map<String, String> params = new HashMap<>();
        params.put("cn", String.valueOf(1));
        params.put("sn", String.valueOf(2));
        params.put("id", String.valueOf(rgId));
        String urlWithParams = ticketQueryUtil.buildUrlWithParams(mtConfigService.getRgQueryUrl() + RG_QUERY_BY_ID, params);
        return HttpUtil.doGet(urlWithParams, ticketQueryUtil.getCommonHeaders(misId));
    }

    public String queryRgAdminUserList(String misId, Long rgId){
        Map<String, String> params = new HashMap<>();
        params.put("cn", String.valueOf(1));
        params.put("sn", String.valueOf(100));
        params.put("rgId", String.valueOf(rgId));
        params.put("roles", "RGADMIN");
        String urlWithParams = ticketQueryUtil.buildUrlWithParams(mtConfigService.getRgQueryUrl() + RG_QUERY_RG_USERS, params);
        return HttpUtil.doGet(urlWithParams, ticketQueryUtil.getCommonHeaders(misId));
    }
    public String queryRgNormalUserList(String misId, Long rgId){
        Map<String, String> params = new HashMap<>();
        params.put("cn", String.valueOf(1));
        params.put("sn", String.valueOf(100));
        params.put("rgId", String.valueOf(rgId));
        params.put("roles", "RGADMIN,NORMAL");
        String urlWithParams = ticketQueryUtil.buildUrlWithParams(mtConfigService.getRgQueryUrl() + RG_QUERY_RG_USERS, params);
        return HttpUtil.doGet(urlWithParams, ticketQueryUtil.getCommonHeaders(misId));
    }
}
