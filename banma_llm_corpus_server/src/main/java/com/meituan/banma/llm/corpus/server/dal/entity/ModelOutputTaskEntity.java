package com.meituan.banma.llm.corpus.server.dal.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.sql.Timestamp;

@Data
@Builder
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class ModelOutputTaskEntity {
    private Long id;

    /**
     * 任务id uuid
     */
    private String taskId;

    /**
     * 任务状态 0-进行中 1-成功 2-失败
     */
    private Integer taskStatus;

    /**
     * TT id
     */
    private String ticketId;
    /**
     * 值班组 id
     */
    private Long rgId;
    /**
     * 任务输出
     */
    private String title = "";
    /**
     * 任务输出内容
     */
    private String content = "";

    /**
     * 任务创建人大象Id
     */
    private Long creatorDxId;

    /**
     * tt大象群Id
     */
    private Long dxGroupId;

    /**
     * 任务创建人misId
     */
    private String creatorMisId;

    /**
     * 任务创建人姓名
     */
    private String creatorUserName;

    /**
     * 来源平台id ConvertTaskPlatformIdEnum 1-大象机器人 2-WEB端
     */
    private Integer platformId;

    private Timestamp createTime;

    private Timestamp updateTime;

    /**
     * 模型识别到的缺失信息 ["abc","abc","abc"]
     */
    private String taskMissingInfo = "[]";

    /**
     * 模型输出任务消息，如任务失败则是失败原因
     */
    private String taskMessage = "";

    /**
     * 标签id列表，英文逗号分隔，如1,2,3
     */
    private String tagsIds;
}
