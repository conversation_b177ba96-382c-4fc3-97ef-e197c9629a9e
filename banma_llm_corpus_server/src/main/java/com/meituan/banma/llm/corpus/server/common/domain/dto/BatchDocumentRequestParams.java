package com.meituan.banma.llm.corpus.server.common.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 批量添加文档请求参数
 * 用于传递批量添加文档所需的参数
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BatchDocumentRequestParams {

    /**
     * 文档名称
     */
    private String name;

    /**
     * 文档URL
     */
    private String url;

    /**
     * 自动更新标志，0表示不自动更新，1表示自动更新
     */
    private Integer autoUpdate;
}