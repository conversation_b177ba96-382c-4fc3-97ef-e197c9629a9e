package com.meituan.banma.llm.corpus.server.common.constants.enums;

import lombok.Getter;

@Getter
public enum FridayAsyncConversationStatusEnum {

    SUCCESS("SUCC", "成功"),
    RUNNING("RUNNING", "执行中"),
    FAILED("FAILED", "失败"),
    NO_RECORD("NO_RECORD", "无记录")
    ;

    private final String code;
    private final String desc;

    FridayAsyncConversationStatusEnum(String code , String desc){
        this.code = code;
        this.desc = desc;
    }
}
