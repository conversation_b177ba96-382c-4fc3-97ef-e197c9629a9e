
package com.meituan.banma.llm.corpus.server.dal.mapper;

import com.meituan.banma.llm.corpus.server.dal.entity.ChatBotQuestionKbRetrievalEntity;
import com.meituan.banma.llm.corpus.server.dal.query.ChatBotQuestionKbRetrievalQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ChatBotQuestionKbRetrievalMapper {
    int insert(ChatBotQuestionKbRetrievalEntity entity);

    List<ChatBotQuestionKbRetrievalEntity> selectByQuestionId(@Param("questionId") String questionId);

    List<ChatBotQuestionKbRetrievalEntity> selectByQuery(ChatBotQuestionKbRetrievalQuery query);

    int deleteByQuestionId(@Param("questionId") String questionId);

    int batchInsert(@Param("entities") List<ChatBotQuestionKbRetrievalEntity> entities);
}

