package com.meituan.banma.llm.corpus.server.common.constants.enums;

/**
 * 聚类算法类型枚举
 */
public enum ClusteringAlgorithmType {
    /**
     * K-means聚类算法
     * 特性：基于质心的聚类算法，簇形状通常是凸的
     * 适用：数据分布较为规则，簇大小相近，密度均匀的场景
     * 优点：实现简单，计算效率高，适合处理大规模数据
     * 缺点：需要预先指定簇数量，对异常值敏感，只能发现凸形状的簇
     */
    KMEANS,

    /**
     * HDBSCAN*聚类算法
     * 特性：基于密度的层次聚类算法，是DBSCAN的层次化扩展版本
     * 适用：数据分布不规则，簇形状复杂，密度不均匀，噪声较多的场景
     * 优点：能自动确定簇的数量，可以发现任意形状的簇，对噪声不敏感
     * 缺点：计算复杂度较高，参数调整较为复杂
     */
    HDBSCAN;
} 