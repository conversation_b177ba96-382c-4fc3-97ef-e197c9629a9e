package com.meituan.banma.llm.corpus.server.common.domain.dto;

import lombok.Data;
import lombok.ToString;

import java.sql.Timestamp;
import java.util.List;

@Data
@ToString
public class CorpusInfoDTO {


    //TT id
    private String ticketId;

    private String title;

    //知识库内容
    private String content;

    //问题类型id（暂无）
    private Integer type;
    /**
     * corpusSourceEnum
     */
    private Integer source;

    //入库人misId
    private String misId;

    //创建时间
    private Timestamp createTime;

    //更新时间
    private Timestamp updateTime;

    private long contentId;

    // 背景知识
    private String backgroundKnowledge;

    // SOP
    private String sop;

    // Rule
    private String rule;

    // 标签名称列表
    private List<String> tagsname;
}
