package com.meituan.banma.llm.corpus.server.service.impl;

import com.cip.crane.client.spring.annotation.Crane;
import com.meituan.banma.llm.corpus.api.constants.BizCode;
import com.meituan.banma.llm.corpus.api.exception.LlmCorpusException;
import com.meituan.banma.llm.corpus.server.common.MtConfigService;
import com.meituan.banma.llm.corpus.server.dal.entity.AccessKeyEntity;
import com.meituan.banma.llm.corpus.server.dal.entity.RgDatasetDocumentEntity;
import com.meituan.banma.llm.corpus.server.dal.mapper.AccessKeyMapper;
import com.meituan.banma.llm.corpus.server.dal.mapper.RgDatasetDocumentMapper;
import com.meituan.banma.llm.corpus.server.dal.mapper.ReviewMapper;
import com.meituan.banma.llm.corpus.server.rpc.friday.FridayRpcService;
import com.meituan.banma.llm.corpus.server.service.IAccessKeyService;
import com.meituan.banma.llm.corpus.server.service.WorkspaceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.security.SecureRandom;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.ArrayList;

/**
 * AccessKey服务实现类
 */
@Slf4j
@Service
public class AccessKeyServiceImpl implements IAccessKeyService {

    @Autowired
    private AccessKeyMapper accessKeyMapper;
    
    @Autowired
    private RgDatasetDocumentMapper rgDatasetDocumentMapper;
    
    @Autowired
    private FridayRpcService fridayRpcService;
    
    @Autowired
    @Lazy
    private WorkspaceService workspaceService;
    
    @Autowired
    private ReviewMapper reviewMapper;
    
    @Autowired
    private MtConfigService mtConfigService;
    
    /**
     * 系统默认的MIS ID
     */
    private static final String SYSTEM_MIS_ID = "system";
    
    /**
     * 知识库名称后缀
     */
    private static final String SUF_NAME = "值班组知识库";

    /**
     * 生成AccessKey
     * 使用SecureRandom生成只包含字母和数字的随机字符串
     * 
     * @return 生成的AccessKey（只包含字母和数字）
     */
    @Override
    public String generateAccessKey() {
        try {
            // 定义字符集，只包含字母和数字
            String characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
            int length = 32; // 生成32位长度的AccessKey
            
            // 使用SecureRandom保证安全性
            SecureRandom secureRandom = new SecureRandom();
            StringBuilder result = new StringBuilder(length);
            
            // 从字符集中随机选择字符
            for (int i = 0; i < length; i++) {
                int randomIndex = secureRandom.nextInt(characters.length());
                result.append(characters.charAt(randomIndex));
            }
            
            return result.toString();
        } catch (Exception e) {
            log.error("#AccessKeyServiceImpl.generateAccessKey 生成AccessKey失败", e);
            // 发生异常时返回一个不含特殊字符的UUID值
            return UUID.randomUUID().toString().replaceAll("-", "");
        }
    }

    /**
     * 将默认AccessKey保存到数据库
     * 
     * @param ak 生成的AccessKey
     * @param rgId 值班组ID
     */
    @Override
    public void saveDefaultAccessKey(String ak, Long rgId) {
        try {
            AccessKeyEntity entity = new AccessKeyEntity();
            entity.setAk(ak);
            entity.setAkName("默认AccessKey");
            entity.setMisId(SYSTEM_MIS_ID);
            entity.setRgIds(String.valueOf(rgId));
            entity.setDefaultKey(true);
            entity.setCount(0L);
            
            // 保存到数据库
            accessKeyMapper.insert(entity);
            log.info("#AccessKeyServiceImpl.saveDefaultAccessKey 成功保存默认AccessKey: rgId={}, ak={}, misId={}", rgId, ak, SYSTEM_MIS_ID);
        } catch (Exception e) {
            log.error("#AccessKeyServiceImpl.saveDefaultAccessKey 保存默认AccessKey失败: rgId={}, ak={}", rgId, ak, e);
            // 这里不抛出异常，避免影响主流程
        }
    }
    
    /**
     * 根据rgId查询默认ak
     * 
     * @param rgId 值班组ID
     * @return 默认AccessKey，如果不存在则返回null
     */
    @Override
    public String getDefaultAccessKey(String rgId) {
        try {
            AccessKeyEntity accessKey = accessKeyMapper.findDefaultByRgId(rgId);
            if (accessKey != null) {
                return accessKey.getAk();
            }
            return null;
        } catch (Exception e) {
            log.error("#AccessKeyServiceImpl.getDefaultAccessKey 查询默认AccessKey失败: rgId={}", rgId, e);
            return null;
        }
    }
    
    /**
     * 获取指定值班组的最新修改人MIS ID
     * 如果找不到则返回空
     *
     * @param rgId 值班组ID
     * @return 最新修改人MIS ID，如果找不到返回null
     */
    @Override
    public String getLatestMisIdByRgId(Long rgId) {
        try {
            // 尝试从modified_output表查询该rgId最新的记录mis_id
            String misId = reviewMapper.findLatestMisIdByRgId(rgId);
            if (misId != null && !misId.isEmpty()) {
                log.info("#AccessKeyServiceImpl.getLatestMisIdByRgId 成功获取rgId={}的最新misId={}", rgId, misId);
                return misId;
            } else {
                log.warn("#AccessKeyServiceImpl.getLatestMisIdByRgId 未找到rgId={}的最新misId", rgId);
                return null;
            }
        } catch (Exception e) {
            log.error("#AccessKeyServiceImpl.getLatestMisIdByRgId 查询rgId={}的最新misId失败", rgId, e);
            return null;
        }
    }

    /**
     * 批量清空所有记录的document_id字段
     * 在清空前先调用Friday RPC服务删除实际文档，然后重新创建新文档
     *
     * @return 操作结果Map，包含总体成功/失败状态和所有失败记录的详细数据
     */
    @Override
    public Map<String, Object> clearDocumentIdByRgIdAndSpaceId() {
        Map<String, Object> result = new HashMap<>();
        List<Map<String, Object>> successList = new ArrayList<>();
        List<Map<String, Object>> failedList = new ArrayList<>();
        int totalCount = 0;
        int successCount = 0;
        
        try {
            // 查询所有需要处理的记录
            List<RgDatasetDocumentEntity> entities = rgDatasetDocumentMapper.findAll();
            
            totalCount = entities.size();
            if (entities.isEmpty()) {
                log.warn("#AccessKeyServiceImpl.clearDocumentIdByRgIdAndSpaceId 未找到任何记录");
                result.put("success", false);
                result.put("message", "未找到任何记录");
                result.put("totalCount", 0);
                result.put("successCount", 0);
                result.put("failedCount", 0);
                return result;
            }
            
            // 处理每条记录
            for (RgDatasetDocumentEntity entity : entities) {
                Map<String, Object> recordResult = new HashMap<>();
                recordResult.put("rgId", entity.getRgId());
                recordResult.put("spaceId", entity.getSpaceId());
                
                // 记录清空前的document_id值，用于日志记录和删除操作
                String documentId = entity.getDocumentId();
                String datasetId = entity.getDatasetId();
                recordResult.put("documentId", documentId);
                recordResult.put("datasetId", datasetId);
                
                // 判断是否需要删除和重新创建文档
                boolean needProcess = false;
                boolean needDelete = false;
                
                // 检查datasetId是否有效
                if (datasetId != null && !datasetId.isEmpty()) {
                    needProcess = true;
                    // 只有当documentId有效时才需要删除旧文档
                    if (documentId != null && !documentId.isEmpty()) {
                        needDelete = true;
                    }
                }
                
                if (needProcess) {
                    try {
                        // 获取工作空间的访问令牌
                        String accessToken = workspaceService.getWorkspaceAccessToken(entity.getAccessKey(), entity.getAppSecret());
                        
                        // 获取rgId对应的最新修改人mis_id作为modifier
                        String modifier = getLatestMisIdByRgId(entity.getRgId());
                        if (modifier == null) {
                            modifier = SYSTEM_MIS_ID;
                        }
                        
                        // 如果documentId存在，则先删除旧文档
                        if (needDelete) {
                            fridayRpcService.deleteDocument(datasetId, documentId, accessToken, modifier, entity.getSpaceId());
                            log.info("#AccessKeyServiceImpl.clearDocumentIdByRgIdAndSpaceId 成功删除Friday文档: rgId={}, spaceId={}, datasetId={}, documentId={}, modifier={}", 
                                    entity.getRgId(), entity.getSpaceId(), datasetId, documentId, modifier);
                        } else {
                            log.info("#AccessKeyServiceImpl.clearDocumentIdByRgIdAndSpaceId 文档ID为空，跳过删除步骤直接创建新文档: rgId={}, spaceId={}, datasetId={}", 
                                    entity.getRgId(), entity.getSpaceId(), datasetId);
                        }
                        
                        // 创建新文档处理流程
                        String newDocumentId = handleAccessKeyAndUploadDocument(
                                entity.getRgId(),
                                entity.getSpaceId(),
                                datasetId,
                                accessToken,
                                modifier);
                        
                        if (newDocumentId == null) {
                            // 创建文档失败
                            log.warn("#AccessKeyServiceImpl.clearDocumentIdByRgIdAndSpaceId 创建新文档失败");
                        }
                        
                        // 更新数据库中的document_id为新的ID
                        entity.setDocumentId(newDocumentId);
                        rgDatasetDocumentMapper.updateAndSpaceId(entity);
                        
                        log.info("#AccessKeyServiceImpl.clearDocumentIdByRgIdAndSpaceId 成功创建新文档并更新记录: rgId={}, spaceId={}, datasetId={}, 旧documentId={}, 新documentId={}", 
                                entity.getRgId(), entity.getSpaceId(), datasetId, documentId, newDocumentId);
                        
                        recordResult.put("success", true);
                        if (needDelete) {
                            recordResult.put("message", "成功删除旧文档并创建新文档");
                        } else {
                            recordResult.put("message", "成功创建新文档");
                        }
                        recordResult.put("newDocumentId", newDocumentId);
                        successList.add(recordResult);
                        successCount++;
                    } catch (Exception e) {
                        String operationType = needDelete ? "删除旧文档并创建新文档" : "创建新文档";
                        log.error("#AccessKeyServiceImpl.clearDocumentIdByRgIdAndSpaceId {}过程中处理失败: rgId={}, spaceId={}, datasetId={}, documentId={}", 
                                operationType, entity.getRgId(), entity.getSpaceId(), datasetId, documentId, e);
                        
                        recordResult.put("success", false);
                        recordResult.put("message", operationType + "处理失败: " + e.getMessage());
                        recordResult.put("errorMessage", e.getMessage());
                        failedList.add(recordResult);
                    }
                } else {
                    String reason = datasetId == null || datasetId.isEmpty() ? "数据集ID为空" : "未知原因";
                    log.warn("#AccessKeyServiceImpl.clearDocumentIdByRgIdAndSpaceId 无法处理该记录: rgId={}, spaceId={}, 原因={}", 
                            entity.getRgId(), entity.getSpaceId(), reason);
                    
                    recordResult.put("success", false);
                    recordResult.put("message", reason);
                    failedList.add(recordResult);
                }
            }
            
            // 汇总结果
            boolean overallSuccess = failedList.isEmpty();
            result.put("success", overallSuccess);
            result.put("message", overallSuccess ? "所有记录处理成功" : "部分记录处理失败");
            result.put("totalCount", totalCount);
            result.put("successCount", successCount);
            result.put("failedCount", failedList.size());
            result.put("successList", successList);
            result.put("failedList", failedList);
            
            return result;
        } catch (Exception e) {
            log.error("#AccessKeyServiceImpl.clearDocumentIdByRgIdAndSpaceId 批量处理过程中发生异常", e);
            
            result.put("success", false);
            result.put("message", "批量处理过程中发生异常");
            result.put("errorMessage", e.getMessage());
            result.put("totalCount", totalCount);
            result.put("successCount", successCount);
            result.put("failedCount", totalCount - successCount);
            result.put("successList", successList);
            result.put("failedList", failedList);
            return result;
        }
    }

    /**
     * 处理AccessKey获取/创建并上传文档
     * 
     * @param rgId 值班组ID 
     * @param spaceId 工作空间ID
     * @param datasetId 数据集ID
     * @param accessToken 访问令牌
     * @param modifier 操作人ID
     * @return 上传的文档ID，失败返回null
     */
    private String handleAccessKeyAndUploadDocument(Long rgId, String spaceId, String datasetId, String accessToken, String modifier) {
        try {
            // 判断如果spaceId是Friday的spaceId，则使用Friday配置的modifier
            if (spaceId != null && spaceId.equals(mtConfigService.getFridaySpaceId())) {
                modifier = mtConfigService.getFridayAccessTokenConfig().getModifier();
                log.info("#AccessKeyServiceImpl.handleAccessKeyAndUploadDocument 检测到Friday spaceId，使用Friday配置的modifier: rgId={}, spaceId={}, modifier={}", 
                        rgId, spaceId, modifier);
            }
            // 先检查是否已有默认AccessKey
            String defaultAk = getDefaultAccessKey(String.valueOf(rgId));
            if (defaultAk != null && !defaultAk.isEmpty()) {
                log.info("#AccessKeyServiceImpl.handleAccessKeyAndUploadDocument 使用已存在的默认AccessKey: rgId={}, existingAk={}", 
                        rgId, defaultAk);
            } else {
                // 如果不存在，才生成新的AccessKey
                defaultAk = generateAccessKey();
                log.info("#AccessKeyServiceImpl.handleAccessKeyAndUploadDocument 生成新的默认AccessKey: rgId={}, newAk={}", 
                        rgId, defaultAk);
            }
            
            // 动态生成URL，拼接AccessKey
            String localUrl = mtConfigService.getDeliveryOpenApiUrl() + "queryLatestContentByRgId?rgId=" + rgId + "&ak=" + defaultAk;
            
            // 上传新文档
            String newDocumentId = fridayRpcService.uploadDocument(
                    datasetId, 
                    localUrl, 
                    rgId + SUF_NAME,
                    accessToken, 
                    modifier, 
                    true, 
                    spaceId);
            
            // 如果是新生成的AccessKey，上传成功后保存到数据库
            if (getDefaultAccessKey(String.valueOf(rgId)) == null) {
                saveDefaultAccessKey(defaultAk, rgId);
                log.info("#AccessKeyServiceImpl.handleAccessKeyAndUploadDocument 保存新生成的默认AccessKey到数据库: rgId={}, ak={}", 
                        rgId, defaultAk);
            }
            
            // 上传成功并保存数据库后，sleep一段时间然后调用refreshDocument方法刷新文档
            if (newDocumentId != null && !newDocumentId.isEmpty()) {
                try {
                    log.info("#AccessKeyServiceImpl.handleAccessKeyAndUploadDocument 上传成功，准备刷新文档: rgId={}, documentId={}", 
                            rgId, newDocumentId);
                    
                    // Sleep 3秒等待文档处理完成
                    Thread.sleep(500);
                    
                    // 调用refreshDocument方法刷新文档
                    fridayRpcService.refreshDocument(datasetId, newDocumentId, accessToken, modifier, spaceId);
                    
                    log.info("#AccessKeyServiceImpl.handleAccessKeyAndUploadDocument 文档刷新完成: rgId={}, documentId={}", 
                            rgId, newDocumentId);
                } catch (InterruptedException e) {
                    log.warn("#AccessKeyServiceImpl.handleAccessKeyAndUploadDocument sleep被中断: rgId={}, documentId={}", 
                            rgId, newDocumentId, e);
                    Thread.currentThread().interrupt(); // 恢复中断状态
                } catch (Exception e) {
                    log.error("#AccessKeyServiceImpl.handleAccessKeyAndUploadDocument 刷新文档失败: rgId={}, documentId={}, error={}", 
                            rgId, newDocumentId, e.getMessage(), e);
                }
            }
            
            return newDocumentId;
        } catch (Exception e) {
            log.error("#AccessKeyServiceImpl.handleAccessKeyAndUploadDocument 上传文档失败: rgId={}, spaceId={}, datasetId={}, error={}", 
                    rgId, spaceId, datasetId, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 生成并保存自定义AccessKey
     * 
     * @param misId 用户MIS ID
     * @param akName AccessKey名称
     * @param rgIds 值班组ID列表（逗号分隔）
     * @return 生成并保存的AccessKey实体
     */
    @Override
    public AccessKeyEntity generateAndSaveAccessKey(String misId, String akName, String rgIds) {
        try {
            // 生成随机AccessKey
            String ak = generateAccessKey();
            
            // 创建AccessKey实体
            AccessKeyEntity entity = new AccessKeyEntity();
            entity.setAk(ak);
            entity.setAkName(akName);
            entity.setMisId(misId);
            entity.setRgIds(rgIds);
            entity.setDefaultKey(false); // 设置为非默认AccessKey
            entity.setCount(0L); // 初始使用次数为0
            
            // 保存到数据库
            int result = accessKeyMapper.insert(entity);
            if (result <= 0) {
                log.error("#AccessKeyServiceImpl.generateAndSaveAccessKey 保存AccessKey失败: misId={}, akName={}, rgIds={}", 
                        misId, akName, rgIds);
                return null;
            }
            
            log.info("#AccessKeyServiceImpl.generateAndSaveAccessKey 成功生成并保存AccessKey: misId={}, akName={}, rgIds={}, ak={}", 
                    misId, akName, rgIds, ak);
            return entity;
        } catch (Exception e) {
            log.error("#AccessKeyServiceImpl.generateAndSaveAccessKey 生成并保存AccessKey过程中发生异常: misId={}, akName={}, rgIds={}", 
                    misId, akName, rgIds, e);
            return null;
        }
    }
    
    /**
     * 根据rgId和misId查询AccessKey列表
     * 返回结果包含该值班组的默认AccessKey
     *
     * @param rgId 值班组ID
     * @param misId 用户MIS ID
     * @return AccessKey实体列表
     */
    @Override
    public List<AccessKeyEntity> findAccessKeysByRgIdAndMisId(Long rgId, String misId) {
        try {
            // 查询符合条件的AccessKey列表
            List<AccessKeyEntity> accessKeys = accessKeyMapper.findByRgIdAndMisId(rgId.toString(), misId);
            
            // 查询该值班组的默认AccessKey
            AccessKeyEntity defaultAccessKey = accessKeyMapper.findDefaultByRgId(rgId.toString());
            
            // 如果默认AccessKey存在且不在结果列表中，则添加到结果列表
            if (defaultAccessKey != null) {
                // 创建一个新的结果列表
                List<AccessKeyEntity> sortedAccessKeys = new ArrayList<>();
                
                // 先添加默认AccessKey
                sortedAccessKeys.add(defaultAccessKey);
                
                // 然后添加其他非默认AccessKey
                boolean defaultAkFound = false;
                for (AccessKeyEntity accessKey : accessKeys) {
                    if (accessKey.getAk().equals(defaultAccessKey.getAk())) {
                        defaultAkFound = true;
                        continue;
                    }
                    sortedAccessKeys.add(accessKey);
                }
                
                log.info("#AccessKeyServiceImpl.findAccessKeysByRgIdAndMisId 成功查询AccessKey列表: rgId={}, misId={}, 结果数量={}, 默认AccessKey在列表中={}", 
                        rgId, misId, sortedAccessKeys.size(), defaultAkFound);
                return sortedAccessKeys;
            }
            
            log.info("#AccessKeyServiceImpl.findAccessKeysByRgIdAndMisId 成功查询AccessKey列表: rgId={}, misId={}, 结果数量={}, 无默认AccessKey", 
                    rgId, misId, accessKeys.size());
            return accessKeys;
        } catch (Exception e) {
            log.error("#AccessKeyServiceImpl.findAccessKeysByRgIdAndMisId 查询AccessKey列表失败: rgId={}, misId={}", rgId, misId, e);
            return new ArrayList<>();
        }
    }

    /**
     * 修改AccessKey名称
     * 修改前会验证ak与misId是否匹配，只有匹配才能修改
     *
     * @param ak AccessKey
     * @param misId 用户MIS ID
     * @param newAkName 新的AccessKey名称
     * @return 修改结果，true表示成功，false表示失败
     */
    @Override
    public boolean updateAccessKeyName(String ak, String misId, String newAkName) {
        try {
            // 参数校验
            if (ak == null || ak.isEmpty() || misId == null || misId.isEmpty() || newAkName == null || newAkName.isEmpty()) {
                log.warn("#AccessKeyServiceImpl.updateAccessKeyName 参数无效: ak={}, misId={}, newAkName={}", 
                        ak, misId, newAkName);
                return false;
            }
            
            // 查询AccessKey是否存在
            AccessKeyEntity accessKey = accessKeyMapper.queryByAk(ak);
            if (accessKey == null) {
                log.warn("#AccessKeyServiceImpl.updateAccessKeyName AccessKey不存在: ak={}", ak);
                return false;
            }
            
            // 验证misId是否匹配
            if (!misId.equals(accessKey.getMisId())) {
                log.warn("#AccessKeyServiceImpl.updateAccessKeyName misId不匹配，无权修改: ak={}, 请求misId={}, 实际misId={}", 
                        ak, misId, accessKey.getMisId());
                return false;
            }
            
            // 执行更新操作
            int result = accessKeyMapper.updateAkName(accessKey.getId(), newAkName);
            if (result > 0) {
                log.info("#AccessKeyServiceImpl.updateAccessKeyName 成功修改AccessKey名称: ak={}, misId={}, 旧名称={}, 新名称={}", 
                        ak, misId, accessKey.getAkName(), newAkName);
                return true;
            } else {
                log.error("#AccessKeyServiceImpl.updateAccessKeyName 修改AccessKey名称失败: ak={}, misId={}, newAkName={}", 
                        ak, misId, newAkName);
                return false;
            }
        } catch (Exception e) {
            log.error("#AccessKeyServiceImpl.updateAccessKeyName 修改AccessKey名称过程中发生异常: ak={}, misId={}, newAkName={}", 
                    ak, misId, newAkName, e);
            return false;
        }
    }

    /**
     * 根据ak和misId删除AccessKey
     * 删除前会验证ak与misId是否匹配，只有匹配才能删除
     * 注意：默认AccessKey不允许删除
     *
     * @param ak AccessKey
     * @param misId 用户MIS ID
     * @return 删除结果，true表示成功，false表示失败
     */
    @Override
    public boolean deleteAccessKey(String ak, String misId) throws LlmCorpusException {
        try {
            // 查询AccessKey是否存在
            AccessKeyEntity accessKey = accessKeyMapper.queryByAk(ak);
            if (accessKey == null) {
                log.warn("#AccessKeyServiceImpl.deleteAccessKey AccessKey不存在: ak={}", ak);
                throw new LlmCorpusException(BizCode.DELETE_AK_ERROR.getCode(), "AccessKey不存在");
            }
            
            // 验证是否为默认AccessKey，默认AccessKey不允许删除
            if (accessKey.getDefaultKey() != null && accessKey.getDefaultKey()) {
                log.warn("#AccessKeyServiceImpl.deleteAccessKey 默认AccessKey不允许删除: ak={}, misId={}", 
                        ak, misId);
                throw new LlmCorpusException(BizCode.DELETE_AK_ERROR.getCode(), "默认AccessKey不允许删除");
            }
            
            // 验证misId是否匹配
            if (!misId.equals(accessKey.getMisId())) {
                log.warn("#AccessKeyServiceImpl.deleteAccessKey misId不匹配，无权删除: ak={}, 请求misId={}, 实际misId={}", 
                        ak, misId, accessKey.getMisId());
                throw new LlmCorpusException(BizCode.DELETE_AK_ERROR.getCode(), "无权删除");
            }
            
            // 执行删除操作
            int result = accessKeyMapper.deleteByAkAndMisId(ak, misId);
            if (result > 0) {
                log.info("#AccessKeyServiceImpl.deleteAccessKey 成功删除AccessKey: ak={}, misId={}, akName={}", 
                        ak, misId, accessKey.getAkName());
                return true;
            } else {
                log.error("#AccessKeyServiceImpl.deleteAccessKey 删除AccessKey失败: ak={}, misId={}", 
                        ak, misId);
                return false;
            }
        } catch (Exception e) {
            log.error("#AccessKeyServiceImpl.deleteAccessKey 删除AccessKey过程中发生异常: ak={}, misId={}", 
                    ak, misId, e);
            return false;
        }
    }

    /**
     * 定时任务：批量清理并重建所有文档
     * 调用clearDocumentIdByRgIdAndSpaceId方法，删除所有文档并重新创建
     * 执行频率由Crane调度系统控制
     */
    @Crane("clear-rebuild-documents-task")
    public void clearAndRebuildDocumentsTask() {
        try {
            log.info("#AccessKeyServiceImpl.clearAndRebuildDocumentsTask 开始执行定时任务：批量清理并重建文档");
            
            // 调用服务处理
            Map<String, Object> result = clearDocumentIdByRgIdAndSpaceId();
            
            // 记录完整的result结果
            log.info("#AccessKeyServiceImpl.clearAndRebuildDocumentsTask 批量清理并重建文档结果详情: {}", result);

        } catch (Exception e) {
            log.error("#AccessKeyServiceImpl.clearAndRebuildDocumentsTask 定时任务执行失败", e);
        }
    }
} 