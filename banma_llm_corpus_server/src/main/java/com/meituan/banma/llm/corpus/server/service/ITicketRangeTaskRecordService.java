package com.meituan.banma.llm.corpus.server.service;

import com.meituan.banma.llm.corpus.server.common.domain.dto.TicketRangeTaskRecordDTO;

import java.util.List;

/**
 * 工单范围任务记录服务接口
 */
public interface ITicketRangeTaskRecordService {
    
    /**
     * 创建新任务记录
     * 
     * @param ticketIds 工单ID列表
     * @return 创建的任务记录DTO，包含生成的任务ID
     */
    TicketRangeTaskRecordDTO createTaskRecord(List<String> ticketIds);
    
    /**
     * 更新任务记录中的工单ID列表
     * 
     * @param taskId 任务ID
     * @param ticketIds 新的工单ID列表
     * @return 更新结果，成功返回true
     */
    boolean updateTaskRecord(String taskId, List<String> ticketIds);
    
    /**
     * 获取任务记录信息
     * 
     * @param taskId 任务ID
     * @return 任务记录DTO，不存在返回null
     */
    TicketRangeTaskRecordDTO getTaskRecord(String taskId);
    
    /**
     * 获取所有任务记录
     * 
     * @return 任务记录列表
     */
    List<TicketRangeTaskRecordDTO> getAllTaskRecords();
    
    /**
     * 根据时间范围查询任务记录
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 任务记录列表
     */
    List<TicketRangeTaskRecordDTO> getTaskRecordsByTimeRange(String startTime, String endTime);
} 