package com.meituan.banma.llm.corpus.server.common.config;

import com.meituan.mafka.client.bean.MafkaProducer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class MafkaBeanConfig {
    private static final String APP_KEY = "com.sankuai.deliverypaotui.llm.corpus";
    private static final String NAME_SPACE = "com.sankuai.mafka.castle.daojialvyue";
    private static final String LLM_CORPUS_CONVERT_TASK_TOPIC = "llm_corpus_convert_task_topic";
    @Bean(initMethod = "start", destroyMethod = "close")
    public MafkaProducer llmCorpusConvertTaskTopic() {
        MafkaProducer llmCorpusConvertTaskTopic = new MafkaProducer();
        llmCorpusConvertTaskTopic.setTopic(LLM_CORPUS_CONVERT_TASK_TOPIC);
        llmCorpusConvertTaskTopic.setAppkey(APP_KEY);
        llmCorpusConvertTaskTopic.setNamespace(NAME_SPACE);
        return llmCorpusConvertTaskTopic;
    }
}
