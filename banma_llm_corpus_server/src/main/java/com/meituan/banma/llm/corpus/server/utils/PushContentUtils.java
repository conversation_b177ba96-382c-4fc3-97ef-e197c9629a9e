package com.meituan.banma.llm.corpus.server.utils;

import com.alibaba.fastjson.JSON;
import com.meituan.banma.llm.corpus.server.common.domain.dto.ConfirmStyleDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;

/**
 * 交互消息内容构建工具类
 */
@Slf4j
public class PushContentUtils {
    // 交互消息大象协议
    private static final String PROTOCOL = "mtdaxiang://www.meituan.com/doVolleyRequest?";
    
    // 默认编码
    private static final String DEFAULT_ENCODING = "UTF-8";

    /**
     * 构建交互消息内容
     *
     * @param buttonName 按钮名称
     * @param targetUrl 目标URL
     * @param bizParams 业务参数
     * @param confirmStyleDTO 确认样式DTO
     * @return 构建的交互消息内容
     */
    public static <T> String buildContent(String buttonName, String targetUrl, T bizParams, ConfirmStyleDTO confirmStyleDTO) {
        StringBuilder sb = new StringBuilder();
        sb.append("[");
        // 按钮名称
        sb.append(buttonName);
        sb.append("|");
        // 大象协议
        sb.append(PROTOCOL);
        // 二级协议
        sb.append("path=%2fupgrade%2ffunction%2fsetting%2fdoVolleyForThird%2fv2");
        // 业务方参数组装
        sb.append("&requestparams=").append(buildBizParams(bizParams, targetUrl));
        // 确认按钮样式组装
        sb.append(buildUrlParams(confirmStyleDTO));
        sb.append("]");

        return sb.toString();
    }

    /**
     * 构建业务参数
     *
     * @param bizParams 业务参数
     * @param targetUrl 目标URL
     * @return 编码后的业务参数字符串
     */
    private static <T> String buildBizParams(T bizParams, String targetUrl) {
        Map<String, Object> map = new HashMap<>();
        map.put("targetUrl", targetUrl);
        map.put("bizParams", bizParams);

        try {
            // 使用JSON序列化，避免自定义序列化可能引发的问题
            return URLEncoder.encode(JSON.toJSONString(map), DEFAULT_ENCODING);
        } catch (UnsupportedEncodingException e) {
            log.error("构建业务参数异常: {}", ExceptionUtils.getStackTrace(e));
        }
        return "";
    }

    /**
     * 构建URL参数
     *
     * @param confirmStyleDTO 确认样式DTO
     * @return 构建的URL参数字符串
     */
    private static String buildUrlParams(ConfirmStyleDTO confirmStyleDTO) {
        StringBuilder sb = new StringBuilder();
        sb.append("&promptType=").append(confirmStyleDTO.getPromptType());
        sb.append("&isNeedConfirm=").append(Boolean.TRUE.equals(confirmStyleDTO.isNeedConfirm()) ? "Y" : "N");
        try {
            sb.append("&confirmTitle=").append(URLEncoder.encode(confirmStyleDTO.getConfirmTitle(), DEFAULT_ENCODING));
            sb.append("&confirmContent=").append(URLEncoder.encode(confirmStyleDTO.getConfirmContent(), DEFAULT_ENCODING));
        } catch (UnsupportedEncodingException e) {
            log.error("构建URL参数异常: {}", ExceptionUtils.getStackTrace(e));
        }
        return sb.toString();
    }
    
    /**
     * 示例：创建一个简单的交互消息内容
     * 
     * 示例用法：
     * <pre>
     * // 创建业务参数
     * Map<String, Object> bizParams = new HashMap<>();
     * bizParams.put("ticketId", 123456);
     * bizParams.put("userId", 789);
     * 
     * // 获取交互消息内容
     * String content = PushContentUtils.createSimpleInteractiveContent(
     *     "查看详情", 
     *     "https://banma.meituan.com/ticket/detail", 
     *     bizParams
     * );
     * </pre>
     *
     * @param buttonName 按钮名称
     * @param targetUrl 目标URL
     * @param bizParams 业务参数
     * @return 构建的交互消息内容
     */
    public static <T> String createSimpleInteractiveContent(String buttonName, String targetUrl, T bizParams) {
        // 使用Toast提示，无需确认
        ConfirmStyleDTO confirmStyleDTO = ConfirmStyleDTO.createSimpleToast();
        return buildContent(buttonName, targetUrl, bizParams, confirmStyleDTO);
    }
    
    /**
     * 示例：创建一个需要确认的交互消息内容
     * 
     * 示例用法：
     * <pre>
     * // 创建业务参数
     * Map<String, Object> bizParams = new HashMap<>();
     * bizParams.put("ticketId", 123456);
     * bizParams.put("action", "close");
     * 
     * // 获取交互消息内容
     * String content = PushContentUtils.createConfirmInteractiveContent(
     *     "关闭工单", 
     *     "https://banma.meituan.com/ticket/close", 
     *     bizParams,
     *     "确认关闭",
     *     "确定要关闭该工单吗？"
     * );
     * </pre>
     *
     * @param buttonName 按钮名称
     * @param targetUrl 目标URL
     * @param bizParams 业务参数
     * @param confirmTitle 确认标题
     * @param confirmContent 确认内容
     * @return 构建的交互消息内容
     */
    public static <T> String createConfirmInteractiveContent(String buttonName, String targetUrl, T bizParams, 
                                                   String confirmTitle, String confirmContent) {
        // 使用系统对话框，需要确认
        ConfirmStyleDTO confirmStyleDTO = ConfirmStyleDTO.createConfirmDialog(confirmTitle, confirmContent);
        return buildContent(buttonName, targetUrl, bizParams, confirmStyleDTO);
    }
}