package com.meituan.banma.llm.corpus.server.service;

import com.meituan.banma.llm.corpus.api.exception.LlmCorpusException;
import com.sankuai.meituan.org.opensdk.model.domain.items.OrgItems;
import com.sankuai.meituan.org.opensdk.model.domain.Org;

/**
 * 组织架构服务接口
 */
public interface IOrgService {
    
    /**
     * 根据关键词搜索组织
     *
     * @param keyword 搜索关键词
     * @param offset 起始位置，从0开始
     * @param size 每页记录数
     * @return 匹配的组织列表
     */
    OrgItems searchOrg(String keyword, Integer offset, Integer size) throws LlmCorpusException;
    
    /**
     * 根据上级组织ID查询子组织(不包含本身)
     *
     * @param superiorId 上级组织ID
     * @param offset 起始位置，从0开始
     * @param size 每页记录数
     * @return 匹配的子组织列表
     */
    OrgItems querySubordinateOrgs(String superiorId, Integer offset, Integer size) throws LlmCorpusException;
    
    /**
     * 根据组织ID查询组织信息
     *
     * @param id 组织ID
     * @return 组织信息
     */
    Org queryOrgById(String id) throws LlmCorpusException;
    
    /**
     * 根据组织名称路径查询组织信息
     *
     * @param orgNamePath 组织路径(有效组织)
     * @return 组织信息
     */
    Org queryByNamePath(String orgNamePath) throws LlmCorpusException;
} 