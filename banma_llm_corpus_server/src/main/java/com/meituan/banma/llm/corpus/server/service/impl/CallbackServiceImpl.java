package com.meituan.banma.llm.corpus.server.service.impl;

import com.meituan.banma.llm.corpus.server.service.ICallbackService;
import com.meituan.banma.llm.corpus.server.thrift.EventCallbackServiceImpl;
import com.sankuai.xm.openplatform.callback.event.data.GroupMsgEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * 回调处理服务实现类
 */
@Slf4j
@Service
public class CallbackServiceImpl implements ICallbackService {

    @Autowired
    private EventCallbackServiceImpl eventCallbackService;

    /**
     * 处理加知识库按钮回调
     *
     * @param request 回调请求参数
     * @return 处理结果
     */
    @Override
    public Map<String, Object> handleKnowledgeAddCallback(Map<String, Object> request) {
        Map<String, Object> result = new HashMap<>();
        Map<String, Object> data = new HashMap<>();
        
        try {
            log.info("处理加知识库回调业务逻辑: {}", request);
            
            // 获取业务参数
            Map<String, Object> bizParams = (Map<String, Object>) request.get("bizParams");
            Map<String, Object> userInfo = (Map<String, Object>) request.get("userInfo");
            
            Long groupId = Long.valueOf(bizParams.get("groupId").toString());
            Long userId = Long.valueOf(userInfo.get("uid").toString());
            String userName = (String) userInfo.get("userId");
            
            // 构造GroupMsgEvent对象
            GroupMsgEvent groupMsgEvent = new GroupMsgEvent();
            groupMsgEvent.setGid(groupId);
            groupMsgEvent.setFromUid(userId);
            groupMsgEvent.setFromName(userName);
            
            // 调用处理加知识库请求的方法
            eventCallbackService.processAddKnowledgeRequest(groupMsgEvent);
            
            // 设置成功响应
            data.put("message", "指令已处理，请查看群内消息反馈");
            result.put("data", data);
            result.put("rescode", 0);
            
            log.info("处理加知识库回调成功, groupId:{}, userId:{}, userName:{}", groupId, userId, userName);
            
        } catch (Exception e) {
            log.error("处理加知识库回调失败, 业务异常: {}", e.getMessage(), e);
            throw new RuntimeException("业务处理失败: " + e.getMessage(), e);
        }
        
        return result;
    }
} 