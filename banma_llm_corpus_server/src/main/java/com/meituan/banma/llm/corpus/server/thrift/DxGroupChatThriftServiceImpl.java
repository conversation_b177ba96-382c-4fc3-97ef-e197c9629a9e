package com.meituan.banma.llm.corpus.server.thrift;

import com.meituan.banma.llm.corpus.api.client.DxGroupChatThriftService;
import com.meituan.banma.llm.corpus.api.response.DxGroupChatResponse;
import com.meituan.banma.llm.corpus.server.common.domain.dto.DxChatMessageRecord;
import com.meituan.banma.llm.corpus.server.service.IDxGroupChatService;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftServer;
import joptsimple.internal.Strings;
import lombok.extern.slf4j.Slf4j;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.ArrayList;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Class DxGroupChatThriftServiceImpl
 * Project banma_llm_corpus_process_server
 *
 * <AUTHOR>
 * @date 2025/5/8
 * Description 获取大象群聊消息
 */

@Slf4j
@MdpThriftServer()
public class DxGroupChatThriftServiceImpl implements DxGroupChatThriftService {
    @Resource
    private IDxGroupChatService dxGroupChatService;
    
    // 链接格式的正则表达式，匹配[文本|url]
    private static final Pattern LINK_PATTERN = Pattern.compile("\\[(.*?)\\|(.*?)\\]");

    @Override
    public List<DxGroupChatResponse> getDxGroupChat(long groupId, String robotAppId) {
        List<DxChatMessageRecord> dxChatMessageRecords = dxGroupChatService.getTTChatInfo(groupId, robotAppId);
        log.info("getDxGroupChat, groupId:{}, robotAppId:{}, dxChatMessageRecords:{}", groupId, robotAppId, dxChatMessageRecords);
        if (CollectionUtils.isEmpty(dxChatMessageRecords)) {
            return Collections.emptyList();
        }
        List<DxGroupChatResponse> dxGroupChatResponses = new ArrayList<>();
        for (DxChatMessageRecord dxChatMessageRecord : dxChatMessageRecords) {
            String extractedMessage = extractTextContent(dxChatMessageRecord);
            if (StringUtils.isBlank(extractedMessage)) {
                continue;
            }
            dxGroupChatResponses.add(new DxGroupChatResponse(extractedMessage, dxChatMessageRecord.getFromMis(),
                    dxChatMessageRecord.getFromName(), dxChatMessageRecord.getCts()));
        }
        return dxGroupChatResponses;
    }

    private String extractTextContent(DxChatMessageRecord dxChatMessageRecord) {
        try {
            int type = dxChatMessageRecord.getType();
            String messageStr = dxChatMessageRecord.getMessage().toString();
            
            switch (type) {
                case 17:
                    // 处理复杂JSON结构，提取nodes中的文本内容
                    return extractTextFromType17(messageStr);
                case 1:
                    // 处理简单JSON结构，直接提取text字段，并清理链接标记
                    return extractTextFromType1(messageStr);
                default:
                    log.warn("未知的消息类型: {}, 消息ID: {}", type, dxChatMessageRecord.getMsgId());
                    return Strings.EMPTY;
            }
        } catch (Exception e) {
            log.error("提取消息内容失败，消息ID: " + dxChatMessageRecord.getMsgId(), e);
            return Strings.EMPTY; // 返回空字符串而不是原始消息，避免污染数据
        }
    }
    
    /**
     * 处理文本中的链接标记
     * 对于[文本|url]格式，保留[文本]部分
     * 对于其他格式，直接返回原文本
     *
     * @param content 原始文本内容
     * @return 处理后的文本
     */
    private String processLinkMarkers(String content) {
        if (StringUtils.isBlank(content)) {
            return Strings.EMPTY;
        }
        
        // 使用正则表达式替换[文本|url]为[文本]
        Matcher matcher = LINK_PATTERN.matcher(content);
        StringBuffer result = new StringBuffer();
        
        while (matcher.find()) {
            String textPart = matcher.group(1);
            // 替换为[文本]
            matcher.appendReplacement(result, "[" + textPart + "]");
        }
        matcher.appendTail(result);
        
        return result.toString().trim();
    }
    
    private String extractTextFromType17(String messageStr) {
        try {
            JSONObject jsonObject = JSON.parseObject(messageStr);
            String dataStr = jsonObject.getString("data");
            
            JSONObject dataJson = JSON.parseObject(dataStr);
            String nodesStr = dataJson.getString("nodes");
            
            // 解析nodes字符串为JSONArray
            JSONArray nodesArray = JSON.parseArray(nodesStr);
            
            // 用于存放处理后的文本内容
            List<String> contentList = new ArrayList<>();
            
            // 遍历所有节点，提取文本
            for (int i = 0; i < nodesArray.size(); i++) {
                JSONObject node = nodesArray.getJSONObject(i);
                if ("text".equals(node.getString("t")) && node.containsKey("c")) {
                    String content = node.getString("c");
                    
                    // 处理链接标记并添加非空内容
                    String processedContent = processLinkMarkers(content);
                    if (StringUtils.isNotBlank(processedContent)) {
                        contentList.add(processedContent);
                    }
                }
            }
            
            // 如果没有找到任何内容，返回空字符串
            if (contentList.isEmpty()) {
                return Strings.EMPTY;
            }
            
            // 将结果行连接成一个字符串
            String cleanedText = String.join("\n", contentList);
            log.info("Type17消息处理后文本: {}", cleanedText);
            
            return cleanedText;
        } catch (Exception e) {
            log.error("解析Type17消息失败", e);
            return Strings.EMPTY;
        }
    }
    
    private String extractTextFromType1(String messageStr) {
        try {
            JSONObject jsonObject = JSON.parseObject(messageStr);
            String text = jsonObject.getString("text");
            
            if (StringUtils.isBlank(text)) {
                return Strings.EMPTY;
            }
            
            log.info("Type1消息原始文本: {}", text);
            
            // 将文本按行分割
            String[] lines = text.split("\\n");
            List<String> resultLines = new ArrayList<>();
            
            // 处理每一行，处理链接标记
            for (String line : lines) {
                String processedLine = processLinkMarkers(line);
                if (StringUtils.isNotBlank(processedLine)) {
                    resultLines.add(processedLine);
                }
            }
            
            // 如果没有找到有效内容，返回空字符串
            if (resultLines.isEmpty()) {
                return Strings.EMPTY;
            }
            
            // 将结果行连接成一个字符串
            String cleanedText = String.join("\n", resultLines);
            log.info("Type1消息处理后文本: {}", cleanedText);
            
            return cleanedText;
        } catch (Exception e) {
            log.error("解析Type1消息失败", e);
            return Strings.EMPTY;
        }
    }
}
