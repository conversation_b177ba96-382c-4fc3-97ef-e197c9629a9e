package com.meituan.banma.llm.corpus.server.dal.mapper;

import com.meituan.banma.llm.corpus.server.dal.entity.FridayBotConversationEntity;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface FridayBotConversationMapper {
    /**
     * 批量插入对话记录
     *
     * @param conversations 对话记录列表
     * @return 插入成功的记录数
     */
    @Insert({
        "<script>",
        "INSERT INTO friday_bot_conversation (",
        "    record_id, id, conversation_id, user_id, user_type, app_id, request_id,",
        "    message_id, parent_message_id, role, generate_type, message, status,",
        "    add_time, update_time, recall_info, assistant_recommendation,",
        "    conversation_name, is_new, deleted, access_channel, last_chat_time,",
        "    record_add_time, record_update_time, dt",
        ")",
        "VALUES",
        "<foreach collection='conversations' item='item' separator=','>",
        "    (",
        "        #{item.recordId}, #{item.id}, #{item.conversationId}, #{item.userId},",
        "        #{item.userType}, #{item.appId}, #{item.requestId}, #{item.messageId},",
        "        #{item.parentMessageId}, #{item.role}, #{item.generateType}, #{item.message},",
        "        #{item.status}, #{item.addTime}, #{item.updateTime}, #{item.recallInfo},",
        "        #{item.assistantRecommendation}, #{item.conversationName}, #{item.isNew},",
        "        #{item.deleted}, #{item.accessChannel}, #{item.lastChatTime},",
        "        #{item.recordAddTime}, #{item.recordUpdateTime}, #{item.dt}",
        "    )",
        "</foreach>",
        "</script>"
    })
    int batchInsert(@Param("conversations") List<FridayBotConversationEntity> conversations);

    /**
     * 根据appId列表和dt范围分页查询对话记录
     *
     * @param appIds    应用ID列表
     * @param startDt   开始日期
     * @param endDt     结束日期
     * @param offset    偏移量
     * @param limit     每页记录数
     * @return 对话记录列表
     */
    @Select({
        "<script>",
        "SELECT *",
        "FROM friday_bot_conversation",
        "WHERE app_id IN",
        "<foreach collection='appIds' item='appId' open='(' separator=',' close=')'>",
        "    #{appId}",
        "</foreach>",
        "AND dt >= #{startDt}",
        "AND dt &lt;= #{endDt}",
        "AND access_channel != 'TEST'",
        "AND deleted = 0",
        "ORDER BY dt ASC",
        "LIMIT #{offset}, #{limit}",
        "</script>"
    })
    List<FridayBotConversationEntity> queryByAppIdsAndDtRange(
            @Param("appIds") List<String> appIds,
            @Param("startDt") Long startDt,
            @Param("endDt") Long endDt,
            @Param("offset") Integer offset,
            @Param("limit") Integer limit
    );

    /**
     * 根据appId列表和dt范围分页查询对话记录For Assistant
     *
     * @param appIds    应用ID列表
     * @param startDt   开始日期
     * @param endDt     结束日期
     * @param offset    偏移量
     * @param limit     每页记录数
     * @return 对话记录列表
     */
    @Select({
        "<script>",
        "SELECT *",
        "FROM friday_bot_conversation",
        "WHERE app_id IN",
        "<foreach collection='appIds' item='appId' open='(' separator=',' close=')'>",
        "    #{appId}",
        "</foreach>",
        "AND dt >= #{startDt}",
        "AND dt &lt;= #{endDt}",
        "AND access_channel != 'TEST'",
        "AND role = 'assistan'",
        "AND deleted = 0",
        "ORDER BY dt ASC",
        "LIMIT #{offset}, #{limit}",
        "</script>"
    })
    List<FridayBotConversationEntity> queryByAppIdsAndDtRangeForAssistant(
            @Param("appIds") List<String> appIds,
            @Param("startDt") Long startDt,
            @Param("endDt") Long endDt,
            @Param("offset") Integer offset,
            @Param("limit") Integer limit
    );
    /**
     * 统计根据appId列表和dt范围的对话记录总数
     *
     * @param appIds    应用ID列表
     * @param startDt   开始日期
     * @param endDt     结束日期
     * @return 对话记录总数
     */
    @Select({
        "<script>",
        "SELECT COUNT(1) FROM friday_bot_conversation",
        "WHERE dt BETWEEN #{startDt} AND #{endDt}",
        "<if test='appIds != null and appIds.size() > 0'>",
        "AND app_id IN",
        "<foreach collection='appIds' item='appId' open='(' separator=',' close=')'>",
        "#{appId}",
        "</foreach>",
        "</if>",
        "</script>"
    })
    int countByAppIdsAndDtRange(
            @Param("appIds") List<String> appIds,
            @Param("startDt") Long startDt,
            @Param("endDt") Long endDt
    );

    @Select({
        "<script>",
        "SELECT COUNT(1) FROM friday_bot_conversation",
        "WHERE dt BETWEEN #{startDt} AND #{endDt}",
        "<if test='appIds != null and appIds.size() > 0'>",
        "AND app_id IN",
        "<foreach collection='appIds' item='appId' open='(' separator=',' close=')'>",
        "#{appId}",
        "</foreach>",
        "</if>",
        "AND role = 'assistan'",
        "</script>"
    })
    int countByAppIdsAndDtRangeForAssistant(
            @Param("appIds") List<String> appIds,
            @Param("startDt") Long startDt,
            @Param("endDt") Long endDt
    );

    /**
     * 根据message_id查询对话记录
     *
     * @param messageId 消息ID
     * @return 对话记录
     */
    @Select("SELECT * FROM friday_bot_conversation WHERE message_id = #{messageId} AND deleted = 0 LIMIT 1")
    FridayBotConversationEntity queryByMessageId(@Param("messageId") String messageId);

    /**
     * 根据conversation_id查询会话的所有消息记录，按照add_time从早到晚排序
     *
     * @param conversationId 会话ID
     * @return 消息记录列表
     */
    @Select("SELECT * FROM friday_bot_conversation WHERE conversation_id = #{conversationId} AND deleted = 0 ORDER BY add_time ASC")
    List<FridayBotConversationEntity> queryByConversationId(@Param("conversationId") String conversationId);
}
