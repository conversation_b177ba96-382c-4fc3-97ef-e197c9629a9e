package com.meituan.banma.llm.corpus.server.dal.mapper;

import com.meituan.banma.llm.corpus.server.dal.entity.RgDocumentUrlEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 值班组知识库文档URL关系Mapper
 */
@Mapper
public interface RgDocumentUrlMapper {
    
    /**
     * 插入实体对象，默认status为0（正常）
     * @param entity 实体对象
     * @return 影响的行数
     */
    int insertEntity(RgDocumentUrlEntity entity);
    
    /**
     * 批量插入实体对象，默认status为0（正常）
     * @param entities 实体对象列表
     * @return 影响的行数
     */
    int batchInsertEntities(List<RgDocumentUrlEntity> entities);
    
    /**
     * 根据rgId和spaceId查询记录，只查询status=0的记录
     * @param rgId 值班组ID
     * @param spaceId 工作空间ID
     * @return 匹配的记录列表
     */
    List<RgDocumentUrlEntity> findByRgIdAndSpaceId(@Param("rgId") Long rgId, @Param("spaceId") String spaceId);
    
    /**
     * 根据rgId、spaceId和URL查询记录，只查询status=0的记录
     * @param rgId 值班组ID
     * @param spaceId 工作空间ID
     * @param url URL地址
     * @return 匹配的记录
     */
    RgDocumentUrlEntity findByRgIdSpaceIdAndUrl(@Param("rgId") Long rgId, @Param("spaceId") String spaceId, @Param("url") String url);

    /**
     * 根据rgId、spaceId和documentId查询记录，只查询status=0的记录
     * @param rgId 值班组ID
     * @param spaceId 工作空间ID
     * @param documentId 文档ID
     * @return 匹配的记录
     */
    RgDocumentUrlEntity findByRgIdSpaceIdAndDocumentId(@Param("rgId") Long rgId, @Param("spaceId") String spaceId, @Param("documentId") String documentId);

    /**
     * 虚拟删除记录（更新status为1），根据rgId、spaceId和documentId
     * @param rgId 值班组ID
     * @param spaceId 工作空间ID
     * @param documentId 文档ID
     * @return 影响的行数
     */
    int deleteByRgIdSpaceIdAndDocumentId(@Param("rgId") Long rgId, @Param("spaceId") String spaceId, @Param("documentId") String documentId);

    /**
     * 检查记录是否存在，包含工作空间ID，只查询status=0的记录
     * @param rgId 值班组ID
     * @param spaceId 工作空间ID
     * @param documentId 文档ID
     * @return 匹配的记录数
     */
    int countByRgIdSpaceIdAndDocumentId(@Param("rgId") Long rgId, @Param("spaceId") String spaceId, @Param("documentId") String documentId);

    /**
     * 批量虚拟删除文档记录（更新status为1），包含工作空间ID
     * @param rgId 值班组ID
     * @param spaceId 工作空间ID
     * @param documentIds 文档ID列表
     * @return 影响的行数
     */
    int batchDeleteByRgIdSpaceIdAndDocumentIds(@Param("rgId") Long rgId, @Param("spaceId") String spaceId, @Param("documentIds") List<String> documentIds);

    /**
     * 更新自动更新标志，包含工作空间ID，只更新status=0的记录
     * @param rgId 值班组ID
     * @param spaceId 工作空间ID
     * @param documentId 文档ID
     * @param autoUpdate 自动更新标志
     */
    int updateByRgIdSpaceIdAndDocumentId(@Param("rgId") Long rgId, @Param("documentId") String documentId, @Param("autoUpdate") int autoUpdate, @Param("spaceId") String spaceId);

    /**
     * 计算符合条件的去重记录总数，只根据rgId，只计算status=0的记录
     * @param rgId 值班组ID
     * @param strMatch 模糊匹配字符串
     * @return 匹配的记录数量（去重后）
     */
    int countDistinctByRgId(@Param("rgId") Long rgId, @Param("strMatch") String strMatch);

    /**
     * 分页查询去重后的文档URL，只查询status=0的记录
     * @param rgId 值班组ID
     * @param strMatch 模糊匹配字符串
     * @param limit 每页数量
     * @param offset 偏移量
     * @return 匹配的记录列表（去重后）
     */
    List<RgDocumentUrlEntity> findDistinctByRgId(@Param("rgId") Long rgId, 
                                              @Param("strMatch") String strMatch,
                                              @Param("limit") int limit,
                                              @Param("offset") int offset);

    /**
     * 根据url、name和rgId查询记录（去重），只查询status=0的记录
     * @param url url
     * @param name name
     * @param rgId 值班组ID
     * @return 匹配的记录数（去重后）
     */
    int findDistinctByUrlNameAndRgId(@Param("url") String url, @Param("name") String name, @Param("rgId") Long rgId);

    /**
     * 根据name和rgId查询记录（去重），只查询status=0的记录
     * @param name 名称
     * @param rgId 值班组ID
     * @return 匹配的记录数（去重后）
     */
    int findDistinctByNameAndRgId(@Param("name") String name, @Param("rgId") Long rgId);

    /**
     * 根据url和rgId查询记录（去重），只查询status=0的记录
     * @param url url
     * @param rgId 值班组ID
     * @return 匹配的记录数（去重后）
     */
    int findDistinctByUrlAndRgId(@Param("url") String url, @Param("rgId") Long rgId);

    /**
     * 根据url、name、rgId和spaceId查询记录，考虑spaceId，只查询status=0的记录
     * @param url url
     * @param name name
     * @param rgId 值班组ID
     * @param spaceId 工作空间ID
     * @return 匹配的记录数（去重后）
     */
    int findDistinctByUrlNameRgIdAndSpaceId(@Param("url") String url, @Param("name") String name, 
                                         @Param("rgId") Long rgId, @Param("spaceId") String spaceId);

    /**
     * 根据rgId和spaceId批量虚拟删除文档记录（更新status为1）
     * @param rgId 值班组ID
     * @param spaceId 工作空间ID
     * @return 影响的行数
     */
    int batchDeleteByRgIdAndSpaceId(@Param("rgId") Long rgId, @Param("spaceId") String spaceId);

    /**
     * 根据rgId和spaceId批量恢复已删除的文档记录（更新status为0）
     * @param rgId 值班组ID
     * @param spaceId 工作空间ID
     * @return 影响的行数
     */
    @Update("UPDATE rg_document_url SET status = 0 WHERE rg_id = #{rgId} AND space_id = #{spaceId} AND status = 1")
    int restoreByRgIdAndSpaceId(@Param("rgId") Long rgId, @Param("spaceId") String spaceId);

    /**
     * 更新整个实体记录，只更新status=0的记录
     * @param entity 实体对象
     * @return 影响的行数
     */
    int updateEntity(RgDocumentUrlEntity entity);

    /**
     * 根据rgId、spaceId和URL查询记录，包括已删除的记录（status=0或status=1）
     * @param rgId 值班组ID
     * @param spaceId 工作空间ID
     * @param url URL地址
     * @return 匹配的记录
     */
    RgDocumentUrlEntity findByRgIdSpaceIdAndUrlIncludeDeleted(@Param("rgId") Long rgId, @Param("spaceId") String spaceId, @Param("url") String url);

    /**
     * 恢复被删除的实体记录（更新status=0），根据实体对象中的关键字段
     * @param entity 实体对象
     * @return 影响的行数
     */
    int restoreEntity(RgDocumentUrlEntity entity);

    /**
     * 查询rgId和spaceId下所有已删除的文档记录(status=1)
     * @param rgId 值班组ID
     * @param spaceId 工作空间ID
     * @return 已删除的文档记录列表
     */
    List<RgDocumentUrlEntity> findDeletedByRgIdAndSpaceId(@Param("rgId") Long rgId, @Param("spaceId") String spaceId);
    
    /**
     * 根据rgId、spaceId和URL恢复指定的已删除文档记录（将status从1改为0）
     * @param rgId 值班组ID
     * @param spaceId 工作空间ID
     * @param url URL地址
     * @return 影响的行数
     */
    @Update("UPDATE rg_document_url SET status = 0 WHERE rg_id = #{rgId} AND space_id = #{spaceId} AND url = #{url} AND status = 1")
    int restoreByRgIdSpaceIdAndUrl(@Param("rgId") Long rgId, @Param("spaceId") String spaceId, @Param("url") String url);
}
