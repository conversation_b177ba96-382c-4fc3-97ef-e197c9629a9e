package com.meituan.banma.llm.corpus.server.common.domain.dto;

import com.meituan.banma.llm.corpus.server.dal.entity.FridayBotConversationEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Friday会话消息DTO，作为防腐层
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FridayConversationMessageDTO {
    /**
     * 消息ID
     */
    private String messageId;
    
    /**
     * 会话ID
     */
    private String conversationId;
    
    /**
     * 用户ID
     */
    private String userId;
    
    /**
     * 用户类型
     */
    private String userType;
    
    /**
     * 角色
     */
    private String role;
    
    /**
     * 生成类型
     */
    private String generateType;
    
    /**
     * 消息内容
     */
    private String message;
    
    /**
     * 消息状态
     */
    private String status;
    
    /**
     * 添加时间
     */
    private LocalDateTime addTime;
    
    /**
     * 父消息ID
     */
    private String parentMessageId;
    
    /**
     * 应用ID
     */
    private String appId;
    
    /**
     * 会话名称
     */
    private String conversationName;
    
    /**
     * 将实体转换为DTO
     * @param entity 实体对象
     * @return DTO对象
     */
    public static FridayConversationMessageDTO fromEntity(FridayBotConversationEntity entity) {
        if (entity == null) {
            return null;
        }
        
        return FridayConversationMessageDTO.builder()
                .messageId(entity.getMessageId())
                .conversationId(entity.getConversationId())
                .userId(entity.getUserId())
                .userType(entity.getUserType())
                .role(entity.getRole())
                .generateType(entity.getGenerateType())
                .message(entity.getMessage())
                .status(entity.getStatus())
                .addTime(entity.getUpdateTime())
                .parentMessageId(entity.getParentMessageId())
                .appId(entity.getAppId())
                .conversationName(entity.getConversationName())
                .build();
    }
    
    /**
     * 批量将实体列表转换为DTO列表
     * @param entities 实体列表
     * @return DTO列表
     */
    public static List<FridayConversationMessageDTO> fromEntities(List<FridayBotConversationEntity> entities) {
        if (entities == null) {
            return null;
        }
        
        return entities.stream()
                .map(FridayConversationMessageDTO::fromEntity)
                .collect(Collectors.toList());
    }
} 