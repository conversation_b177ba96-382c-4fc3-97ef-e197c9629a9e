package com.meituan.banma.llm.corpus.server.common.domain.dto;

import lombok.Data;

import java.sql.Timestamp;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/4
 */
@Data
public class TTInfoDTO {
    /**
     * 值班组id  rgId
     * 大象群id  groupId
     * TT id    ticketId
     */
    private long groupId;
    private Long rgId;
    private String ticketId;

    private String itemName;
    private String typeName;
    private String name;
    private String sla;
    private String ticketType;
    private String assigned;
    private String state;
    private String categoryName;
    private Long updatedAt;
    private String updatedBy;
    private Long createdAt;
    private String desc;
    private String reporter;
    /**
     * 自定义字段
     */
    private List<CustomFieldValuesItem> customFieldValues;
    /**
     * 知识库状态,KnowledgeBaseStatusEnum
     */
    private int kbStatus;
    private long kbTimestamp;
    private String kbUpdateUser;
    /**
     * 合并结果的id
     */
    private String kbMergedToId;
}
