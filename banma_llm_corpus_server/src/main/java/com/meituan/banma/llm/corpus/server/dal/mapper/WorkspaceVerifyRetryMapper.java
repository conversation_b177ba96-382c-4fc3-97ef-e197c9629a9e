package com.meituan.banma.llm.corpus.server.dal.mapper;

import com.meituan.banma.llm.corpus.server.dal.entity.WorkspaceVerifyRetryEntity;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 工作空间验证重试任务Mapper
 */
@Mapper
public interface WorkspaceVerifyRetryMapper {
    
    /**
     * 插入重试任务
     */
    @Insert("INSERT INTO workspace_verify_retry(rg_id, space_id, space_name, access_key, app_secret, retry_count, fail_reason, mis_id, status, create_time, update_time) " +
            "VALUES(#{rgId}, #{spaceId}, #{spaceName}, #{accessKey}, #{appSecret}, #{retryCount}, #{failReason}, #{misId}, #{status}, NOW(), NOW())")
    int insertRetryTask(WorkspaceVerifyRetryEntity entity);
    
    /**
     * 更新重试任务状态
     */
    @Update("UPDATE workspace_verify_retry SET status = #{status}, retry_count = #{retryCount}, update_time = NOW(), fail_reason = #{failReason} WHERE id = #{id}")
    int updateTaskStatus(WorkspaceVerifyRetryEntity entity);
    
    /**
     * 查询待处理的任务
     */
    @Select("SELECT * FROM workspace_verify_retry WHERE status = 0 ORDER BY create_time ASC LIMIT #{limit}")
    List<WorkspaceVerifyRetryEntity> findPendingTasks(@Param("limit") int limit);
    
    /**
     * 查询处理中但超时的任务
     */
    @Select("SELECT * FROM workspace_verify_retry WHERE status = 1 AND update_time < DATE_SUB(NOW(), INTERVAL 30 MINUTE) ORDER BY update_time ASC")
    List<WorkspaceVerifyRetryEntity> findStuckTasks();
    
    /**
     * 查询特定任务
     */
    @Select("SELECT * FROM workspace_verify_retry WHERE rg_id = #{rgId} AND space_id = #{spaceId}")
    WorkspaceVerifyRetryEntity findTask(@Param("rgId") long rgId, @Param("spaceId") String spaceId);
} 