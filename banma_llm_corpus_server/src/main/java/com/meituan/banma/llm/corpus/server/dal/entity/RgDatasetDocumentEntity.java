package com.meituan.banma.llm.corpus.server.dal.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RgDatasetDocumentEntity {
    private Long id;

    private Long rgId;

    // 空间ID
    private String spaceId;

    // 空间名称
    private String spaceName;

    //知识库Id
    private String datasetId;

    // 文档ID
    private String documentId;

    // 创建时间
    private Timestamp timestamp;

    // 访问密钥
    private String accessKey;

    // 应用密钥
    private String appSecret;

    /**
     * 状态：0-正常，1-已删除
     */
    private Integer status;
}
