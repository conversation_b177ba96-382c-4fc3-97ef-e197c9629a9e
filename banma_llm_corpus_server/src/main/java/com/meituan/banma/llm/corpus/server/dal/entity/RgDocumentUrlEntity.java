package com.meituan.banma.llm.corpus.server.dal.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

/**
 * 值班组知识库文档URL实体类
 * 对应数据库表：rg_document_url
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RgDocumentUrlEntity {
    /**
     * 主键，自增ID
     */
    private Long id;
    
    /**
     * TT值班组ID
     */
    private Long rgId;
    
    /**
     * 工作空间ID
     */
    private String spaceId;
    
    /**
     * 对应文档ID
     */
    private String documentId;
    
    /**
     * 名称
     */
    private String name;
    
    /**
     * URL地址
     */
    private String url;
    
    /**
     * 操作人misID
     */
    private String misId;
    
    /**
     * 记录创建时间
     */
    private Timestamp createTime;

    /**
     * 是否自动更新
     */
    private int autoUpdate;

    /**
     * 记录状态（0-正常，1-已删除）
     */
    private int status;
}
