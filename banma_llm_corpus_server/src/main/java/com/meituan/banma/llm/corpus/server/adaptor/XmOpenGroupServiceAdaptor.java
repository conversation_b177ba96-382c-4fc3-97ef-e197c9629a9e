package com.meituan.banma.llm.corpus.server.adaptor;

import com.meituan.banma.llm.corpus.api.constants.BizCode;
import com.meituan.banma.llm.corpus.api.exception.LlmCorpusException;
import com.meituan.banma.llm.corpus.server.rpc.dx.XmAuthRpcService;
import com.sankuai.xm.openplatform.api.entity.GetGroupNoticeReq;
import com.sankuai.xm.openplatform.api.entity.GetGroupNoticeResp;
import com.sankuai.xm.openplatform.api.service.open.XmOpenGroupServiceI;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * Class XmOpenGroupServiceAdaptor
 * Project banma_llm_corpus_process_server
 *
 * <AUTHOR>
 * @date 2025/3/25
 * Description 大象开放平台群组服务适配器
 */
@Service
@Slf4j
public class XmOpenGroupServiceAdaptor {

    @Resource
    private XmAuthRpcService xmAuthRpcService;

    @Resource
    private XmOpenGroupServiceI.Iface xmOpenGroupServiceIface;

    @Retryable(
            value = {Exception.class}, // 指定要重试的异常类型
            backoff = @Backoff(delay = 300)                       // 重试延迟(毫秒)
    )
    public GetGroupNoticeResp getGroupNoticeResp(long groupId) throws Exception {
        // 获取群公告中的TTid
        // GroupNoticeInfo(noticeContent:发起人：daili07 ｜ 处理人：daili07 ｜ [快捷查看|mtdaxiang://www.meituan.com/dxmp?appkey=OPEN_PLATFORM_trouble_tracker&path=%2Fdxmp-detail%3Fid%3D324915588] ｜ [系统内查看|http://tt.cloud.test.sankuai.com/ticket/detail?id=324915588], updateTime:1735182608432)
        GetGroupNoticeReq getGroupNoticeReq = new GetGroupNoticeReq().setGid(groupId);

        GetGroupNoticeResp getGroupNoticeResp = xmOpenGroupServiceIface.getGroupNotice(xmAuthRpcService.getToken(), getGroupNoticeReq);
        log.info("#xmOpenGroupServiceIface.getGroupNoticeResp:{}", getGroupNoticeResp);
        if (getGroupNoticeResp == null || getGroupNoticeResp.getStatus().code != 0) {
            log.error("Failed to retrieve group notice. GroupId: {}, Status code: {}", groupId,
                    getGroupNoticeResp != null ? getGroupNoticeResp.getStatus().code : "null");
            throw new LlmCorpusException(BizCode.GET_NOTICE_DETAIL_ERROR.getCode(), "获取大象群公告详情失败");
        }
        return getGroupNoticeResp;
    }
}
