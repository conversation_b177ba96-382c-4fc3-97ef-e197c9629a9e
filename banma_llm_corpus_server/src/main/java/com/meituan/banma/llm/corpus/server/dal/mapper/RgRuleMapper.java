package com.meituan.banma.llm.corpus.server.dal.mapper;

import com.meituan.banma.llm.corpus.server.dal.entity.RgRuleEntity;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface RgRuleMapper {
    // 查询某个值班组的背景知识总数
    @Select("SELECT COUNT(*) FROM rg_rules WHERE rg_id=#{rgId}")
    int countByRgId(@Param("rgId") long rgId);

    // 根据rgId分页查询历史背景知识
    @Select("SELECT * FROM rg_rules WHERE rg_id=#{rgId} ORDER BY version DESC LIMIT #{pageSize} OFFSET #{offset}")
    List<RgRuleEntity> queryByRgId(@Param("rgId") long rgId, @Param("pageSize") int pageSize, @Param("offset") int offset);

    // 插入（仅当version更大时才插入）
    @Insert("INSERT INTO rg_rules (rg_id, rule, version, update_time, mis_id) " +
            "SELECT #{rgId}, #{rule}, #{version}, #{updateTime}, #{misId} " +
            "WHERE NOT EXISTS (SELECT 1 FROM rg_rules WHERE rg_id=#{rgId} AND version>=#{version})")
    int insertWithVersionCheck(RgRuleEntity entity);

    // 查询最新一条背景知识
    @Select("SELECT * FROM rg_rules WHERE rg_id=#{rgId} ORDER BY version DESC LIMIT 1")
    RgRuleEntity queryLatestByRgId(@Param("rgId") long rgId);

    // 根据ID查询单条Rule记录
    @Select("SELECT * FROM rg_rules WHERE id=#{id}")
    RgRuleEntity queryById(@Param("id") Long id);
}
