package com.meituan.banma.llm.corpus.server.common.constants.enums;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum MergeTriggerSourceEnum {
    CORPUS_LIST(1, "语料列表"),
    REVIEW_PAGE(2, "审核页"),
    ;


    private final int code;
    private final String desc;

    public static MergeTriggerSourceEnum fromCode(int code){
        for (MergeTriggerSourceEnum item : MergeTriggerSourceEnum.values()){
            if (item.getCode() == code){
                return item;
            }
        }
        return null;
    }
}
