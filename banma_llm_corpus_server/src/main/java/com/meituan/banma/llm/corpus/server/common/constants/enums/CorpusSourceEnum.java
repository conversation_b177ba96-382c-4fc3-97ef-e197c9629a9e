package com.meituan.banma.llm.corpus.server.common.constants.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum CorpusSourceEnum {
    TT(0, "TT/大象群"),
    OTHER(1, "其他"),
    MANUAL(2, "手动新增"),
    KM(3, "学城"),
    MERGE(4, "合并"),
    SINGLE(5,"单聊")
    ;

    private final Integer code;
    private final String desc;

    public static CorpusSourceEnum fromCode(int code){
        for (CorpusSourceEnum item: CorpusSourceEnum.values()){
            if (code == item.getCode()){
                return item;
            }
        }
        return null;
    }
}
