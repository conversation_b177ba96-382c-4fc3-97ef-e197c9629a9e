package com.meituan.banma.llm.corpus.server.utils;

import java.net.InetAddress;
import java.net.UnknownHostException;

public class NetworkUtil {

    /**
     * 获取本机的 IP 地址
     *
     * @return 本机 IP 地址的字符串表示
     * @throws RuntimeException 如果无法获取 IP 地址
     */
    public static String getLocalIpAddress() {
        try {
            InetAddress inetAddress = InetAddress.getLocalHost();
            return inetAddress.getHostAddress();
        } catch (UnknownHostException e) {
            throw new RuntimeException("无法获取本机 IP 地址", e);
        }
    }
}

