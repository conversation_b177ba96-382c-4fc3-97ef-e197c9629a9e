package com.meituan.banma.llm.corpus.server.common.constants.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 知识库状态枚举
 */
@Getter
@AllArgsConstructor
public enum KnowledgeBaseStatusEnum {
    /**
     * TT的知识库状态
     */
    UNHANDLED(0, "未入库"),
    HANDLED(1, "已入库"),
    MARKED_AS_IGNORED(2, "无需入库"),
    MERGED(3, "已合并"),
    UNRESOLVED(4, "待解决"),
    RESOLVED_NO_GROUP(5, "未建群"),

    ;

    private final Integer code;
    private final String desc;

    public static KnowledgeBaseStatusEnum fromCode(int code){
        for (KnowledgeBaseStatusEnum item : KnowledgeBaseStatusEnum.values()){
            if (item.getCode() == code){
                return item;
            }
        }
        return null;
    }
}
