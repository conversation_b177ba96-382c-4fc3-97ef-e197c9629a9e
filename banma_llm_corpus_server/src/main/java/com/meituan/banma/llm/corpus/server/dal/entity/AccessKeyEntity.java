package com.meituan.banma.llm.corpus.server.dal.entity;

import lombok.Data;

import java.sql.Timestamp;

@Data
public class AccessKeyEntity {
    // 主键ID
    private Long id;

    // AccessKey
    private String ak;

    // AccessKey名称
    private String akName;

    // 操作人misID
    private String misId;

    // TT值班组ID列表，逗号分隔
    private String rgIds;

    // 是否默认ak，1为默认，0为非默认
    private Boolean defaultKey;

    // 该ak被使用的次数
    private Long count;

    // 创建时间
    private Timestamp ctime;

    // 更新时间
    private Timestamp utime;
} 