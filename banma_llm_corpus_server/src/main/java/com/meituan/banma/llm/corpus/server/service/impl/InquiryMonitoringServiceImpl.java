package com.meituan.banma.llm.corpus.server.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.converters.string.StringStringConverter;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.meituan.banma.llm.corpus.api.constants.BizCode;
import com.meituan.banma.llm.corpus.api.exception.LlmCorpusException;
import com.meituan.banma.llm.corpus.server.common.domain.dto.PageDTO;
import com.meituan.banma.llm.corpus.server.controller.request.monitoring.MonitoringGroupRequest;
import com.meituan.banma.llm.corpus.server.controller.request.monitoring.QuestionTypeRequest;
import com.meituan.banma.llm.corpus.server.dal.entity.InquiryMonitoringEntity;
import com.meituan.banma.llm.corpus.server.dal.entity.MonitoringGroupEntity;
import com.meituan.banma.llm.corpus.server.dal.entity.QuestionTypeEntity;
import com.meituan.banma.llm.corpus.server.dal.mapper.InquiryMonitoringMapper;
import com.meituan.banma.llm.corpus.server.dal.mapper.MonitoringGroupMapper;
import com.meituan.banma.llm.corpus.server.dal.mapper.QuestionTypeMapper;
import com.meituan.banma.llm.corpus.server.service.IInquiryMonitoringService;
import com.meituan.banma.llm.corpus.server.service.IDxGroupChatService;
import com.meituan.banma.llm.corpus.server.utils.ExcelExportUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 问询监控服务实现类
 */
@Slf4j
@Service
public class InquiryMonitoringServiceImpl implements IInquiryMonitoringService {
    
    @Resource
    private InquiryMonitoringMapper inquiryMonitoringMapper;
    
    @Resource
    private IDxGroupChatService dxGroupChatService;
    
    @Resource
    private MonitoringGroupMapper monitoringGroupMapper;
    
    @Resource
    private QuestionTypeMapper questionTypeMapper;
    
    @Override
    public PageDTO<InquiryMonitoringEntity> queryMonitoringData(Long startTime, Long endTime, Integer pageNum, Integer pageSize, List<Long> monitorGroupIds) throws LlmCorpusException {
        try {
            if (monitorGroupIds.isEmpty()) {
                log.warn("监控组ID为空，返回空数据");
                return createEmptyPageDTO(pageNum, pageSize);
            }
            
            // 查询符合条件的监控数据
            log.info("查询监控数据: monitorGroupIds={}, startTime={}, endTime={}", 
                    monitorGroupIds, startTime, endTime);
            List<InquiryMonitoringEntity> dataList = inquiryMonitoringMapper.selectByMonitorGroupIdsAndTimeRange(
                    monitorGroupIds, startTime, endTime);
            
            // 为每条记录填充大象群名称
            if (!dataList.isEmpty()) {
                for (InquiryMonitoringEntity entity : dataList) {
                    if (entity != null && entity.getDxGroupId() != null) {
                        try {
                            String groupName = dxGroupChatService.getGroupNameByDxGroupId(entity.getDxGroupId());
                            entity.setDxGroupName(groupName);
                        } catch (Exception e) {
                            log.warn("获取大象群名称异常: dxGroupId={}, error={}", entity.getDxGroupId(), e.getMessage());
                            // 设置默认名称，避免前端显示空值
                            entity.setDxGroupName("未知群组");
                        }
                    }
                }
            }
            
            // 计算总记录数和总页数
            int total = dataList.size();
            int totalPage = (total + pageSize - 1) / pageSize;
            
            // 分页处理结果
            int fromIndex = (pageNum - 1) * pageSize;
            int toIndex = Math.min(fromIndex + pageSize, total);
            
            List<InquiryMonitoringEntity> pageData = fromIndex < total ? 
                    dataList.subList(fromIndex, toIndex) : 
                    Collections.emptyList();
            
            // 构建分页结果
            PageDTO<InquiryMonitoringEntity> pageDTO = new PageDTO<>();
            pageDTO.setPageNum(pageNum);
            pageDTO.setPageSize(pageSize);
            pageDTO.setTotalCount(total);
            pageDTO.setTotalPage(totalPage);
            pageDTO.setData(pageData);
            
            log.info("查询监控数据成功，共获取{}条记录，返回第{}页，每页{}条", total, pageNum, pageSize);
            return pageDTO;
        } catch (Exception e) {
            log.error("查询监控数据异常: startTime={}, endTime={}, error={}", 
                    startTime, endTime, e.getMessage(), e);
            throw new LlmCorpusException(BizCode.DX_MONITOR_QUERY.getCode(), "查询监控数据异常: " + e.getMessage());
        }
    }
    
    @Override
    public String generateExcelAndUploadToS3(Long startTime, Long endTime, List<Long> monitorGroupIds) throws LlmCorpusException {
        try {
            // 查询所有数据（不分页）
            List<InquiryMonitoringEntity> dataList = queryAllMonitoringData(startTime, endTime, monitorGroupIds);
            
            if (CollectionUtils.isEmpty(dataList)) {
                log.warn("查询结果为空，无法生成Excel文件");
                return null;
            }
            
            // 生成Excel内容
            byte[] excelContent = convertToExcelContent(dataList);
            if (excelContent == null || excelContent.length == 0) {
                log.warn("生成Excel内容失败");
                return null;
            }
            
            // 生成任务ID用于文件名
            String taskId = "inquiry_monitoring_" + UUID.randomUUID().toString().replace("-", "");
            
            // 上传到S3并获取下载链接
            String downloadLink = ExcelExportUtil.uploadContentToS3(excelContent, taskId, ".xlsx");
            
            if (downloadLink != null) {
                log.info("Excel文件上传成功，下载链接: {}", downloadLink);
                return downloadLink;
            } else {
                log.warn("Excel文件上传失败，无法生成下载链接");
                return null;
            }
        } catch (Exception e) {
            log.error("生成Excel并上传到S3异常: startTime={}, endTime={}, error={}", 
                    startTime, endTime, e.getMessage(), e);
            throw new LlmCorpusException(BizCode.SERVER_INTERNAL_ERROR.getCode(), 
                    "生成Excel并上传到S3异常: " + e.getMessage());
        }
    }
    
    /**
     * 查询所有满足条件的监控数据（不分页）
     * 
     * @param startTime 开始时间戳
     * @param endTime 结束时间戳
     * @param monitorGroupIds 监控组ID列表，为空时从配置中获取
     * @return 监控数据列表
     * @throws LlmCorpusException 业务异常
     */
    @Override
    public List<InquiryMonitoringEntity> queryAllMonitoringData(Long startTime, Long endTime, List<Long> monitorGroupIds) throws LlmCorpusException {
        try {
            if (monitorGroupIds.isEmpty()) {
                log.warn("监控组ID为空，返回空数据");
                return Collections.emptyList();
            }
            
            // 查询符合条件的监控数据
            log.info("查询全部监控数据: monitorGroupIds={}, startTime={}, endTime={}", 
                    monitorGroupIds, startTime, endTime);
            List<InquiryMonitoringEntity> dataList = inquiryMonitoringMapper.selectByMonitorGroupIdsAndTimeRange(
                    monitorGroupIds, startTime, endTime);
                    
            // 为每条记录填充大象群名称
            if (!dataList.isEmpty()) {
                for (InquiryMonitoringEntity entity : dataList) {
                    if (entity != null && entity.getDxGroupId() != null) {
                        try {
                            String groupName = dxGroupChatService.getGroupNameByDxGroupId(entity.getDxGroupId());
                            entity.setDxGroupName(groupName);
                        } catch (Exception e) {
                            log.warn("获取大象群名称异常: dxGroupId={}, error={}", entity.getDxGroupId(), e.getMessage());
                            // 设置默认名称，避免前端显示空值
                            entity.setDxGroupName("未知群组");
                        }
                    }
                }
            }
            
            return dataList;
        } catch (Exception e) {
            log.error("查询全部监控数据异常: startTime={}, endTime={}, error={}", 
                    startTime, endTime, e.getMessage(), e);
            throw new LlmCorpusException(BizCode.DX_MONITOR_QUERY.getCode(), 
                    "查询全部监控数据异常: " + e.getMessage());
        }
    }
    
    /**
     * 将监控数据列表转换为Excel字节数组
     * 
     * @param dataList 监控数据列表
     * @return Excel内容的字节数组
     */
    private byte[] convertToExcelContent(List<InquiryMonitoringEntity> dataList) {
        if (CollectionUtils.isEmpty(dataList)) {
            log.warn("监控数据列表为空，无法转换为Excel内容");
            return null;
        }
        
        ByteArrayOutputStream outputStream = null;
        try {
            outputStream = new ByteArrayOutputStream();
            
            // 定义表头
            List<List<String>> headList = new ArrayList<>();
            headList.add(Collections.singletonList("ID"));
            headList.add(Collections.singletonList("监控组ID"));
            headList.add(Collections.singletonList("提问者MIS ID"));
            headList.add(Collections.singletonList("提问者工号"));
            headList.add(Collections.singletonList("提问者组织ID"));
            headList.add(Collections.singletonList("提问者组织名称"));
            headList.add(Collections.singletonList("原始问题消息"));
            headList.add(Collections.singletonList("问题概要"));
            headList.add(Collections.singletonList("问题类型"));
            headList.add(Collections.singletonList("回应状态"));
            headList.add(Collections.singletonList("消息ID"));
            headList.add(Collections.singletonList("消息时间"));
            headList.add(Collections.singletonList("大象群ID"));
            headList.add(Collections.singletonList("大象群名称"));
            headList.add(Collections.singletonList("创建时间"));
            headList.add(Collections.singletonList("更新时间"));
            
            // 创建表头和内容样式
            WriteCellStyle headStyle = createHeadStyle();
            WriteCellStyle contentStyle = createContentStyle();
            HorizontalCellStyleStrategy styleStrategy = new HorizontalCellStyleStrategy(headStyle, contentStyle);
            
            // 准备数据行
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            List<List<Object>> dataRows = new ArrayList<>();
            
            for (InquiryMonitoringEntity entity : dataList) {
                if (entity == null) {
                    continue;
                }
                
                List<Object> row = new ArrayList<>();
                row.add(entity.getId());
                row.add(entity.getMonitorGroupId());
                row.add(entity.getQuestionerMisId());
                row.add(entity.getQuestionerEmpId());
                row.add(entity.getQuestionerOrgId());
                row.add(entity.getQuestionerOrgName());
                row.add(entity.getRawQuestionMsg());
                row.add(entity.getSummarizedQuestion());
                row.add(entity.getQuestionType());
                row.add(entity.getReplyStatus());
                // 强制将消息ID转为字符串格式，以避免科学计数法显示
                String fromMessageId = String.valueOf(entity.getFromMessageId());
                row.add(fromMessageId != null ? fromMessageId : "");
                
                // 格式化日期时间
                String messageCtsStr = formatDate(entity.getMessageCts(), dateFormat);
                row.add(messageCtsStr);
                
                // 将大象群ID作为字符串处理
                Long dxGroupId = entity.getDxGroupId();
                row.add(dxGroupId != null ? String.valueOf(dxGroupId) : "");
                
                // 将大象群名称作为字符串处理
                String dxGroupName = entity.getDxGroupName();
                row.add(dxGroupName != null ? dxGroupName : "");
                
                // 格式化创建和更新时间
                String ctimeStr = formatDate(entity.getCtime(), dateFormat);
                String utimeStr = formatDate(entity.getUtime(), dateFormat);
                row.add(ctimeStr);
                row.add(utimeStr);
                
                dataRows.add(row);
            }
            
            // 写入Excel
            EasyExcel.write(outputStream)
                    .head(headList)
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .registerWriteHandler(styleStrategy)
                    // 全局设置，所有数值都以字符串形式输出
                    .registerConverter(new StringStringConverter())
                    .sheet("问询监控数据")
                    .doWrite(dataRows);
            
            return outputStream.toByteArray();
        } catch (Exception e) {
            log.error("转换监控数据为Excel内容异常: {}", e.getMessage(), e);
            return null;
        } finally {
            // 关闭输出流
            if (outputStream != null) {
                try {
                    outputStream.close();
                } catch (IOException e) {
                    log.error("关闭Excel输出流异常: {}", e.getMessage());
                }
            }
        }
    }
    
    /**
     * 创建表头样式
     */
    private WriteCellStyle createHeadStyle() {
        WriteCellStyle headStyle = new WriteCellStyle();
        // 设置背景颜色
        headStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        // 设置字体
        WriteFont headFont = new WriteFont();
        headFont.setFontHeightInPoints((short) 12);
        headFont.setBold(true);
        headStyle.setWriteFont(headFont);
        // 设置居中
        headStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        headStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        // 设置边框
        headStyle.setBorderBottom(BorderStyle.THIN);
        headStyle.setBorderLeft(BorderStyle.THIN);
        headStyle.setBorderRight(BorderStyle.THIN);
        headStyle.setBorderTop(BorderStyle.THIN);
        return headStyle;
    }
    
    /**
     * 创建内容样式
     */
    private WriteCellStyle createContentStyle() {
        WriteCellStyle contentStyle = new WriteCellStyle();
        // 设置自动换行
        contentStyle.setWrapped(true);
        // 设置垂直居中
        contentStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        // 设置边框
        contentStyle.setBorderBottom(BorderStyle.THIN);
        contentStyle.setBorderLeft(BorderStyle.THIN);
        contentStyle.setBorderRight(BorderStyle.THIN);
        contentStyle.setBorderTop(BorderStyle.THIN);
        return contentStyle;
    }
    
    /**
     * 格式化日期
     * 
     * @param timestamp 时间戳
     * @param dateFormat 日期格式化器
     * @return 格式化后的日期字符串
     */
    private String formatDate(Long timestamp, SimpleDateFormat dateFormat) {
        if (timestamp == null) {
            return "";
        }
        try {
            return dateFormat.format(new Date(timestamp));
        } catch (Exception e) {
            log.warn("日期格式化异常: timestamp={}, error={}", timestamp, e.getMessage());
            return String.valueOf(timestamp);
        }
    }
    
    /**
     * 创建空的分页数据对象
     * 
     * @param pageNum 页码
     * @param pageSize 每页记录数
     * @return 空的分页数据对象
     */
    private PageDTO<InquiryMonitoringEntity> createEmptyPageDTO(Integer pageNum, Integer pageSize) {
        PageDTO<InquiryMonitoringEntity> pageDTO = new PageDTO<>();
        pageDTO.setPageNum(pageNum);
        pageDTO.setPageSize(pageSize);
        pageDTO.setTotalCount(0);
        pageDTO.setTotalPage(0);
        pageDTO.setData(Collections.emptyList());
        return pageDTO;
    }
    
    /**
     * 验证监控任务是否可行
     * 检查小助手和所有监控组成员是否在指定的群聊中
     *
     * @param monitoringGroupEntity 监控组实体
     * @throws LlmCorpusException 业务异常
     */
    @Override
    public void validateMonitoringTask(MonitoringGroupEntity monitoringGroupEntity) throws LlmCorpusException {
        try {
            if (monitoringGroupEntity == null) {
                throw new LlmCorpusException(BizCode.ILLEGAL_ARGUMENT.getCode(), "监控组参数不能为空");
            }

            Long[] dxGroupIdArray = monitoringGroupEntity.getDxGroupIdArray();

            List<String> misIdList = new ArrayList<>();
            String[] ownerArray = monitoringGroupEntity.getMonitoringGroupOwnerArray();
            for (String misIdStr : ownerArray) {
                if (StringUtils.isNotEmpty(misIdStr)) {
                    misIdList.add(misIdStr.trim());
                }
            }

            Map<String, Map<String, Long>> empIdentityMap = Collections.emptyMap();
            if (!misIdList.isEmpty()) {
                empIdentityMap = dxGroupChatService.queryEmpIdentityByMisList(misIdList);
                log.info("批量查询员工身份信息结果: misIdList={}, 结果数量={}", misIdList, empIdentityMap.size());
            }
            
            // 校验所有成员misId都能查到员工ID
            for (String misId : misIdList) {
                Map<String, Long> identityInfo = empIdentityMap.get(misId);
                if (identityInfo == null || !identityInfo.containsKey("empId") || identityInfo.get("empId") == null) {
                    throw new LlmCorpusException(BizCode.QUERY_DX_USER_INFO_ERROR.getCode(),
                            "无法获取监控组成员的员工ID: " + misId);
                }
            }
            // 检查每个群聊，只校验第一个成员是否在群里
            for (Long groupId : dxGroupIdArray) {
                if (groupId == null) {
                    continue;
                }
                if (!misIdList.isEmpty()) {
                    String firstMisId = misIdList.get(0);
                    Map<String, Long> identityInfo = empIdentityMap.get(firstMisId);
                    Long empId = identityInfo.get("empId");
                    if (!dxGroupChatService.isUserInGroup(groupId, empId)) {
                        throw new LlmCorpusException(BizCode.VALIDATE_MONITOR.getCode(),
                                "监控组成员不在群聊中，请先将成员拉入群聊: " + firstMisId + "，群ID: " + groupId);
                    }
                }
            }

        } catch (LlmCorpusException e) {
            throw e;
        } catch (Exception e) {
            log.error("验证监控任务异常: {}", e.getMessage(), e);
            throw new LlmCorpusException(BizCode.VALIDATE_MONITOR.getCode(),
                    "验证监控任务异常: " + e.getMessage());
        }
    }
    
    /**
     * 新增监控组
     *
     * @param monitoringGroupEntity 监控组实体
     * @return 新增的监控组ID
     * @throws LlmCorpusException 业务异常
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long addMonitoringGroup(MonitoringGroupEntity monitoringGroupEntity) throws LlmCorpusException {
        try {
            // 基本非空检查
            if (monitoringGroupEntity == null) {
                throw new LlmCorpusException(BizCode.PARAM_ERROR.getCode(), "监控组参数不能为空");
            }
            
            // 设置默认值
            if (monitoringGroupEntity.getStatus() == null) {
                monitoringGroupEntity.setStatus(0); // 默认为正常状态
            }
            
            // 调用Mapper插入记录
            int result = monitoringGroupMapper.insert(monitoringGroupEntity);
            if (result <= 0) {
                throw new LlmCorpusException(BizCode.ADD_MONITOR_GROUP_ERROR.getCode(), "插入监控组记录失败");
            }
            
            log.info("新增监控组成功: monitoringGroupId={}, name={}", 
                    monitoringGroupEntity.getMonitoringGroupId(), 
                    monitoringGroupEntity.getMonitoringGroupName());
            
            return monitoringGroupEntity.getMonitoringGroupId();
        } catch (LlmCorpusException e) {
            throw e;
        } catch (Exception e) {
            log.error("新增监控组异常: {}", e.getMessage(), e);
            throw new LlmCorpusException(BizCode.ADD_MONITOR_GROUP_ERROR.getCode(),
                    "新增监控组异常: " + e.getMessage());
        }
    }

    /**
     * 将监控组请求对象转换为实体对象
     * 
     * @param request 监控组请求对象
     * @return 监控组实体对象
     */
    @Override
    public MonitoringGroupEntity convertRequestToEntity(MonitoringGroupRequest request) {
        if (request == null) {
            return null;
        }
        
        MonitoringGroupEntity entity = new MonitoringGroupEntity();
        entity.setMonitoringGroupId(request.getMonitoringGroupId());
        entity.setMonitoringGroupName(request.getMonitoringGroupName());
        entity.setMonitoringGroupDesc(request.getMonitoringGroupDesc());
        
        // 处理监控组所有者列表
        if (request.getMonitoringGroupOwner() != null && !request.getMonitoringGroupOwner().isEmpty()) {
            entity.setMonitoringGroupOwner(String.join(",", request.getMonitoringGroupOwner()));
        }
        
        // 处理DX群组ID列表
        if (request.getDxGroupIds() != null && !request.getDxGroupIds().isEmpty()) {
            entity.setDxGroupIds(request.getDxGroupIds().stream()
                    .map(String::valueOf)
                    .collect(Collectors.joining(",")));
        }
        
        // 处理监控组织ID列表
        if (request.getMonitoredOrgIds() != null && !request.getMonitoredOrgIds().isEmpty()) {
            entity.setMonitoredOrgIds(String.join(",", request.getMonitoredOrgIds()));
        }
        
        // 处理监控MIS账号列表
        if (request.getMonitoredMisIds() != null && !request.getMonitoredMisIds().isEmpty()) {
            entity.setMonitoredMisIds(String.join(",", request.getMonitoredMisIds()));
        }
        
        // 处理关键词列表
        if (request.getKeywords() != null && !request.getKeywords().isEmpty()) {
            entity.setKeywords(String.join(",", request.getKeywords()));
        }
        
        entity.setMonitoringTimeRangeType(request.getMonitoringTimeRangeType());
        entity.setStatus(request.getStatus() != null ? request.getStatus() : 0);
        
        return entity;
    }
    
    /**
     * 保存问题类型数据
     *
     * @param monitoringGroupId 监控组ID
     * @param questionTypes 问题类型列表
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveQuestionTypes(Long monitoringGroupId, List<QuestionTypeRequest> questionTypes) throws LlmCorpusException {
        try {
            if (monitoringGroupId == null) {
                throw new LlmCorpusException(BizCode.PARAM_ERROR.getCode(), "监控组ID不能为空");
            }
            
            // 如果没有新的问题类型数据，直接返回
            if (CollectionUtils.isEmpty(questionTypes)) {
                return;
            }
            
            // 获取已有的问题类型数据
            List<QuestionTypeEntity> existingTypes = questionTypeMapper.selectByMonitoringGroupId(monitoringGroupId);
            Map<Integer, QuestionTypeEntity> existingTypeMap = existingTypes.stream()
                    .collect(Collectors.toMap(QuestionTypeEntity::getQuestionTypeId, entity -> entity, (v1, v2) -> v1));
            
            // 收集需要保留的问题类型ID（前端传来的已有ID）
            Set<Integer> requestTypeIds = questionTypes.stream()
                    .map(QuestionTypeRequest::getQuestionTypeId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());
            
            // 需要删除的问题类型（数据库中有但前端没传的）
            List<Long> typesToDelete = existingTypes.stream()
                    .filter(entity -> !requestTypeIds.contains(entity.getQuestionTypeId()))
                    .map(QuestionTypeEntity::getId)
                    .collect(Collectors.toList());
            
            // 执行删除
            if (!typesToDelete.isEmpty()) {
                for (Long id : typesToDelete) {
                    questionTypeMapper.deleteById(id);
                }
                log.info("删除问题类型数据: monitoringGroupId={}, count={}", monitoringGroupId, typesToDelete.size());
            }
            
            // 批量处理新增和更新
            List<QuestionTypeEntity> typesToInsert = new ArrayList<>();
            int defaultSortOrder = 0; // 默认的排序序号

            // 获取当前最大的问题类型ID，用于新增时自动分配ID
            Integer maxQuestionTypeId = existingTypes.stream()
                    .map(QuestionTypeEntity::getQuestionTypeId)
                    .filter(Objects::nonNull)
                    .max(Integer::compareTo)
                    .orElse(0);
            
            for (QuestionTypeRequest typeRequest : questionTypes) {
                // 判断是更新还是新增
                if (typeRequest.getQuestionTypeId() != null && existingTypeMap.containsKey(typeRequest.getQuestionTypeId())) {
                    // 更新已有记录
                    QuestionTypeEntity existingEntity = existingTypeMap.get(typeRequest.getQuestionTypeId());
                    existingEntity.setTypeName(typeRequest.getQuestionTypeName());
                    existingEntity.setTypeDesc(typeRequest.getQuestionTypeDesc());
                    // 确保sort_order不为null
                    existingEntity.setSortOrder(typeRequest.getSortOrder() != null ? typeRequest.getSortOrder() : defaultSortOrder++);
                    questionTypeMapper.updateById(existingEntity);
                } else {
                    // 添加新记录，自动分配问题类型ID
                    QuestionTypeEntity newEntity = new QuestionTypeEntity();
                    newEntity.setMonitoringGroupId(monitoringGroupId);
                    
                    // 如果前端没有指定questionTypeId或指定的ID在数据库中已存在，则自动分配新ID
                    if (typeRequest.getQuestionTypeId() == null || existingTypeMap.containsKey(typeRequest.getQuestionTypeId())) {
                        newEntity.setQuestionTypeId(++maxQuestionTypeId);
                    } else {
                        newEntity.setQuestionTypeId(typeRequest.getQuestionTypeId());
                    }
                    
                    newEntity.setTypeName(typeRequest.getQuestionTypeName());
                    newEntity.setTypeDesc(typeRequest.getQuestionTypeDesc());
                    // 确保sort_order不为null
                    newEntity.setSortOrder(typeRequest.getSortOrder() != null ? typeRequest.getSortOrder() : defaultSortOrder++);
                    typesToInsert.add(newEntity);
                }
            }
            
            // 执行批量插入
            if (!typesToInsert.isEmpty()) {
                // 再次检查确保所有记录的必填字段都不为null
                for (int i = 0; i < typesToInsert.size(); i++) {
                    QuestionTypeEntity entity = typesToInsert.get(i);
                    if (entity.getSortOrder() == null) {
                        entity.setSortOrder(defaultSortOrder + i);
                    }
                    // 确保typeName不为null
                    if (entity.getTypeName() == null) {
                        entity.setTypeName("问题类型" + entity.getQuestionTypeId());
                    }
                }
                
                int insertCount = questionTypeMapper.batchInsert(typesToInsert);
                log.info("批量插入问题类型数据: monitoringGroupId={}, count={}", monitoringGroupId, insertCount);
            }
            
            log.info("问题类型数据保存完成: monitoringGroupId={}, 更新数量={}, 新增数量={}, 删除数量={}",
                    monitoringGroupId, existingTypes.size() - typesToDelete.size(), typesToInsert.size(), typesToDelete.size());
            
        } catch (LlmCorpusException e) {
            throw e;
        } catch (Exception e) {
            log.error("保存问题类型数据异常: monitoringGroupId={}, error={}", monitoringGroupId, e.getMessage(), e);
            throw new LlmCorpusException(BizCode.SERVER_INTERNAL_ERROR.getCode(), "保存问题类型数据异常: " + e.getMessage());
        }
    }
    
    /**
     * 获取监控组的问题类型列表
     *
     * @param monitoringGroupId 监控组ID
     * @return 问题类型列表
     */
    @Override
    public List<QuestionTypeEntity> getQuestionTypesByMonitoringGroupId(Long monitoringGroupId) {
        if (monitoringGroupId == null) {
            return Collections.emptyList();
        }
        return questionTypeMapper.selectByMonitoringGroupId(monitoringGroupId);
    }

    /**
     * 根据MIS ID查询该用户参与的所有监控组
     *
     * @param misId 用户MIS ID
     * @return 监控组列表
     * @throws LlmCorpusException 业务异常
     */
    @Override
    public List<MonitoringGroupEntity> getMonitoringGroupsByMisId(String misId) throws LlmCorpusException {
        try {
            if (StringUtils.isEmpty(misId)) {
                throw new LlmCorpusException(BizCode.ILLEGAL_ARGUMENT.getCode(), "MIS ID不能为空");
            }
            
            // 查询包含该MIS ID的监控组
            List<MonitoringGroupEntity> monitoringGroups = monitoringGroupMapper.selectByMisId(misId);
            
            log.info("查询用户监控组成功: misId={}, count={}", misId, monitoringGroups.size());
            return monitoringGroups;
        } catch (LlmCorpusException e) {
            throw e;
        } catch (Exception e) {
            log.error("查询用户监控组异常: misId={}, error={}", misId, e.getMessage(), e);
            throw new LlmCorpusException(BizCode.SERVER_INTERNAL_ERROR.getCode(), "查询用户监控组异常: " + e.getMessage());
        }
    }

    /**
     * 更新监控组信息
     *
     * @param monitoringGroupEntity 监控组实体
     * @return 是否更新成功
     * @throws LlmCorpusException 业务异常
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateMonitoringGroup(MonitoringGroupEntity monitoringGroupEntity) throws LlmCorpusException {
        try {
            // 基本非空检查
            if (monitoringGroupEntity == null) {
                throw new LlmCorpusException(BizCode.PARAM_ERROR.getCode(), "监控组参数不能为空");
            }
            
            if (monitoringGroupEntity.getMonitoringGroupId() == null) {
                throw new LlmCorpusException(BizCode.PARAM_ERROR.getCode(), "监控组ID不能为空");
            }
            
            // 查询原记录，确认存在
            MonitoringGroupEntity existingEntity = monitoringGroupMapper.selectById(monitoringGroupEntity.getMonitoringGroupId());
            if (existingEntity == null) {
                throw new LlmCorpusException(BizCode.PARAM_ERROR.getCode(), "监控组不存在，ID: " + monitoringGroupEntity.getMonitoringGroupId());
            }
            
            // 调用Mapper更新记录
            int result = monitoringGroupMapper.updateById(monitoringGroupEntity);
            if (result <= 0) {
                throw new LlmCorpusException(BizCode.UPDATE_MONITOR_GROUP_ERROR.getCode(), "更新监控组记录失败");
            }
            
            log.info("更新监控组成功: monitoringGroupId={}, name={}", 
                    monitoringGroupEntity.getMonitoringGroupId(), 
                    monitoringGroupEntity.getMonitoringGroupName());
            
            return true;
        } catch (LlmCorpusException e) {
            throw e;
        } catch (Exception e) {
            log.error("更新监控组异常: {}", e.getMessage(), e);
            throw new LlmCorpusException(BizCode.SERVER_INTERNAL_ERROR.getCode(), 
                    "更新监控组异常: " + e.getMessage());
        }
    }

    /**
     * 根据ID获取监控组信息
     *
     * @param monitoringGroupId 监控组ID
     * @return 监控组实体，不存在则返回null
     * @throws LlmCorpusException 业务异常
     */
    @Override
    public MonitoringGroupEntity getMonitoringGroupById(Long monitoringGroupId) throws LlmCorpusException {
        try {
            if (monitoringGroupId == null) {
                throw new LlmCorpusException(BizCode.ILLEGAL_ARGUMENT.getCode(), "监控组ID不能为空");
            }
            
            // 查询监控组信息
            MonitoringGroupEntity entity = monitoringGroupMapper.selectById(monitoringGroupId);
            if (entity == null) {
                log.info("监控组不存在: monitoringGroupId={}", monitoringGroupId);
                return null;
            }
            
            log.info("查询监控组成功: monitoringGroupId={}", monitoringGroupId);
            return entity;
        } catch (LlmCorpusException e) {
            throw e;
        } catch (Exception e) {
            log.error("查询监控组异常: monitoringGroupId={}, error={}", monitoringGroupId, e.getMessage(), e);
            throw new LlmCorpusException(BizCode.ILLEGAL_REQUEST.getCode(),
                    "查询监控组异常: " + e.getMessage());
        }
    }
    
    /**
     * 删除监控组
     *
     * @param monitoringGroupId 监控组ID
     * @return 是否删除成功
     * @throws LlmCorpusException 业务异常
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteMonitoringGroup(Long monitoringGroupId) throws LlmCorpusException {
        try {
            if (monitoringGroupId == null) {
                throw new LlmCorpusException(BizCode.ILLEGAL_ARGUMENT.getCode(), "监控组ID不能为空");
            }
            
            // 查询是否存在
            MonitoringGroupEntity entity = monitoringGroupMapper.selectById(monitoringGroupId);
            if (entity == null) {
                throw new LlmCorpusException(BizCode.ILLEGAL_ARGUMENT.getCode(), "监控组不存在，ID: " + monitoringGroupId);
            }
            
            // 删除相关的问题类型数据
            questionTypeMapper.deleteByMonitoringGroupId(monitoringGroupId);
            
            // 删除监控组
            int result = monitoringGroupMapper.deleteById(monitoringGroupId);
            if (result <= 0) {
                throw new LlmCorpusException(BizCode.UPDATE_MONITOR_GROUP_ERROR.getCode(), "删除监控组失败");
            }
            
            log.info("删除监控组成功: monitoringGroupId={}", monitoringGroupId);
            return true;
        } catch (LlmCorpusException e) {
            throw e;
        } catch (Exception e) {
            log.error("删除监控组异常: monitoringGroupId={}, error={}", monitoringGroupId, e.getMessage(), e);
            throw new LlmCorpusException(BizCode.UPDATE_MONITOR_GROUP_ERROR.getCode(),
                    "删除监控组异常: " + e.getMessage());
        }
    }


    /**
     * 获取指定时间范围和监控组的咨询统计数据
     *
     * @param startTime 开始时间戳（毫秒）
     * @param endTime 结束时间戳（毫秒）
     * @param monitorGroupIds 监控组ID列表
     * @return 统计数据，包含总咨询数量、问题类型数量、总咨询人数等信息
     * @throws LlmCorpusException 业务异常
     */
    @Override
    public Map<String, Object> getInquiryStatistics(Long startTime, Long endTime, List<Long> monitorGroupIds) throws LlmCorpusException {
        try {
            if (monitorGroupIds == null || monitorGroupIds.isEmpty()) {
                log.warn("监控组ID为空，返回空数据");
                return new HashMap<>();
            }
            
            // 查询符合条件的监控数据
            log.info("查询统计数据: monitorGroupIds={}, startTime={}, endTime={}", 
                    monitorGroupIds, startTime, endTime);
            List<InquiryMonitoringEntity> dataList = inquiryMonitoringMapper.selectByMonitorGroupIdsAndTimeRange(
                    monitorGroupIds, startTime, endTime);
            
            // 为所有数据填充大象群名称
            fillDxGroupNames(dataList);
            
            // 计算并返回统计数据
            return calculateStatistics(dataList, monitorGroupIds);
            
        } catch (Exception e) {
            log.error("查询统计数据异常: monitorGroupIds={}, startTime={}, endTime={}, error={}", 
                    monitorGroupIds, startTime, endTime, e.getMessage(), e);
            throw new LlmCorpusException(BizCode.SERVER_INTERNAL_ERROR.getCode(),
                    "查询统计数据异常: " + e.getMessage());
        }
    }
    
    /**
     * 填充大象群名称
     * 
     * @param dataList 需要填充群名称的数据列表
     */
    private void fillDxGroupNames(List<InquiryMonitoringEntity> dataList) {
        if (CollectionUtils.isEmpty(dataList)) {
            return;
        }
        
        for (InquiryMonitoringEntity entity : dataList) {
            if (entity != null && entity.getDxGroupId() != null && entity.getDxGroupName() == null) {
                try {
                    String groupName = dxGroupChatService.getGroupNameByDxGroupId(entity.getDxGroupId());
                    entity.setDxGroupName(groupName);
                } catch (Exception e) {
                    log.warn("获取大象群名称异常: dxGroupId={}, error={}", entity.getDxGroupId(), e.getMessage());
                    entity.setDxGroupName("未知群组");
                }
            }
        }
    }
    
    /**
     * 计算各项统计数据
     * 
     * @param dataList 原始数据列表
     * @param monitorGroupIds 监控组ID列表
     * @return 包含各项统计结果的Map
     */
    private Map<String, Object> calculateStatistics(List<InquiryMonitoringEntity> dataList, List<Long> monitorGroupIds) {
        Map<String, Object> statistics = new HashMap<>();
        
        if (CollectionUtils.isEmpty(dataList)) {
            log.info("数据列表为空，返回空统计结果");
            return statistics;
        }
        
        // 1. 总咨询数量
        int totalInquiries = dataList.size();
        statistics.put("totalInquiries", totalInquiries);
        
        // 2. 获取问题类型统计信息
        Set<Integer> questionTypeIds = dataList.stream()
                .map(InquiryMonitoringEntity::getQuestionType)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        statistics.put("questionTypeCount", questionTypeIds.size());
        
        // 3. 总咨询人数（去重的MIS ID数量）
        Set<String> uniqueMisIds = dataList.stream()
                .map(InquiryMonitoringEntity::getQuestionerMisId)
                .filter(StringUtils::isNotEmpty)
                .collect(Collectors.toSet());
        statistics.put("totalInquirers", uniqueMisIds.size());
        
        // 4. 问题类型统计
        processQuestionTypeStatistics(dataList, monitorGroupIds, statistics);
        
        // 5. 大象群统计
        processDxGroupStatistics(dataList, statistics);
        
        // 6. 咨询人统计
        processInquirerStatistics(dataList, statistics);
        
        // 7. 添加总咨询详细列表
        statistics.put("inquiryDetails", dataList);
        
        // 记录日志
        logStatisticsInfo(statistics);
        
        return statistics;
    }
    
    /**
     * 处理问题类型统计
     */
    private void processQuestionTypeStatistics(List<InquiryMonitoringEntity> dataList, List<Long> monitorGroupIds, Map<String, Object> statistics) {
        // 获取所有问题类型定义
        List<QuestionTypeEntity> allQuestionTypes = questionTypeMapper.selectByMonitoringGroupIds(monitorGroupIds);
        Map<Integer, String> questionTypeNameMap = allQuestionTypes.stream()
                .collect(Collectors.toMap(QuestionTypeEntity::getQuestionTypeId, QuestionTypeEntity::getTypeName, (v1, v2) -> v1));
        
        // 统计每种问题类型的数量
        Map<Integer, Long> questionTypeCountMap = dataList.stream()
                .filter(entity -> entity.getQuestionType() != null)
                .collect(Collectors.groupingBy(InquiryMonitoringEntity::getQuestionType, Collectors.counting()));
        
        // 构建问题类型统计列表
        List<Map<String, Object>> questionTypeStatistics = questionTypeCountMap.entrySet().stream()
                .map(entry -> {
                    Map<String, Object> item = new HashMap<>();
                    item.put("typeId", entry.getKey());
                    item.put("typeName", questionTypeNameMap.getOrDefault(entry.getKey(), "未知类型"));
                    item.put("count", entry.getValue());
                    return item;
                })
                .sorted((a, b) -> Long.compare((Long) b.get("count"), (Long) a.get("count")))
                .collect(Collectors.toList());
        
        statistics.put("questionTypeStatistics", questionTypeStatistics);
    }
    
    /**
     * 处理大象群统计
     */
    private void processDxGroupStatistics(List<InquiryMonitoringEntity> dataList, Map<String, Object> statistics) {
        // 统计不同大象群ID及其数量
        Map<Long, Long> dxGroupCountMap = dataList.stream()
                .filter(entity -> entity.getDxGroupId() != null)
                .collect(Collectors.groupingBy(InquiryMonitoringEntity::getDxGroupId, Collectors.counting()));
        
        // 构建大象群统计列表
        List<Map<String, Object>> dxGroupStatistics = dxGroupCountMap.entrySet().stream()
                .map(entry -> {
                    Map<String, Object> item = new HashMap<>();
                    item.put("groupId", entry.getKey());
                    
                    // 使用填充过的大象群名称
                    String groupName = dataList.stream()
                            .filter(entity -> entry.getKey().equals(entity.getDxGroupId()) && entity.getDxGroupName() != null)
                            .map(InquiryMonitoringEntity::getDxGroupName)
                            .findFirst()
                            .orElse("未知群组");
                    
                    item.put("groupName", groupName);
                    item.put("count", entry.getValue());
                    return item;
                })
                .sorted((a, b) -> Long.compare((Long) b.get("count"), (Long) a.get("count")))
                .collect(Collectors.toList());
        
        statistics.put("dxGroupStatistics", dxGroupStatistics);
    }
    
    /**
     * 处理咨询人统计
     */
    private void processInquirerStatistics(List<InquiryMonitoringEntity> dataList, Map<String, Object> statistics) {
        // 按咨询人MIS ID分组统计
        Map<String, List<InquiryMonitoringEntity>> inquirerEntityMap = dataList.stream()
                .filter(entity -> StringUtils.isNotEmpty(entity.getQuestionerMisId()))
                .collect(Collectors.groupingBy(InquiryMonitoringEntity::getQuestionerMisId));
        
        // 构建咨询人统计列表
        List<Map<String, Object>> inquirerStatistics = inquirerEntityMap.entrySet().stream()
                .map(entry -> {
                    String misId = entry.getKey();
                    List<InquiryMonitoringEntity> entityList = entry.getValue();
                    InquiryMonitoringEntity firstEntity = entityList.get(0);
                    
                    Map<String, Object> item = new HashMap<>();
                    item.put("misId", misId);
                    item.put("count", (long) entityList.size());
                    item.put("orgId", firstEntity.getQuestionerOrgId());
                    item.put("orgName", firstEntity.getQuestionerOrgName());
                    
                    return item;
                })
                .sorted((a, b) -> Long.compare((Long) b.get("count"), (Long) a.get("count")))
                .collect(Collectors.toList());
        
        statistics.put("inquirerStatistics", inquirerStatistics);
    }
    
    /**
     * 记录统计结果日志
     */
    private void logStatisticsInfo(Map<String, Object> statistics) {
        int totalInquiries = (int) statistics.getOrDefault("totalInquiries", 0);
        int questionTypeCount = (int) statistics.getOrDefault("questionTypeCount", 0);
        int totalInquirers = (int) statistics.getOrDefault("totalInquirers", 0);
        
        List<?> questionTypeStatistics = (List<?>) statistics.getOrDefault("questionTypeStatistics", Collections.emptyList());
        List<?> dxGroupStatistics = (List<?>) statistics.getOrDefault("dxGroupStatistics", Collections.emptyList());
        List<?> inquirerStatistics = (List<?>) statistics.getOrDefault("inquirerStatistics", Collections.emptyList());
        
        log.info("查询统计数据成功: 总咨询数量={}, 问题类型数量={}, 总咨询人数={}, " +
                "问题类型统计项数={}, 大象群统计项数={}, 咨询人统计项数={}", 
                totalInquiries, questionTypeCount, totalInquirers, 
                questionTypeStatistics.size(), dxGroupStatistics.size(), inquirerStatistics.size());
    }

    @Override
    public Map<String, Object> updateMonitoringGroupWithValidation(MonitoringGroupRequest request) throws LlmCorpusException {
        // 先查询原监控组信息，确认存在
        MonitoringGroupEntity existingEntity = getMonitoringGroupById(request.getMonitoringGroupId());
        if (existingEntity == null) {
            throw new LlmCorpusException(BizCode.ILLEGAL_ARGUMENT.getCode(), "监控组不存在，ID: " + request.getMonitoringGroupId());
        }

        // 取原有和新dxGroupIds
        List<Long> oldDxGroupIds = existingEntity.getDxGroupIdArray() != null ? Arrays.asList(existingEntity.getDxGroupIdArray()) : Collections.emptyList();
        List<Long> newDxGroupIds = request.getDxGroupIds() != null ? request.getDxGroupIds() : Collections.emptyList();

        // 取新增的群聊
        Set<Long> addedDxGroupIds = new HashSet<>(newDxGroupIds);
        addedDxGroupIds.removeAll(oldDxGroupIds);

        // 先批量查成员empId
        List<String> misIdList = request.getMonitoringGroupOwner();
        Map<String, Map<String, Long>> empIdentityMap = Collections.emptyMap();
        if (!misIdList.isEmpty()) {
            empIdentityMap = dxGroupChatService.queryEmpIdentityByMisList(misIdList);
            log.info("批量查询员工身份信息结果: misIdList={}, 结果数量={}", misIdList, empIdentityMap.size());
        }
        // 校验所有成员misId都能查到员工ID
        for (String misId : misIdList) {
            Map<String, Long> identityInfo = empIdentityMap.get(misId);
            if (identityInfo == null || !identityInfo.containsKey("empId") || identityInfo.get("empId") == null) {
                throw new LlmCorpusException(BizCode.QUERY_DX_USER_INFO_ERROR.getCode(), "无法获取监控组成员的员工ID: " + misId);
            }
        }

        // 校验所有群聊：监控组成员任意一个在群里即可
        for (Long dxGroupId : newDxGroupIds) {
            boolean anyMemberInGroup = false;
            for (String memberMisId : misIdList) {
                Long empId = empIdentityMap.get(memberMisId).get("empId");
                if (dxGroupChatService.isUserInGroup(dxGroupId, empId)) {
                    anyMemberInGroup = true;
                    break;
                }
            }
            if (!anyMemberInGroup) {
                throw new LlmCorpusException(BizCode.VALIDATE_MONITOR.getCode(), "无监控组成员在群聊[" + dxGroupId + "]中，无法保存");
            }
        }

        // 校验新增群聊：操作人必须在群里
        if (request.getOperatorMisId() != null && !addedDxGroupIds.isEmpty()) {
            Map<String, Map<String, Long>> operatorIdentityMap = dxGroupChatService.queryEmpIdentityByMisList(
                Collections.singletonList(request.getOperatorMisId()));
            Map<String, Long> operatorIdentity = operatorIdentityMap.get(request.getOperatorMisId());
            if (operatorIdentity == null || operatorIdentity.get("empId") == null) {
                throw new LlmCorpusException(BizCode.QUERY_DX_USER_INFO_ERROR.getCode(), "无法获取操作人的员工ID: " + request.getOperatorMisId());
            }
            Long operatorEmpId = operatorIdentity.get("empId");
            for (Long dxGroupId : addedDxGroupIds) {
                if (!dxGroupChatService.isUserInGroup(dxGroupId, operatorEmpId)) {
                    throw new LlmCorpusException(BizCode.VALIDATE_MONITOR.getCode(), "操作人不在新增群聊[" + dxGroupId + "]中，无法添加该群聊");
                }
            }
        }

        // 将请求转换为实体对象
        MonitoringGroupEntity entity = convertRequestToEntity(request);

        // 验证通过后，调用服务更新监控组
        boolean updated = updateMonitoringGroup(entity);

        // 更新问题类型数据
        if (updated && request.getQuestionTypes() != null && !request.getQuestionTypes().isEmpty()) {
            saveQuestionTypes(request.getMonitoringGroupId(), request.getQuestionTypes());
        }

        // 查询更新后的完整信息（包含问题类型）
        MonitoringGroupEntity updatedEntity = getMonitoringGroupById(request.getMonitoringGroupId());
        List<QuestionTypeEntity> questionTypes = getQuestionTypesByMonitoringGroupId(request.getMonitoringGroupId());
        updatedEntity.setQuestionTypes(questionTypes);

        // 构建返回结果
        Map<String, Object> result = new HashMap<>();
        result.put("monitoringGroup", updatedEntity);
        result.put("updated", updated);
        return result;
    }
} 