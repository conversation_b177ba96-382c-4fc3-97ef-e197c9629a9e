package com.meituan.banma.llm.corpus.server.controller;

import com.google.common.base.Preconditions;
import com.meituan.banma.llm.corpus.api.exception.LlmCorpusException;
import com.meituan.banma.llm.corpus.server.common.CommonResult;
import com.meituan.banma.llm.corpus.server.service.IOrgService;
import com.sankuai.meituan.org.opensdk.model.domain.items.OrgItems;
import com.sankuai.meituan.org.opensdk.model.domain.Org;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 组织架构控制器
 * 提供组织树查询等相关接口
 */
@Slf4j
@RestController
@RequestMapping("/org")
public class OrgController {

    @Autowired
    private IOrgService orgService;

    /**
     * 根据关键词搜索组织
     *
     * @param keyword 搜索关键词
     * @param offset 起始位置，从0开始
     * @param size 每页记录数，默认20，最大500
     * @return 匹配的组织列表
     */
    @GetMapping("/search")
    public Object searchOrg(
            @RequestParam("keyword") String keyword,
            @RequestParam(value = "offset", required = false, defaultValue = "0") Integer offset,
            @RequestParam(value = "size", required = false, defaultValue = "9999") Integer size) {
        try {
            // 校验参数
            Preconditions.checkArgument(StringUtils.isNotEmpty(keyword), "搜索关键词不能为空");

            OrgItems orgItems = orgService.searchOrg(keyword, offset, size);
            
            log.info("searchOrg# 搜索组织成功, keyword:{}, offset:{}, size:{}, 结果数量:{}", 
                    keyword, offset, size, orgItems == null ? 0 : orgItems.getCount());
            
            return CommonResult.success(orgItems);
        } catch (IllegalArgumentException e) {
            log.error("searchOrg# 搜索组织失败, 参数异常, 关键词:{}, 异常原因:{}", keyword, e.getMessage(), e);
            return CommonResult.error(e.getMessage());
        } catch (LlmCorpusException e) {
            log.error("searchOrg# 搜索组织失败, 业务异常, 关键词:{}, 异常原因:{}", keyword, e.getMessage(), e);
            return CommonResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("searchOrg# 搜索组织失败, 未知异常, 关键词:{}, 异常原因:{}", keyword, e.getMessage(), e);
            return CommonResult.error(String.format("搜索组织失败, 异常原因=%s", e.getMessage()));
        }
    }

    /**
     * 根据上级组织ID查询子组织(不包含本身)
     *
     * @param superiorId 上级组织ID
     * @param offset 起始位置，从0开始
     * @param size 每页记录数，默认20，最大500
     * @return 匹配的子组织列表
     */
    @GetMapping("/subordinate")
    public Object querySubordinateOrgs(
            @RequestParam("superiorId") String superiorId,
            @RequestParam(value = "offset", required = false, defaultValue = "0") Integer offset,
            @RequestParam(value = "size", required = false, defaultValue = "9999") Integer size) {
        try {
            // 校验参数
            Preconditions.checkArgument(StringUtils.isNotEmpty(superiorId), "上级组织ID不能为空");

            OrgItems subOrgItems = orgService.querySubordinateOrgs(superiorId, offset, size);
            
            log.info("querySubordinateOrgs# 查询子组织成功, superiorId:{}, offset:{}, size:{}, 结果数量:{}", 
                    superiorId, offset, size, subOrgItems == null ? 0 : subOrgItems.getCount());
            
            return CommonResult.success(subOrgItems);
        } catch (IllegalArgumentException e) {
            log.error("querySubordinateOrgs# 查询子组织失败, 参数异常, superiorId:{}, 异常原因:{}", superiorId, e.getMessage(), e);
            return CommonResult.error(e.getMessage());
        } catch (LlmCorpusException e) {
            log.error("querySubordinateOrgs# 查询子组织失败, 业务异常, superiorId:{}, 异常原因:{}", superiorId, e.getMessage(), e);
            return CommonResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("querySubordinateOrgs# 查询子组织失败, 未知异常, superiorId:{}, 异常原因:{}", superiorId, e.getMessage(), e);
            return CommonResult.error(String.format("查询子组织失败, 异常原因=%s", e.getMessage()));
        }
    }

    /**
     * 根据组织ID查询组织信息
     *
     * @param id 组织ID
     * @return 组织信息
     */
    @GetMapping("/queryOrgById")
    public Object queryOrgById(@RequestParam("id") String id) {
        try {
            // 校验参数
            Preconditions.checkArgument(StringUtils.isNotEmpty(id), "组织ID不能为空");

            Org org = orgService.queryOrgById(id);
            
            log.info("queryOrgById# 查询组织成功, id:{}", id);
            
            return CommonResult.success(org);
        } catch (IllegalArgumentException e) {
            log.error("queryOrgById# 查询组织失败, 参数异常, id:{}, 异常原因:{}", id, e.getMessage(), e);
            return CommonResult.error(e.getMessage());
        } catch (LlmCorpusException e) {
            log.error("queryOrgById# 查询组织失败, 业务异常, id:{}, 异常原因:{}", id, e.getMessage(), e);
            return CommonResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("queryOrgById# 查询组织失败, 未知异常, id:{}, 异常原因:{}", id, e.getMessage(), e);
            return CommonResult.error(String.format("查询组织失败, 异常原因=%s", e.getMessage()));
        }
    }

    /**
     * 根据组织名称路径查询组织信息
     *
     * @param orgNamePath 组织路径(有效组织)
     * @return 组织信息
     */
    @GetMapping("/queryByNamePath")
    public Object queryByNamePath(@RequestParam("orgNamePath") String orgNamePath) {
        try {
            // 校验参数
            Preconditions.checkArgument(StringUtils.isNotEmpty(orgNamePath), "组织路径不能为空");

            Org org = orgService.queryByNamePath(orgNamePath);
            
            log.info("queryByNamePath# 查询组织成功, orgNamePath:{}", orgNamePath);
            
            return CommonResult.success(org);
        } catch (IllegalArgumentException e) {
            log.error("queryByNamePath# 查询组织失败, 参数异常, orgNamePath:{}, 异常原因:{}", orgNamePath, e.getMessage(), e);
            return CommonResult.error(e.getMessage());
        } catch (LlmCorpusException e) {
            log.error("queryByNamePath# 查询组织失败, 业务异常, orgNamePath:{}, 异常原因:{}", orgNamePath, e.getMessage(), e);
            return CommonResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("queryByNamePath# 查询组织失败, 未知异常, orgNamePath:{}, 异常原因:{}", orgNamePath, e.getMessage(), e);
            return CommonResult.error(String.format("查询组织失败, 异常原因=%s", e.getMessage()));
        }
    }
} 