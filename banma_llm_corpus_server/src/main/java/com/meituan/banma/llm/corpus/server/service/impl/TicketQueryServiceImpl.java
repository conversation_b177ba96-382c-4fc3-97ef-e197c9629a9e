package com.meituan.banma.llm.corpus.server.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.meituan.banma.llm.corpus.api.constants.BizCode;
import com.meituan.banma.llm.corpus.api.exception.LlmCorpusException;
import com.meituan.banma.llm.corpus.server.common.MtConfigService;
import com.meituan.banma.llm.corpus.server.common.constants.enums.ConvertTaskPlatformId;
import com.meituan.banma.llm.corpus.server.common.constants.enums.CorpusStatusEnum;
import com.meituan.banma.llm.corpus.server.common.constants.enums.DeliveryTypeEnum;
import com.meituan.banma.llm.corpus.server.common.constants.enums.KnowledgeBaseStatusEnum;
import com.meituan.banma.llm.corpus.server.common.constants.enums.ModelOutputTaskStatusEnum;
import com.meituan.banma.llm.corpus.server.common.domain.dto.BatchAddTTContentResultDTO;
import com.meituan.banma.llm.corpus.server.common.domain.dto.ConvertTtToKnowledgeParam;
import com.meituan.banma.llm.corpus.server.common.domain.dto.QueryTTInfoDTO;
import com.meituan.banma.llm.corpus.server.common.domain.dto.RgInfoDTO;
import com.meituan.banma.llm.corpus.server.common.domain.dto.RgListItemDTO;
import com.meituan.banma.llm.corpus.server.common.domain.dto.RgUserInfo;
import com.meituan.banma.llm.corpus.server.common.domain.dto.TTInfoDTO;
import com.meituan.banma.llm.corpus.server.common.domain.dto.TicketDetailDTO;
import com.meituan.banma.llm.corpus.server.common.domain.dto.TicketRangeDTO;
import com.meituan.banma.llm.corpus.server.common.domain.dto.TicketRangeProcessResultDTO;
import com.meituan.banma.llm.corpus.server.common.domain.dto.TicketRangeResultDTO;
import com.meituan.banma.llm.corpus.server.common.domain.dto.TicketRangeTaskRecordDTO;
import com.meituan.banma.llm.corpus.server.dal.entity.KnowledgeBaseVersionEntity;
import com.meituan.banma.llm.corpus.server.dal.entity.ModelOutputTaskEntity;
import com.meituan.banma.llm.corpus.server.dal.mapper.ModelOutputMapper;
import com.meituan.banma.llm.corpus.server.dal.mapper.ReviewMapper;
import com.meituan.banma.llm.corpus.server.rpc.dx.XmAuthRpcService;
import com.meituan.banma.llm.corpus.server.service.IDxGroupChatService;
import com.meituan.banma.llm.corpus.server.service.IKnowledgeBaseService;
import com.meituan.banma.llm.corpus.server.service.ITicketQueryService;
import com.meituan.banma.llm.corpus.server.service.ITicketRangeTaskRecordService;
import com.meituan.banma.llm.corpus.server.service.mapper.KnowledgeBaseMapper;
import com.meituan.banma.llm.corpus.server.utils.AsyncTaskUtils;
import com.meituan.banma.llm.corpus.server.utils.ExcelExportUtil;
import com.meituan.banma.llm.corpus.server.utils.RgQueryUtil;
import com.meituan.banma.llm.corpus.server.utils.TicketQueryUtil;
import com.sankuai.dxenterprise.open.gateway.service.dx.api.DxService;
import com.sankuai.dxenterprise.open.gateway.service.dx.api.req.GetUidByEmpIdReq;
import com.sankuai.dxenterprise.open.gateway.service.dx.api.resp.GetUidByEmpIdResp;
import com.sankuai.xm.openplatform.api.entity.UserDetail;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.stream.IntStream;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;

import com.meituan.banma.llm.corpus.server.common.domain.dto.DynamicTemplateTicketDTO;
import com.meituan.banma.llm.corpus.server.common.domain.dto.DynamicTemplateResultDTO;
import com.meituan.banma.llm.corpus.server.common.domain.dto.TemplateConfig;

/**
 * <AUTHOR>
 * @date 2024/12/4
 */
@Slf4j
@Service
public class TicketQueryServiceImpl implements ITicketQueryService {
    @Resource
    private TicketQueryUtil ticketQueryUtil;

    @Resource
    private RgQueryUtil rgQueryUtil;

    @Resource
    private IKnowledgeBaseService knowledgeBaseService;

    @Resource
    private IDxGroupChatService dxGroupChatService;

    @Autowired
    private ReviewMapper reviewMapper;

    @Resource
    private ModelOutputMapper modelOutputMapper;

    @Resource
    private DxService dxService;

    @Resource
    private XmAuthRpcService xmAuthRpcService;

    @Resource
    private ITicketRangeTaskRecordService ticketRangeTaskRecordService;

    public static final Long EMPTY_CHAT_ROOM = -1L;

    private KnowledgeBaseMapper knowledgeBaseMapper = KnowledgeBaseMapper.get();
    @Autowired
    private MtConfigService mtConfigService;

    @Resource
    private DeliveryTypeEnum deliveryTypeEnum;

    @Override
    public QueryTTInfoDTO queryTTListByRgIdALL(String misId, Long rgId, int pageNum, int pageSize, Long createdAtStart, Long createdAtEnd) {
        Assert.notNull(rgId, "rgId不能为空");

        List<TTInfoDTO> ttInfoList = Lists.newArrayList();
        String resp = ticketQueryUtil.queryByRgIdALL(misId, rgId, pageNum, pageSize, createdAtStart, createdAtEnd);
        // 解析响应结果，拿到TicketDetailDTO的列表
        JSONObject respObj = parseResponseToJSONObject(resp);
        QueryTTInfoDTO queryTtInfoDTO = QueryTTInfoDTO.builder()
                .ticketDetailDTOList(ttInfoList)
                .build();
        if (respObj == null) {
            return queryTtInfoDTO;
        }
        JSONArray items = respObj.getJSONArray("items");
        int totalNum = respObj.getInteger("tn");
        queryTtInfoDTO.setTotalNum(totalNum);
        if (items == null) {
            return queryTtInfoDTO;
        }
        List<TicketDetailDTO> ticketDetailDTOList = items.toJavaList(TicketDetailDTO.class);
        // 遍历列表，调getDxGroupIdByTicketId，作为入参调trans2TTInfoDTO
        for (TicketDetailDTO ticketDetailDTO : ticketDetailDTOList) {
            String ticketId = ticketDetailDTO.getId();
            Long dxGroupId = getDxGroupIdByTicketId(misId, ticketId);
            ttInfoList.add(KnowledgeBaseMapper.trans2TtInfoDTO(dxGroupId, rgId, ticketDetailDTO));
        }

        List<String> ticketIds = ttInfoList.stream().map(TTInfoDTO::getTicketId).collect(Collectors.toList());
        List<KnowledgeBaseVersionEntity> knowledgeBaseVersionEntities = null;
        try{
            knowledgeBaseVersionEntities = queryModelOutputByTicketIds(ticketIds, rgId);
        } catch (Exception e){
            log.error("queryModelOutputByTciketIds error,ticketIds:{}",ticketIds, e);
        }
        if (CollectionUtils.isNotEmpty(knowledgeBaseVersionEntities)) {
            // 如果ttInfoList中的id存在于knowledgeBaseVersionEntities中，则将ttInfoList中对应item的kbStatus设置为1
            Map<String, KnowledgeBaseVersionEntity> knowledgeBaseVersionEntityMap = knowledgeBaseVersionEntities.stream()
                    .collect(Collectors.toMap(KnowledgeBaseVersionEntity::getTicketId, Function.identity(), (a, b) -> a));
            for (TTInfoDTO ttInfoDTO : ttInfoList) {
                if (knowledgeBaseVersionEntityMap.containsKey(ttInfoDTO.getTicketId())) {
                    KnowledgeBaseVersionEntity knowledgeBaseVersionEntity = knowledgeBaseVersionEntityMap.get(ttInfoDTO.getTicketId());
                    if (knowledgeBaseVersionEntity == null) {
                        log.warn("#TicketQueryService.queryTTListByRgId#error,knowledgeBaseVersionEntity is null,id:{}",ttInfoDTO.getTicketId());
                        continue;
                    }
                    if (Objects.equals(knowledgeBaseVersionEntity.getCorpusStatus(), CorpusStatusEnum.MARKED_AS_IGNORE.getCode())) {
                        ttInfoDTO.setKbStatus(KnowledgeBaseStatusEnum.MARKED_AS_IGNORED.getCode());
                    } else if (Objects.equals(knowledgeBaseVersionEntity.getCorpusStatus(), CorpusStatusEnum.MERGED.getCode())) {
                        ttInfoDTO.setKbMergedToId(knowledgeBaseVersionEntity.getMergedToId());
                        ttInfoDTO.setKbStatus(KnowledgeBaseStatusEnum.MERGED.getCode());
                    } else {
                        // 使用提取的方法判断是否未解决且无大象群
                        if (isTicketUnresolved(ttInfoDTO)) {
                            ttInfoDTO.setKbStatus(KnowledgeBaseStatusEnum.UNRESOLVED.getCode());
                        } else if (hasNoGroup(ttInfoDTO, misId)) {
                            ttInfoDTO.setKbStatus(KnowledgeBaseStatusEnum.RESOLVED_NO_GROUP.getCode());
                        } else {
                            ttInfoDTO.setKbStatus(KnowledgeBaseStatusEnum.HANDLED.getCode());
                        }
                    }
                    ttInfoDTO.setKbTimestamp(knowledgeBaseVersionEntity.getTimestamp().getTime());
                    ttInfoDTO.setKbUpdateUser(knowledgeBaseVersionEntity.getMisId());
                } else{
                    // 如果不在knowledgeBaseVersionEntityMap中，说明未入库，设置为未解决且无大象群状态
                    if (isTicketUnresolved(ttInfoDTO)) {
                        ttInfoDTO.setKbStatus(KnowledgeBaseStatusEnum.UNRESOLVED.getCode());
                    } else if (hasNoGroup(ttInfoDTO, misId)) {
                        ttInfoDTO.setKbStatus(KnowledgeBaseStatusEnum.RESOLVED_NO_GROUP.getCode());
                    }
                }
            }
        } else {
            // 如果knowledgeBaseVersionEntities为空，说明所有ticket都未入库，设置相应状态
            for (TTInfoDTO ttInfoDTO : ttInfoList) {
                if (isTicketUnresolved(ttInfoDTO)) {
                    ttInfoDTO.setKbStatus(KnowledgeBaseStatusEnum.UNRESOLVED.getCode());
                } else if (hasNoGroup(ttInfoDTO, misId)) {
                    ttInfoDTO.setKbStatus(KnowledgeBaseStatusEnum.RESOLVED_NO_GROUP.getCode());
                }
            }
        }
        return queryTtInfoDTO;
    }

    @Override
    public QueryTTInfoDTO queryTTListByRgId(String misId, Long rgId, int pageNum, int pageSize, Long createdAtStart, Long createdAtEnd) {
        Assert.notNull(rgId, "rgId不能为空");

        List<TTInfoDTO> ttInfoList = Lists.newArrayList();
        String resp = ticketQueryUtil.queryByRgId(misId, rgId, pageNum, pageSize, createdAtStart, createdAtEnd);
        // 解析响应结果，拿到TicketDetailDTO的列表
        JSONObject respObj = parseResponseToJSONObject(resp);
        QueryTTInfoDTO queryTtInfoDTO = QueryTTInfoDTO.builder()
                .ticketDetailDTOList(ttInfoList)
                .build();
        if (respObj == null) {
            return queryTtInfoDTO;
        }
        JSONArray items = respObj.getJSONArray("items");
        int totalNum = respObj.getInteger("tn");
        queryTtInfoDTO.setTotalNum(totalNum);
        if (items == null) {
            return queryTtInfoDTO;
        }
        List<TicketDetailDTO> ticketDetailDTOList = items.toJavaList(TicketDetailDTO.class);
        // 遍历列表，调getDxGroupIdByTicketId，作为入参调trans2TTInfoDTO
        for (TicketDetailDTO ticketDetailDTO : ticketDetailDTOList) {
            String ticketId = ticketDetailDTO.getId();
            Long dxGroupId = getDxGroupIdByTicketId(misId, ticketId);
            if (dxGroupId == null || dxGroupId.equals(EMPTY_CHAT_ROOM)) {
                continue;
            }
            ttInfoList.add(KnowledgeBaseMapper.trans2TtInfoDTO(dxGroupId, rgId, ticketDetailDTO));
        }
        List<String> ticketIds = ttInfoList.stream().map(TTInfoDTO::getTicketId).collect(Collectors.toList());
        List<KnowledgeBaseVersionEntity> knowledgeBaseVersionEntities = null;
        try{
            knowledgeBaseVersionEntities = queryModelOutputByTicketIds(ticketIds, rgId);
        } catch (Exception e){
            log.error("queryModelOutputByTciketIds error,ticketIds:{}",ticketIds, e);
        }
        if (CollectionUtils.isNotEmpty(knowledgeBaseVersionEntities)) {
            // 如果ttInfoList中的id存在于knowledgeBaseVersionEntities中，则将ttInfoList中对应item的kbStatus设置为1
            Map<String, KnowledgeBaseVersionEntity> knowledgeBaseVersionEntityMap = knowledgeBaseVersionEntities.stream()
                    .collect(Collectors.toMap(KnowledgeBaseVersionEntity::getTicketId, Function.identity(), (a, b) -> a));
            for (TTInfoDTO ttInfoDTO : ttInfoList) {
                if (knowledgeBaseVersionEntityMap.containsKey(ttInfoDTO.getTicketId())) {
                    KnowledgeBaseVersionEntity knowledgeBaseVersionEntity = knowledgeBaseVersionEntityMap.get(ttInfoDTO.getTicketId());
                    if (knowledgeBaseVersionEntity == null) {
                        log.warn("#TicketQueryService.queryTTListByRgId#error,knowledgeBaseVersionEntity is null,id:{}",ttInfoDTO.getTicketId());
                        continue;
                    }
                    if (Objects.equals(knowledgeBaseVersionEntity.getCorpusStatus(), CorpusStatusEnum.MARKED_AS_IGNORE.getCode())) {
                        ttInfoDTO.setKbStatus(KnowledgeBaseStatusEnum.MARKED_AS_IGNORED.getCode());
                    } else if (Objects.equals(knowledgeBaseVersionEntity.getCorpusStatus(), CorpusStatusEnum.MERGED.getCode())) {
                        ttInfoDTO.setKbMergedToId(knowledgeBaseVersionEntity.getMergedToId());
                        ttInfoDTO.setKbStatus(KnowledgeBaseStatusEnum.MERGED.getCode());
                    } else {
                        ttInfoDTO.setKbStatus(KnowledgeBaseStatusEnum.HANDLED.getCode());
                    }
                    ttInfoDTO.setKbTimestamp(knowledgeBaseVersionEntity.getTimestamp().getTime());
                    ttInfoDTO.setKbUpdateUser(knowledgeBaseVersionEntity.getMisId());
                }
            }
        }
        return queryTtInfoDTO;
    }

    @Override
    public List<KnowledgeBaseVersionEntity> queryModelOutputByTicketIds(List<String> ticketIds, Long rgId) {
        return reviewMapper.findByTicketIdsExcludeDeletedOnly(ticketIds, rgId);
    }

    @Override
    public Long getDxGroupIdByTicketId(String misId, String ticketId) {
        Assert.notNull(ticketId, "ticketId不能为空");

        String resp = ticketQueryUtil.queryDxInfoByTicketId(misId, ticketId);

        // 解析响应结果。返回大象群id，若此TT没有群组返回-1
        JSONObject data = parseResponseToJSONObject(resp);
        if (data == null) {
            return null;
        }
        boolean chatRoomCreated = data.getBooleanValue("chatRoomCreated");
        if (!chatRoomCreated) {
            return EMPTY_CHAT_ROOM;
        }
        return data.getLong("id");
    }

    @Override
    public List<RgInfoDTO> queryRGList(String misId, int pageNum, int pageSize) {
        String rgInfoDTOListJSONString = rgQueryUtil.queryMyRgList(misId, pageNum, pageSize);
        if (StringUtils.isBlank(rgInfoDTOListJSONString)) {
            return Collections.emptyList();
        }
        return parseJsonToRgInfoDTOList(rgInfoDTOListJSONString);
    }

    @Override
    public RgListItemDTO queryRgInfoByRgId(String misId, Long rgId) {
        Assert.notNull(rgId, "rgId不能为空");
        String rgInfoDTOListJSONString = rgQueryUtil.queryRgInfoById(misId, rgId);
        if (StringUtils.isBlank(rgInfoDTOListJSONString)) {
            return null;
        }
        List<RgListItemDTO> rgInfoDTOList = parseJsonToRgListItemDTOList(rgInfoDTOListJSONString);
        if (CollectionUtils.isEmpty(rgInfoDTOList)) {
            return null;
        }
        return rgInfoDTOList.get(0);
    }

    public static List<RgInfoDTO> parseJsonToRgInfoDTOList(String jsonString) {
        JSONObject jsonObject = JSON.parseObject(jsonString);
        // 获取"data"节点中的"items"数组
        String itemsJsonString = jsonObject.getJSONObject("data").getJSONArray("items").toJSONString();
        // 将"items"数组解析为List<RgInfoDTO>
        return JSON.parseObject(itemsJsonString, new TypeReference<List<RgInfoDTO>>() {});
    }

    private static List<RgListItemDTO> parseJsonToRgListItemDTOList(String jsonString) {
        JSONObject jsonObject = JSON.parseObject(jsonString);
        String itemsJsonString = jsonObject.getJSONObject("data").getJSONArray("items").toJSONString();
        return JSON.parseObject(itemsJsonString, new TypeReference<List<RgListItemDTO>>() {});
    }
    @Override
    public TTInfoDTO getTTInfoByTicketId(String misId, String ticketId, Long groupId) {
        Assert.notNull(ticketId, "ticketId不能为空");
        return ticketQueryUtil.getTtInfo(misId, ticketId, groupId);
    }

    @Override
    public String addTTContentByTTId(Long empId, String misId, String ticketId) throws Exception {
        Assert.notNull(ticketId, "ticketId不能为空");

        Long dxGroupId = getDxGroupIdByTicketId(misId, ticketId);
        TTInfoDTO ttInfo = ticketQueryUtil.getTtInfo(misId, ticketId, dxGroupId);

        GetUidByEmpIdResp uidByEmpId = dxService.getUidByEmpId(xmAuthRpcService.getToken(), new GetUidByEmpIdReq().setEmpId(empId));
        UserDetail userDetailByUid = dxGroupChatService.getUserDetailByUid(uidByEmpId.getData().getUid());

        // 获取机器人ID
        Long botId = mtConfigService.getChatBotId();

        // 构建任务参数
        ConvertTtToKnowledgeParam param = createTaskParam(ticketId, dxGroupId, ttInfo.getRgId(), misId,
                uidByEmpId.getData().getUid(), userDetailByUid.getName());

        // 邀请用户和机器人进群
        String errorMessage = inviteUsersAndBotToGroup(misId, ticketId, dxGroupId, botId);

        // 根据是否有错误决定后续处理
        if (errorMessage != null) {
            // 如果有错误，直接创建失败任务
            String taskId = createFailedTask(errorMessage, param);
            return taskId;
        } else {
            // 正常创建任务并返回
            TTInfoDTO ttInfoDTO = ticketQueryUtil.getTtInfo(param.getMisId(), param.getTtId(), param.getDxGroupId());
            return knowledgeBaseService.createConvertTtToKnowledgeTask(param, ttInfoDTO);
        }
    }

    /**
     * 构建任务参数
     */
    private ConvertTtToKnowledgeParam createTaskParam(String ticketId, Long dxGroupId, Long rgId,
                                                     String misId, Long creatorDxUserId, String creatorUserName) {
        ConvertTtToKnowledgeParam param = new ConvertTtToKnowledgeParam();
        param.setTtId(ticketId);
        param.setDxGroupId(dxGroupId);
        param.setRgId(rgId);
        param.setMisId(misId);
        param.setCreatorDxUserId(creatorDxUserId);
        param.setCreatorUserName(creatorUserName);
        param.setPlatformId(ConvertTaskPlatformId.WEB);
        return param;
    }

    /**
     * 邀请用户和机器人进群
     * @return 如果有错误返回错误信息，否则返回null
     */
    private String inviteUsersAndBotToGroup(String misId, String ticketId, Long dxGroupId, Long botId) {
        if (dxGroupId == null || dxGroupId.equals(EMPTY_CHAT_ROOM)) {
            return null;
        }

        try {
            log.info("准备将测试账号和机器人拉入群组. groupId:{}", dxGroupId);

            // 测试账号列表
            List<String> testUserList = Collections.singletonList(mtConfigService.getSsoLoginName());

            // 将测试用户拉入群
            ticketQueryUtil.inviteUsersToDxGroup(misId, ticketId, dxGroupId, testUserList);

            // 将机器人拉入群
            dxGroupChatService.addBotToGroup(botId, dxGroupId);
            log.info("机器人已加入群组. groupId:{}, botId:{}", dxGroupId, botId);

            return null;
        } catch (LlmCorpusException e) {
            log.error("邀请用户加入大象群失败. ticketId:{}, groupId:{}, errorMsg:{}",
                    ticketId, dxGroupId, e.getMessage());
            return e.getMessage();
        } catch (Exception e) {
            log.error("邀请机器人进群失败. groupId:{}, botId:{}", dxGroupId, botId, e);
            return null;
        }
    }

    /**
     * 创建失败状态的任务
     */
    private String createFailedTask(String errorMessage, ConvertTtToKnowledgeParam param) {
        String taskId = UUID.randomUUID().toString();

        try {
            // 初始化任务记录
            ModelOutputTaskEntity entity = new ModelOutputTaskEntity();
            entity.setTaskId(taskId);
            entity.setTicketId(param.getTtId());
            entity.setCreatorDxId(param.getCreatorDxUserId());
            entity.setDxGroupId(param.getDxGroupId());
            entity.setCreatorMisId(param.getMisId());
            entity.setCreatorUserName(param.getCreatorUserName());
            entity.setRgId(param.getRgId());
            entity.setTitle("");
            entity.setContent("");
            entity.setPlatformId(param.getPlatformId().getCode());
            entity.setTaskStatus(ModelOutputTaskStatusEnum.PROCESSING.getCode());
            modelOutputMapper.insertModelOutputTask(entity);

            // 设置为失败状态
            knowledgeBaseService.llmTaskFail(taskId, errorMessage, param);
            log.info("创建失败状态任务: taskId={}, errorMsg={}", taskId, errorMessage);
        } catch (Exception e) {
            log.error("创建失败状态任务出错: {}", e.getMessage(), e);
        }

        return taskId;
    }

    /**
     * 批量根据TT id添加TT内容
     *
     * @param empId 员工ID
     * @param misId 用户MIS ID
     * @param ticketIds TT ID列表
     * @return 处理结果DTO，包含成功和失败的信息
     * @throws LlmCorpusException 处理异常
     */
    @Override
    public BatchAddTTContentResultDTO batchAddTTContentByTTIds(Long empId, String misId, List<String> ticketIds) throws LlmCorpusException {
        // 参数校验
        if (empId == null || empId <= 0) {
            log.error("batchAddTTContentByTTIds# empId不能为空且必须大于0");
            throw LlmCorpusException.buildWithMsg(BizCode.ILLEGAL_ARGUMENT, "empId不能为空且必须大于0");
        }
        if (StringUtils.isBlank(misId)) {
            log.error("batchAddTTContentByTTIds# misId不能为空");
            throw LlmCorpusException.buildWithMsg(BizCode.ILLEGAL_ARGUMENT, "misId不能为空");
        }
        if (CollectionUtils.isEmpty(ticketIds)) {
            log.error("batchAddTTContentByTTIds# ticketIds不能为空");
            throw LlmCorpusException.buildWithMsg(BizCode.ILLEGAL_ARGUMENT, "ticketIds不能为空");
        }

        // 默认最大处理限制
        if (ticketIds.size() > mtConfigService.getBatchProcessTtMaxLimit()) {
            log.error("batchAddTTContentByTTIds# 批量添加TT内容超过最大限制");
            throw LlmCorpusException.buildWithMsg(BizCode.ILLEGAL_ARGUMENT, "批量添加TT内容超过最大限制，最大支持" + mtConfigService.getBatchProcessTtMaxLimit() + "条");
        }

        // 过滤空值并去重
        List<String> validTicketIds = ticketIds.stream()
                .filter(StringUtils::isNotBlank)
                .distinct()
                .collect(Collectors.toList());

        log.info("batchAddTTContentByTTIds# 开始批量处理, 总数:{}, 有效数:{}",
                ticketIds.size(), validTicketIds.size());

        // 获取线程池，使用专门为批量处理TT内容创建的线程池
        ExecutorService executor = AsyncTaskUtils.getBatchProcessTtThreadPool();

        // 创建结果集
        List<String> taskIds = Collections.synchronizedList(new ArrayList<>());
        List<String> failedTicketIds = Collections.synchronizedList(new ArrayList<>());
        Map<String, String> failureReasons = Collections.synchronizedMap(new HashMap<>());
        AtomicInteger failedCount = new AtomicInteger(0);

        // 使用CompletableFuture并行处理
        List<CompletableFuture<Void>> futures = new ArrayList<>();

        // 并行处理每个ticketId
        for (String ticketId : validTicketIds) {
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                try {
                    String taskId = addTTContentByTTId(empId, misId, ticketId);
                    taskIds.add(taskId);
                    log.info("batchAddTTContentByTTIds# 处理成功, ticketId:{}, taskId:{}", ticketId, taskId);
                } catch (Exception e) {
                    failedCount.incrementAndGet();
                    failedTicketIds.add(ticketId);
                    String errorMsg = e.getMessage() != null ? e.getMessage() : "未知错误";
                    failureReasons.put(ticketId, errorMsg);
                    log.error("batchAddTTContentByTTIds# 单条处理失败, ticketId:{}, 异常原因:{}", ticketId, errorMsg);
                }
            }, executor);

            futures.add(future);
        }

        // 等待所有任务完成
        try {
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                    .get(mtConfigService.getBatchProcessTtTimeout(), TimeUnit.MILLISECONDS);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("batchAddTTContentByTTIds# 等待任务完成被中断", e);
            throw LlmCorpusException.buildWithMsg(BizCode.SERVER_INTERNAL_ERROR, "批量处理被中断");
        } catch (ExecutionException e) {
            log.error("batchAddTTContentByTTIds# 等待任务完成出现异常", e);
            throw LlmCorpusException.buildWithMsg(BizCode.SERVER_INTERNAL_ERROR, "批量处理执行异常：" + e.getMessage());
        } catch (TimeoutException e) {
            log.error("batchAddTTContentByTTIds# 批量处理超时, 设定超时时间:{}ms", mtConfigService.getBatchProcessTtTimeout(), e);
            throw LlmCorpusException.buildWithMsg(BizCode.RPC_TIMEOUT, "批量处理超时，请减少处理数量或稍后重试");
        }

        // 构建结果DTO
        BatchAddTTContentResultDTO resultDTO = BatchAddTTContentResultDTO.builder()
                .taskIds(taskIds)
                .total(validTicketIds.size())
                .success(taskIds.size())
                .failed(failedCount.get())
                .failedTicketIds(failedTicketIds)
                .failureReasons(failureReasons)
                .build();

        log.info("batchAddTTContentByTTIds# 批量处理完成, 总数:{}, 成功:{}, 失败:{}",
                validTicketIds.size(), taskIds.size(), failedCount.get());

        return resultDTO;
    }

    private JSONObject parseResponseToJSONObject(String resp) {
        if (StringUtils.isBlank(resp)) {
            return null;
        }

        JSONObject respObj = JSONObject.parseObject(resp);
        if (respObj.getInteger("code") != 200) {
            return null;
        }

        return respObj.getJSONObject("data");
    }

    @Override
    public List<RgUserInfo> queryRgAdminUserInfoByRgId(String misId, Long rgId) {
        String rgAdminUsersString = rgQueryUtil.queryRgAdminUserList(misId, rgId);
        if (StringUtils.isBlank(rgAdminUsersString)) {
            return Collections.emptyList();
        }
        return parseJsonToRgUserInfoList(rgAdminUsersString);
    }

    @Override
    public List<RgUserInfo> queryRgNormalUserInfoByRgId(String misId, Long rgId) {
        String rgNormalUsersString = rgQueryUtil.queryRgNormalUserList(misId, rgId);
        if (StringUtils.isBlank(rgNormalUsersString)) {
            return Collections.emptyList();
        }
        return parseJsonToRgUserInfoList(rgNormalUsersString);
    }

    private List<RgUserInfo> parseJsonToRgUserInfoList(String jsonString) {
        JSONObject jsonObject = JSON.parseObject(jsonString);
        if (jsonObject == null) {
            return Collections.emptyList();
        }
        if (jsonObject.getInteger("code") != 200) {
            return Collections.emptyList();
        }
        String itemsJsonString = jsonObject.getJSONObject("data").getJSONArray("items").toJSONString();
        return JSON.parseObject(itemsJsonString, new TypeReference<List<RgUserInfo>>() {});

    }

    @Override
    public List<TicketDetailDTO> queryTicketListWithDate(String misId,Long rgId, Long startDate, Long endDate) {
        List<TTInfoDTO> ttInfoList = Lists.newArrayList();
        List<TicketDetailDTO> ticketDetailDTOList = Lists.newArrayList();
        String resp = ticketQueryUtil.queryByRgIdWithDateFilter(misId, rgId, 1, 1000, startDate, endDate);
        // 解析响应结果，拿到TicketDetailDTO的列表
        JSONObject respObj = parseResponseToJsonObject(resp);
        QueryTTInfoDTO queryTtInfoDTO = QueryTTInfoDTO.builder()
                .ticketDetailDTOList(ttInfoList)
                .build();
        if (respObj == null) {
            log.error("#TicketQueryServiceImpl.queryTicketListWithDate#error,respObj is null,resp:{}", resp);
            return ticketDetailDTOList;
        }
        JSONArray items = respObj.getJSONArray("items");
        int totalNum = respObj.getInteger("tn");
        queryTtInfoDTO.setTotalNum(totalNum);
        if (items == null) {
            log.error("#TicketQueryServiceImpl.queryTicketListWithDate#error,items is null,respObj:{}", respObj);
            return ticketDetailDTOList;
        }
        ticketDetailDTOList = items.toJavaList(TicketDetailDTO.class);
        if (CollectionUtils.isEmpty(ticketDetailDTOList)) {
            log.error("#TicketQueryServiceImpl.queryTicketListWithDate#error,ticketDetailDTOList is empty,respObj:{}", respObj);
        }
        return ticketDetailDTOList;
    }
    private JSONObject parseResponseToJsonObject(String resp) {
        if (org.apache.commons.lang3.StringUtils.isBlank(resp)) {
            return null;
        }

        JSONObject respObj = JSONObject.parseObject(resp);
        if (respObj.getInteger("code") != 200) {
            return null;
        }

        return respObj.getJSONObject("data");
    }

    /**
     * 检查ticket是否未解决
     */
    private boolean isTicketUnresolved(TTInfoDTO ttInfoDTO) {
        return !TicketQueryUtil.getResolvedStates().contains(ttInfoDTO.getState());
    }

    /**
     * 检查ticket是否没有大象群
     */
    private boolean hasNoGroup(TTInfoDTO ttInfoDTO, String misId) {
        Long dxGroupId = getDxGroupIdByTicketId(misId, ttInfoDTO.getTicketId());
        return dxGroupId == null || dxGroupId.equals(EMPTY_CHAT_ROOM);
    }

    /**
     * 查询范围工单信息
     *
     * @param misId 用户MIS ID
     * @param createdAtStart 创建开始时间，格式：yyyy-MM-dd
     * @param createdAtEnd 创建结束时间，格式：yyyy-MM-dd
     * @param state 工单状态，多个状态以逗号分隔
     * @return ticketIds 工单ID列表
     */
    @Override
    public TicketRangeResultDTO queryRangeTicketsInfo(String misId, String createdAtStart, String createdAtEnd, String state) {
        Long rgId = mtConfigService.getTicketRangeQueryConfig().getRgId();

        // 转换日期格式为时间戳
        Long startTimestamp = convertDateToTimestamp(createdAtStart);
        Long endTimestamp = convertDateToTimestamp(createdAtEnd);
        
        // 对结束日期特殊处理，设置为当天的23:59:59
        if (endTimestamp != null) {
            endTimestamp = endTimestamp + 24 * 60 * 60 * 1000 - 1000; // 加上23小时59分59秒
        }
        log.info("转换后的时间戳: startTimestamp={}, endTimestamp={}", startTimestamp, endTimestamp);

        // 1. 查询工单列表
        List<String> ticketIds = queryTicketIds(rgId, misId, startTimestamp, endTimestamp, state);
        if (CollectionUtils.isEmpty(ticketIds)) {
            log.info("未查询到符合条件的工单");
            return TicketRangeResultDTO.builder()
                    .ticketList(Collections.emptyList())
                    .totalCount(0)
                    .build();
        }
        log.info("查询到工单ID列表: size={}, ticketIds={}", ticketIds.size(), ticketIds);

        // 2. 创建任务记录
        String taskId = createTicketRangeTask(ticketIds);
        
        // 3. 并行获取工单详细信息
        TicketRangeProcessResultDTO result = processTicketDetails(rgId, misId, ticketIds, taskId);
        
        // 4. 上传Excel到S3并获取下载链接
        String downloadLink = generateAndUploadExcel(result.getResultList(), taskId);
        
        // 构建并返回结果
        TicketRangeResultDTO resultDTO = TicketRangeResultDTO.builder()
                .ticketList(result.getResultList())
                .excelDownloadLink(downloadLink)
                .taskId(taskId)
                .totalCount(result.getResultList().size())
                .build();

        return resultDTO;
    }
    
    /**
     * 将日期字符串转换为时间戳
     * 
     * @param dateStr 日期字符串，格式：yyyy-MM-dd
     * @return 时间戳，转换失败返回null
     */
    private Long convertDateToTimestamp(String dateStr) {
        if (StringUtils.isBlank(dateStr)) {
            return null;
        }
        
        try {
            java.text.SimpleDateFormat format = new java.text.SimpleDateFormat("yyyy-MM-dd");
            format.setLenient(false);
            java.util.Date date = format.parse(dateStr);
            return date.getTime();
        } catch (Exception e) {
            log.error("日期转换异常: dateStr={}, error={}", dateStr, e.getMessage());
            return null;
        }
    }
    
    /**
     * 查询工单ID列表
     * 
     * @param rgId 组织ID
     * @param misId 用户MIS ID
     * @param createdAtStart 创建开始时间
     * @param createdAtEnd 创建结束时间
     * @param state 工单状态，多个状态以逗号分隔
     * @return 工单ID列表
     */
    private List<String> queryTicketIds(Long rgId, String misId, Long createdAtStart, Long createdAtEnd, String state) {
        String resp = ticketQueryUtil.queryRangeTickets(rgId, createdAtStart, createdAtEnd, state, misId);
        JSONObject respObj = parseResponseToJSONObject(resp);
                
        if (respObj == null) {
            return Collections.emptyList();
        }
        
        JSONArray items = respObj.getJSONArray("items");
        
        if (items == null || items.isEmpty()) {
            return Collections.emptyList();
        }

        List<String> ticketIds = IntStream.range(0, items.size())
                .mapToObj(items::getJSONObject)
                .map(item -> item.getString("id"))
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
                
        return ticketIds;
    }
    
    /**
     * 创建工单范围任务记录
     * 
     * @param ticketIds 工单ID列表
     * @return 任务ID，创建失败则返回null
     */
    private String createTicketRangeTask(List<String> ticketIds) {
        String taskId = null;
        try {
            TicketRangeTaskRecordDTO taskRecord = ticketRangeTaskRecordService.createTaskRecord(ticketIds);
            if (taskRecord != null) {
                taskId = taskRecord.getTaskId();
            } else {
                log.warn("创建任务记录失败");
            }
        } catch (Exception e) {
            log.error("创建任务记录异常: {}", e.getMessage(), e);
        }
        return taskId;
    }
    
    /**
     * 并行处理工单详细信息
     * 
     * @param rgId 组织ID
     * @param misId 用户MIS ID
     * @param ticketIds 工单ID列表
     * @param taskId 任务ID
     * @return 处理结果，包含成功的结果列表和处理状态
     */
    private TicketRangeProcessResultDTO processTicketDetails(Long rgId, String misId, List<String> ticketIds, String taskId) {
        List<TicketRangeDTO> resultList = Collections.synchronizedList(new ArrayList<>());
        List<String> successTicketIds = Collections.synchronizedList(new ArrayList<>());
        List<String> failedTicketIds = Collections.synchronizedList(new ArrayList<>());
        
        // 获取线程池
        ExecutorService executor = AsyncTaskUtils.getBatchProcessTtThreadPool();
        
        // 创建异步任务列表
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        
        // 对每个工单ID异步获取详细信息
        for (String ticketId : ticketIds) {
            CompletableFuture<Void> future = AsyncTaskUtils.supplyAsyncWithThreadLocal(executor, () -> {
                try {
                    // 获取工单范围详细信息
                    TicketRangeDTO rangeDTO = ticketQueryUtil.getTicketRangeInfo(rgId, ticketId, misId);
                    if (rangeDTO != null && StringUtils.isNotBlank(rangeDTO.getTicketId())) {
//                        // 保存工单范围信息到数据库
//                        boolean saved = ticketRangeService.saveTicketRange(rangeDTO);
//                        if (saved) {
//                            // 添加到成功列表
//                            successTicketIds.add(ticketId);
//                        } else {
//                            log.warn("工单范围信息保存失败: ticketId={}", ticketId);
//                            failedTicketIds.add(ticketId);
//                        }
                        resultList.add(rangeDTO);
                    } else {
                        log.warn("获取工单范围信息失败或信息为空: ticketId={}", ticketId);
                        failedTicketIds.add(ticketId);
                    }
                    return null;
                } catch (Exception e) {
                    log.error("处理工单详情异常: ticketId={}, error={}", ticketId, e.getMessage(), e);
                    failedTicketIds.add(ticketId);
                    return null;
                }
            });
            
            futures.add(future);
        }
        
        try {
            // 等待所有异步任务完成
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                .get(mtConfigService.getBatchProcessTtTimeout(), TimeUnit.MILLISECONDS);
        } catch (InterruptedException e) {
            log.error("等待获取工单信息被中断: taskId={}, {}", taskId, e.getMessage());
            Thread.currentThread().interrupt();
        } catch (ExecutionException e) {
            log.error("获取工单信息执行异常: taskId={}, {}", taskId, e.getMessage(), e);
        } catch (TimeoutException e) {
            log.error("获取工单信息超时, taskId={}, 已处理{}个, 总数{}", 
                    taskId, resultList.size(), ticketIds.size());
        }

//        updateTaskRecord(taskId, successTicketIds);
        
        return new TicketRangeProcessResultDTO(resultList, successTicketIds, failedTicketIds);
    }
    
    /**
     * 更新任务记录
     * 
     * @param taskId 任务ID
     * @param successTicketIds 成功处理的工单ID列表
     */
    private void updateTaskRecord(String taskId, List<String> successTicketIds) {
        if (taskId != null && !successTicketIds.isEmpty()) {
            try {
                boolean updated = ticketRangeTaskRecordService.updateTaskRecord(taskId, successTicketIds);
                if (updated) {
                    log.info("#updateTaskRecord 更新任务记录成功，taskId={}, 成功工单数量={}", taskId, successTicketIds.size());
                } else {
                    log.warn("#updateTaskRecord 更新任务记录失败，taskId={}", taskId);
                }
            } catch (Exception e) {
                log.error("#updateTaskRecord 更新任务记录异常: taskId={}, error={}", taskId, e.getMessage(), e);
            }
        }
    }
    
    /**
     * 生成Excel并上传到S3
     * 
     * @param resultList 工单范围信息列表
     * @param taskId 任务ID
     * @return S3下载链接，生成失败则返回null
     */
    private String generateAndUploadExcel(List<TicketRangeDTO> resultList, String taskId) {
        // 上传文件内容到S3并获取预签名URL
        String downloadLink = null;
        if (!CollectionUtils.isEmpty(resultList)) {
            try {
                // 按商家ID拆分记录
                List<TicketRangeDTO> splitResultList = splitTicketRangeByMerchantId(resultList);
                
                byte[] excelContent = convertToExcelContent(splitResultList);
                if (excelContent != null && excelContent.length > 0) {
                    downloadLink = ExcelExportUtil.uploadContentToS3(excelContent, taskId, ".xlsx");
                    if (downloadLink != null) {
                        log.info("Excel文件上传成功，S3预签名下载链接: {}", downloadLink);
                    } else {
                        log.warn("Excel文件上传失败，无法生成S3预签名下载链接");
                    }
                } else {
                    log.warn("生成Excel内容失败");
                }
            } catch (Exception e) {
                log.error("上传Excel到S3异常: {}", e.getMessage(), e);
            }
        } else {
            log.warn("工单列表为空，未生成下载链接");
        }
        return downloadLink;
    }
    
    /**
     * 按商家ID拆分工单范围记录
     * 将包含多个商家ID（以逗号分隔）的记录拆分为多条记录，每条记录只包含一个商家ID
     *
     * @param originalList 原始工单范围信息列表
     * @return 拆分后的工单范围信息列表
     */
    private List<TicketRangeDTO> splitTicketRangeByMerchantId(List<TicketRangeDTO> originalList) {
        if (CollectionUtils.isEmpty(originalList)) {
            return Collections.emptyList();
        }
        
        List<TicketRangeDTO> resultList = new ArrayList<>();
        
        for (TicketRangeDTO originalDTO : originalList) {
            if (originalDTO == null) {
                continue;
            }
            
            String merchantId = originalDTO.getMerchantId();
            if (StringUtils.isBlank(merchantId)) {
                resultList.add(originalDTO);
                continue;
            }

            if (!merchantId.contains(",")) {
                resultList.add(originalDTO);
                continue;
            }
            
            // 按逗号拆分商家ID
            String[] merchantIds = merchantId.split(",");
            for (String singleMerchantId : merchantIds) {
                singleMerchantId = singleMerchantId.trim();
                if (StringUtils.isBlank(singleMerchantId)) {
                    continue;
                }

                TicketRangeDTO newDTO = new TicketRangeDTO();
                
                // 复制属性
                newDTO.setTicketId(originalDTO.getTicketId());
                newDTO.setRgId(originalDTO.getRgId());
                newDTO.setDate(originalDTO.getDate());
                newDTO.setTimeSlot(originalDTO.getTimeSlot());
                newDTO.setReason(originalDTO.getReason());
                newDTO.setTypeName(originalDTO.getTypeName());
                newDTO.setTypeId(originalDTO.getTypeId());
                newDTO.setCityName(originalDTO.getCityName());
                newDTO.setCityId(originalDTO.getCityId());
                newDTO.setOrderId(originalDTO.getOrderId());
                newDTO.setAddress(originalDTO.getAddress());
                newDTO.setMerchantId(singleMerchantId);
                
                resultList.add(newDTO);
            }
        }
        
        return resultList;
    }

    /**
     * 将工单列表转换为Excel字节数组
     *
     * @param ticketList 工单列表
     * @return Excel内容的字节数组
     */
    private byte[] convertToExcelContent(List<TicketRangeDTO> ticketList) {
        if (CollectionUtils.isEmpty(ticketList)) {
            log.warn("工单列表为空，无法转换为Excel内容");
            return null;
        }

        ByteArrayOutputStream outputStream = null;
        try {
            outputStream = new ByteArrayOutputStream();

            List<List<String>> headList = new ArrayList<>();
            headList.add(Collections.singletonList("工单ID"));
            headList.add(Collections.singletonList("日期"));
            headList.add(Collections.singletonList("商家ID"));
            headList.add(Collections.singletonList("时段"));
            headList.add(Collections.singletonList("原因"));
            headList.add(Collections.singletonList("类型ID"));
            headList.add(Collections.singletonList("类型"));
            headList.add(Collections.singletonList("城市"));
            headList.add(Collections.singletonList("城市ID"));
            headList.add(Collections.singletonList("订单ID"));
            headList.add(Collections.singletonList("地址"));

            List<List<Object>> dataRows = new ArrayList<>();
            for (TicketRangeDTO ticket : ticketList) {
                if (ticket == null) {
                    continue;
                }

                String dateStr = "";
                if (ticket.getDate() != null) {
                    try {
                        dateStr = new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(ticket.getDate());
                    } catch (Exception e) {
                        log.warn("日期格式转换异常: {}", e.getMessage());
                        dateStr = String.valueOf(ticket.getDate());
                    }
                }
                
                // 获取类型ID
                String typeName = ticket.getTypeName();
                String typeId = "";
                if (typeName != null && !typeName.isEmpty()) {
                    String id = deliveryTypeEnum.getTypeIdByName(typeName);
                    typeId = id != null ? id : "";
                }
                
                List<Object> row = new ArrayList<>();
                row.add(ticket.getTicketId() != null ? ticket.getTicketId() : "");
                row.add(dateStr);
                row.add(ticket.getMerchantId() != null ? ticket.getMerchantId() : "");
                row.add(ticket.getTimeSlot() != null ? ticket.getTimeSlot() : "");
                row.add(ticket.getReason() != null ? ticket.getReason() : "");
                row.add(typeId);
                row.add(ticket.getTypeName() != null ? ticket.getTypeName() : "");
                row.add(ticket.getCityName() != null ? ticket.getCityName() : "");
                row.add(ticket.getCityId() != null ? ticket.getCityId() : "");
                row.add(ticket.getOrderId() != null ? ticket.getOrderId() : "");
                row.add(ticket.getAddress() != null ? ticket.getAddress() : "");
                
                dataRows.add(row);
            }

            EasyExcel.write(outputStream)
                    .head(headList)
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .sheet("工单范围信息")
                    .doWrite(dataRows);

            byte[] content = outputStream.toByteArray();
            return content;
        } catch (Exception e) {
            log.error("转换工单列表为Excel内容异常: {}", e.getMessage(), e);
            return null;
        } finally {
            // 关闭流
            if (outputStream != null) {
                try {
                    outputStream.close();
                } catch (IOException e) {
                    log.error("关闭Excel输出流异常: {}", e.getMessage());
                }
            }
        }
    }

    /**
     * 根据模板名称查询范围工单信息
     *
     * @param misId 用户MIS ID
     * @param createdAtStart 创建开始时间，格式：yyyy-MM-dd
     * @param createdAtEnd 创建结束时间，格式：yyyy-MM-dd
     * @param state 工单状态，多个状态以逗号分隔
     * @param template 模板名称
     * @return 工单范围查询结果DTO
     */
    @Override
    public DynamicTemplateResultDTO queryRangeTicketsInfoByTemplate(String misId, String createdAtStart, String createdAtEnd, String state, String template) {
        // 1. 获取模板配置
        TemplateConfig templateConfig = null;
        if (StringUtils.isNotBlank(template)) {
            List<TemplateConfig> configs = mtConfigService.getTicketRangeQueryTemplate().getTemplateConfigs();
            Optional<TemplateConfig> config = configs.stream()
                    .filter(c -> template.equals(c.getTemplateName()))
                    .findFirst();
            templateConfig = config.orElse(null);
        }
        
        if (templateConfig == null) {
            log.error("未找到指定的模板配置: {}", template);
            return DynamicTemplateResultDTO.builder()
                    .ticketList(Collections.emptyList())
                    .totalCount(0)
                    .templateName(template)
                    .build();
        }

        Long rgId = Long.parseLong(templateConfig.getRgId().toString());
        
        String templateId = templateConfig.getTemplateId();
        List<String> fields = templateConfig.getFields();

        // 转换日期格式为时间戳
        Long startTimestamp = convertDateToTimestamp(createdAtStart);
        Long endTimestamp = convertDateToTimestamp(createdAtEnd);
        
        // 对结束日期特殊处理，设置为当天的23:59:59
        if (endTimestamp != null) {
            endTimestamp = endTimestamp + 24 * 60 * 60 * 1000 - 1000; // 加上23小时59分59秒
        }

        // 2. 查询工单列表
        List<String> ticketIds = queryTicketIds(rgId, misId, startTimestamp, endTimestamp, state);
        if (CollectionUtils.isEmpty(ticketIds)) {
            return DynamicTemplateResultDTO.builder()
                    .ticketList(Collections.emptyList())
                    .totalCount(0)
                    .templateName(template)
                    .templateId(templateId)
                    .fields(fields)
                    .build();
        }

        // 3. 创建任务记录
        String taskId = createTicketRangeTask(ticketIds);
        
        // 4. 获取工单详细信息并按模板字段处理
        List<DynamicTemplateTicketDTO> dynamicTickets = processTicketDetailsByTemplate(rgId, misId, ticketIds, taskId, templateConfig);
        
        // 5. 生成Excel文件
        String downloadLink = generateDynamicExcel(dynamicTickets, fields, taskId, template);
        
        // 构建并返回结果
        return DynamicTemplateResultDTO.builder()
                .ticketList(dynamicTickets)
                .excelDownloadLink(downloadLink)
                .taskId(taskId)
                .totalCount(dynamicTickets.size())
                .templateName(template)
                .templateId(templateId)
                .fields(fields)
                .build();
    }
    
    /**
     * 根据模板配置处理工单详细信息
     * 
     * @param rgId 组织ID
     * @param misId 用户MIS ID
     * @param ticketIds 工单ID列表
     * @param taskId 任务ID
     * @param templateConfig 模板配置
     * @return 处理后的动态工单列表
     */
    private List<DynamicTemplateTicketDTO> processTicketDetailsByTemplate(Long rgId, String misId, List<String> ticketIds, String taskId, TemplateConfig templateConfig) {
        log.info("开始根据模板处理工单详细信息: taskId={}, rgId={}, templateName={}, templateId={}, ticketCount={}", 
                taskId, rgId, templateConfig.getTemplateName(), templateConfig.getTemplateId(), ticketIds.size());
        
        List<DynamicTemplateTicketDTO> resultList = Collections.synchronizedList(new ArrayList<>());
        
        // 获取线程池
        ExecutorService executor = AsyncTaskUtils.getBatchProcessTtThreadPool();
        
        // 创建异步任务列表
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        
        // 对每个工单ID异步获取详细信息
        for (String ticketId : ticketIds) {
            CompletableFuture<Void> future = AsyncTaskUtils.supplyAsyncWithThreadLocal(executor, () -> {
                try {
                    // 获取工单模板信息
                    DynamicTemplateTicketDTO dynamicTicket = ticketQueryUtil.getTicketTemplateInfo(ticketId, misId, templateConfig);
                    if (dynamicTicket != null) {
                        resultList.add(dynamicTicket);
                    }
                    return null;
                } catch (Exception e) {
                    log.error("处理工单详情异常: ticketId={}, error={}", ticketId, e.getMessage(), e);
                    return null;
                }
            });
            
            futures.add(future);
        }
        
        try {
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                .get(mtConfigService.getBatchProcessTtTimeout(), TimeUnit.MILLISECONDS);
        } catch (InterruptedException e) {
            log.error("等待获取工单信息被中断: taskId={}, {}", taskId, e.getMessage());
            Thread.currentThread().interrupt();
        } catch (ExecutionException e) {
            log.error("获取工单信息执行异常: taskId={}, {}", taskId, e.getMessage(), e);
        } catch (TimeoutException e) {
            log.error("获取工单信息超时, taskId={}, 已处理{}个, 总数{}", 
                    taskId, resultList.size(), ticketIds.size());
        }
        
        return resultList;
    }
    
    /**
     * 生成动态Excel并上传到S3
     * 
     * @param tickets 动态工单列表
     * @param fields 字段列表
     * @param taskId 任务ID
     * @param templateName 模板名称
     * @return 下载链接
     */
    private String generateDynamicExcel(List<DynamicTemplateTicketDTO> tickets, List<String> fields, String taskId, String templateName) {
        if (CollectionUtils.isEmpty(tickets)) {
            return "";
        }
        
        try {
            // 创建Excel
            byte[] excelContent = convertDynamicToExcelContent(tickets, fields);
            if (excelContent != null && excelContent.length > 0) {
                String downloadLink = ExcelExportUtil.uploadContentToS3(excelContent, taskId, ".xlsx");
                if (downloadLink != null) {
                    log.info("Excel文件上传成功，S3预签名下载链接: {}", downloadLink);
                } else {
                    log.warn("Excel文件上传失败，无法生成S3预签名下载链接");
                }
                return downloadLink;
            } else {
                log.warn("生成Excel内容失败");
                return "";
            }
        } catch (Exception e) {
            log.error("生成Excel异常: templateName={}, taskId={}, error={}", templateName, taskId, e.getMessage(), e);
            return "";
        }
    }
    
    /**
     * 将动态工单列表转换为Excel内容
     * 
     * @param tickets 动态工单列表
     * @param fields 字段列表
     * @return Excel字节数组
     */
    private byte[] convertDynamicToExcelContent(List<DynamicTemplateTicketDTO> tickets, List<String> fields) {
        if (CollectionUtils.isEmpty(tickets)) {
            log.warn("工单列表为空，无法转换为Excel内容");
            return null;
        }

        ByteArrayOutputStream outputStream = null;
        try {
            outputStream = new ByteArrayOutputStream();

            List<List<String>> headList = new ArrayList<>();
            headList.add(Collections.singletonList("工单ID"));
            // 添加字段列表
            for (String field : fields) {
                headList.add(Collections.singletonList(field));
            }

            List<List<Object>> dataRows = new ArrayList<>();
            for (DynamicTemplateTicketDTO ticket : tickets) {
                if (ticket == null) {
                    continue;
                }
                
                List<Object> row = new ArrayList<>();
                row.add(ticket.getTicketId() != null ? ticket.getTicketId() : "");
                
                // 添加字段值
                for (String field : fields) {
                    Object value = ticket.getField(field);
                    if (value != null) {
                        if (value instanceof Date) {
                            try {
                                String dateStr = new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(value);
                                row.add(dateStr);
                            } catch (Exception e) {
                                log.warn("日期格式转换异常: {}", e.getMessage());
                                row.add(String.valueOf(value));
                            }
                        } else {
                            row.add(String.valueOf(value));
                        }
                    } else {
                        row.add("");
                    }
                }
                
                dataRows.add(row);
            }

            EasyExcel.write(outputStream)
                    .head(headList)
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .sheet("工单范围信息")
                    .doWrite(dataRows);

            return outputStream.toByteArray();
        } catch (Exception e) {
            log.error("转换工单列表为Excel内容异常: {}", e.getMessage(), e);
            return null;
        } finally {
            // 关闭流
            if (outputStream != null) {
                try {
                    outputStream.close();
                } catch (IOException e) {
                    log.error("关闭Excel输出流异常: {}", e.getMessage());
                }
            }
        }
    }
}
