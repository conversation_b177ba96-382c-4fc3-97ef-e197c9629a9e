package com.meituan.banma.llm.corpus.server.common.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 工单范围处理结果DTO
 * 用于封装工单处理过程中的结果数据
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TicketRangeProcessResultDTO {
    /**
     * 成功获取到的工单范围信息列表
     */
    private List<TicketRangeDTO> resultList;
    
    /**
     * 成功处理（保存到数据库）的工单ID列表
     */
    private List<String> successTicketIds;
    
    /**
     * 处理失败的工单ID列表
     */
    private List<String> failedTicketIds;
} 