package com.meituan.banma.llm.corpus.server.common.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import java.sql.Timestamp;

/**
 * 工单范围信息DTO
 */
@Data
public class TicketRangeDTO {
    /**
     * 自增主键ID
     */
    private Long id;

    /**
     * 组织ID
     */
    private Long rgId;

    /**
     * 工单ID
     */
    @ExcelProperty("工单ID")
    private String ticketId;

    /**
     * 日期
     */
    @ExcelProperty("日期")
    private Timestamp date;

    /**
     * 商家ID
     */
    @ExcelProperty("商家ID")
    private String merchantId;

    /**
     * 时段
     */
    @ExcelProperty("时段")
    private String timeSlot;

    /**
     * 原因
     */
    @ExcelProperty("原因")
    private String reason;

    /**
     * 类型ID
     */
    @ExcelProperty("类型ID")
    private String typeId;

    /**
     * 类型名称
     */
    @ExcelProperty("类型")
    private String typeName;

    /**
     * 城市名称
     */
    @ExcelProperty("城市")
    private String cityName;

    /**
     * 城市ID
     */
    @ExcelProperty("城市ID")
    private String cityId;

    /**
     * 订单ID
     */
    @ExcelProperty("订单ID")
    private String orderId;

    /**
     * 地址
     */
    @ExcelProperty("地址")
    private String address;
} 