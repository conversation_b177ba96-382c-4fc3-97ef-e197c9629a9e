package com.meituan.banma.llm.corpus.server.rpc.friday.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.dianping.rhino.annotation.Degrade;
import com.meituan.ai.friday.sdk.OpenAiService;
import com.meituan.ai.friday.sdk.api.OpenAiHttpException;
import com.meituan.ai.friday.sdk.api.embedding.Embedding;
import com.meituan.ai.friday.sdk.api.embedding.EmbeddingRequest;
import com.meituan.ai.friday.sdk.api.embedding.EmbeddingResult;
import com.meituan.banma.llm.corpus.api.constants.BizCode;
import com.meituan.banma.llm.corpus.api.exception.LlmCorpusException;
import com.meituan.banma.llm.corpus.server.common.MtConfigService;
import com.meituan.banma.llm.corpus.server.common.constants.enums.FridayAsyncConversationStatusEnum;
import com.meituan.banma.llm.corpus.server.common.domain.dto.FridayEmbeddingResDTO;
import com.meituan.banma.llm.corpus.server.rpc.friday.FridayRpcService;
import com.meituan.banma.llm.corpus.server.rpc.friday.dto.AsyncOrchestratorSubmitResponseDTO;
import com.meituan.banma.llm.corpus.server.rpc.friday.dto.FridayConversationParams;
import com.meituan.banma.llm.corpus.server.rpc.friday.dto.FridayConversationResponseDTO;
import com.meituan.banma.llm.corpus.server.rpc.friday.dto.FridayConversationResponseDataDTO;
import com.meituan.banma.llm.corpus.server.utils.AsyncTaskUtils;
import com.meituan.banma.llm.corpus.server.utils.HttpUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.http.NameValuePair;
import org.apache.http.client.fluent.Form;
import org.apache.http.client.fluent.Request;
import org.apache.http.conn.ConnectTimeoutException;
import org.apache.http.entity.ContentType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;

@Slf4j
@Service
public class FridayRpcServiceImpl implements FridayRpcService {
    @Autowired
    private MtConfigService mtConfigService;
    private volatile long updatedAt;
    private String token;

    private static final String CREATE_DATASET_PATH = "openapi/appfactory/dataset/api/v1/create";
    private static final String URL_UPLOAD_SAVE_PATH = "openapi/appfactory/document/api/v1/urlUploadSave";
    private static final String REFRESH_PATH = "openapi/appfactory/document/api/v1/refresh";
    private static final String CHANGE_REFRESH_PATH = "/openapi/appfactory/document/api/v1/autoRefresh";
    private static final String DOCUMENT_DELETE_PATH = "/openapi/appfactory/document/api/v1/delete";
    private static final String DOCUMENT_SOURCE_FORMAT = "doc";
    private static final String DATASET_DELETE_PATH = "/openapi/appfactory/dataset/api/v1/delete";

    @Override
    @Degrade(rhinoKey = "FridayRpcService.conversation", fallBackMethod =
            "conversationFallBack")
    public FridayConversationResponseDataDTO conversation(FridayConversationParams params) throws LlmCorpusException {
        try {
            // 将params对象转换为JSON字符串
            String jsonBody = JSON.toJSONString(params);

            // 发送POST请求
            String res = Request.Post(mtConfigService.getFridayConversationUrl()).connectTimeout(mtConfigService.getFridayConversationTimeout()).socketTimeout(mtConfigService.getFridayConversationTimeout())
                    .addHeader("Content-Type", "application/json")
                    .bodyString(jsonBody, ContentType.APPLICATION_JSON).execute().returnContent().asString(java.nio.charset.StandardCharsets.UTF_8);
            if (StringUtils.isNotBlank(res)) {
                // 使用FastJSON解析嵌套的JSON字符串
                JSONObject jsonObject = JSON.parseObject(res);
                // 递归处理所有可能嵌套的JSON字符串
                processNestedJsonStrings(jsonObject);
                // 将处理后的JSON对象转回字符串
                res = JSON.toJSONString(jsonObject);
            }
            log.info("#FridayRpcServiceImpl.conversation#info,res={}", res);
            // 解析响应
            FridayConversationResponseDTO<FridayConversationResponseDataDTO> responseDTO = JSON.parseObject(res,
                    new TypeReference<FridayConversationResponseDTO<FridayConversationResponseDataDTO>>(){});
            if (responseDTO.getCode() != 0 || responseDTO.getData() == null) {
                log.error("#FridayRpcServiceImpl.conversation#error,params:{},response:{}", params, responseDTO);
                throw LlmCorpusException.build(BizCode.RPC_BIZ_ERROR);
            }
            return responseDTO.getData();
        } catch (ConnectTimeoutException e) {
            log.warn("#FridayRpcServiceImpl.conversation#warn,timeout,params:{}", params, e);
            throw LlmCorpusException.build(BizCode.RPC_TIMEOUT);
        } catch (LlmCorpusException e){
            throw e;
        } catch (Exception e) {
            log.error("#FridayRpcServiceImpl.conversation#error,params:{}", params, e);
            throw LlmCorpusException.build(BizCode.SERVER_INTERNAL_ERROR);
        }
    }
    private void processNestedJsonStrings(JSONObject jsonObject) {
        if (jsonObject == null) {
            return;
        }

        Set<String> keys = new HashSet<>(jsonObject.keySet());
        for (String key : keys) {
            Object value = jsonObject.get(key);
            if (value instanceof String) {
                String strValue = (String) value;
                // 尝试将字符串解析为JSON对象
                try {
                    if (strValue.startsWith("{") && strValue.endsWith("}")) {
                        JSONObject nestedObj = JSON.parseObject(strValue);
                        jsonObject.put(key, nestedObj);
                        processNestedJsonStrings(nestedObj);
                    } else if (strValue.startsWith("[") && strValue.endsWith("]")) {
                        JSONArray nestedArray = JSON.parseArray(strValue);
                        jsonObject.put(key, nestedArray);
                        processNestedJsonArray(nestedArray);
                    }
                } catch (Exception e) {
                    // 解析失败，说明不是有效的JSON，保持原样
                }
            } else if (value instanceof JSONObject) {
                // 递归处理嵌套的JSON对象
                processNestedJsonStrings((JSONObject) value);
            } else if (value instanceof JSONArray) {
                // 递归处理JSON数组
                processNestedJsonArray((JSONArray) value);
            }
        }
    }
    private void processNestedJsonArray(JSONArray jsonArray) {
        if (jsonArray == null) {
            return;
        }

        for (int i = 0; i < jsonArray.size(); i++) {
            Object item = jsonArray.get(i);
            if (item instanceof String) {
                String strValue = (String) item;
                try {
                    if (strValue.startsWith("{") && strValue.endsWith("}")) {
                        JSONObject nestedObj = JSON.parseObject(strValue);
                        jsonArray.set(i, nestedObj);
                        processNestedJsonStrings(nestedObj);
                    } else if (strValue.startsWith("[") && strValue.endsWith("]")) {
                        JSONArray nestedArray = JSON.parseArray(strValue);
                        jsonArray.set(i, nestedArray);
                        processNestedJsonArray(nestedArray);
                    }
                } catch (Exception e) {
                    // 解析失败，保持原样
                }
            } else if (item instanceof JSONObject) {
                processNestedJsonStrings((JSONObject) item);
            } else if (item instanceof JSONArray) {
                processNestedJsonArray((JSONArray) item);
            }
        }
    }
    public FridayConversationResponseDataDTO conversationFallBack(FridayConversationParams params, Throwable t) {
        log.warn("#FridayRpcServiceImpl.conversationFallBack#fallback,params:{}", params, t);
        return null;
    }

    @Override
    @Degrade(rhinoKey = "FridayRpcService.conversationWithoutPostProcess", fallBackMethod =
            "conversationWithoutPostProcessFallBack")
    public FridayConversationResponseDataDTO conversationWithoutPostProcess(FridayConversationParams params) throws LlmCorpusException {
        try {
            // 将params对象转换为JSON字符串
            String jsonBody = JSON.toJSONString(params);

            // 发送POST请求
            String res = Request.Post(mtConfigService.getFridayConversationUrl()).connectTimeout(mtConfigService.getFridayConversationTimeout()).socketTimeout(mtConfigService.getFridayConversationTimeout())
                    .addHeader("Content-Type", "application/json")
                    .bodyString(jsonBody, ContentType.APPLICATION_JSON).execute().returnContent().asString(java.nio.charset.StandardCharsets.UTF_8);
            log.info("#FridayRpcServiceImpl.conversation#info,res={}", res);
            // 解析响应
            FridayConversationResponseDTO<FridayConversationResponseDataDTO> responseDTO = JSON.parseObject(res,
                    new TypeReference<FridayConversationResponseDTO<FridayConversationResponseDataDTO>>(){});
            if (responseDTO.getCode() != 0 || responseDTO.getData() == null) {
                log.error("#FridayRpcServiceImpl.conversation#error,params:{},response:{}", params, responseDTO);
                throw LlmCorpusException.build(BizCode.RPC_BIZ_ERROR);
            }
            return responseDTO.getData();
        } catch (ConnectTimeoutException e) {
            log.warn("#FridayRpcServiceImpl.conversation#warn,timeout,params:{}", params, e);
            throw LlmCorpusException.build(BizCode.RPC_TIMEOUT);
        } catch (LlmCorpusException e){
            throw e;
        } catch (Exception e) {
            log.error("#FridayRpcServiceImpl.conversation#error,params:{}", params, e);
            throw LlmCorpusException.build(BizCode.SERVER_INTERNAL_ERROR);
        }
    }

    public FridayConversationResponseDataDTO conversationWithoutPostProcessFallBack(FridayConversationParams params , Throwable t){
        log.warn("#FridayRpcServiceImpl.conversationWithoutPostProcessFallBack#fallback,params:{}", params, t);
        return null;
    }

    @Override
    @Degrade(rhinoKey = "FridayRpcService.conversationWithAsync", fallBackMethod =
            "conversationWithAsyncFallBack")
    public FridayConversationResponseDataDTO conversationWithAsync(FridayConversationParams params) throws LlmCorpusException {
        FridayConversationResponseDTO<AsyncOrchestratorSubmitResponseDTO> responseDTO = submitAsyncConversation(params);
        if (responseDTO.getCode() != 0 || responseDTO.getData() == null) {
            log.error("#FridayRpcServiceImpl.conversationWithAsync#error,params:{},response:{}", params, responseDTO);
            throw LlmCorpusException.buildWithMsg(BizCode.RPC_BIZ_ERROR, responseDTO.getMessage()==null?"调用接口失败":responseDTO.getMessage());
        }

        String rid = responseDTO.getData().getRid();
        if (StringUtils.isBlank(rid)) {
            log.error("#FridayRpcServiceImpl.conversationWithAsync#error,params:{},response:{}", params, responseDTO);
            throw LlmCorpusException.buildWithMsg(BizCode.RPC_BIZ_ERROR, "调用接口失败，接口返回结果缺失。");
        }

        ExecutorService pool = AsyncTaskUtils.getFridayConversationThreadPool();
        CompletableFuture<FridayConversationResponseDataDTO> future = CompletableFuture.supplyAsync(()  -> {
            long startTime = System.currentTimeMillis();
            long timeout = mtConfigService.getFridayConversationTimeout();
            int pollInterval = 3000; // 轮询间隔3秒

            while (System.currentTimeMillis() - startTime <= timeout) {
                try {
                    FridayConversationResponseDTO<FridayConversationResponseDataDTO> result =
                            getAsyncConversationResult(rid);
                    if (result != null && result.getCode() == 0 && result.getData() != null && Objects.equals(result.getData().getStatus(), FridayAsyncConversationStatusEnum.SUCCESS
                            .getCode())) {
                        return result.getData();
                    }
                    if (result != null && result.getCode() != 0){
                        log.error("#FridayRpcServiceImpl.conversationWithAsync#error,params:{},response:{}", params, result);
                        throw LlmCorpusException.buildWithMsg(BizCode.RPC_BIZ_ERROR, result.getMessage());
                    }
                    TimeUnit.MILLISECONDS.sleep(pollInterval);
                } catch (LlmCorpusException e){
                    throw new CompletionException(e);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    throw new CompletionException(
                            LlmCorpusException.buildWithMsg(BizCode.SERVER_INTERNAL_ERROR, "轮询被中断")
                    );
                }
            }
            throw new CompletionException(
                    LlmCorpusException.buildWithMsg(BizCode.RPC_TIMEOUT, "异步调用超时")
            );
        }, pool);

        try {
            return future.get(mtConfigService.getFridayConversationTimeout(), TimeUnit.MILLISECONDS);
        } catch (TimeoutException e) {
            log.error("#FridayRpcServiceImpl.conversationWithAsync#timeout,params:{},rid:{}", params, rid);
            throw LlmCorpusException.buildWithMsg(BizCode.RPC_TIMEOUT, "异步调用超时");
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("#FridayRpcServiceImpl.conversationWithAsync#interrupted,params:{},rid:{}", params, rid);
            throw LlmCorpusException.buildWithMsg(BizCode.SERVER_INTERNAL_ERROR, "轮询被中断");
        } catch (ExecutionException e) {
            if (e.getCause() instanceof LlmCorpusException) {
                throw (LlmCorpusException) e.getCause();
            }
            log.error("#FridayRpcServiceImpl.conversationWithAsync#error,params:{},rid:{}", params, rid, e);
            throw LlmCorpusException.buildWithMsg(BizCode.SERVER_INTERNAL_ERROR, "执行异常: " + e.getMessage());
        }catch (Exception e){
            log.error("#FridayRpcServiceImpl.conversationWithAsync#error,params:{},rid:{}", params, rid, e);
            throw LlmCorpusException.buildWithMsg(BizCode.SERVER_INTERNAL_ERROR);
        }
    }

    public FridayConversationResponseDataDTO conversationWithAsyncFallBack(FridayConversationParams params, Throwable t) {
        log.warn("FridayRpcServiceImpl.conversationWithAsyncFallBack#fallback,params:{}", params, t);
        return null;
    }
    
    private FridayConversationResponseDTO<AsyncOrchestratorSubmitResponseDTO> submitAsyncConversation(FridayConversationParams params) throws LlmCorpusException {
        try{
            // 将params对象转换为JSON字符串
            String jsonBody = JSON.toJSONString(params);
            // 发送POST请求
            String res = Request.Post(mtConfigService.getFridayAsyncConversationUrl()).connectTimeout(mtConfigService.getFridayConversationTimeout()).socketTimeout(mtConfigService.getFridayConversationTimeout())
                    .addHeader("Content-Type", "application/json")
                    .bodyString(jsonBody, ContentType.APPLICATION_JSON).execute().returnContent().asString(java.nio.charset.StandardCharsets.UTF_8);
            // 解析响应
            FridayConversationResponseDTO<AsyncOrchestratorSubmitResponseDTO> responseDTO = JSON.parseObject(res,
                    new TypeReference<FridayConversationResponseDTO<AsyncOrchestratorSubmitResponseDTO>>(){});
            return responseDTO;
        } catch (ConnectTimeoutException e) {
            log.warn("#FridayRpcServiceImpl.submitAsyncConversation#warn,timeout,params:{}", params, e);
            throw LlmCorpusException.build(BizCode.RPC_TIMEOUT);
        } catch (Exception e) {
            log.error("#FridayRpcServiceImpl.submitAsyncConversation#error,params:{}", params, e);
            throw LlmCorpusException.build(BizCode.SERVER_INTERNAL_ERROR);
        }

    }

    private FridayConversationResponseDTO<FridayConversationResponseDataDTO> getAsyncConversationResult(String rid) throws LlmCorpusException {
        try {
            String res = Request.Get(mtConfigService.getFridayAsyncGetConversationResultUrl() + "?rid=" + rid).connectTimeout(mtConfigService.getFridayConversationTimeout()).socketTimeout(mtConfigService.getFridayConversationTimeout())
                    .addHeader("Content-Type", "application/json")
                    .execute().returnContent().asString(java.nio.charset.StandardCharsets.UTF_8);
            return JSON.parseObject(res, new TypeReference<FridayConversationResponseDTO<FridayConversationResponseDataDTO>>(){});
        } catch (ConnectTimeoutException e) {
            log.warn("#FridayRpcServiceImpl.submitAsyncConversation#warn,timeout,rid:{}", rid, e);
            throw LlmCorpusException.build(BizCode.RPC_TIMEOUT);
        } catch (Exception e) {
            log.error("#FridayRpcServiceImpl.submitAsyncConversation#error,rid:{}", rid, e);
            throw LlmCorpusException.build(BizCode.SERVER_INTERNAL_ERROR);
        }
    }
    @Override
    public String getFridayAccessToken() {
        // TODO: 后面需要改成缓存形式
        if (!isValid()) {
            synchronized (this) {
                if (!isValid()) {
                    flushToken();
                }
            }
        }
        return token;
    }

    /**
     * 获取新token
     */
    private void flushToken() {
        List<NameValuePair> pairs = Form.form().add("grant_type", "client_credentials")
                .add("client_id", mtConfigService.getFridayAccessTokenConfig().getAccessKey())
                .add("client_secret", mtConfigService.getFridayAccessTokenConfig().getAppSecret()).build();
        String res = null;
        try {
            res = Request.Post(mtConfigService.getFridayAccessTokenConfig().getAuthApi()).bodyForm(pairs).execute()
                    .returnContent().asString();
            JSONObject obj = JSON.parseObject(res);
            if (obj.getInteger("errcode") == 0) {
                token = obj.getJSONObject("data").getString("access_token");
                updatedAt = System.currentTimeMillis() / 1000;
            }
        } catch (Exception e) {
            log.error("FridayRpcServiceImpl.flushToken error, res:{}", res, e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 判断token是否有效
     *
     * @return
     */
    protected boolean isValid() {
        if (token == null) {
            return false;
        }
        long now = System.currentTimeMillis() / 1000;
        return now - this.updatedAt < mtConfigService.getFridayAccessTokenConfig().getExpireTime();
    }

    @Override
    public String createDataset(String name, String accessToken, String modifier, String description, String spaceId) {
        JSONObject requestPayload = new JSONObject();
        requestPayload.put("spaceId", spaceId);
        requestPayload.put("name", name);
        requestPayload.put("accessToken", accessToken);
        requestPayload.put("modifier", modifier);
        if (description != null) {
            requestPayload.put("description", description);
        }

        String url = mtConfigService.getFridayApiUrl() + CREATE_DATASET_PATH;
        log.info("Creating dataset with URL: {} and payload: {}", url, requestPayload);

        try {
            String response = HttpUtil.doPost(url, null, requestPayload);
            log.info("Received response for createDataset: {}", response);
            return parseResponse(response, "datasetId");
        } catch (Exception e) {
            log.error("Error creating dataset", e);
            throw new RuntimeException("Failed to create dataset", e);
        }
    }

    @Override
    public String uploadDocument(String datasetId, String url, String name, String accessToken, String modifier, boolean autoRefresh, String spaceId) {
        JSONObject requestPayload = new JSONObject();
        requestPayload.put("datasetId", datasetId);
        requestPayload.put("url", url);
        requestPayload.put("spaceId", spaceId);
        requestPayload.put("name", name);
        requestPayload.put("accessToken", accessToken);
        requestPayload.put("modifier", modifier);
        requestPayload.put("rules.chunkSize", mtConfigService.getFridayDocumentChunkSize());
        requestPayload.put("autoRefresh", autoRefresh);

        String fullUrl = mtConfigService.getFridayApiUrl() + URL_UPLOAD_SAVE_PATH;
        log.info("Uploading document with URL: {} and payload: {}", fullUrl, requestPayload);

        try {
            String response = HttpUtil.doPost(fullUrl, null, requestPayload);
            log.info("Received response for uploadDocument: {}", response);
            return parseResponse(response, "documentId");
        } catch (Exception e) {
            log.error("Error uploading document", e);
            throw new RuntimeException("Failed to upload document", e);
        }
    }

    @Override
    public void deleteDocument(String datasetId, String documentId, String accessToken, String modifier, String spaceId) {
        JSONObject requestPayload = new JSONObject();
        requestPayload.put("documentId", documentId);
        requestPayload.put("sourceFormat", DOCUMENT_SOURCE_FORMAT);
        requestPayload.put("datasetId", datasetId);
        requestPayload.put("spaceId", spaceId);
        requestPayload.put("accessToken", accessToken);
        requestPayload.put("modifier", modifier);

        String fullUrl = mtConfigService.getFridayApiUrl() + DOCUMENT_DELETE_PATH;
        log.info("Deleting document with URL: {} and payload: {}", fullUrl, requestPayload);

        try {
            String response = HttpUtil.doPost(fullUrl, null, requestPayload);
            log.info("Received response for deleteDocument: {}", response);
            parseResponse(response, null);
        } catch (Exception e) {
            log.error("Error deleting document", e);
            throw new RuntimeException("Failed to delete document", e);
        }
    }

    @Override
    public void refreshDocument(String datasetId, String documentId, String accessToken, String modifier, String spaceId) {
        JSONObject requestPayload = new JSONObject();
        requestPayload.put("datasetId", datasetId);
        requestPayload.put("documentId", documentId);
        requestPayload.put("spaceId", spaceId);
        requestPayload.put("accessToken", accessToken);
        requestPayload.put("modifier", modifier);

        String url = mtConfigService.getFridayApiUrl() + REFRESH_PATH;
        log.info("Refreshing document with URL: {} and payload: {}", url, requestPayload);

        try {
            String response = HttpUtil.doPost(url, null, requestPayload);
            log.info("Received response for refreshDocument: {}", response);
            parseResponse(response, null);
        } catch (Exception e) {
            log.error("Error refreshing document", e);
            throw new RuntimeException("Failed to refresh document", e);
        }
    }

    @Override
    public void changeRefreshDocument(String datasetId, String documentId, String accessToken, String modifier, boolean autoRefresh, String spaceId) {
        JSONObject requestPayload = new JSONObject();
        requestPayload.put("datasetId", datasetId);
        requestPayload.put("documentId", documentId);
        requestPayload.put("spaceId", spaceId);
        requestPayload.put("accessToken", accessToken);
        requestPayload.put("modifier", modifier);
        requestPayload.put("autoRefresh", autoRefresh);

        String url = mtConfigService.getFridayApiUrl() + CHANGE_REFRESH_PATH;
        log.info("Change refresh document with URL: {} and payload: {}", url, requestPayload);

        try {
            String response = HttpUtil.doPost(url, null, requestPayload);
            log.info("Received response for changeRefreshDocument: {}", response);
            parseResponse(response, null);
        } catch (Exception e) {
            log.error("Error change refresh document", e);
            throw new RuntimeException("Failed to change refresh document", e);
        }
    }

    @Override
    public void deleteDataset(String datasetId, String accessToken, String modifier, String spaceId) {
        JSONObject requestPayload = new JSONObject();
        requestPayload.put("datasetId", datasetId);
        requestPayload.put("spaceId", spaceId);
        requestPayload.put("accessToken", accessToken);
        requestPayload.put("modifier", modifier);

        String fullUrl = mtConfigService.getFridayApiUrl() + DATASET_DELETE_PATH;
        log.info("Deleting dataset with URL: {} and payload: {}", fullUrl, requestPayload);

        try {
            String response = HttpUtil.doPost(fullUrl, null, requestPayload);
            log.info("Received response for deleteDataset: {}", response);
            parseResponse(response, null);
        } catch (Exception e) {
            log.error("Error deleting dataset", e);
            throw new RuntimeException("Failed to delete dataset", e);
        }
    }

    private String parseResponse(String responseBody, String key) {
        JSONObject responseObject = JSONObject.parseObject(responseBody);
        log.debug("Parsing response: {}", responseBody);

        if (responseObject.getIntValue("code") == 200) {
            String result = key != null ? responseObject.getJSONObject("data").getString(key) : null;
            log.debug("Parsed result: {}", result);
            return result;
        } else {
            String errorMessage = responseObject.getString("message");
            log.error("Request failed with message: {}", errorMessage);
            throw new RuntimeException("Request failed: " + errorMessage);
        }
    }

    @Override
    @Degrade(rhinoKey = "FridayRpcService.modelFactoryEmbedding", fallBackMethod =
            "modelFactoryEmbeddingFallBack")
    public List<FridayEmbeddingResDTO> modelFactoryEmbedding(String misId, List<String> input, String modelName) throws LlmCorpusException {
        String modelFactoryAppId = mtConfigService.getFridayModelFactoryAppId();
        OpenAiService service = new OpenAiService(modelFactoryAppId, true);
        EmbeddingRequest embeddingRequest = EmbeddingRequest.builder().model(modelName).input(input).user(misId)
                .build();
        try{
            EmbeddingResult result = service.createEmbeddings(embeddingRequest);
            if (result == null || result.getData() == null){
                log.error("#FridayRpcServiceImpl.modelFactoryEmbedding#error,params:{},misId:{},modelName:{}", input, misId, modelName);
                return null;
            }
            List<Embedding> data = result.getData();
            if (data == null || data.isEmpty()){
                log.error("#FridayRpcServiceImpl.modelFactoryEmbedding#error,params:{},misId:{},modelName:{}", input, misId, modelName);
                return null;
            }
            return data.stream().map(embedding -> {
                FridayEmbeddingResDTO resDTO = new FridayEmbeddingResDTO();
                resDTO.setEmbedding(embedding.getEmbedding());
                resDTO.setIndex(embedding.getIndex());
                return resDTO;
            }).collect(Collectors.toList());
        }catch (OpenAiHttpException e){
            log.error("#FridayRpcServiceImpl.modelFactoryEmbedding#error,params:{},misId:{},modelName:{}", input, misId, modelName, e);
            throw LlmCorpusException.buildWithMsg(BizCode.SERVER_INTERNAL_ERROR, e.getMessage());
        } catch (Exception e){
            log.error("#FridayRpcServiceImpl.modelFactoryEmbedding#error,params:{},misId:{},modelName:{}", input, misId, modelName, e);
            throw LlmCorpusException.buildWithMsg(BizCode.SERVER_INTERNAL_ERROR);
        }
    }

    public List<Double> modelFactoryEmbeddingFallBack(String misId, List<String> input, String modelName, Throwable t) {
        log.warn("FridayRpcServiceImpl.modelFactoryEmbeddingFallBack#fallback,misId:{},input:{},modelName:{}", misId, input, modelName, t);
        return null;
    }

}

