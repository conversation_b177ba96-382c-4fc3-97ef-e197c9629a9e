package com.meituan.banma.llm.corpus.server.service.impl;

import com.meituan.banma.llm.corpus.api.constants.BizCode;
import com.meituan.banma.llm.corpus.api.exception.LlmCorpusException;
import com.meituan.banma.llm.corpus.server.common.MtConfigService;
import com.meituan.banma.llm.corpus.server.common.constants.enums.CorpusSourceEnum;
import com.meituan.banma.llm.corpus.server.common.constants.enums.CorpusStatusEnum;
import com.meituan.banma.llm.corpus.server.common.domain.dto.LatestContentDTO;
import com.meituan.banma.llm.corpus.server.common.domain.dto.RgListItemDTO;
import com.meituan.banma.llm.corpus.server.common.domain.dto.RelatedIdsDTO;
import com.meituan.banma.llm.corpus.server.common.domain.dto.SopDefaultConfig;
import com.meituan.banma.llm.corpus.server.dal.entity.AccessKeyEntity;
import com.meituan.banma.llm.corpus.server.dal.mapper.AccessKeyMapper;
import com.meituan.banma.llm.corpus.server.dal.mapper.ModelOutputMapper;
import com.meituan.banma.llm.corpus.server.dal.mapper.ReviewMapper;
import com.meituan.banma.llm.corpus.server.dal.mapper.RgDatasetDocumentMapper;
import com.meituan.banma.llm.corpus.server.dal.mapper.RgBackgroundKnowledgeMapper;
import com.meituan.banma.llm.corpus.server.dal.mapper.RgSopMapper;
import com.meituan.banma.llm.corpus.server.dal.mapper.RgRuleMapper;
import com.meituan.banma.llm.corpus.server.dal.entity.KnowledgeBaseVersionEntity;
import com.meituan.banma.llm.corpus.server.dal.entity.RgDatasetDocumentEntity;
import com.meituan.banma.llm.corpus.server.dal.entity.RgBackgroundKnowledgeEntity;
import com.meituan.banma.llm.corpus.server.dal.entity.RgSopEntity;
import com.meituan.banma.llm.corpus.server.dal.entity.RgRuleEntity;
import com.meituan.banma.llm.corpus.server.rpc.friday.FridayRpcService;
import com.meituan.banma.llm.corpus.server.service.IKnowledgeBaseService;
import com.meituan.banma.llm.corpus.server.service.IAccessKeyService;
import com.meituan.banma.llm.corpus.server.service.IReviewService;
import com.meituan.banma.llm.corpus.server.service.ITicketQueryService;
import com.meituan.banma.llm.corpus.server.utils.AsyncTaskUtils;
import com.meituan.banma.llm.corpus.server.utils.HtmlContentUtil;
import com.meituan.banma.llm.corpus.server.utils.TagsUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.context.annotation.Lazy;

import java.sql.Timestamp;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class ReviewServiceImpl implements IReviewService {
    @Autowired
    private ReviewMapper reviewMapper;
    @Autowired
    private ModelOutputMapper modelOutputMapper;
    @Autowired
    private RgDatasetDocumentMapper rgDatasetDocumentMapper;
    @Autowired
    private MtConfigService mtConfigService;
    @Autowired
    private FridayRpcService fridayRpcService;
    @Autowired
    private IKnowledgeBaseService knowledgeBaseService;
    @Autowired
    private ITicketQueryService ticketQueryService;
    @Lazy
    @Autowired
    private WorkspaceServiceImpl workspaceService;
    @Autowired
    private IAccessKeyService accessKeyService;
    @Autowired
    private AccessKeyMapper accessKeyMapper;
    @Autowired
    private RgBackgroundKnowledgeMapper rgBackgroundKnowledgeMapper;
    @Autowired
    private RgSopMapper rgSopMapper;
    @Autowired
    private RgRuleMapper rgRuleMapper;
    @Autowired
    private TagsUtil tagsUtil;
    @Autowired
    private HtmlContentUtil htmlContentUtil;

    private static final String SUF_NAME = "值班组知识库";

    /**
     * 获取某个rgId对应的三个关联表的最大版本ID
     */
    @Override
    public RelatedIdsDTO getRelatedIds(long rgId) {
        // 获取背景知识最大版本ID
        RgBackgroundKnowledgeEntity bgEntity = rgBackgroundKnowledgeMapper.queryLatestByRgId(rgId);
        Long backgroundKnowledgeId = bgEntity != null ? bgEntity.getId() : null;
        
        // 获取SOP最大版本ID
        RgSopEntity sopEntity = rgSopMapper.queryLatestByRgId(rgId);
        Long corpusSopId = null;
        if (sopEntity != null) {
            corpusSopId = sopEntity.getId();
        } else {
            // 如果查不到目标rgId的SOP，查询配置中心设置的默认rgId的SOP
            SopDefaultConfig sopDefaultConfig = mtConfigService.getSopDefaultConfig();
            List<RgSopEntity> defaultRgSopEntityList = rgSopMapper.queryRgSopByRgId(sopDefaultConfig.getDefaultRgId(), 1, 0);
            if (!defaultRgSopEntityList.isEmpty()) {
                corpusSopId = defaultRgSopEntityList.get(0).getId();
                log.info("#ReviewServiceImpl.getRelatedIds 使用默认rgId的SOP: rgId={}, defaultRgId={}, sopId={}", rgId, sopDefaultConfig.getDefaultRgId(), corpusSopId);
            } else {
                log.info("#ReviewServiceImpl.getRelatedIds 未找到默认rgId的SOP，设置为空: rgId={}, defaultRgId={}", rgId, sopDefaultConfig.getDefaultRgId());
            }
        }
        
        // 获取规则最大版本ID
        RgRuleEntity ruleEntity = rgRuleMapper.queryLatestByRgId(rgId);
        Long ruleId = ruleEntity != null ? ruleEntity.getId() : null;
        
        return new RelatedIdsDTO(backgroundKnowledgeId, corpusSopId, ruleId);
    }

    @Override
    public Object queryModelOutputByTicketId(String ticketId) {
        // queryConvertTtToKnowledgeTaskByTaskId
        // TTtoCorpusTaskInfo
        // title + content
        // ## 【TTid】{title}
        // {content}
        return modelOutputMapper.queryModelOutputByTicketId(ticketId);
    }

    @Override
    @Transactional
    public void saveModifiedOutput(String ticketId, String title, String modifiedContent, String misId, Long rgId, String taskId, String tagsIds) {
        boolean success = false;
        int retryCount = 0;
        final int maxRetries = 3;

        while (!success && retryCount < maxRetries) {
            try {
                // 尝试保存修改后的输出
                success = saveModifiedOutputEntry(ticketId, title, modifiedContent, misId, rgId, taskId, tagsIds);
            } catch (Exception e) {
                log.error("#ReviewController.saveModifiedOutput#Exception during insert operation: ticketId={}, retryCount={}", ticketId, retryCount, e);
            } finally {
                // 无论成功或失败，重试计数都增加
                retryCount++;
            }
        }

        if (!success) {
            throw new RuntimeException("#ReviewController.saveModifiedOutput#Failed to insert new version record after multiple retries.");
        }
        log.info("#ReviewController.saveModifiedOutput#insert operation success: ticketId={}, retryCount={}", ticketId, retryCount);
        // 处理RgDatasetDocument
        handleRgDatasetDocument(rgId, misId);
    }

    private boolean saveModifiedOutputEntry(String ticketId, String title, String modifiedContent, String misId, Long rgId, String taskId, String tagsIds) {
        // 查询当前的最新内容和版本号
        KnowledgeBaseVersionEntity latestOutput = reviewMapper.findLatestContentByTicketIdWithRgIdIncludeDeleted(ticketId, rgId);

        int currentVersion = (latestOutput != null) ? latestOutput.getVersion() : -1;
        int newVersion = currentVersion + 1;

        RelatedIdsDTO relatedIds = getRelatedIds(rgId);
        
        // 处理标签ID，如果为空则使用默认标签
        String processedTagsIds = tagsUtil.processTagsIdsWithDefault(tagsIds);
        
        KnowledgeBaseVersionEntity newOutput = KnowledgeBaseVersionEntity.builder()
                .ticketId(ticketId)
                .title(title)
                .content(modifiedContent)
                .version(newVersion)
                .misId(misId)
                .type(-1)
                .rgId(rgId)
                .corpusStatus(CorpusStatusEnum.VALID.getCode())
                .source(CorpusSourceEnum.TT.getCode())
                .timestamp(new Timestamp(System.currentTimeMillis()))
                .backgroundKnowledgeId(relatedIds.getBackgroundKnowledgeId())
                .corpusSopId(relatedIds.getCorpusSopId())
                .ruleId(relatedIds.getRuleId())
                .tagsIds(processedTagsIds)
                .build();

        int result = reviewMapper.insertModifiedOutputWithVersionCheck(newOutput);
        if (result != 0) {
            return true;
        } else {
            log.warn("#ReviewServiceImpl.saveModifiedOutputEntry# 插入新版本记录失败,重试: ticketId={}, currentVersion={}", ticketId, currentVersion);
            return false;
        }
    }

    @Override
    public void refreshKbDocument(Long rgId, String misId) {
        handleRgDatasetDocument(rgId, misId);
    }

    private void handleRgDatasetDocument(Long rgId, String misId) {
        try {
            String default_misId = mtConfigService.getFridayAccessTokenConfig().getModifier();

            // 获取该值班组下的所有工作空间配置
            List<RgDatasetDocumentEntity> rgDatasetDocuments = rgDatasetDocumentMapper.findByRgId(rgId);
            
            // 获取Friday默认空间ID
            String fridaySpaceId = mtConfigService.getFridaySpaceId();
            
            // 检查是否存在Friday默认空间配置
            boolean hasFridaySpace = false;
            if (rgDatasetDocuments != null && !rgDatasetDocuments.isEmpty()) {
                for (RgDatasetDocumentEntity doc : rgDatasetDocuments) {
                    if (fridaySpaceId.equals(doc.getSpaceId())) {
                        hasFridaySpace = true;
                        break;
                    }
                }
            }
            
            if (rgDatasetDocuments == null || rgDatasetDocuments.isEmpty() || !hasFridaySpace) {
                log.info("handleRgDatasetDocument# 未找到rgId={}的Friday默认工作空间配置，创建默认配置", rgId);
                Long rgIdLong = Long.valueOf(rgId);
                // 调用WorkspaceService创建默认工作空间
                boolean success = workspaceService.createDefaultWorkspace(rgIdLong, misId);
                if (!success) {
                    log.warn("MisIdInterceptor# rgId={}创建默认工作空间配置失败", rgId);
                    throw new LlmCorpusException(BizCode.WORKSPACE_NOT_FOUND.getCode(), "找不到对应的工作空间配置，请先添加工作空间");
                }
                // 重新获取工作空间配置列表
                rgDatasetDocuments = rgDatasetDocumentMapper.findByRgId(rgId);
            }
            
            log.info("#ReviewServiceImpl.handleRgDatasetDocument 获取到工作空间配置数量: rgId={}, count={}", rgId, rgDatasetDocuments != null ? rgDatasetDocuments.size() : 0);
            
            // 对每个工作空间配置进行处理
            for (RgDatasetDocumentEntity rgDatasetDocument : rgDatasetDocuments) {
                String spaceId = rgDatasetDocument.getSpaceId();
                
                // 判断是否是默认工作空间，如果是则使用default_misId
                String currentMisId = misId;
                if (spaceId != null && spaceId.equals(mtConfigService.getFridaySpaceId())) {
                    currentMisId = default_misId;
                    log.info("#ReviewServiceImpl.handleRgDatasetDocument 使用默认工作空间，切换为default_misId: rgId={}, spaceId={}", rgId, spaceId);
                }

                try {
                    // 获取accessToken
                    String accessToken = null;
                    if (rgDatasetDocument.getAccessKey() != null && rgDatasetDocument.getAppSecret() != null) {
                        try {
                            accessToken = workspaceService.getWorkspaceAccessToken(
                                    rgDatasetDocument.getAccessKey(), rgDatasetDocument.getAppSecret());
                            log.info("#ReviewServiceImpl.handleRgDatasetDocument 成功获取工作空间令牌: rgId={}, spaceId={}", rgId, spaceId);
                        } catch (Exception e) {
                            log.error("#ReviewServiceImpl.handleRgDatasetDocument 获取工作空间令牌失败: rgId={}, spaceId={}", rgId, spaceId, e);
                            continue; // 跳过当前工作空间，处理下一个
                        }
                    } else {
                        // 没有找到记录或记录中缺少密钥信息
                        log.error("#ReviewServiceImpl.handleRgDatasetDocument 工作空间配置缺少密钥信息: rgId={}, spaceId={}", rgId, spaceId);
                        continue; // 跳过当前工作空间，处理下一个
                    }

                    try {
                        fridayRpcService.refreshDocument(rgDatasetDocument.getDatasetId(), rgDatasetDocument.getDocumentId(), 
                                accessToken, currentMisId, spaceId);
                        log.info("#ReviewServiceImpl.handleRgDatasetDocument 刷新现有文档成功: rgId={}, spaceId={}, datasetId={}, documentId={}", 
                                rgId, spaceId, rgDatasetDocument.getDatasetId(), rgDatasetDocument.getDocumentId());
                    } catch (Exception e) {
                        log.error("#ReviewServiceImpl.handleRgDatasetDocument 刷新文档失败，尝试重新创建知识库与语料文档并刷新: rgId={}, spaceId={}, datasetId={}, documentId={}",
                                rgId, spaceId, rgDatasetDocument.getDatasetId(), rgDatasetDocument.getDocumentId(), e);
                        try {
                            RgDatasetDocumentEntity newRgDatasetDocument = createDatasetAndDocumentInternal(rgId, spaceId, rgDatasetDocument.getSpaceName(), currentMisId);
                            if (newRgDatasetDocument != null) {
                                fridayRpcService.refreshDocument(newRgDatasetDocument.getDatasetId(), newRgDatasetDocument.getDocumentId(), accessToken, currentMisId, spaceId);
                                log.info("#ReviewServiceImpl.handleRgDatasetDocument 重新创建并刷新成功: rgId={}, spaceId={}", rgId, spaceId);
                            } else {
                                log.error("#ReviewServiceImpl.handleRgDatasetDocument 重新创建知识库与语料文档失败: rgId={}, spaceId={}", rgId, spaceId);
                            }
                        } catch (Exception ex) {
                            log.error("#ReviewServiceImpl.handleRgDatasetDocument 重新创建知识库与语料文档并刷新异常: rgId={}, spaceId={}, error={}", 
                                    rgId, spaceId, ex.getMessage(), ex);
                        }
                    }
                    //兜底一次，因为保存和调用第一次都会出现只更新时间戳，不更新内容的情况
                    ExecutorService refreshDocumentThreadPool = AsyncTaskUtils.getRefreshDocumentThreadPool();
                    RgDatasetDocumentEntity finalRgDatasetDocument = rgDatasetDocument;
                    String finalAccessToken = accessToken;
                    String finalCurrentMisId = currentMisId;

                    refreshDocumentThreadPool.submit(() -> {
                        try {
                            // 等待3000ms
                            TimeUnit.MILLISECONDS.sleep(3000);
                            fridayRpcService.refreshDocument(finalRgDatasetDocument.getDatasetId(),
                                    finalRgDatasetDocument.getDocumentId(), finalAccessToken, finalCurrentMisId, spaceId);
                            log.info("#ReviewServiceImpl.handleRgDatasetDocument 延迟刷新文档成功: rgId={}, spaceId={}", rgId, spaceId);
                        } catch (Exception e) {
                            log.error("#ReviewServiceImpl.handleRgDatasetDocument 延迟更新文档失败: rgId={}, spaceId={}, datasetId={}, documentId={}",
                                    rgId, spaceId, finalRgDatasetDocument.getDatasetId(), finalRgDatasetDocument.getDocumentId(), e);
                        }
                    });
                } catch (Exception e) {
                    log.error("#ReviewServiceImpl.handleRgDatasetDocument 处理工作空间时发生异常: rgId={}, spaceId={}, error={}",
                            rgId, spaceId, e.getMessage(), e);
                }


            }
        } catch (LlmCorpusException e) {
            // 直接将业务异常抛出，由上层处理
            log.warn("#ReviewServiceImpl.handleRgDatasetDocument 业务异常: rgId={}, code={}, message={}",
                    rgId, e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("#ReviewServiceImpl.handleRgDatasetDocument 处理异常: rgId={}, error={}",
                    rgId, e.getMessage(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public RgDatasetDocumentEntity createDatasetDocument(Long rgId, String misId, String spaceId, String spaceName) throws LlmCorpusException {
        boolean permission = knowledgeBaseService.validateUserPermission(misId, rgId);
        if (!permission) {
            throw new LlmCorpusException(BizCode.NO_PERMISSION.getCode(), "用户无对应值班组权限");
        }
        return createDatasetAndDocumentInternal(rgId, spaceId, spaceName, misId);
    }

    /**
     * 创建数据集和文档的内部实现方法
     *
     * @param rgId 研发组ID
     * @param spaceId 工作空间ID，可为null表示使用默认空间
     * @param misId 用户ID
     * @return 创建的数据集文档实体
     */
    private RgDatasetDocumentEntity createDatasetAndDocumentInternal(Long rgId, String spaceId, String spaceName, String misId) {
        try {
            RgDatasetDocumentEntity rgDatasetDocumentIn = null;

            // 如果提供了spaceId，先尝试查找对应的工作空间记录
            if (spaceId != null && !spaceId.isEmpty()) {
                rgDatasetDocumentIn = rgDatasetDocumentMapper.findByRgIdAndSpaceId(rgId, spaceId);
            }

            String rgName = "";
            RgListItemDTO rgListItemDTO = ticketQueryService.queryRgInfoByRgId(misId, rgId);
            if (rgListItemDTO != null) {
                rgName = rgListItemDTO.getName();
            }
            String accessToken = workspaceService.getWorkspaceAccessToken(rgDatasetDocumentIn.getAccessKey(), rgDatasetDocumentIn.getAppSecret());
            // 如果不存在，调用创建知识库API
            boolean success = false;
            String datasetIdStr;
            try {
                // 如果创建成功，则获得到datasetIdStr
                datasetIdStr = fridayRpcService.createDataset(rgId + "|" + rgName + SUF_NAME,
                        accessToken, misId, rgId + "|" + rgName + SUF_NAME, spaceId);
                success = true;
                log.info("#createDatasetAndDocumentInternal 创建知识库成功: rgId={}, spaceId={}", rgId, spaceId);
            } catch (Exception e) {
                if (e.getMessage() != null && e.getMessage().contains("当前请求modifier不属于当前工作空间")) {
                    log.warn("#checkAppFactoryStatus 工作空间权限验证失败: rgId={}, spaceId={}, 错误信息={}", rgId, spaceId, e.getMessage());
                     throw LlmCorpusException.buildWithMsg(BizCode.FORBIDDEN, "无当前工作空间权限");
                }
                // 如果创建失败，则friday中已经有同名知识库，曾经创建调用过creatDatasetAndDocument方法
                if (rgDatasetDocumentIn != null) {
                    datasetIdStr = rgDatasetDocumentIn.getDatasetId();
                } else {
                    log.error("#createDatasetAndDocumentInternal 创建知识库失败且无现有记录: rgId={}, spaceId={}", rgId, spaceId, e);
                    throw new RuntimeException("#createDatasetAndDocumentInternal 创建知识库失败且无现有记录", e);
                }
            }

            // 先检查是否已有默认AccessKey
            String defaultAk = accessKeyService.getDefaultAccessKey(String.valueOf(rgId));
            if (defaultAk != null && !defaultAk.isEmpty()) {
                log.info("#createDatasetAndDocumentInternal 使用已存在的默认AccessKey: rgId={}, existingAk={}", rgId, defaultAk);
            } else {
                // 如果不存在，才生成新的AccessKey
                defaultAk = accessKeyService.generateAccessKey();
                log.info("#createDatasetAndDocumentInternal 生成新的默认AccessKey: rgId={}, newAk={}", rgId, defaultAk);
            }
            
            // 动态生成 URL，拼接默认AccessKey
            String localUrl = mtConfigService.getDeliveryOpenApiUrl() + "queryLatestContentByRgId?rgId=" + rgId + "&ak=" + defaultAk;

            // 调用上传文档API
            String documentIdStr;
            try {
                // 如果创建成功，则获得到documentIdStr
                documentIdStr = fridayRpcService.uploadDocument(datasetIdStr, localUrl, rgId + SUF_NAME,
                        accessToken, misId, true, spaceId);
                success = true;
                
                // 只有当默认AccessKey不存在时，才保存到数据库
                if (accessKeyService.getDefaultAccessKey(String.valueOf(rgId)) == null) {
                    accessKeyService.saveDefaultAccessKey(defaultAk, rgId);
                    log.info("#createDatasetAndDocumentInternal 保存新生成的默认AccessKey到数据库: rgId={}, ak={}", rgId, defaultAk);
                }
                
                log.info("#createDatasetAndDocumentInternal 上传文档成功: rgId={}, spaceId={}", rgId, spaceId);
            } catch (Exception e) {
                log.info("#createDatasetAndDocumentInternal Friday中已经有了该document: rgId={}, spaceId={}, error={}",
                        rgId, spaceId, e.getMessage());
                // 如果创建失败，则friday中已经有同名document，曾经创建调用过creatDatasetAndDocument方法
                if (rgDatasetDocumentIn != null) {
                    documentIdStr = rgDatasetDocumentIn.getDocumentId();
                } else {
                    log.error("#createDatasetAndDocumentInternal 上传文档失败且无现有记录: rgId={}, spaceId={}", rgId, spaceId, e);
                    throw new RuntimeException("#createDatasetAndDocumentInternal 上传文档失败且无现有记录", e);
                }
            }

            RgDatasetDocumentEntity rgDatasetDocument;
            // 只有在成功创建或更新了dataset或document时，才更新数据库
            if (success) {
                if (rgDatasetDocumentIn == null) {
                    // 如果不存在，则创建
                    rgDatasetDocument = RgDatasetDocumentEntity.builder()
                            .rgId(rgId)
                            .spaceId(spaceId)
                            .spaceName(spaceName)
                            .datasetId(datasetIdStr)
                            .documentId(documentIdStr)
                            .timestamp(new Timestamp(System.currentTimeMillis()))
                            .accessKey(rgDatasetDocumentIn.getAccessKey())
                            .appSecret(rgDatasetDocumentIn.getAppSecret())
                            .build();
                    rgDatasetDocumentMapper.insertRgDatasetDocumentAndSpaceId(rgDatasetDocument);
                    log.info("#checkAppFactoryStatus 成功创建新的记录: rgId={}, spaceId={}, datasetId={}, documentId={}",
                            rgId, spaceId, datasetIdStr, documentIdStr);
                } else {
                    // 如果存在，则更新
                    rgDatasetDocumentIn.setSpaceId(spaceId);
                    rgDatasetDocumentIn.setDatasetId(datasetIdStr);
                    rgDatasetDocumentIn.setDocumentId(documentIdStr);
                    rgDatasetDocumentIn.setTimestamp(new Timestamp(System.currentTimeMillis()));
                    rgDatasetDocumentIn.setAccessKey(rgDatasetDocumentIn.getAccessKey());
                    rgDatasetDocumentIn.setAppSecret(rgDatasetDocumentIn.getAppSecret());

                    // 使用updateByRgId方法进行更新，这样可以同时更新spaceId
                    rgDatasetDocumentMapper.updateAndSpaceId(rgDatasetDocumentIn);
                    log.info("#checkAppFactoryStatus 成功更新记录: rgId={}, spaceId={}, datasetId={}, documentId={}",
                            rgId, spaceId, datasetIdStr, documentIdStr);
                    rgDatasetDocument = rgDatasetDocumentIn;
                }
            } else {
                // 没有成功创建或更新，直接返回原有记录
                rgDatasetDocument = rgDatasetDocumentIn;
                log.info("createDatasetAndDocumentInternal: 未进行创建或更新操作，使用现有记录 rgId={}", rgId);
            }

            return rgDatasetDocument;
        } catch (LlmCorpusException e) {
            // 直接将业务异常抛出，由上层处理
            log.warn("#createDatasetAndDocumentInternal 业务异常: rgId={}, spaceId={}, code={}, message={}", 
                    rgId, spaceId, e.getCode(), e.getMessage());
        } catch (Exception e) {
            // 其他异常包装成业务异常
            log.error("#createDatasetAndDocumentInternal 处理异常: rgId={}, spaceId={}, error={}", 
                    rgId, spaceId, e.getMessage(), e);
        }
        return null;
    }
    
    @Override
    public String queryLatestContentByRgId(Long rgId, String ak) {
        // 验证AccessKey
        boolean isValidAccessKey = false;
        
        try {
            // 检查rgId是否在白名单中
            List<Long> whitelistRgIds = mtConfigService.getAccessKeyWhitelistRgIds();
            if (whitelistRgIds.contains(rgId)) {
                // rgId在白名单中，不需要验证ak
                log.info("#ReviewServiceImpl.queryLatestContentByRgId rgId在白名单中，无需验证ak: rgId={}", rgId);
                isValidAccessKey = true;
            } else if (ak != null && !ak.isEmpty()) {
                // 检查ak是否存在并且与rgId匹配
                AccessKeyEntity accessKey = accessKeyMapper.queryByAk(ak);
                if (accessKey != null) {
                    // 检查ak是否与rgId匹配
                    String rgIdStr = String.valueOf(rgId);
                    if (accessKey.getRgIds() != null && accessKey.getRgIds().contains(rgIdStr)) {
                        // ak验证成功，增加使用次数
                        accessKeyMapper.incrementCount(ak);
                        isValidAccessKey = true;
                    }
                }
            }
        } catch (Exception e) {
            log.error("#ReviewServiceImpl.queryLatestContentByRgId 验证AccessKey失败: rgId={}, ak={}, error={}",
                    rgId, ak, e.getMessage(), e);
        }
        
        if (isValidAccessKey) {
            // ak验证成功或rgId在白名单中，返回正常内容
            List<LatestContentDTO> contents = reviewMapper.findLatestContentsByRgId(rgId);

            String htmlContent = htmlContentUtil.createHtmlContent(contents);
            return htmlContent;
        } else {
            // ak验证失败且rgId不在白名单中，返回403错误页面
            return mtConfigService.getForbiddenAccessKeyMessage();
        }
    }

    @Override
    public String testString() {
        return HtmlContentUtil.testString();
    }

    @Override
    public String testRandomString() {
        return HtmlContentUtil.testRandomString();
    }

    @Override
    public List<RgDatasetDocumentEntity> findDatasetDocumentByRgId(Long rgId) throws LlmCorpusException {
        try {
            // 根据rgId查询数据库记录
            List<RgDatasetDocumentEntity> documentList = rgDatasetDocumentMapper.findByRgId(rgId);
            
            if (documentList == null || documentList.isEmpty()) {
                log.warn("#ReviewServiceImpl.findDatasetDocumentByRgId 未找到对应记录: rgId={}", rgId);
                throw new LlmCorpusException(BizCode.WORKSPACE_NOT_FOUND.getCode(), "未找到对应的工作空间配置");
            }
            
            log.info("#ReviewServiceImpl.findDatasetDocumentByRgId 成功查询到记录: rgId={}, 记录数={}", 
                    rgId, documentList.size());
            
            return documentList;
        } catch (LlmCorpusException e) {
            // 直接将业务异常抛出，由上层处理
            log.warn("#ReviewServiceImpl.findDatasetDocumentByRgId 业务异常: rgId={}, code={}, message={}", 
                    rgId, e.getCode(), e.getMessage());
            throw e;
        } catch (Exception e) {
            // 其他异常包装成业务异常
            log.error("#ReviewServiceImpl.findDatasetDocumentByRgId 处理异常: rgId={}, error={}", 
                    rgId, e.getMessage(), e);
            throw new LlmCorpusException(BizCode.ILLEGAL_REQUEST.getCode(),
                    "查询工作空间配置失败: " + e.getMessage());
        }
    }
}
