package com.meituan.banma.llm.corpus.server.service;

import com.meituan.banma.llm.corpus.api.exception.LlmCorpusException;
import com.meituan.banma.llm.corpus.server.common.domain.dto.PageDTO;
import com.meituan.banma.llm.corpus.server.controller.request.km.SplitContentForm;
import com.meituan.banma.llm.corpus.server.dal.entity.RgDocumentUrlEntity;
import com.sankuai.dxenterprise.open.gateway.service.citadel.api.resp.GetContentMetaBySsoResp;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 */
public interface IKmService {

    /**
     * 异步检查URL及获取文档元数据
     * @param url 文档URL
     * @param rgId 值班组ID
     * @param misId 用户ID
     * @return CompletableFuture包含检查结果Map
     */
    CompletableFuture<Map<String, Object>> checkUrlAndGetMetaAsync(String url, long rgId, String misId);
    
    /**
     * 异步检查URL是否存在，并获取元数据
     * @param url 文档URL
     * @param rgId 值班组ID
     * @param misId 用户ID
     * @return CompletableFuture包含检查结果Map
     */
    CompletableFuture<Map<String, Object>> checkUrlExistsAsync(String url, long rgId, String misId);

    GetContentMetaBySsoResp getKmMetaByUrl(String url, long rgId, String misId) throws LlmCorpusException;


    String testSimplfyBody(String body);

    String kmToCorpusByContentId(long contentId, long rgId, String misId) throws LlmCorpusException;

    boolean getDocAccessByUrl(String url, long rgId, String misId) throws LlmCorpusException;

    Object getKmSplitByContentId(long contentId, long rgId, String misId, int isTable) throws LlmCorpusException;

    Object getKmHtmlByContentId(long contentId, long rgId, String misId) throws LlmCorpusException ;

    String splitContentToCorpus(SplitContentForm splitContentForm, long rgId, String misId) throws LlmCorpusException ;

    /**
     * 分页查询指定值班组的文档URL
     * @param rgId 值班组ID
     * @param misId 用户ID
     * @param strMatch 匹配字符串，用于匹配name和url
     * @param pageNum 页码
     * @param pageSize 页大小
     * @return 文档URL分页列表
     * @throws LlmCorpusException 业务异常
     */
    PageDTO<RgDocumentUrlEntity> queryDocumentByRgId(long rgId, String misId, String strMatch, int pageNum, int pageSize) throws LlmCorpusException;

    /**
     * 检查文档是否可以添加，检查URL和名称是否已存在
     * @param rgId 值班组ID
     * @param url 文档URL
     * @param name 文档名称
     * @param misId 用户ID
     * @return 检查结果，包含是否可添加和原因
     * @throws LlmCorpusException 业务异常
     */
    Map<String, Object> checkDocumentCanAdd(long rgId, String url, String name, String misId) throws LlmCorpusException;
    
    /**
     * 检查文档URL是否已存在
     * @param rgId 值班组ID
     * @param url 文档URL
     * @param misId 用户ID
     * @return 是否存在
     * @throws LlmCorpusException 业务异常
     */
    boolean checkUrlExists(long rgId, String url, String misId) throws LlmCorpusException;
    
    /**
     * 检查文档名称是否已存在
     * @param rgId 值班组ID
     * @param name 文档名称
     * @param misId 用户ID
     * @return 是否存在
     * @throws LlmCorpusException 业务异常
     */
    boolean checkNameExists(long rgId, String name, String misId) throws LlmCorpusException;

    /**
     * 批量删除文档记录
     * @param rgId 值班组ID
     * @param documentIds 文档ID列表
     * @param misId 用户ID
     * @return 删除成功的文档ID列表
     * @throws LlmCorpusException 业务异常
     */
    List<String> batchDeleteByDocumentIds(long rgId, List<String> documentIds, String misId) throws LlmCorpusException;

    /**
     * 批量添加文档记录（支持自动更新设置）
     * @param rgId 值班组ID
     * @param nameList 文档名称列表
     * @param urlList 文档URL列表
     * @param autoUpdateList 自动更新标志列表，0表示不自动更新，1表示自动更新
     * @param misId 用户ID
     * @return 添加成功的文档ID列表
     * @throws LlmCorpusException 业务异常
     */
    List<String> addBatchDocumentByNameAndUrl(long rgId, List<String> nameList, List<String> urlList, List<Integer> autoUpdateList, String misId) throws LlmCorpusException;

    boolean changeRefreshByDocumentId(long rgId, String url, int autoUpdate, String misId) throws LlmCorpusException ;

}
