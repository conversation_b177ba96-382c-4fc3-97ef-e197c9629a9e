package com.meituan.banma.llm.corpus.server.rpc.friday.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

@Data
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class LlmKmToCorpusDTO {
    /**
     * 状态码
     */
    private Integer code;
    
    /**
     * 消息
     */
    private String message;
    
    /**
     * 数据
     */
    private LlmKmToCorpusTaskResultDTO data;

    @Data
    @Builder
    @ToString
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LlmKmToCorpusTaskResultDTO {
        /**
         * 知识库问答列表
         */
        private List<LlmKmToCorpusTaskResultItem> kmQaList;

        @Data
        @Builder
        @ToString
        @NoArgsConstructor
        @AllArgsConstructor
        public static class LlmKmToCorpusTaskResultItem {
            /**
             * 标题
             */
            private String title;
            
            /**
             * 结果文档
             */
            private ResultDoc resultDoc;
            
            /**
             * 缺失信息列表
             */
            private List<String> missingInfo;
            
            /**
             * 标签id列表，英文逗号分隔，如1,2,3
             */
            private String tagsIds;

            @Data
            @Builder
            @ToString
            @NoArgsConstructor
            @AllArgsConstructor
            public static class ResultDoc {
                /**
                 * 问题内容
                 */
                private String question;
                
                /**
                 * 文档内容
                 */
                private String docs;
            }
        }
    }
}
