package com.meituan.banma.llm.corpus.server.rpc.friday;

import com.meituan.banma.llm.corpus.api.exception.LlmCorpusException;
import com.meituan.banma.llm.corpus.server.common.domain.dto.FridayEmbeddingResDTO;
import com.meituan.banma.llm.corpus.server.rpc.friday.dto.FridayConversationParams;
import com.meituan.banma.llm.corpus.server.rpc.friday.dto.FridayConversationResponseDataDTO;

import java.util.List;

public interface FridayRpcService {
    /**
     * 调用Friday大模型接口
     *
     * @param params
     * @return
     */
    FridayConversationResponseDataDTO conversation(FridayConversationParams params) throws LlmCorpusException;

    FridayConversationResponseDataDTO conversationWithoutPostProcess(FridayConversationParams params)  throws LlmCorpusException;

    FridayConversationResponseDataDTO conversationWithAsync(FridayConversationParams params) throws LlmCorpusException;
    /**
     * 获取Friday应用工厂接口accessToken
     * @return
     */
    String getFridayAccessToken();

    /**
     * 创建Friday值班组知识库（指定工作空间）
     * @param name 知识库名称
     * @param accessToken 访问令牌
     * @param modifier 修改者
     * @param description 描述
     * @param spaceId 工作空间ID
     * @return 数据集ID
     */
    String createDataset(String name, String accessToken, String modifier, String description, String spaceId);

    /**
     * 上传值班组文档（指定工作空间）
     * @param datasetId 数据集ID
     * @param url 文档URL
     * @param name 文档名称
     * @param accessToken 访问令牌
     * @param modifier 修改者
     * @param autoRefresh 是否自动刷新
     * @param spaceId 工作空间ID
     * @return 文档ID
     */
    String uploadDocument(String datasetId, String url, String name, String accessToken, String modifier, boolean autoRefresh, String spaceId);

    /**
     * 删除值班组文档（指定工作空间）
     * @param datasetId 数据集ID
     * @param documentId 文档ID
     * @param accessToken 访问令牌
     * @param modifier 修改者
     * @param spaceId 工作空间ID
     */
    void deleteDocument(String datasetId, String documentId, String accessToken, String modifier, String spaceId);

    List<FridayEmbeddingResDTO> modelFactoryEmbedding(String misId, List<String> input, String modelName) throws LlmCorpusException;
    void refreshDocument(String datasetId, String documentId, String accessToken, String modifier, String spaceId);

    /**
     * 修改文档刷新设置（指定工作空间）
     * @param datasetId 数据集ID
     * @param documentId 文档ID
     * @param accessToken 访问令牌
     * @param modifier 修改者
     * @param autoRefresh 是否自动刷新
     * @param spaceId 工作空间ID
     */
    void changeRefreshDocument(String datasetId, String documentId, String accessToken, String modifier, boolean autoRefresh, String spaceId);

    /**
     * 删除值班组知识库（指定工作空间）
     * @param datasetId 数据集ID
     * @param accessToken 访问令牌
     * @param modifier 修改者
     * @param spaceId 工作空间ID
     */
    void deleteDataset(String datasetId, String accessToken, String modifier, String spaceId);
}
