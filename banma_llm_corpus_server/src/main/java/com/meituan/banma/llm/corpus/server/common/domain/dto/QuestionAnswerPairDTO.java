package com.meituan.banma.llm.corpus.server.common.domain.dto;

import com.meituan.banma.llm.corpus.server.common.domain.dto.stat.AppStatusDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 问答对DTO，用于表示用户提问与AI助手回复
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QuestionAnswerPairDTO {
    /**
     * 用户提问
     */
    private AppStatusDTO.Question question;
    
    /**
     * AI助手回复内容
     */
    private String answer;
    
    /**
     * AI助手回复消息ID
     */
    private String answerMessageId;
    
    /**
     * AI助手回复时间
     */
    private Long answerTime;
    
    /**
     * 会话ID
     */
    private String conversationId;
} 