package com.meituan.banma.llm.corpus.server.rpc.dx;


import com.meituan.banma.llm.corpus.server.common.MtConfigService;
import com.meituan.banma.llm.corpus.server.common.interceptor.MisIdInterceptor;
import com.meituan.banma.llm.corpus.server.utils.AuthUtil;
import com.meituan.banma.llm.corpus.server.utils.HttpUtil;
import com.sankuai.xm.openplatform.auth.entity.AccessTokenResp;
import com.sankuai.xm.openplatform.auth.entity.AppAuthInfo;
import com.sankuai.xm.openplatform.auth.service.XmAuthServiceI;
import groovy.util.logging.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/12/5
 * 办公开放平台通用token。访问大象，学城的api都需要带这个token
 */
@Slf4j
@Service
public class XmAuthRpcService {
    private static final Logger log = LoggerFactory.getLogger(XmAuthRpcService.class);

    private long expireTime = 0;
    private String accessToken = "";
    private final static long FIVE_MINUTES = 1000 * 60 * 5;

    // Token过期时间（3天，单位：毫秒）
    private static final long SSOID_TOKEN_EXPIRE_DURATION = 3 * 24 * 60 * 60 * 1000;
    
    // 缓存的token和过期时间
    private volatile String ssoidToken = null;
    private volatile long ssoidTokenExpireTime = 0;

    private volatile String kmSsoidToken = null;
    private volatile long kmSsoidTokenExpireTimeKm = 0;
    @Resource
    private XmAuthServiceI.Iface xmAuthService;

    @Resource
    private MtConfigService mtConfigService;

    @Resource
    private XmAuthRpcCommonService xmAuthRpcCommonService;

    //------------------开放平台token方法------------------//
    /**
     * 获取开放平台token
     * 
     * @return 开放平台token
     * @throws RuntimeException 获取token失败时抛出异常
     */
    public String getToken(String... robotAppId) { //appKey即为办公后台-大象机器人的app id
        // 优先使用传入的appKey，如果没有传入则使用默认的appKey(语料机器人)
        if (robotAppId.length > 0) {
            return xmAuthRpcCommonService.getToken(robotAppId[0]);
        }

        // 如果当前Token未过期且距离过期时间超过五分钟，直接返回当前Token
        long nowTime = System.currentTimeMillis();
        if (expireTime - FIVE_MINUTES > nowTime) {
            return accessToken;
        }

        synchronized (this) {
            nowTime = System.currentTimeMillis();
            if (expireTime - FIVE_MINUTES > nowTime) {
                return accessToken;
            }
            AppAuthInfo appAuthInfo = new AppAuthInfo();
            appAuthInfo.setAppkey(mtConfigService.getOpenPlatformAppKey());
            appAuthInfo.setAppSecret(mtConfigService.getOpenPlatformAppSecret());
            try {
                AccessTokenResp resp = xmAuthService.accessToken(appAuthInfo);
                log.info("生成开放平台Token:{}", resp);
                if (resp.status.getCode() == 0) {
                    accessToken = resp.getAccessToken().getToken();
                    expireTime = resp.getAccessToken().getExpireTime();
                } else {
                    throw new RuntimeException("生成开放平台Token失败");
                }
                return accessToken;
            } catch (TException e) {
                log.error("生成开放平台Token失败", e);
                throw new RuntimeException("生成开放平台Token失败", e);
            }
        }
    }

    //------------------SSO token方法------------------//
    /**
     * 获取带ssoid的token
     * 通过SSO接口获取认证token
     * 
     * @return 带ssoid的token字符串
     * @throws RuntimeException 获取token失败时抛出异常
     */
    public String getDxSsoidToken() {
        try {
            // 如果当前Token未过期且距离过期时间超过五分钟，直接返回当前Token
            long nowTime = System.currentTimeMillis();
            if (StringUtils.isNotBlank(ssoidToken) && ssoidTokenExpireTime - FIVE_MINUTES > nowTime) {
                return ssoidToken;
            }

            synchronized (this) {
                nowTime = System.currentTimeMillis();
                
                // 双重检查，避免多线程重复获取token
                if (StringUtils.isNotBlank(ssoidToken) && ssoidTokenExpireTime - FIVE_MINUTES > nowTime) {
                    return ssoidToken;
                }
                
                // 获取新token
                String newSsoidToken = obtainSsoidToken(mtConfigService.getSsoDxClientId());
                if (StringUtils.isNotBlank(newSsoidToken)) {
                    this.ssoidToken = newSsoidToken;
                    this.ssoidTokenExpireTime = System.currentTimeMillis() + SSOID_TOKEN_EXPIRE_DURATION;
                    return newSsoidToken;
                }
                
                throw new RuntimeException("获取SSO token失败");
            }
        } catch (Exception e) {
            log.error("#getSsoidToken#获取token异常", e);
            throw new RuntimeException("获取SSO token失败", e);
        }
    }

    //学城sso token
    public String getKmSsoidToken() {
        try {
            // 首先尝试从MisIdInterceptor获取存储的ssoid
            String ssoid = MisIdInterceptor.getKmSsoid();
            if (ssoid != null && !ssoid.isEmpty()) {
                log.info("Using ssoid from MisIdInterceptor: {}", ssoid);
                return ssoid;
            }
            
            // 作为备选方案，如果从MisIdInterceptor获取失败，尝试从请求中获取
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();

            // 确保 attributes 不为 null
            if (attributes == null) {
                log.warn("Request attributes are null");
                return null;
            }

            HttpServletRequest request = attributes.getRequest();

            // 获取 Cookies
            Cookie[] cookies = request.getCookies();
            if (cookies != null) {
                for (Cookie cookie : cookies) {
                    String name = cookie.getName();
                    String value = cookie.getValue();
                    // 检查名称是否以 _ssoid 结尾
                    if (name.endsWith("_ssoid")) {
                        log.info("Found _ssoid cookie: {} = {}", name, value);
                        return value;
                    }
                }
            }
            log.warn("No _ssoid cookie found");
            return null;
        } catch (Exception e) {
            log.error("获取KM SSOID异常", e);
            return null;
        }
    }

    /**
     * 获取通用用户SsoId
     * 
     * @return 用户SsoId，获取失败则返回null
     */
    public String getUserSsoId() {
        try {
            // 首先尝试从MisIdInterceptor获取存储的ssoId
            String ssoId = MisIdInterceptor.getUserSsoId();
            if (ssoId != null && !ssoId.isEmpty()) {
                log.info("Using user ssoId from MisIdInterceptor: {}", ssoId);
                return ssoId;
            }
            
            // 作为备选方案，如果从MisIdInterceptor获取失败，尝试从请求中获取
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();

            // 确保 attributes 不为 null
            if (attributes == null) {
                log.warn("User ssoId request attributes are null");
                return null;
            }

            HttpServletRequest request = attributes.getRequest();

            // 获取 Cookies
            Cookie[] cookies = request.getCookies();
            if (cookies != null) {
                for (Cookie cookie : cookies) {
                    String name = cookie.getName();
                    String value = cookie.getValue();
                    // 检查名称是否为标准ssoId格式
                    if ("ssoId".equals(name) || name.endsWith("_ssoId")) {
                        log.info("Found user ssoId cookie: {} = {}", name, value);
                        return value;
                    }
                }
            }

            log.warn("No user ssoId found in cookies or headers");
            return null;
        } catch (Exception e) {
            log.error("获取用户SSOID异常", e);
            return null;
        }
    }

    /**
     * 初次获取SSO token（虚拟账户）
     * 
     * @return ssoid token字符串
     * @throws RuntimeException 获取token失败时抛出异常
     * 注释为线上环境配置
     */
    private String obtainSsoidToken(String ssoClientId) {
        // SSO接口URL
        String url = mtConfigService.getSsoBaseUrl() + mtConfigService.getSsoAuthUri();

        // 请求体
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("loginName", mtConfigService.getSsoLoginName());
        requestBody.put("password", mtConfigService.getSsoLoginPassword());
        requestBody.put("clientId", ssoClientId);
        
        try {
            // 获取认证所需的请求头
            Map<String, String> headers = AuthUtil.buildSsoHeaders(
                "POST", mtConfigService.getSsoAuthUri(), mtConfigService.getSsoClientId(), mtConfigService.getSsoClientSecret());
            // 使用HttpUtil发送请求
            String response = HttpUtil.doPost(url, headers, requestBody);
            
            // 解析响应
            return AuthUtil.parseResponse(response, "accessToken");
        } catch (Exception e) {
            log.error("#obtainSsoidToken#获取token异常", e);
            throw new RuntimeException("获取SSO token失败", e);
        }
    }


}
