package com.meituan.banma.llm.corpus.server.common.domain.dto.stat;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CorpusStatsRgInfoItem {
    private String rgName;
    private String rgId;
    private String rgDesc;
    private String rgOwner;
    private String rgOrgId;
    private String rgOrgName;
    private String rgOrgPath;
    private Long timeRangeCorpusCount;
    private Long totalCorpusCount;
    private List<CorpusGenerateTaskItemForStats> allRunningStatusTasks;
}
