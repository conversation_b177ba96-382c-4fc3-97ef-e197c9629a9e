package com.meituan.banma.llm.corpus.server.service.impl;

import com.cip.crane.client.spring.annotation.Crane;
import com.meituan.banma.llm.corpus.server.dal.entity.RgDatasetDocumentEntity;
import com.meituan.banma.llm.corpus.server.dal.mapper.RgDatasetDocumentMapper;
import com.meituan.banma.llm.corpus.server.dal.mapper.ReviewMapper;
import com.meituan.banma.llm.corpus.server.rpc.friday.FridayRpcService;
import com.meituan.banma.llm.corpus.server.service.IDocumentRefreshService;
import com.meituan.banma.llm.corpus.server.service.WorkspaceService;
import com.meituan.banma.llm.corpus.server.common.MtConfigService;
import com.meituan.banma.llm.corpus.server.service.IAccessKeyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class DocumentRefreshServiceImpl implements IDocumentRefreshService {

    @Autowired
    private RgDatasetDocumentMapper rgDatasetDocumentMapper;

    @Autowired
    private ReviewMapper reviewMapper;

    @Autowired
    private FridayRpcService fridayRpcService;

    @Autowired
    @Lazy
    private WorkspaceService workspaceService;

    @Autowired
    private MtConfigService mtConfigService;

    @Autowired
    private IAccessKeyService accessKeyService;

    @Override
    @Crane("refresh-all-documents-task")
    public void refreshAllDocumentsTask() {
        try {
            log.info("#DocumentRefreshServiceImpl.refreshAllDocumentsTask 开始执行定时任务：批量刷新所有文档");
            Map<String, Object> result = refreshAllDocuments();
            log.info("#DocumentRefreshServiceImpl.refreshAllDocumentsTask 批量刷新文档结果详情: {}", result);
        } catch (Exception e) {
            log.error("#DocumentRefreshServiceImpl.refreshAllDocumentsTask 定时任务执行失败", e);
        }
    }

    @Override
    public Map<String, Object> refreshAllDocuments() {
        Map<String, Object> result = new HashMap<>();
        List<Map<String, Object>> successList = new ArrayList<>();
        List<Map<String, Object>> failedList = new ArrayList<>();
        int totalCount = 0;
        int successCount = 0;
        try {
            // 批量检测数据库存在与否的动作：获取modified_output中的所有rg_id，检查并初始化工作空间配置
            log.info("#DocumentRefreshServiceImpl.refreshAllDocuments 开始批量检测和初始化工作空间配置");
            List<Long> allRgIds = reviewMapper.findAllDistinctRgIds();
            log.info("#DocumentRefreshServiceImpl.refreshAllDocuments 获取到modified_output中的所有rg_id数量: {}", allRgIds.size());
            
            for (Long rgId : allRgIds) {
                try {
                    // 获取该值班组下的所有工作空间配置
                    List<RgDatasetDocumentEntity> rgDatasetDocuments = rgDatasetDocumentMapper.findByRgId(rgId);
                    
                    // 获取Friday默认空间ID
                    String fridaySpaceId = mtConfigService.getFridaySpaceId();
                    
                    // 检查是否存在Friday默认空间配置
                    boolean hasFridaySpace = false;
                    if (rgDatasetDocuments != null && !rgDatasetDocuments.isEmpty()) {
                        for (RgDatasetDocumentEntity doc : rgDatasetDocuments) {
                            if (fridaySpaceId.equals(doc.getSpaceId())) {
                                hasFridaySpace = true;
                                break;
                            }
                        }
                    }
                    
                    if (rgDatasetDocuments == null || rgDatasetDocuments.isEmpty() || !hasFridaySpace) {
                        log.info("#DocumentRefreshServiceImpl.refreshAllDocuments 未找到rgId={}的Friday默认工作空间配置，创建默认配置", rgId);
                        // 获取最新的misId作为创建者
                        String misId = accessKeyService.getLatestMisIdByRgId(rgId);
                        // 调用WorkspaceService创建默认工作空间
                        boolean success = workspaceService.createDefaultWorkspace(rgId, misId);
                        if (!success) {
                            log.warn("#DocumentRefreshServiceImpl.refreshAllDocuments# rgId={}创建默认工作空间配置失败", rgId);
                        } else {
                            log.info("#DocumentRefreshServiceImpl.refreshAllDocuments rgId={}默认工作空间配置创建成功", rgId);
                        }
                    }
                } catch (Exception e) {
                    log.error("#DocumentRefreshServiceImpl.refreshAllDocuments 检测和初始化rgId={}的工作空间配置失败", rgId, e);
                }
            }
            log.info("#DocumentRefreshServiceImpl.refreshAllDocuments 批量检测和初始化工作空间配置完成");
            
            List<RgDatasetDocumentEntity> entities = rgDatasetDocumentMapper.findAll();
            totalCount = entities.size();
            if (entities.isEmpty()) {
                log.warn("#DocumentRefreshServiceImpl.refreshAllDocuments 未找到任何记录");
                result.put("success", false);
                result.put("message", "未找到任何记录");
                result.put("totalCount", 0);
                result.put("successCount", 0);
                result.put("failedCount", 0);
                return result;
            }
            log.info("#DocumentRefreshServiceImpl.refreshAllDocuments 开始批量刷新文档，总记录数: {}", totalCount);
            for (RgDatasetDocumentEntity entity : entities) {
                Map<String, Object> recordResult = new HashMap<>();
                recordResult.put("rgId", entity.getRgId());
                recordResult.put("spaceId", entity.getSpaceId());
                recordResult.put("documentId", entity.getDocumentId());
                recordResult.put("datasetId", entity.getDatasetId());
                String documentId = entity.getDocumentId();
                String datasetId = entity.getDatasetId();
                String spaceId = entity.getSpaceId();
                if (datasetId == null || datasetId.isEmpty()) {
                    log.warn("#DocumentRefreshServiceImpl.refreshAllDocuments 数据集ID为空，跳过处理: rgId={}, spaceId={}", entity.getRgId(), spaceId);
                    recordResult.put("success", false);
                    recordResult.put("message", "数据集ID为空");
                    failedList.add(recordResult);
                    continue;
                }
                if (documentId == null || documentId.isEmpty()) {
                    log.warn("#DocumentRefreshServiceImpl.refreshAllDocuments 文档ID为空，跳过处理: rgId={}, spaceId={}, datasetId={}", entity.getRgId(), spaceId, datasetId);
                    recordResult.put("success", false);
                    recordResult.put("message", "文档ID为空");
                    failedList.add(recordResult);
                    continue;
                }
                try {
                    String accessToken = workspaceService.getWorkspaceAccessToken(entity.getAccessKey(), entity.getAppSecret());
                    String modifier;
                    if (spaceId != null && spaceId.equals(mtConfigService.getFridaySpaceId())) {
                        modifier = mtConfigService.getFridayAccessTokenConfig().getModifier();
                        log.info("#DocumentRefreshServiceImpl.refreshAllDocuments 检测到Friday spaceId，使用Friday配置的modifier: rgId={}, spaceId={}, modifier={}", entity.getRgId(), spaceId, modifier);
                    } else {
                        modifier = accessKeyService.getLatestMisIdByRgId(entity.getRgId());
                        log.info("#DocumentRefreshServiceImpl.refreshAllDocuments 使用最新修改人作为modifier: rgId={}, spaceId={}, modifier={}", entity.getRgId(), spaceId, modifier);
                    }
                    fridayRpcService.refreshDocument(datasetId, documentId, accessToken, modifier, spaceId);
                    log.info("#DocumentRefreshServiceImpl.refreshAllDocuments 成功刷新文档: rgId={}, spaceId={}, datasetId={}, documentId={}, modifier={}", entity.getRgId(), spaceId, datasetId, documentId, modifier);
                    recordResult.put("success", true);
                    recordResult.put("message", "文档刷新成功");
                    recordResult.put("modifier", modifier);
                    successList.add(recordResult);
                    successCount++;
                } catch (Exception e) {
                    log.error("#DocumentRefreshServiceImpl.refreshAllDocuments 刷新文档失败: rgId={}, spaceId={}, datasetId={}, documentId={}", entity.getRgId(), spaceId, datasetId, documentId, e);
                    recordResult.put("success", false);
                    recordResult.put("message", "文档刷新失败: " + e.getMessage());
                    recordResult.put("errorMessage", e.getMessage());
                    failedList.add(recordResult);
                }
            }
            boolean overallSuccess = failedList.isEmpty();
            result.put("success", overallSuccess);
            result.put("message", overallSuccess ? "所有文档刷新成功" : "部分文档刷新失败");
            result.put("totalCount", totalCount);
            result.put("successCount", successCount);
            result.put("failedCount", failedList.size());
            result.put("successList", successList);
            result.put("failedList", failedList);
            log.info("#DocumentRefreshServiceImpl.refreshAllDocuments 批量刷新文档完成: 总数={}, 成功={}, 失败={}", totalCount, successCount, failedList.size());
            return result;
        } catch (Exception e) {
            log.error("#DocumentRefreshServiceImpl.refreshAllDocuments 批量刷新文档过程中发生异常", e);
            result.put("success", false);
            result.put("message", "批量刷新文档过程中发生异常");
            result.put("errorMessage", e.getMessage());
            result.put("totalCount", totalCount);
            result.put("successCount", successCount);
            result.put("failedCount", totalCount - successCount);
            result.put("successList", successList);
            result.put("failedList", failedList);
            return result;
        }
    }

}