package com.meituan.banma.llm.corpus.server.common;

import com.alibaba.fastjson.JSONObject;

/**
 * <AUTHOR>
 * @date 2024/12/5
 * 通用返回结果
 */
public class CommonResult {
    private final int code;
    private final String msg;
    private final Object data;

    // 私有构造器，确保通过静态工厂方法创建实例
    private CommonResult(int code, String msg, Object data) {
        this.code = code;
        this.msg = msg;
        this.data = data;
    }

    public static CommonResult success() {
        return new CommonResult(0, "success", null);
    }

    public static CommonResult success(Object data) {
        return new CommonResult(0, "success", data);
    }

    public static CommonResult success(String msg, Object data) {
        return new CommonResult(0, msg, data);
    }

    public static CommonResult error() {
        return new CommonResult(1, "未知错误", null);
    }

    public static CommonResult error(String msg) {
        return new CommonResult(1, msg, null);
    }

    public static CommonResult error(int code, String msg) {
        return new CommonResult(code, msg, null);
    }

    // Getters
    public int getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    public Object getData() {
        return data;
    }

    public boolean isSuccess() {
        return this.code == 0;
    }

    public JSONObject toJson() {
        JSONObject json = new JSONObject();
        json.put("code", this.code);
        json.put("msg", this.msg);
        json.put("data", this.data);
        return json;
    }
}
