package com.meituan.banma.llm.corpus.server.common.config;

import com.meituan.banma.llm.corpus.server.common.MtConfigService;
import com.meituan.banma.llm.corpus.server.common.domain.dto.TalosMccConfig;
import com.meituan.talos.commons.exception.TalosException;
import com.sankuai.data.talos.AsyncTalosClient;
import com.sankuai.data.talos.Talos;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;

import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@Configuration
public class TalosBeanConfig {
    @Autowired
    private MtConfigService mtConfigService;
    @Bean
    public AsyncTalosClient talosClient() throws Exception {
        TalosMccConfig talosMccConfig = mtConfigService.getTalosMccConfig();
        AsyncTalosClient talosClient = new AsyncTalosClient(talosMccConfig.getTalosUser(), talosMccConfig.getTalosPass());
        try {
            talosClient.openSession(new Talos.RequestOption(60, 60, 60, TimeUnit.SECONDS));
        } catch (TalosException e) {
            throw new Exception(e);
        }
        return talosClient;
    }

    @Bean
    public ThreadPoolTaskScheduler taskScheduler(AsyncTalosClient talosClient) {
        ThreadPoolTaskScheduler taskScheduler = new ThreadPoolTaskScheduler();
        taskScheduler.setPoolSize(2);
        taskScheduler.setThreadNamePrefix("renew-token-pool-");
        taskScheduler.setWaitForTasksToCompleteOnShutdown(true);
        taskScheduler.setAwaitTerminationSeconds(120);
        taskScheduler.setRejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy());
        taskScheduler.initialize();
        taskScheduler.scheduleWithFixedDelay(new Runnable() {
            @Override
            public void run() {
                try {
                    talosClient.openSession(new Talos.RequestOption(60,60,60, TimeUnit.SECONDS));
                } catch (TalosException e) {
                    //handle exception
                }
            }
        }, 2160000);
        return taskScheduler;
    }
}
