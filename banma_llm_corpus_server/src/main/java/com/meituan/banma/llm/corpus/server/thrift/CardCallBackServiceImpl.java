package com.meituan.banma.llm.corpus.server.thrift;

import com.dianping.cat.util.StringUtils;
import com.dianping.rhino.Rhino;
import com.dianping.rhino.threadpool.DefaultThreadPoolProperties;
import com.dianping.rhino.threadpool.ThreadPool;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.meituan.banma.llm.corpus.api.exception.LlmCorpusException;
import com.meituan.banma.llm.corpus.server.common.constants.enums.ConvertTaskPlatformId;
import com.meituan.banma.llm.corpus.server.common.domain.dto.ConvertTtToKnowledgeParam;
import com.meituan.banma.llm.corpus.server.common.domain.dto.TTInfoDTO;
import com.meituan.banma.llm.corpus.server.service.IDxGroupChatService;
import com.meituan.banma.llm.corpus.server.service.IKnowledgeBaseService;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftServer;
import com.sankuai.xm.openplatform.api.entity.*;
import com.sankuai.xm.openplatform.api.service.sdk.OpenCardSeviceSI;
import com.sankuai.xm.openplatform.common.entity.RespStatus;
import com.sankuai.xm.openplatform.common.enums.ResCodeEnum;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.util.UUID;

@MdpThriftServer
public class CardCallBackServiceImpl implements OpenCardSeviceSI.Iface {

    private static final Logger log = LoggerFactory.getLogger(CardCallBackServiceImpl.class);

    private static final String SINGLE_CHAT_NAME = "的单聊消息";
    @Resource
    private IDxGroupChatService dxGroupChatService;

    @Resource
    private IKnowledgeBaseService knowledgeBaseService;

    // 定义线程池
    private static final ThreadPool threadPool = Rhino.newThreadPool("threadPool-cardCallback",
            DefaultThreadPoolProperties.Setter().withCoreSize(10).withMaxSize(20));

    @Override
    public CardCallBackResp cardCallBack(CardCallBackReq cardCallBackReq) throws TException {
        log.info("Received card callback request: {}", cardCallBackReq);
        CardCallBackResp cardCallBackResp = new CardCallBackResp();

        try {
            // 异步处理卡片回调请求
            threadPool.execute(() -> {
                try {
                    handleCardCallback(cardCallBackReq);
                } catch (Exception e) {
                    log.error("Error handling card callback", e);
                }
            });
        } catch (Exception e) {
            log.error("Failed to handle card callback", e);
        }

        // 设置响应状态
        RespStatus status = new RespStatus();
        status.setCode(ResCodeEnum.SUCCESS.getCode());
        status.setMsg(ResCodeEnum.SUCCESS.getMsg());
        cardCallBackResp.setStatus(status);

        return cardCallBackResp;
    }

    private void handleCardCallback(CardCallBackReq cardCallBackReq) {
        long empId = cardCallBackReq.getOperator().getEmpId();
        long uid = dxGroupChatService.getUidByEmpId(empId);
        String misId = dxGroupChatService.getMisIdByUid(uid);
        String empName = dxGroupChatService.getUserDetailByUid(uid).getName();
        if (StringUtils.isBlank(misId)) {
            log.error("#CardCallBackServiceImpl.getMisIdByUid#Failed to get misId by empId: {}", empId);
            return;
        }

        String interactiveParam = cardCallBackReq.getInteractiveParam();
        long rgId = extractRgIdFromInteractiveParam(interactiveParam);

        if (cardCallBackReq.getFieldData().getFieldType().equals(FieldTypeEnum.GROUP_CHAT) &&
                !StringUtils.isBlank(cardCallBackReq.getFieldData().getFieldId())) {
            String groupId = cardCallBackReq.getFieldData().getFieldId();
            try {
                processGroupChatCallback(groupId, rgId, misId, uid, empName);
            } catch (LlmCorpusException e) {
                log.error("LlmCorpusException in processGroupChatCallback", e);
            }
        } else if (cardCallBackReq.getFieldData().getFieldType().equals(FieldTypeEnum.PUB_CHAT) &&
                !StringUtils.isBlank(cardCallBackReq.getFieldData().getFieldId())) {
            String pubId = cardCallBackReq.getFieldData().getFieldId();
            try {
                processSingleChatCallback(rgId, misId, uid, empName);
            } catch (LlmCorpusException e) {
                log.error("LlmCorpusException in processGroupChatCallback", e);
            }
        }
    }

    private long extractRgIdFromInteractiveParam(String interactiveParam) {
        try {
            JsonObject jsonObject = JsonParser.parseString(interactiveParam).getAsJsonObject();
            JsonArray rgIdArray = jsonObject.getAsJsonArray("rgId");
            if (rgIdArray != null && rgIdArray.size() > 0) {
                JsonObject firstObject = rgIdArray.get(0).getAsJsonObject();
                return firstObject.get("value").getAsLong();
            }
        } catch (Exception e) {
            log.error("Failed to extract value from interactiveParam: {}", interactiveParam, e);
        }
        return -1;  // 返回一个默认值或根据需要处理错误
    }

    private void processGroupChatCallback(String groupId, long rgId, String misId,long uid, String operatorName) throws LlmCorpusException {
        if (rgId < 1) {
            log.info("#CardCallBackServiceImpl.processGroupChatCallback# rgId is invalid");
            dxGroupChatService.sendMessageToGroupWithAt(Long.parseLong(groupId),  uid,  operatorName,"非调用人，无需点击卡片");
            return;
        }
        ConvertTtToKnowledgeParam param = new ConvertTtToKnowledgeParam();
        param.setTtId("g" + groupId);
        param.setCreatorDxUserId(uid);
        param.setRgId(rgId);
        param.setDxGroupId(Long.parseLong(groupId));
        param.setMisId(misId);
        param.setCreatorUserName(operatorName);
        param.setPlatformId(ConvertTaskPlatformId.DX_GROUP_BOT);

        TTInfoDTO ttInfoDTO = new TTInfoDTO();
        ttInfoDTO.setTicketId("g" + groupId);
        ttInfoDTO.setRgId(rgId);
        ttInfoDTO.setGroupId(Long.parseLong(groupId));

        ttInfoDTO.setName(dxGroupChatService.getGroupNameByDxGroupId(Long.parseLong(groupId)));

        String task = knowledgeBaseService.createConvertTtToKnowledgeTask(param, ttInfoDTO);
        log.info("#CardCallBackServiceImpl.processGroupChatCallback# convertTtToKnowledgeTask: {}", task);
        dxGroupChatService.sendMessageToGroup(Long.parseLong(groupId), "开始处理，请等待大象通知");
    }

    private void processSingleChatCallback(long rgId, String misId, long uid, String operatorName) throws LlmCorpusException {
        if (rgId < 1) {
            log.info("#CardCallBackServiceImpl.processSingleChatCallback# rgId is invalid");
            dxGroupChatService.sendMessageToSingle(uid, "非调用人，无需点击卡片");
            return;
        }
        ConvertTtToKnowledgeParam param = new ConvertTtToKnowledgeParam();
        // 生成UUID并获取其字符串形式
        String uuid = UUID.randomUUID().toString();

        // 提取UUID的前8位
        String uuidPrefix = uuid.substring(0, 8);
        String ttId = "S" + uuidPrefix;
        param.setTtId(ttId);
        param.setCreatorDxUserId(uid);
        param.setRgId(rgId);
        param.setDxGroupId(-1L);
        param.setMisId(misId);
        param.setCreatorUserName(operatorName);
        param.setPlatformId(ConvertTaskPlatformId.DX_SINGLE_CHAT);

        TTInfoDTO ttInfoDTO = new TTInfoDTO();
        ttInfoDTO.setTicketId(ttId);
        ttInfoDTO.setRgId(rgId);
        ttInfoDTO.setGroupId(-1L);

        ttInfoDTO.setName(operatorName + SINGLE_CHAT_NAME);

        String task = knowledgeBaseService.createConvertTtToKnowledgeTask(param, ttInfoDTO);
        log.info("#CardCallBackServiceImpl.processSingleChatCallback# convertTtToKnowledgeTask: {}", task);
        dxGroupChatService.sendMessageToSingle(uid, "开始处理，请等待大象通知");
    }

    @Override
    public PullCardResp pullCard(PullCardReq pullCardReq) throws TException {
        return null;
    }
}
