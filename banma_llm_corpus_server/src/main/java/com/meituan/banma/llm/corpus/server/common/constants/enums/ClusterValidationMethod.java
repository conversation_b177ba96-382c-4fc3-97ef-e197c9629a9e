package com.meituan.banma.llm.corpus.server.common.constants.enums;

/**
 * 聚类有效性评估方法枚举
 * 用于K-means聚类算法中自动确定最佳聚类数量
 */
public enum ClusterValidationMethod {
    /**
     * 轮廓系数法（Silhouette Coefficient）
     * 特性：衡量样本与自身所在簇的相似度与其他簇的差异度
     * 适用：形状规则、密度均匀的球状簇，适合大多数普通场景
     * 优势：直观、计算简单，对簇形状和密度不均匀有一定容忍度
     * 评估标准：值越大越好，范围为[-1, 1]
     */
    SILHOUETTE,

    /**
     * Davies-Bouldin指数法
     * 特性：基于簇内分散度与簇间距离的比率
     * 适用：密度相似但形状多变的簇，适合检测紧密且分离良好的簇
     * 优势：对噪声不敏感，计算效率高
     * 评估标准：值越小越好，最小值为0
     */
    DB_INDEX,

    /**
     * Calinski-Harabasz指数法（又称方差比准则）
     * 特性：评估簇间离散度与簇内离散度的比率
     * 适用：高密度、球状且大小相近的簇，适合高维数据
     * 优势：计算快速，特别适合大规模数据集
     * 评估标准：值越大越好
     */
    CH_INDEX,

    /**
     * Gap统计量法
     * 特性：比较观察数据与随机参考分布的聚类分散度差异
     * 适用：非球形、不规则形状簇，当簇数量不明确时效果最佳
     * 优势：对真实结构的敏感性高，能发现自然聚类
     * 评估标准：寻找最大正Gap值
     */
    GAP_STATISTIC,

    /**
     * 结合轮廓系数和DB指数的组合评估法
     * 特性：计算两种方法的平均排名
     * 适用：当单一评估方法可能不可靠时，提供更平衡的结果
     * 优势：综合考虑不同评估指标，降低单一指标偏差
     * 评估标准：组合多个指标的排名，选择平均排名最佳的K值
     */
    COMBINED,

    /**
     * 综合评估法，结合多种方法选取最佳结果
     * 特性：采用投票机制，集成四种评估方法的结果
     * 适用：复杂、多样化的数据集，需要保守稳健的结果
     * 优势：最大程度降低误判风险，提供稳健可靠的估计
     * 评估标准：根据多个方法的投票结果确定最佳K值
     */
    COMPREHENSIVE;
} 