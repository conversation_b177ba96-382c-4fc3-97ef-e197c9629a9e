package com.meituan.banma.llm.corpus.server.common.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

@Data
@ToString
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CreateQuestionSummaryLlmTaskParam {
    private Long dxGroupId;
    private Long startTime;
    private Long endTime;
    private Long monitoringGroupId;
    private List<String> monitoringGroupOwner;
    private LlmDxGroupMonitoringTaskMessageDTO messageDTO;
}
