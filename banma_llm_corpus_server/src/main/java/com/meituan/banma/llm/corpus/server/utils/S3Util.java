package com.meituan.banma.llm.corpus.server.utils;

import com.amazonaws.AmazonClientException;
import com.amazonaws.AmazonServiceException;
import com.amazonaws.ClientConfiguration;
import com.amazonaws.HttpMethod;
import com.amazonaws.Protocol;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3KmsClient;
import com.amazonaws.services.s3.S3ClientOptions;
import com.amazonaws.services.s3.model.GeneratePresignedUrlRequest;
import com.meituan.banma.llm.corpus.server.common.MtConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.File;
import java.net.URL;
import java.util.Date;

/**
 * S3工具类
 */
@Slf4j
@Component
public class S3Util {

    // S3客户端
    private AmazonS3 s3client;

    private String bucketName;
    private int urlExpirationMinutes;
    private String appKey;
    private String endpoint;
    
    private static S3Util instance;

    @Resource
    private MtConfigService mtConfigService;

    private static MtConfigService staticMtConfigService;
    
    @PostConstruct
    public void init() {
        try {
            // 从配置服务获取S3相关配置
            this.bucketName = mtConfigService.getTicketRangeQueryConfig().getS3BucketName();
            this.urlExpirationMinutes = mtConfigService.getTicketRangeQueryConfig().getS3UrlExpirationMinutes();
            this.appKey = mtConfigService.getTicketRangeQueryConfig().getS3AppKey();
            this.endpoint = mtConfigService.getTicketRangeQueryConfig().getS3Endpoint();
            
            // 创建S3客户端连接
            this.s3client = createAmazonS3Conn(appKey, endpoint);
            
            // 初始化静态实例
            instance = this;
            instance.s3client = this.s3client;
            instance.bucketName = this.bucketName;
            instance.urlExpirationMinutes = this.urlExpirationMinutes;
            staticMtConfigService = this.mtConfigService;

            log.info("S3客户端初始化成功: appKey={}, endpoint={}, bucketName={}", appKey, endpoint, bucketName);
        } catch (Exception e) {
            log.error("S3客户端初始化失败: appKey={}, endpoint={}, message={}", appKey, endpoint, e.getMessage(), e);
        }
    }
    
    /**
     * 创建S3客户端连接
     * 
     * @param appkey 部署机器对应的appkey
     * @param hostname MSS的endpoint服务地址
     * @return AmazonS3客户端
     * @throws Exception 获取S3凭证失败异常
     */
    private static AmazonS3 createAmazonS3Conn(String appkey, String hostname) throws Exception {
        ClientConfiguration configuration = new ClientConfiguration();
        // 默认协议为HTTPS
        configuration.setProtocol(Protocol.HTTPS);

        // 设置客户端生成的http请求格式，目前只支持path type的格式
        S3ClientOptions s3ClientOptions = new S3ClientOptions();
        // 目前s3只支持path style,下面选项需要设置为true
        s3ClientOptions.setPathStyleAccess(true);

        // 生成云存储api client
        AmazonS3 s3client = new AmazonS3KmsClient(hostname, appkey, configuration, s3ClientOptions);
        return s3client;
    }

    /**
     * 上传指定文件到指定的Bucket
     *
     * @param bucketName S3存储桶名称
     * @param objectName S3对象键（路径）
     * @param filePath 文件路径
     */
    public static void putObjectFileExample(String bucketName, String objectName, String filePath) {
        // 检查实例是否初始化，如果未初始化则重新初始化
        checkAndReinitialize();
        
        try {
            if (instance == null || instance.s3client == null) {
                log.error("S3客户端未初始化或初始化失败，无法上传文件");
                return;
            }
            
            instance.s3client.putObject(bucketName, objectName, new File(filePath));
            log.info("成功上传文件到S3: bucketName={}, objectName={}, filePath={}", bucketName, objectName, filePath);
        } catch (AmazonServiceException ase) {
            log.error("S3服务异常: bucketName={}, objectName={}, message={}, statusCode={}, errorCode={}, errorType={}",
                    bucketName, objectName, ase.getMessage(), ase.getStatusCode(), ase.getErrorCode(), ase.getErrorType());
        } catch (AmazonClientException ace) {
            log.error("S3客户端异常: bucketName={}, objectName={}, message={}", bucketName, objectName, ace.getMessage());
        } catch (Exception e) {
            log.error("S3上传文件未预期异常: bucketName={}, objectName={}, message={}", bucketName, objectName, e.getMessage(), e);
        }
    }
    
    /**
     * 生成指定Bucket对象的预签名下载URL
     *
     * @param bucketName S3存储桶名称
     * @param objectName S3对象键（路径）
     * @return 预签名URL
     */
    public static String presignUrlExample(String bucketName, String objectName) {
        // 检查实例是否初始化，如果未初始化则重新初始化
        checkAndReinitialize();
        
        try {
            if (instance == null || instance.s3client == null || staticMtConfigService == null) {
                log.error("S3客户端或配置服务未初始化或初始化失败，无法生成预签名URL");
                return null;
            }
            
            Long expirationSeconds = staticMtConfigService.getTicketRangeQueryConfig().getS3UrlExpirationSeconds();

            Date expiration = new Date();
            long milliSeconds = expiration.getTime();
            milliSeconds += expirationSeconds;
            expiration.setTime(milliSeconds);

            // 指定授权的bucket和object
            GeneratePresignedUrlRequest generatePresignedUrlRequest =
                    new GeneratePresignedUrlRequest(bucketName, objectName);
            // 指定授权的请求类型
            generatePresignedUrlRequest.setMethod(HttpMethod.GET);
            generatePresignedUrlRequest.setExpiration(expiration);

            URL url = instance.s3client.generatePresignedUrl(generatePresignedUrlRequest);
            return url.toString();
        } catch (AmazonServiceException ase) {
            // 存储服务端处理异常
            log.error("生成预签名URL异常: bucketName={}, objectName={}, message={}, statusCode={}, errorCode={}",
                    bucketName, objectName, ase.getMessage(), ase.getStatusCode(), ase.getErrorCode());
            return null;
        } catch (AmazonClientException ace) {
            // 客户端处理异常
            log.error("生成预签名URL客户端异常: bucketName={}, objectName={}, message={}", 
                    bucketName, objectName, ace.getMessage());
            return null;
        } catch (Exception e) {
            log.error("生成预签名URL未预期异常: bucketName={}, objectName={}, message={}", 
                    bucketName, objectName, e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * 检查实例是否初始化，如果未初始化则重新初始化
     */
    private static synchronized void checkAndReinitialize() {
        if (instance == null || instance.s3client == null || staticMtConfigService == null) {
            log.warn("S3Util实例未初始化或初始化失败，尝试重新初始化");
            try {
                // 通过Spring容器获取S3Util实例
                S3Util s3Util = SpringContextHolder.getBean(S3Util.class);
                if (s3Util != null) {
                    // 手动调用init方法
                    s3Util.init();
                    log.info("S3Util重新初始化成功");
                } else {
                    log.error("无法从Spring容器获取S3Util实例");
                }
            } catch (Exception e) {
                log.error("S3Util重新初始化失败: {}", e.getMessage(), e);
            }
        }
    }
} 