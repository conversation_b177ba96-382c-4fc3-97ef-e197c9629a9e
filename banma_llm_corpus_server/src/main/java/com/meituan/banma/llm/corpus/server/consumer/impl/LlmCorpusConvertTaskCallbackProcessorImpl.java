package com.meituan.banma.llm.corpus.server.consumer.impl;

import com.meituan.banma.llm.corpus.server.common.MtConfigService;
import com.meituan.banma.llm.corpus.server.common.constants.enums.ConvertTaskPlatformId;
import com.meituan.banma.llm.corpus.server.common.constants.enums.ModelOutputTaskStatusEnum;
import com.meituan.banma.llm.corpus.server.common.domain.dto.LlmCorpusConvertTaskMessageDTO;
import com.meituan.banma.llm.corpus.server.consumer.MessageProcessor;
import com.meituan.banma.llm.corpus.server.service.IDxGroupChatService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service("llmCorpusConvertTaskCallbackProcessorImpl")
public class LlmCorpusConvertTaskCallbackProcessorImpl implements MessageProcessor<LlmCorpusConvertTaskMessageDTO> {

    @Resource
    private IDxGroupChatService dxGroupChatService;

    @Resource
    private MtConfigService mtConfigService;

    @Override
    public void process(LlmCorpusConvertTaskMessageDTO callbackMessage) throws Exception {
        if (callbackMessage == null) {
            log.warn("#LlmCorpusConvertTaskCallbackProcessorImpl.process#warn, message is null");
            return;
        }
        // 处理成功，发群信息
        if (callbackMessage.getTaskStatus() == ModelOutputTaskStatusEnum.SUCCESS.getCode()) {
            // 拼接url，review页面规则：https://dos.banma.test.sankuai.com/api/llm/corpus/review/{taskId}
            String url = mtConfigService.getReviewUrl() + callbackMessage.getTaskId();
            String msg = mtConfigService.getTicketPreprocessMessageTemplate(url);
            if(callbackMessage.getPlatformId() == ConvertTaskPlatformId.DX_SINGLE_CHAT.getCode()){
                dxGroupChatService.sendMessageToSingle(callbackMessage.getCreatorUserDxId(), msg);
            }else {
                dxGroupChatService.sendMessageToGroupWithAt(callbackMessage.getDxGroupId(), callbackMessage.getCreatorUserDxId(),
                        callbackMessage.getCreatorUserName(), msg);
            }
        } else if (callbackMessage.getTaskStatus() == ModelOutputTaskStatusEnum.FAIL.getCode()) {
            String msg = "预处理失败，请人工介入处理。[%s]";
            if (StringUtils.isNotBlank(callbackMessage.getTaskMessage())) {
                msg = String.format(msg, callbackMessage.getTaskMessage());
            }
            if(callbackMessage.getPlatformId() == ConvertTaskPlatformId.DX_SINGLE_CHAT.getCode()){
                dxGroupChatService.sendMessageToSingle(callbackMessage.getCreatorUserDxId(), msg);
            }else {
                dxGroupChatService.sendMessageToGroupWithAt(callbackMessage.getDxGroupId(), callbackMessage.getCreatorUserDxId(),
                        callbackMessage.getCreatorUserName(), msg);
            }
        }
    }
}
