package com.meituan.banma.llm.corpus.server.service;

import java.util.Map;

/**
 * 文档刷新服务接口
 * 提供文档批量刷新相关功能
 */
public interface IDocumentRefreshService {

    /**
     * 批量刷新所有记录的文档
     * 遍历所有记录并调用Friday RPC服务刷新文档
     *
     * @return 操作结果Map，包含总体成功/失败状态和所有失败记录的详细数据
     */
    Map<String, Object> refreshAllDocuments();

    /**
     * 定时任务：批量刷新所有文档
     * 遍历所有记录并调用Friday RPC服务刷新文档
     * 执行频率由Crane调度系统控制
     */
    void refreshAllDocumentsTask();
} 