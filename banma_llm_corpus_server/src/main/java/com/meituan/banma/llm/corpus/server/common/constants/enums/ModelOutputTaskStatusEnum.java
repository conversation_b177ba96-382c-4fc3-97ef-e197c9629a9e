package com.meituan.banma.llm.corpus.server.common.constants.enums;

import lombok.Getter;

@Getter
public enum ModelOutputTaskStatusEnum {
    /**
     *
     */
    PROCESSING(0, "进行中"),
    SUCCESS(1, "成功"),
    FAIL(2, "失败"),
    ;
    private final int code;
    private final String desc;
    ModelOutputTaskStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    public static ModelOutputTaskStatusEnum fromCode(int code) {
        for (ModelOutputTaskStatusEnum status : ModelOutputTaskStatusEnum.values()) {
            if (status.code == code) {
                return status;
            }
        }
        return null;
    }
}
