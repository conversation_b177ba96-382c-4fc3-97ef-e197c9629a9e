package com.meituan.banma.llm.corpus.server.dal.entity;


import lombok.Data;
import java.io.Serializable;

/**
 * 配送知识AI机器人租户下机器人会话数据实体类
 */
@Data
public class FridayBotConversationViewEntity{

    private static final long serialVersionUID = 1L;

    /**
     * 唯一键
     */
    private String recordId;

    /**
     * 对话ID
     */
    private String conversationId;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 用户类型
     */
    private String userType;

    /**
     * 应用id
     */
    private String appId;

    /**
     * 请求唯一标识
     */
    private String requestId;

    /**
     * 唯一消息id
     */
    private String messageId;

    /**
     * 关联的上一条消息id
     */
    private String parentMessageId;

    /**
     * 角色
     */
    private String role;

    /**
     * 生成类型
     */
    private String generateType;

    /**
     * 内容
     */
    private String message;

    /**
     * 标识该消息是否正常产出
     */
    private String status;

    /**
     * 添加时间
     */
    private String addTime;

    /**
     * 更新时间
     */
    private String updateTime;

    /**
     * 标识该消息是否正常产出
     */
    private String recallInfo;

    /**
     * 助手的推荐动作，如推荐问
     */
    private String assistantRecommendation;

    /**
     * 对话名称
     */
    private String conversationName;

    /**
     * 1：该会话是一个新的会话；0：非新会话
     */
    private Integer isNew;

    /**
     * 会话是否被删除 0未删除 1已删除
     */
    private Integer deleted;

    /**
     * 请求来源
     */
    private String accessChannel;

    /**
     * 该会话最新一次的聊天时间
     */
    private String lastChatTime;

    /**
     * record添加时间
     */
    private String recordAddTime;

    /**
     * record更新时间
     */
    private String recordUpdateTime;

    /**
     * 日期分区字段，格式为datekey(yyyymmdd)
     */
    private String dt;
}
