package com.meituan.banma.llm.corpus.server.dal.mapper;

import com.meituan.banma.llm.corpus.server.dal.entity.DocumentUploadRetryEntity;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 文档上传重试任务Mapper
 */
@Mapper
public interface DocumentUploadRetryMapper {
    
    /**
     * 插入重试任务
     */
    @Insert("INSERT INTO document_upload_retry(rg_id, space_id, url, name, retry_count, fail_reason, mis_id, auto_update, status, create_time, update_time) " +
            "VALUES(#{rgId}, #{spaceId}, #{url}, #{name}, #{retryCount}, #{failReason}, #{misId}, #{autoUpdate}, #{status}, NOW(), NOW())")
    int insertRetryTask(DocumentUploadRetryEntity entity);
    
    /**
     * 更新重试任务状态
     */
    @Update("UPDATE document_upload_retry SET status = #{status}, retry_count = #{retryCount}, update_time = NOW(), fail_reason = #{failReason} WHERE id = #{id}")
    int updateTaskStatus(DocumentUploadRetryEntity entity);
    
    /**
     * 查询待处理的任务
     */
    @Select("SELECT * FROM document_upload_retry WHERE status = 0 ORDER BY create_time ASC LIMIT #{limit}")
    List<DocumentUploadRetryEntity> findPendingTasks(int limit);
    
    /**
     * 查询处理中但超时的任务
     */
    @Select("SELECT * FROM document_upload_retry WHERE status = 1 AND update_time < DATE_SUB(NOW(), INTERVAL 30 MINUTE) ORDER BY update_time ASC")
    List<DocumentUploadRetryEntity> findStuckTasks();
    
    /**
     * 查询特定任务
     */
    @Select("SELECT * FROM document_upload_retry WHERE rg_id = #{rgId} AND space_id = #{spaceId} AND url = #{url}")
    DocumentUploadRetryEntity findTask(long rgId, String spaceId, String url);
} 