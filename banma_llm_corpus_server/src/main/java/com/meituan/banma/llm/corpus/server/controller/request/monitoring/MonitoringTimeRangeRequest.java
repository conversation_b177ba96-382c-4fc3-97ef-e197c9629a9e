package com.meituan.banma.llm.corpus.server.controller.request.monitoring;

import lombok.Data;
import java.util.List;

/**
 * 监控时间范围请求参数
 */
@Data
public class MonitoringTimeRangeRequest {
    /**
     * 开始时间（毫秒时间戳）
     */
    private Long startTime;
    
    /**
     * 结束时间（毫秒时间戳）
     */
    private Long endTime;
    
    /**
     * 当前页码
     */
    private Integer pageNum = 1;
    
    /**
     * 每页记录数
     */
    private Integer pageSize = 10;
    
    /**
     * 监控组ID列表
     */
    private List<Long> monitorGroupIds;
} 