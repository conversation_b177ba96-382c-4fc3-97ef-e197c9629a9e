package com.meituan.banma.llm.corpus.server.utils;

import com.alibaba.fastjson.JSONObject;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;
import java.util.TimeZone;

/**
 * 认证工具类
 * 提供认证签名、日期格式化、响应解析等工具方法
 */
public class AuthUtil {
    private static final Logger log = LoggerFactory.getLogger(AuthUtil.class);

    private AuthUtil() {
        // 工具类禁止实例化
    }

    /**
     * 构建SSO请求头
     * 
     * @param method HTTP方法
     * @param uri 请求路径
     * @param clientId 客户端ID
     * @param secret 客户端密钥
     * @return 包含签名信息的请求头Map
     */
    public static Map<String, String> buildSsoHeaders(String method, String uri, String clientId, String secret) {
        String[] headersArray = getSignedHeaders(method, uri, clientId, secret);
        Map<String, String> headers = new HashMap<>();
        for (int i = 0; i < headersArray.length; i += 2) {
            headers.put(headersArray[i], headersArray[i + 1]);
        }
        return headers;
    }
    
    /**
     * 解析API响应
     * 
     * @param responseBody 响应体
     * @param key 需要提取的数据字段，如果为null则只检查响应状态
     * @return 提取的数据值，如果key为null则返回null
     * @throws RuntimeException 解析失败或响应状态码不为200时抛出异常
     */
    public static String parseResponse(String responseBody, String key) {
        if (StringUtils.isBlank(responseBody)) {
            log.error("解析响应失败: 响应为空");
            throw new RuntimeException("响应为空");
        }
        
        try {
            JSONObject responseObject = JSONObject.parseObject(responseBody);
            log.debug("解析响应: {}", responseBody);

            if (responseObject.getIntValue("code") == 200) {
                if (key != null && responseObject.containsKey("data")) {
                    JSONObject data = responseObject.getJSONObject("data");
                    if (data != null && data.containsKey(key)) {
                        String result = data.getString(key);
                        log.debug("解析结果: {}", result);
                        return result;
                    }
                    log.error("解析响应失败: 数据中不包含字段 {}", key);
                    throw new RuntimeException("解析失败: 数据中不包含字段 " + key);
                }
                return null;
            } else {
                String errorMessage = responseObject.getString("message");
                log.error("请求失败: {}", errorMessage);
                throw new RuntimeException("请求失败: " + errorMessage);
            }
        } catch (Exception e) {
            log.error("解析响应失败", e);
            throw new RuntimeException("解析响应失败", e);
        }
    }
    
    /**
     * 获取签名请求头
     * 
     * @param method HTTP方法
     * @param uri 请求路径
     * @param clientId 客户端ID
     * @param secret 客户端密钥
     * @return 包含签名信息的请求头数组
     */
    public static String[] getSignedHeaders(String method, String uri, String clientId, String secret) {
        if (method == null || uri == null) {
            return new String[]{};
        }
        String date = getAuthDate(new Date());
        method = method.toUpperCase();
        String authorization = getAuthorization(uri, method, date, clientId, secret);
        return new String[]{"Content-Type", "application/json;charset=UTF-8", "Authorization", authorization, "Date", date};
    }

    /**
     * 获取认证日期格式
     * 
     * @param date 日期
     * @return 格式化后的日期字符串
     */
    public static String getAuthDate(Date date) {
        DateFormat df = new SimpleDateFormat("EEE, dd MMM yyyy HH:mm:ss z", Locale.ENGLISH);
        df.setTimeZone(TimeZone.getTimeZone("GMT"));
        return df.format(date);
    }

    /**
     * 获取认证头信息
     * 
     * @param uri 请求路径
     * @param method HTTP方法
     * @param date 日期
     * @param clientId 客户端ID
     * @param secret 客户端密钥
     * @return 认证头值
     */
    public static String getAuthorization(String uri, String method, String date, String clientId, String secret) {
        String stringToSign = method + " " + uri + "\n" + date;
        String signature = getSignature(stringToSign, secret);
        String authoriation = "MWS" + " " + clientId + ":" + signature;
        return authoriation;
    }

    /**
     * 生成签名
     * 
     * @param data 待签名数据
     * @param secret 密钥
     * @return Base64编码的签名
     * @throws RuntimeException 签名失败时抛出异常
     */
    public static String getSignature(String data, String secret) {
        String result;
        try {
            SecretKeySpec signingKey = new SecretKeySpec(secret.getBytes(), "HmacSHA1");
            Mac mac = Mac.getInstance("HmacSHA1");
            mac.init(signingKey);
            byte[] rawHmac = mac.doFinal(data.getBytes());
            result = Base64.encodeBase64String(rawHmac);
            return result;
        } catch (Exception e) {
            log.error("生成签名失败", e);
            throw new RuntimeException("Failed to generate HMAC : " + e.getMessage());
        }
    }
} 