package com.meituan.banma.llm.corpus.server.common.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class FridayConversationRecallInfoDTO {
    private String traceId;
    private ChatRequest chatRequest;
    private WorkflowRunOutput workflowRunOutput;
    private List<FunctionExecuteDetails> functionExecuteDetails;
    private boolean functionCallingSupport;
    private RecallRequest recallRequest;
    private String finishReason;
    private LlmHitDetail llmHitDetail;
    private RecallResponse recallResponse;
    private LlmExecuteDetails llmExecuteDetails;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ChatRequest {
        private String model;
        private double temperature;
        private boolean stream;
        private List<BaseFunction> baseFunctions;
        private String requestId;
        private String conversationId;
        private boolean originalStream;
        private String appId;
        private ModelDefaultParams modelDefaultParams;
        private String serviceTypeEnum;
        private String systemPrompt;
        private String userSystemPrompt;
        private List<Content> contents;
        private List<Object> historyMessages;
        private List<DocRecallItem> docRecallItems;
        private List<FaqRecallItem> faqRecallItems;
        private List<FunctionRecallItem> functionRecallItems;
        private List<WorkflowRecallItem> workflowRecallItems;
        private RecallMetaItem recallMetaItem;
        private RichTextBacktrackMap richTextBacktrackMap;
        private boolean debug;
        private boolean security;
        private boolean resourceEnabled;
        private RecallRequest recallRequest;
        private RecallResponse recallResponse;
        private LlmExecuteDetails llmExecuteDetails;
        private int functionCallCount;
        private int invalidFunctionCallCount;
        private SysContextParams sysContextParams;
        private LlmAdoptedDetails llmAdoptedDetails;
        private int chatTurns;
        private int estimatedTokenConsumption;
        private List<Message> rememberedChatHistory;
        private int remainingToken;
        private FunctionRTConfig functionRTConfig;
        private ChatWorkflowConfig chatWorkflowConfig;
        private RecallConfig recallConfig;
        private int historyTurnNumber;
        private AssembleResult assembleResult;
        private boolean workflowStepSwicth;
        private long oldTokenTime;
        private List<ToolExecuteDetail> toolExecuteDetails;
        private int timeout;
        private String accessChannel;
        private UserInfo userInfo;
        private int max_tokens;
        private List<Tool> tools;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class WorkflowRunOutput {
        private boolean verbose;
        private List<WorkflowRecallItem> workflowRecallItems;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class WorkflowRecallItem {
        private String id;
        private String name;
        private String description;
        private boolean hit;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class FunctionExecuteDetails {
        private LlmRequest llmRequest;
        private TargetFunction targetFunction;
        private ChatToolCall chatFunctionCall;
        private String functionRequest;
        private String sysContextParam;
        private String businessParams;
        private String functionResponse;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class BaseFunction {
        private String type;
        private String id;
        private String name;
        private String description;
        private List<ApiParam> apiParams;
        private ApiParamValues apiParamValues;
        private List<InvokeParam> invokeParams;
        private boolean directOutput;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class RecallConfig {
        private int configDocTopK;
        private int configFaqTopK;
        private int configFunctionTopK;
        private int configWorkflowTopK;
        private String recallPattern;
        private double similarThreshold;
        private boolean rerankSwitch;
        private boolean recallSug;
        private double docSimilarThreshold;
        private double faqSimilarThreshold;
        private double functionSimilarThreshold;
        private double workflowSimilarThreshold;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ChatWorkflowConfig {
        private List<String> fixedReturnWorkflowIds;
        private boolean recallRequired;
        private List<String> excludeRecallWorkflowIds;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class FunctionRTConfig {
        private List<String> fixedOrderedFunctionIds;
        private List<String> recallFunctionIds;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class AssembleResult {
        private List<Message> messages;
        private List<Tool> tools;
        private List<Faq> faqs;
        private List<DocSegment> docSegments;
        private List<Object> forgetHistory;
        private List<Object> forgetTools;
        private List<Object> forgetFAQs;
        private List<Object> forgetDocSegments;
        private int maxTokens;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class UserInfo {
        private String userId;
        private String userType;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Tool {
        private String type;
        private Function function;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ToolExecuteDetail {
        private LlmRequest llmRequest;
        private TargetFunction targetFunction;
        private ChatToolCall chatToolCall;
        private String functionRequest;
        private String sysContextParam;
        private String businessParams;
        private String functionResponse;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class LlmRequest {
        private String model;
        private List<Message> messages;
        private double temperature;
        private int n;
        private String user;
        private int max_tokens;
        private List<Tool> tools;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class TargetFunction {
        private String type;
        private String id;
        private String name;
        private String description;
        private List<ApiParam> apiParams;
        private ApiParamValues apiParamValues;
        private List<InvokeParam> invokeParams;
        private boolean directOutput;
        private PostProcessor postProcessor;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ChatToolCall {
        private String id;
        private int index;
        private String type;
        private Function function;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Function {
        private String name;
        private Arguments arguments;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Arguments {
        private String routeType;
        private String routeValue;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Message {
        private String role;
        private String content;
        private List<ChatToolCall> tool_calls;
        private String name;
        private String tool_call_id;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ApiParam {
        private String name;
        private String label;
        private String type;
        private String description;
        private boolean required;
        private Object defaultValue;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ApiParamValues {
        private String rpc_method;
        private int rpc_retry_times;
        private String rpc_port;
        private String rpc_app_key;
        private String rpc_serviceName;
        private int rpc_retry_wait_time;
        private String rpc_tracer_context;
        private int rpc_timeout;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class InvokeParam {
        private String name;
        private String label;
        private String type;
        private String description;
        private List<String> enumValues;
        private boolean required;
        private String defaultValue;
        private Object properties;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class PostProcessor {
        private String type;
        private PostProcessorContext context;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class PostProcessorContext {
        private String pattern;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Faq {
        private String question;
        private String answer;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class DocSegment {
        private String content;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ModelDefaultParams {
        private String description;
        private boolean hot;
        private boolean enable;
        private boolean streamSupport;
        private boolean functionCallingSupport;
        private boolean visible;
        private List<String> alwaysVisibleList;
        private double temperature;
        private int maxPromptTokens;
        private int maxGenerateTokens;
        private int minGenerateTokens;
        private int faqRecallCount;
        private int docRecallCount;
        private int functionRecallCount;
        private int workflowRecallCount;
        private String modelType;
        private boolean jsonModeSupport;
        private boolean visionSupport;
        private boolean inferenceOptimSupport;
        private boolean toolCallSupport;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class RecallRequest {
        private DocQaRequest docQaRequest;
        private FaqQaRequest faqQaRequest;
        private FunctionQaRequest functionQaRequest;
        private WorkflowQaRequest workflowQaRequest;
        private BusinessParam businessParam;
        private AdvancedConfig advancedConfig;
        private boolean grayEmbeddingScale;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class LlmAdoptedDetails {
        private List<Record> records;
        private String lastLLMReturn;
        private String reasoningContent;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SysContextParams {
        private String userId;
        private String userType;
        private String appId;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class LlmExecuteDetails {
        private List<ChatCompletionRequest> chatCompletionRequests;
        private Usage usage;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Content {
        private String type;
        private String text;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class FaqRecallItem {
        private String recallSource;
        private String recallPattern;
        private String rerankModel;
        private double score;
        private String businessName;
        private int position;
        private String questionId;
        private int questionUniqueId;
        private String qaId;
        private String question;
        private String answer;
        private double weight;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class LlmHitDetail {
        private List<Record> records;
        private String lastLLMReturn;
        private String reasoningContent;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class RecallResponse {
        private DocQaResponse docQaResponse;
        private FaqQaResponse faqQaResponse;
        private FunctionQaResponse functionQaResponse;
        private WorkflowQaResponse workflowQaResponse;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class DocQaRequest {
        private List<String> businessNames;
        private String text;
        private int topK;
        private int textPosition;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class FaqQaRequest {
        private List<String> businessNames;
        private String question;
        private int topK;
        private int textPosition;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class FunctionQaRequest {
        private List<String> businessNames;
        private String functionDescription;
        private int topK;
        private List<String> excludeFunctionIds;
        private int textPosition;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class WorkflowQaRequest {
        private List<String> businessNames;
        private String workflowDescription;
        private int topK;
        private List<String> excludeWorkflowIds;
        private int textPosition;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class BusinessParam {
        private String requestId;
        private String appId;
        private String spaceId;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class AdvancedConfig {
        private int configDocTopK;
        private int configFaqTopK;
        private int configFunctionTopK;
        private int configWorkflowTopK;
        private String recallPattern;
        private double similarThreshold;
        private boolean rerankSwitch;
        private boolean recallSug;
        private double docSimilarThreshold;
        private double faqSimilarThreshold;
        private double functionSimilarThreshold;
        private double workflowSimilarThreshold;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Record {
        private int turn;
        private double distance;
        private double importance;
        private RecallItem recallItem;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Usage {
        private int prompt_tokens;
        private int completion_tokens;
        private int total_tokens;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ChatCompletionRequest {
        private String model;
        private List<Message> messages;
        private double temperature;
        private int n;
        private String user;
        private int max_tokens;
        private List<Tool> tools;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class DocQaResponse {
        private String responseCode;
        private String message;
        private List<DocRecallItem> docRecallItems;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class FaqQaResponse {
        private String responseCode;
        private String message;
        private List<FaqRecallItem> faqRecallItems;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class FunctionQaResponse {
        private String responseCode;
        private String message;
        private List<FunctionRecallItem> functionRecallItems;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class WorkflowQaResponse {
        private String responseCode;
        private String message;
        private List<WorkflowRecallItem> workflowRecallItems;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class RecallItem {
        private String recallSource;
        private String recallPattern;
        private String rerankModel;
        private double score;
        private String businessName;
        private int position;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class DocRecallItem extends RecallItem {
        private RichData richData;
        private String textId;
        private String text;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class RichData {
        private RichMaps richMaps;
        private Source source;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class RichMaps {
        private Map<String, Object> data;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Source {
        private String type;
        private Long docId;
        private String docName;
        private List<String> nodeIds;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class FunctionRecallItem extends RecallItem {
        private String functionId;
        private String functionDescription;
        private String functionApi;
        private boolean fromRecall;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class RecallMetaItem {
        private boolean empty;
        private Map<String, RichData> richMappers;
        private Map<String, List<RichData>> ssourceListMap;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class RichTextBacktrackMap {
        private Map<String, String> backtrackMap;
    }
}
