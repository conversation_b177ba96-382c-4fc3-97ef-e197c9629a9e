package com.meituan.banma.llm.corpus.server.common.constants.enums;

import com.meituan.banma.llm.corpus.server.common.MtConfigService;
import com.meituan.banma.llm.corpus.server.common.domain.dto.DeliveryTypeConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 配送方式类型工具类
 * 从配置中心获取配送方式类型配置
 */
@Slf4j
@Component
public class DeliveryTypeEnum {
    
    @Resource
    private MtConfigService mtConfigService;
    
    /**
     * 根据配送方式名称获取类型ID
     *
     * @param name 配送方式名称
     * @return 类型ID，如果没有匹配返回null
     */
    public String getTypeIdByName(String name) {
        if (name == null) {
            return null;
        }
        
        try {
            List<DeliveryTypeConfig> configs = mtConfigService.getDeliveryTypeConfig();
            for (DeliveryTypeConfig config : configs) {
                if (name.equals(config.getName())) {
                    return config.getTypeId();
                }
            }
        } catch (Exception e) {
            log.error("获取配送类型ID异常, name={}", name, e);
        }
        return null;
    }
    
    /**
     * 根据类型ID获取配送方式名称
     *
     * @param typeId 类型ID
     * @return 配送方式名称，如果没有匹配返回null
     */
    public String getNameByTypeId(String typeId) {
        if (typeId == null) {
            return null;
        }
        
        try {
            List<DeliveryTypeConfig> configs = mtConfigService.getDeliveryTypeConfig();
            for (DeliveryTypeConfig config : configs) {
                if (typeId.equals(config.getTypeId())) {
                    return config.getName();
                }
            }
        } catch (Exception e) {
            log.error("获取配送类型名称异常, typeId={}", typeId, e);
        }
        return null;
    }
    
    /**
     * 获取所有配送类型
     * 
     * @return 配送类型列表
     */
    public List<DeliveryTypeConfig> getAllDeliveryTypes() {
        return mtConfigService.getDeliveryTypeConfig();
    }
} 