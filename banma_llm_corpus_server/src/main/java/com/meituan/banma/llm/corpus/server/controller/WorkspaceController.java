package com.meituan.banma.llm.corpus.server.controller;

import com.meituan.banma.llm.corpus.api.exception.LlmCorpusException;
import com.meituan.banma.llm.corpus.server.common.CommonResult;
import com.meituan.banma.llm.corpus.server.common.domain.dto.WorkspaceValidateRequest;
import com.meituan.banma.llm.corpus.server.dal.entity.RgDatasetDocumentEntity;
import com.meituan.banma.llm.corpus.server.service.IReviewService;
import com.meituan.banma.llm.corpus.server.service.WorkspaceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 工作空间管理控制器
 * 提供自定义工作空间相关的接口
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/workspace")
@Slf4j
public class WorkspaceController {
    
    @Resource
    private WorkspaceService workspaceService;
    
    @Resource
    private IReviewService reviewService;
    
    /**
     * 测试验证工作空间配置是否有效
     * 此接口用于测试用户提供的工作空间参数是否可用
     * 
     * @param request 工作空间验证请求
     * @return 验证结果
     */
    @PostMapping("/test/validate")
    public Object testValidateWorkspace(@RequestBody WorkspaceValidateRequest request) {
        
        log.info("#testValidateWorkspace 开始验证工作空间配置: rgId={}, spaceId={}", request.getRgId(), request.getSpaceId());

        
        try {
            // 调用服务验证工作空间配置
            boolean isValid = workspaceService.validateWorkspaceAddCondition(
                    request.getRgId(), request.getSpaceId(), request.getAccessKey(), request.getAppSecret(), request.getSpaceName());
            
            // 构建响应
            Map<String, Object> result = new HashMap<>();
            result.put("valid", isValid);
            
            if (isValid) {
                result.put("message", "工作空间配置验证通过");
            } else {
                result.put("message", "工作空间配置验证失败，请检查配置信息");
            }
            
            log.info("#testValidateWorkspace 工作空间配置验证结果: rgId={}, spaceId={}, isValid={}", 
                    request.getRgId(), request.getSpaceId(), isValid);
            
            return CommonResult.success(result);
        } catch (LlmCorpusException e) {
            log.warn("#testValidateWorkspace 验证工作空间配置异常: rgId={}, spaceId={}, code={}, message={}", 
                    request.getRgId(), request.getSpaceId(), e.getCode(), e.getMessage());
            return CommonResult.error(String.format("验证工作空间失败，异常原因=%s", e.getMessage()));
        } catch (Exception e) {
            log.error("#testValidateWorkspace 验证工作空间配置失败: rgId={}, spaceId={}", 
                    request.getRgId(), request.getSpaceId(), e);
            return CommonResult.error(String.format("执行失败，异常原因=%s", e.getMessage()));
        }
    }
    
    /**
     * 根据值班组ID查询工作空间配置
     * 
     * @param rgId 值班组ID
     * @return 工作空间配置信息
     */
    @GetMapping("/config")
    public Object getWorkspaceConfig(@RequestParam("rgId") Long rgId) {
        log.info("#getWorkspaceConfig 开始查询工作空间配置: rgId={}", rgId);

        try {
            // 调用服务查询工作空间配置
            List<RgDatasetDocumentEntity> documentList = reviewService.findDatasetDocumentByRgId(rgId);
            
            log.info("#getWorkspaceConfig 工作空间配置查询成功: rgId={}, 记录数={}", 
                    rgId, documentList.size());
            
            // 出于安全考虑，不返回敏感信息
            documentList.forEach(doc -> {
                doc.setAccessKey(null);
                doc.setAppSecret(null);
            });
            
            return CommonResult.success(documentList);
        } catch (LlmCorpusException e) {
            log.warn("#getWorkspaceConfig 查询工作空间配置异常: rgId={}, code={}, message={}", 
                    rgId, e.getCode(), e.getMessage());
            return CommonResult.error(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("#getWorkspaceConfig 查询工作空间配置失败: rgId={}", rgId, e);
            return CommonResult.error(String.format("执行失败，异常原因=%s", e.getMessage()));
        }
    }

    /**
     * 删除工作空间
     * 此接口用于删除指定值班组下的指定工作空间及其所有文档
     *
     * @param rgId 值班组ID
     * @param spaceId 工作空间ID
     * @param misId 用户ID
     * @return 删除结果
     */
    @PostMapping("/delete")
    public Object deleteWorkspace(@RequestParam("rgId") Long rgId, 
                                 @RequestParam("spaceId") String spaceId,
                                 @RequestParam("misId") String misId) {
        
        log.info("#deleteWorkspace 开始删除工作空间: rgId={}, spaceId={}, misId={}", rgId, spaceId, misId);

        // 参数校验
        if (rgId == null || rgId <= 0) {
            log.warn("#deleteWorkspace 参数错误: rgId无效={}", rgId);
            return CommonResult.error("参数错误，值班组ID不能为空且必须大于0");
        }

        if (spaceId == null || spaceId.trim().isEmpty()) {
            log.warn("#deleteWorkspace 参数错误: spaceId为空");
            return CommonResult.error("参数错误，工作空间ID不能为空");
        }

        if (misId == null || misId.trim().isEmpty()) {
            log.warn("#deleteWorkspace 参数错误: misId为空");
            return CommonResult.error("参数错误，用户ID不能为空");
        }

        try {
            // 调用服务删除工作空间
            boolean isSuccess = workspaceService.deleteWorkspace(rgId, spaceId, misId);
            
            // 构建响应
            Map<String, Object> result = new HashMap<>();
            result.put("success", isSuccess);
            
            if (isSuccess) {
                result.put("message", "工作空间删除成功");
                log.info("#deleteWorkspace 工作空间删除成功: rgId={}, spaceId={}", rgId, spaceId);
            } else {
                result.put("message", "工作空间删除失败");
                log.warn("#deleteWorkspace 工作空间删除失败: rgId={}, spaceId={}", rgId, spaceId);
            }
            
            return CommonResult.success(result);
        } catch (LlmCorpusException e) {
            log.warn("#deleteWorkspace 删除工作空间异常: rgId={}, spaceId={}, code={}, message={}", 
                    rgId, spaceId, e.getCode(), e.getMessage());
            return CommonResult.error(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("#deleteWorkspace 删除工作空间失败: rgId={}, spaceId={}", rgId, spaceId, e);
            return CommonResult.error(String.format("执行失败，异常原因=%s", e.getMessage()));
        }
    }

    /**
     * 检查默认工作空间权限
     * 此接口用于检查用户是否具有默认工作空间的权限
     * 
     * @param modifier 用户ID
     * @return 权限检查结果
     */
    @GetMapping("/defaultPermission")
    public Object checkDefaultWorkspacePermission(@RequestParam("modifier") String modifier) {
        // 参数校验
        if (modifier == null || modifier.trim().isEmpty()) {
            log.warn("#checkDefaultWorkspacePermission 参数错误: modifier为空");
            return CommonResult.error("参数错误，用户ID不能为空");
        }

        try {
            // 调用服务检查默认工作空间权限
            boolean hasPermission = workspaceService.checkDefaultWorkspacePermission(modifier);
            
            // 构建响应
            Map<String, Object> result = new HashMap<>();
            result.put("hasPermission", hasPermission);
            
            if (hasPermission) {
                result.put("message", "用户具有默认工作空间权限");
                log.info("#checkDefaultWorkspacePermission 用户具有默认工作空间权限: modifier={}", modifier);
            } else {
                result.put("message", "用户不具有默认工作空间权限");
                log.warn("#checkDefaultWorkspacePermission 用户不具有默认工作空间权限: modifier={}", modifier);
            }
            
            return CommonResult.success(result);
        } catch (Exception e) {
            log.error("#checkDefaultWorkspacePermission 检查默认工作空间权限失败: modifier={}", modifier, e);
            return CommonResult.error(String.format("执行失败，异常原因=%s", e.getMessage()));
        }
    }

    /**
     * 检查该值班组是否具有自定义空间
     * 如果用户没有默认工作空间的权限，并且该值班组除了默认工作空间没有其他工作空间，则认为无权限
     * 
     * @param modifier 用户ID
     * @param rgId 值班组ID
     * @return 权限检查结果
     */
    @GetMapping("/checkPermission")
    public Object checkUserRgPermission(@RequestParam("modifier") String modifier, 
                                     @RequestParam("rgId") Long rgId) {
        // 参数校验
        if (modifier == null || modifier.trim().isEmpty()) {
            log.warn("#checkUserRgPermission 参数错误: modifier为空");
            return CommonResult.error("参数错误，用户ID不能为空");
        }
        
        if (rgId == null || rgId <= 0) {
            log.warn("#checkUserRgPermission 参数错误: rgId无效={}", rgId);
            return CommonResult.error("参数错误，值班组ID不能为空且必须大于0");
        }
        
        try {
            // 调用服务检查用户对值班组的权限
            boolean hasPermission = workspaceService.checkUserRgPermission(modifier, rgId);
            
            // 构建响应
            Map<String, Object> result = new HashMap<>();
            result.put("hasPermission", hasPermission);
            
            if (hasPermission) {
                result.put("message", "该值班组有自定义空间");
                log.info("#checkUserRgPermission 该值班组有自定义空间: modifier={}, rgId={}", modifier, rgId);
            } else {
                result.put("message", "该值班组没有自定义空间");
                log.warn("#checkUserRgPermission 该值班组没有自定义空间: modifier={}, rgId={}", modifier, rgId);
            }
            
            return CommonResult.success(result);
        } catch (Exception e) {
            log.error("#checkUserRgPermission 检查该值班组是否具有自定义空间失败: modifier={}, rgId={}", modifier, rgId, e);
            return CommonResult.error(String.format("执行失败，异常原因=%s", e.getMessage()));
        }
    }
} 