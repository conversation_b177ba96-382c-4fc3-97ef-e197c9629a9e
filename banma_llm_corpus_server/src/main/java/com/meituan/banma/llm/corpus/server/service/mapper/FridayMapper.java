package com.meituan.banma.llm.corpus.server.service.mapper;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.meituan.banma.llm.corpus.api.response.ImageToTextResponse;
import com.meituan.banma.llm.corpus.server.common.domain.dto.*;
import com.meituan.banma.llm.corpus.server.rpc.friday.dto.LlmAnswerDTO;
import com.meituan.banma.llm.corpus.server.rpc.friday.dto.LlmTemplateDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Mapper()
public abstract class FridayMapper {
    private static final FridayMapper FRIDAY_MAPPER = Mappers.getMapper(FridayMapper.class);

    public static FridayMapper get() {
        return FRIDAY_MAPPER;
    }
    public FridayConversationUtterance trans2FridayConversationUtterance(TTInfoDTO ttInfoDTO, List<DxChatMessageRecord> dxChatMessageRecords) {
        FridayConversationUtterance fridayConversationUtterance = new FridayConversationUtterance();
        fridayConversationUtterance.setTitle(ttInfoDTO.getName());
        fridayConversationUtterance.setDescription(ttInfoDTO.getName());
        fridayConversationUtterance.setOrgId(String.valueOf(ttInfoDTO.getRgId()));
        fridayConversationUtterance.setTtId(ttInfoDTO.getTicketId());
        fridayConversationUtterance.setChatMessageRecord(dxChatMessageRecords);
        return fridayConversationUtterance;
    }
    public FridayConversationKmUtterance trans2FridayConversationKmUtterance(String body, long rgId) {
        FridayConversationKmUtterance fridayConversationKmUtterance = new FridayConversationKmUtterance();
        fridayConversationKmUtterance.setBody(body);
        fridayConversationKmUtterance.setOrgId(String.valueOf(rgId));
        return fridayConversationKmUtterance;
    }
    public FridayConversationUtterance trans2FridayConversationUtterance(TTInfoDTO ttInfoDTO) {
        FridayConversationUtterance fridayConversationUtterance = new FridayConversationUtterance();
        fridayConversationUtterance.setTitle(ttInfoDTO.getName());
        fridayConversationUtterance.setDescription(ttInfoDTO.getDesc());
        fridayConversationUtterance.setOrgId(String.valueOf(ttInfoDTO.getRgId()));
        fridayConversationUtterance.setTtId(ttInfoDTO.getTicketId());
        return fridayConversationUtterance;
    }
    @Mappings({
            @Mapping(target = "rgId", source = "rgId"),
            @Mapping(target = "query", source = "query")
    })
    public abstract FridayQuerySimilarityConversationUtterance trans2FridayQuerySimilarityConversationUtterance(Long rgId, String query) ;


    public List<KnowledgeSimilarityRecord> trans2KnowledgeSimilarityRecordList(List<JSONObject> knowledgeSimilarityRecordList) {
        if (CollectionUtils.isEmpty(knowledgeSimilarityRecordList)) {
            return null;
        }
        JSONObject docRecallItems = null;
        for (JSONObject jsonObject : knowledgeSimilarityRecordList) {
            if (jsonObject == null) {
                continue;
            }
            if ("docRecallItems".equals(jsonObject.get("name"))) {
                docRecallItems = jsonObject;
            }
        }
        if (docRecallItems == null) {
            return null;
        }
        JSONArray valueWrap = docRecallItems.getJSONArray("value");
        if (CollectionUtils.isEmpty(valueWrap)) {
            return null;
        }
        List<KnowledgeSimilarityRecord> result = new ArrayList<>();
        for (int i = 0; i < valueWrap.size(); i++) {
            if (valueWrap.get(i) == null) {
                continue;
            }
            List<JSONObject> docRecallItemsList = JSON.parseObject(valueWrap.getJSONArray(i).toJSONString(), new TypeReference<List<JSONObject>>() {});
            result.add(trans2KnowledgeSimilarityRecord(docRecallItemsList));
        }
        return result;
    }

    private KnowledgeSimilarityRecord trans2KnowledgeSimilarityRecord(List<JSONObject> docRecallItemsList) {
        KnowledgeSimilarityRecord knowledgeSimilarityRecord = new KnowledgeSimilarityRecord();

        for (JSONObject docRecallItem : docRecallItemsList) {
            if (docRecallItem == null) {
                continue;
            }
            if ("text".equals(docRecallItem.getString("name"))) {
                knowledgeSimilarityRecord.setText(docRecallItem.getString("value"));
            }
            if ("score".equals(docRecallItem.getString("name"))) {
                double value = docRecallItem.getDouble("value");
                BigDecimal bd = new BigDecimal(value);
                bd = bd.setScale(3, RoundingMode.HALF_UP); // 四舍五入
                knowledgeSimilarityRecord.setScore(bd.doubleValue());
            }
        }
        return knowledgeSimilarityRecord;
    }

    public TtChatCorpusDTO trans2TtChatCorpusDTO(LlmAnswerDTO llmAnswerDTO) {
        if (llmAnswerDTO == null || llmAnswerDTO.getData() == null || llmAnswerDTO.getData().getResultDoc() == null) {
            return null;
        }
        LlmTemplateDTO llmTemplateDTO = llmAnswerDTO.getData().getResultDoc();

        TtChatCorpusDTO ttChatCorpusDTO = new TtChatCorpusDTO();
        ttChatCorpusDTO.setDocs(llmTemplateDTO.getDocs());
        ttChatCorpusDTO.setQuestion(llmTemplateDTO.getQuestion());
        ttChatCorpusDTO.setMissingInfo(llmAnswerDTO.getData().getMissingInfo());
        ttChatCorpusDTO.setMessage(llmAnswerDTO.getMessage());
        ttChatCorpusDTO.setTagsIds(llmAnswerDTO.getData().getTagsIds());
        return ttChatCorpusDTO;
    }

    public List<ImageToTextResponse> trans2ImageToTextResponse(List<ImageToTextResultDTO> resp) {
        List<ImageToTextResponse> result = new ArrayList<>();
        for (ImageToTextResultDTO dto : resp) {
            ImageToTextResponse response = new ImageToTextResponse();
            response.setImageUrl(dto.getUrl());
            response.setText(dto.getText());
            result.add(response);
        }
        return result;
    }

    /**
     * 将文本响应转换为ContentQualityDTO对象
     * 
     * @param responseText 响应文本，可能包含JSON格式标记
     * @return ContentQualityDTO对象
     */
    public ContentQualityDTO trans2ContentQualityDTO(String responseText) {
        if (StringUtils.hasText(responseText)) {
            try {
                // 处理响应文本，可能包含JSON格式标记如```json\n
                if (responseText.startsWith("```json")) {
                    responseText = responseText.substring(responseText.indexOf("{"), responseText.lastIndexOf("}") + 1);
                }
                
                // 解析JSON
                ContentQualityDTO result = JSON.parseObject(responseText, ContentQualityDTO.class);
                return result != null ? result : new ContentQualityDTO();
            } catch (Exception e) {
                log.error("trans2ContentQualityDTO# 解析结果失败, text:{}", responseText, e);
            }
        }
        return new ContentQualityDTO();
    }
}
