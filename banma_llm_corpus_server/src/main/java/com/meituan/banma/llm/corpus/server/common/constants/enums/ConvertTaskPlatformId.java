package com.meituan.banma.llm.corpus.server.common.constants.enums;

import lombok.Getter;

@Getter
public enum ConvertTaskPlatformId {
    /**
     * 任务来源
     */
    DX_GROUP_BOT(1, "大象群机器人"),
    WEB(2, "Web端"),
    WEB_MERGE(3, "Web端合并"),
    DX_MONITORING(4, "大象监控"),
    KM_QA_UPLOADING(5,"学城问答上传"),
    DX_SINGLE_CHAT(6,"大象单聊合并转发")
    ;

    private int code;
    private String desc;

    ConvertTaskPlatformId(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    public static ConvertTaskPlatformId fromCode(int code) {
        for (ConvertTaskPlatformId value : ConvertTaskPlatformId.values()) {
            if (value.getCode() == code) {
                return value;
            }
        }
        return null;
    }
}
