package com.meituan.banma.llm.corpus.server.rpc.emp;

import com.meituan.banma.llm.corpus.api.exception.LlmCorpusException;
import com.sankuai.meituan.org.opensdk.model.domain.Emp;

import java.util.Date;
import java.util.List;

public interface EmpQueryRpcService {
    List<Emp> batchQueryByMis(List<String> misIdList, Date snapshot) throws LlmCorpusException;
    List<Emp> batchQueryByEmpId(List<String> empIdList, Date snapshot) throws LlmCorpusException;
}
