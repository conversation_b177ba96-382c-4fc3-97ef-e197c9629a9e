package com.meituan.banma.llm.corpus.server.utils;

import com.meituan.banma.llm.corpus.server.common.MtConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.File;
import java.util.UUID;

/**
 * 文件上传工具类 - 使用S3存储
 */
@Slf4j
@Component
public class ExcelExportUtil {
    
    @Resource
    private MtConfigService mtConfigService;
    
    private static MtConfigService staticMtConfigService;

    // S3对象前缀
    private static final String S3_PREFIX = "ticket_export/";
    private static final String FILE_NAME_PREFIX = "ticket_range_";
    private static final String FILE_EXTENSION = ".xlsx";
    
    private static ExcelExportUtil instance;
    
    @PostConstruct
    public void init() {
        instance = this;
        staticMtConfigService = this.mtConfigService;
        log.info("ExcelExportUtil初始化成功");
    }
    
    /**
     * 将文件内容上传到S3并返回预签名URL
     *
     * @param fileContent 文件内容
     * @param taskId 任务ID
     * @param fileExtension 文件扩展名（如.xlsx, .pdf等）
     * @return 预签名下载URL
     */
    public static String uploadContentToS3(byte[] fileContent, String taskId, String fileExtension) {
        if (fileContent == null || fileContent.length == 0) {
            log.warn("上传文件失败，内容为空");
            return null;
        }
        
        // 检查实例是否初始化，如果未初始化则重新初始化
        checkAndReinitialize();
        
        // 检查配置服务是否可用
        if (staticMtConfigService == null) {
            log.error("配置服务未初始化或初始化失败，无法上传文件");
            return null;
        }
        
        // 从配置服务获取存储桶名称
        String bucketName = staticMtConfigService.getTicketRangeQueryConfig().getS3BucketName();
        if (bucketName == null) {
            log.error("#uploadContentToS3 无法获取S3存储桶名称，请检查配置");
            return null;
        }
        
        // 默认为Excel扩展名
        String extension = fileExtension != null ? fileExtension : FILE_EXTENSION;
        
        // 构建S3对象键
        String fileName = FILE_NAME_PREFIX + (taskId != null ? taskId : UUID.randomUUID().toString()) + extension;
        String objectKey = S3_PREFIX + fileName;
        
        try {
            // 根据文件扩展名确定内容类型
            String contentType = getContentType(extension);
            
            // 创建临时文件
            File tempFile = File.createTempFile("temp_export_", extension);
            java.nio.file.Files.write(tempFile.toPath(), fileContent);
            
            // 上传到S3
            S3Util.putObjectFileExample(bucketName, objectKey, tempFile.getAbsolutePath());
            
            // 删除临时文件
            tempFile.delete();
            
            // 生成预签名URL
            String presignedUrl = S3Util.presignUrlExample(bucketName, objectKey);
            log.info("文件内容成功上传到S3并生成预签名URL: bucketName={}, objectKey={}, url={}", 
                    bucketName, objectKey, presignedUrl);
            return presignedUrl;
        } catch (Exception e) {
            log.error("文件内容上传异常: bucketName={}, objectKey={}, error={}", 
                    bucketName, objectKey, e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * 检查实例是否初始化，如果未初始化则重新初始化
     */
    private static synchronized void checkAndReinitialize() {
        if (instance == null || staticMtConfigService == null) {
            log.warn("ExcelExportUtil实例未初始化或初始化失败，尝试重新初始化");
            try {
                // 通过Spring容器获取ExcelExportUtil实例
                ExcelExportUtil excelExportUtil = SpringContextHolder.getBean(ExcelExportUtil.class);
                if (excelExportUtil != null) {
                    // 手动调用init方法
                    excelExportUtil.init();
                    log.info("ExcelExportUtil重新初始化成功");
                } else {
                    log.error("无法从Spring容器获取ExcelExportUtil实例");
                }
            } catch (Exception e) {
                log.error("ExcelExportUtil重新初始化失败: {}", e.getMessage(), e);
            }
        }
    }
    
    /**
     * 根据文件扩展名获取内容类型
     */
    private static String getContentType(String fileExtension) {
        if (fileExtension == null) {
            return "application/octet-stream";
        }
        
        switch (fileExtension.toLowerCase()) {
            case ".xlsx":
                return "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
            case ".xls":
                return "application/vnd.ms-excel";
            case ".pdf":
                return "application/pdf";
            case ".txt":
                return "text/plain";
            case ".json":
                return "application/json";
            case ".csv":
                return "text/csv";
            default:
                return "application/octet-stream";
        }
    }
} 