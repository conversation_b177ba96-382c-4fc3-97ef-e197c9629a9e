package com.meituan.banma.llm.corpus.server.dal.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

/**
 * 文档上传重试任务实体
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DocumentUploadRetryEntity {
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 值班组ID
     */
    private Long rgId;
    
    /**
     * 工作空间ID
     */
    private String spaceId;
    
    /**
     * URL
     */
    private String url;
    
    /**
     * 文档名称
     */
    private String name;
    
    /**
     * 重试次数
     */
    private Integer retryCount;
    
    /**
     * 失败原因
     */
    private String failReason;
    
    /**
     * 用户ID
     */
    private String misId;
    
    /**
     * 自动更新设置
     */
    private Integer autoUpdate;
    
    /**
     * 任务状态: 0-待处理, 1-处理中, 2-处理成功, 3-处理失败
     */
    private Integer status;
    
    /**
     * 创建时间
     */
    private Timestamp createTime;
    
    /**
     * 更新时间
     */
    private Timestamp updateTime;
} 