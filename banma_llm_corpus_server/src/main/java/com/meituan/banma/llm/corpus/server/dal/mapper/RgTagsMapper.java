package com.meituan.banma.llm.corpus.server.dal.mapper;

import com.meituan.banma.llm.corpus.server.dal.entity.RgTagsEntity;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface RgTagsMapper {

    /**
     * 新增标签
     */
    @Insert("INSERT INTO rg_tags (rg_id, tag_name, tag_desc, mis_id) VALUES " +
            "(#{rgId}, #{tagName}, #{tagDesc}, #{misId})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(RgTagsEntity entity);

    /**
     * 根据id删除标签
     */
    @Delete("DELETE FROM rg_tags WHERE id = #{id}")
    int deleteById(@Param("id") Long id);

    /**
     * 根据rgId和tagName删除标签
     */
    @Delete("DELETE FROM rg_tags WHERE rg_id = #{rgId} AND tag_name = #{tagName}")
    int deleteByRgIdAndTagName(@Param("rgId") Long rgId, @Param("tagName") String tagName);

    /**
     * 修改标签信息
     */
    @Update("UPDATE rg_tags SET tag_name = #{tagName}, tag_desc = #{tagDesc}, mis_id = #{misId} WHERE id = #{id}")
    int updateById(RgTagsEntity entity);

    /**
     * 根据id查询标签
     */
    @Select("SELECT * FROM rg_tags WHERE id = #{id}")
    RgTagsEntity findById(@Param("id") Long id);

    /**
     * 根据rgId查询标签列表
     */
    @Select("SELECT * FROM rg_tags WHERE rg_id = #{rgId} ORDER BY ctime DESC")
    List<RgTagsEntity> findByRgId(@Param("rgId") Long rgId);

    /**
     * 根据ids批量查询标签列表
     */
    @Select("<script>" +
            "SELECT * FROM rg_tags WHERE id IN " +
            "<foreach collection='ids' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            " ORDER BY ctime DESC" +
            "</script>")
    List<RgTagsEntity> findByIds(@Param("ids") List<Long> ids);

    /**
     * 根据rgId和tagName查询标签（用于重复校验）
     */
    @Select("SELECT * FROM rg_tags WHERE rg_id = #{rgId} AND tag_name = #{tagName}")
    RgTagsEntity findByRgIdAndTagName(@Param("rgId") Long rgId, @Param("tagName") String tagName);

    /**
     * 查询所有标签
     */
    @Select("SELECT * FROM rg_tags ORDER BY rg_id, ctime DESC")
    List<RgTagsEntity> findAll();
} 