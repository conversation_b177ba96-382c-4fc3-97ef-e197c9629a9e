package com.meituan.banma.llm.corpus.server.dal.mapper;

import com.meituan.banma.llm.corpus.server.dal.entity.TicketRangeEntity;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 工单范围信息Mapper
 */
@Mapper
public interface TicketRangeMapper {
    
    /**
     * 插入工单范围信息
     * 
     * @param entity 工单范围信息实体
     * @return 受影响的行数
     */
    @Insert("INSERT INTO ticket_range(rg_id, ticket_id, date, merchant_id, time_slot, reason, type_id, type_name, " +
            "city_name, city_id, order_id, address) " +
            "VALUES(#{rgId}, #{ticketId}, #{date}, #{merchantId}, #{timeSlot}, #{reason}, #{typeId}, #{typeName}, " +
            "#{cityName}, #{cityId}, #{orderId}, #{address})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(TicketRangeEntity entity);
    
    /**
     * 更新工单范围信息
     * 
     * @param entity 工单范围信息实体
     * @return 受影响的行数
     */
    @Update("UPDATE ticket_range SET " +
            "rg_id = #{rgId}, " +
            "date = #{date}, " +
            "merchant_id = #{merchantId}, " +
            "time_slot = #{timeSlot}, " +
            "reason = #{reason}, " +
            "type_id = #{typeId}, " +
            "type_name = #{typeName}, " +
            "city_name = #{cityName}, " +
            "city_id = #{cityId}, " +
            "order_id = #{orderId}, " +
            "address = #{address} " +
            "WHERE ticket_id = #{ticketId}")
    int update(TicketRangeEntity entity);
    
    /**
     * 根据主键ID更新工单范围信息
     * 
     * @param entity 工单范围信息实体
     * @return 受影响的行数
     */
    @Update("UPDATE ticket_range SET " +
            "rg_id = #{rgId}, " +
            "ticket_id = #{ticketId}, " +
            "date = #{date}, " +
            "merchant_id = #{merchantId}, " +
            "time_slot = #{timeSlot}, " +
            "reason = #{reason}, " +
            "type_id = #{typeId}, " +
            "type_name = #{typeName}, " +
            "city_name = #{cityName}, " +
            "city_id = #{cityId}, " +
            "order_id = #{orderId}, " +
            "address = #{address} " +
            "WHERE id = #{id}")
    int updateByPrimaryKey(TicketRangeEntity entity);
    
    /**
     * 插入或更新工单范围信息
     * 当工单ID已存在时更新，不存在时插入
     * 
     * @param entity 工单范围信息实体
     * @return 受影响的行数
     */
    @Insert("INSERT INTO ticket_range(rg_id, ticket_id, date, merchant_id, time_slot, reason, type_id, type_name, " +
            "city_name, city_id, order_id, address) " +
            "VALUES(#{rgId}, #{ticketId}, #{date}, #{merchantId}, #{timeSlot}, #{reason}, #{typeId}, #{typeName}, " +
            "#{cityName}, #{cityId}, #{orderId}, #{address}) " +
            "ON DUPLICATE KEY UPDATE " +
            "rg_id = VALUES(rg_id), " +
            "date = VALUES(date), " +
            "merchant_id = VALUES(merchant_id), " +
            "time_slot = VALUES(time_slot), " +
            "reason = VALUES(reason), " +
            "type_id = VALUES(type_id), " +
            "type_name = VALUES(type_name), " +
            "city_name = VALUES(city_name), " +
            "city_id = VALUES(city_id), " +
            "order_id = VALUES(order_id), " +
            "address = VALUES(address)")
    int insertOrUpdate(TicketRangeEntity entity);
    
    /**
     * 检查指定工单ID的记录是否存在
     * 
     * @param ticketId 工单ID
     * @return 存在返回1，不存在返回0
     */
    @Select("SELECT COUNT(1) FROM ticket_range WHERE ticket_id = #{ticketId}")
    int existsByTicketId(@Param("ticketId") String ticketId);
    
    /**
     * 根据工单ID查询工单范围信息
     * 
     * @param ticketId 工单ID
     * @return 工单范围信息
     */
    @Select("SELECT * FROM ticket_range WHERE ticket_id = #{ticketId}")
    TicketRangeEntity findByTicketId(@Param("ticketId") String ticketId);
    
    /**
     * 根据工单ID和组织ID查询工单范围信息
     * 
     * @param ticketId 工单ID
     * @param rgId 组织ID
     * @return 工单范围信息
     */
    @Select("SELECT * FROM ticket_range WHERE ticket_id = #{ticketId} AND rg_id = #{rgId}")
    TicketRangeEntity findByTicketIdAndRgId(@Param("ticketId") String ticketId, @Param("rgId") Long rgId);
    
    /**
     * 根据多个工单ID批量查询工单范围信息
     * 
     * @param ticketIds 工单ID列表
     * @return 工单范围信息列表
     */
    @Select("<script>" +
            "SELECT * FROM ticket_range WHERE ticket_id IN " +
            "<foreach item='item' collection='ticketIds' open='(' separator=',' close=')'>" +
            "#{item}" +
            "</foreach>" +
            "</script>")
    List<TicketRangeEntity> findByTicketIds(@Param("ticketIds") List<String> ticketIds);
    
    /**
     * 根据任务ID查询工单范围信息
     * 
     * @param taskId 任务ID
     * @return 工单范围信息列表
     */
    @Select("SELECT * FROM ticket_range WHERE task_id = #{taskId}")
    List<TicketRangeEntity> findByTaskId(@Param("taskId") String taskId);
    
    /**
     * 批量插入工单范围信息
     * 
     * @param entities 工单范围信息实体列表
     * @return 受影响的行数
     */
    @Insert("<script>" +
            "INSERT INTO ticket_range(rg_id, ticket_id, date, merchant_id, time_slot, reason, type_id, type_name, " +
            "city_name, city_id, order_id, address) VALUES " +
            "<foreach item='entity' collection='entities' separator=','>" +
            "(#{entity.rgId}, #{entity.ticketId}, #{entity.date}, #{entity.merchantId}, #{entity.timeSlot}, " +
            "#{entity.reason}, #{entity.typeId}, #{entity.typeName}, #{entity.cityName}, #{entity.cityId}, " +
            "#{entity.orderId}, #{entity.address})" +
            "</foreach>" +
            "</script>")
    int batchInsert(@Param("entities") List<TicketRangeEntity> entities);
} 