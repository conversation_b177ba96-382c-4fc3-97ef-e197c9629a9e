package com.meituan.banma.llm.corpus.server.common.domain.dto;

import lombok.Data;
import java.util.HashMap;
import java.util.Map;

/**
 * 动态模板工单DTO
 * 用于根据模板配置返回不同的字段
 */
@Data
public class DynamicTemplateTicketDTO {
    /**
     * 工单ID
     */
    private String ticketId;
    
    /**
     * 模板名称
     */
    private String templateName;
    
    /**
     * 模板ID
     */
    private String templateId;
    
    /**
     * 动态字段集合
     */
    private Map<String, Object> fields = new HashMap<>();
    
    /**
     * 添加字段值
     * 
     * @param fieldName 字段名称
     * @param value 字段值
     */
    public void addField(String fieldName, Object value) {
        fields.put(fieldName, value);
    }
    
    /**
     * 获取字段值
     * 
     * @param fieldName 字段名称
     * @return 字段值
     */
    public Object getField(String fieldName) {
        return fields.get(fieldName);
    }
    
    /**
     * 检查是否包含指定字段
     * 
     * @param fieldName 字段名称
     * @return 是否包含该字段
     */
    public boolean hasField(String fieldName) {
        return fields.containsKey(fieldName);
    }
} 