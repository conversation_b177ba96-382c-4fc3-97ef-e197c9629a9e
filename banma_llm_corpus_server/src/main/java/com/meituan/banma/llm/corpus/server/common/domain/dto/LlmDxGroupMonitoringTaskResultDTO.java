package com.meituan.banma.llm.corpus.server.common.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

@Data
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class LlmDxGroupMonitoringTaskResultDTO {
    List<LlmDxGroupMonitoringTaskResultItem> summaryQuestionList;
    public static class LlmDxGroupMonitoringTaskResultItem {
        /**
         * 问题提问人misId
         */
        private String questionFromMisId;
        /**
         * 问题提问人组织id
         */
        private String questionFromOrgId;
        /**
         * 问题提问人消息id
         */
        private Long questionFromMsgId;
        /**
         * 原始问题消息内容
         */
        private String rawQuestionContent;
        /**
         * 汇总后的更加可读的问题内容
         */
        private String summaryQuestionContent;
        /**
         * 问题状态：0-无回应，1-有回应
         */
        private Integer replyStatus;
        /**
         * 问题消息的cts
         */
        private Long questionFromMsgCts;
        /**
         * 问题类型id 来自输入的questionTypeList
         */
        private Integer questionTypeId;

        public LlmDxGroupMonitoringTaskResultItem(String questionFromMisId, String questionFromOrgId, Long questionFromMsgId, String rawQuestionContent, String summaryQuestionContent, Integer replyStatus, Long questionFromMsgCts, Integer questionTypeId) {
            this.questionFromMisId = questionFromMisId;
            this.questionFromOrgId = questionFromOrgId;
            this.questionFromMsgId = questionFromMsgId;
            this.rawQuestionContent = rawQuestionContent;
            this.summaryQuestionContent = summaryQuestionContent;
            this.questionFromMsgCts = questionFromMsgCts;
            this.questionTypeId = questionTypeId;
            this.replyStatus = replyStatus;
        }

        public Long getQuestionFromMsgCts() {
            return questionFromMsgCts;
        }

        public void setQuestionFromMsgCts(Long questionFromMsgCts) {
            this.questionFromMsgCts = questionFromMsgCts;
        }

        public Long getQuestionFromMsgId() {
            return questionFromMsgId;
        }

        public void setQuestionFromMsgId(Long questionFromMsgId) {
            this.questionFromMsgId = questionFromMsgId;
        }

        public Integer getQuestionTypeId() {
            return questionTypeId;
        }

        public void setQuestionTypeId(Integer questionTypeId) {
            this.questionTypeId = questionTypeId;
        }

        public String getQuestionFromMisId() {
            return questionFromMisId;
        }

        public void setQuestionFromMisId(String questionFromMisId) {
            this.questionFromMisId = questionFromMisId;
        }

        public String getQuestionFromOrgId() {
            return questionFromOrgId;
        }

        public void setQuestionFromOrgId(String questionFromOrgId) {
            this.questionFromOrgId = questionFromOrgId;
        }


        public String getRawQuestionContent() {
            return rawQuestionContent;
        }

        public void setRawQuestionContent(String rawQuestionContent) {
            this.rawQuestionContent = rawQuestionContent;
        }

        public String getSummaryQuestionContent() {
            return summaryQuestionContent;
        }

        public void setSummaryQuestionContent(String summaryQuestionContent) {
            this.summaryQuestionContent = summaryQuestionContent;
        }

        public Integer getReplyStatus() {
            return replyStatus;
        }

        public void setReplyStatus(Integer replyStatus) {
            this.replyStatus = replyStatus;
        }
    }
}
