package com.meituan.banma.llm.corpus.server.utils;

import com.meituan.banma.llm.corpus.server.common.interceptor.MisIdInterceptor;
import lombok.extern.slf4j.Slf4j;

/**
 * ThreadLocal值传递辅助类
 * 用于在不同线程间传递ThreadLocal值
 */
@Slf4j
public class ThreadLocalTransferUtil  {
    
    /**
     * 用于在线程间传递的上下文对象
     */
    public static class ThreadLocalContext {
        private final String misId;
        private final String kmSsoid;
        
        public ThreadLocalContext(String misId, String kmSsoid) {
            this.misId = misId;
            this.kmSsoid = kmSsoid;
        }
        
        public String getMisId() {
            return misId;
        }
        
        public String getKmSsoid() {
            return kmSsoid;
        }
    }
    
    /**
     * 捕获当前线程的ThreadLocal值
     * @return 包含当前线程ThreadLocal值的上下文对象
     */
    public static ThreadLocalContext capture() {
        String misId = MisIdInterceptor.getMisId();
        String kmSsoid = MisIdInterceptor.getKmSsoid();
        
        log.debug("Captured ThreadLocal values: misId={}, kmSsoid={}", misId, kmSsoid);
        return new ThreadLocalContext(misId, kmSsoid);
    }
    
    /**
     * 在当前线程中应用捕获的ThreadLocal值
     * @param context 捕获的ThreadLocal上下文
     */
    public static void apply(ThreadLocalContext context) {
        if (context == null) {
            log.warn("Cannot apply null ThreadLocal context");
            return;
        }
        
        try {
            // 临时修改misIdThreadLocal的访问控制，使其可以通过反射设置值
            java.lang.reflect.Field misIdField = MisIdInterceptor.class.getDeclaredField("misIdThreadLocal");
            misIdField.setAccessible(true);
            ThreadLocal<String> misIdThreadLocal = (ThreadLocal<String>) misIdField.get(null);
            
            // 临时修改ssoidThreadLocal的访问控制，使其可以通过反射设置值
            java.lang.reflect.Field ssoidField = MisIdInterceptor.class.getDeclaredField("ssoidThreadLocal");
            ssoidField.setAccessible(true);
            ThreadLocal<String> ssoidThreadLocal = (ThreadLocal<String>) ssoidField.get(null);
            
            // 在当前线程中设置捕获的值
            if (context.getMisId() != null) {
                misIdThreadLocal.set(context.getMisId());
            }
            
            if (context.getKmSsoid() != null) {
                ssoidThreadLocal.set(context.getKmSsoid());
            }
            
            log.debug("Applied ThreadLocal values: misId={}, kmSsoid={}", 
                     context.getMisId(), context.getKmSsoid());
        } catch (Exception e) {
            log.error("Failed to apply ThreadLocal context", e);
        }
    }
    
    /**
     * 清理当前线程的ThreadLocal值
     */
    public static void clear() {
        try {
            // 临时修改misIdThreadLocal的访问控制，使其可以通过反射设置值
            java.lang.reflect.Field misIdField = MisIdInterceptor.class.getDeclaredField("misIdThreadLocal");
            misIdField.setAccessible(true);
            ThreadLocal<String> misIdThreadLocal = (ThreadLocal<String>) misIdField.get(null);
            
            // 临时修改ssoidThreadLocal的访问控制，使其可以通过反射设置值
            java.lang.reflect.Field ssoidField = MisIdInterceptor.class.getDeclaredField("ssoidThreadLocal");
            ssoidField.setAccessible(true);
            ThreadLocal<String> ssoidThreadLocal = (ThreadLocal<String>) ssoidField.get(null);
            
            // 清理ThreadLocal值
            misIdThreadLocal.remove();
            ssoidThreadLocal.remove();
            
            log.debug("Cleared ThreadLocal values");
        } catch (Exception e) {
            log.error("Failed to clear ThreadLocal context", e);
        }
    }
} 