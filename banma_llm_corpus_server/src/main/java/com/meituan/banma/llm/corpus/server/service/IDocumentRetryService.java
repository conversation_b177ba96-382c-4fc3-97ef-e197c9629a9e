package com.meituan.banma.llm.corpus.server.service;

import com.meituan.banma.llm.corpus.api.exception.LlmCorpusException;

/**
 * 文档上传重试服务接口
 */
public interface IDocumentRetryService {
    
    /**
     * 记录文档上传失败任务
     * 
     * @param rgId 值班组ID
     * @param spaceId 工作空间ID
     * @param url 文档URL
     * @param name 文档名称
     * @param misId 用户ID
     * @param autoUpdate 自动更新设置
     * @param failReason 失败原因
     */
    void recordFailedTask(long rgId, String spaceId, String url, String name, String misId, int autoUpdate, String failReason);
    
    /**
     * 执行定时重试任务
     */
    void processRetryTasks();
    
    /**
     * 尝试上传文档
     * 
     * @param rgId 值班组ID
     * @param spaceId 工作空间ID
     * @param url 文档URL
     * @param name 文档名称
     * @param misId 用户ID
     * @param autoUpdate 自动更新设置
     * @return 处理结果
     * @throws LlmCorpusException 业务异常
     */
    boolean retryUploadDocument(long rgId, String spaceId, String url, String name, String misId, int autoUpdate) throws LlmCorpusException;
} 