package com.meituan.banma.llm.corpus.server.dal.mapper;

import com.meituan.banma.llm.corpus.server.dal.entity.MonitoringGroupEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 监控组数据访问接口
 */
public interface MonitoringGroupMapper {
    
    /**
     * 插入新的监控组记录
     * 
     * @param record 监控组实体
     * @return 影响的行数
     */
    int insert(MonitoringGroupEntity record);
    
    /**
     * 根据ID删除监控组记录
     * 
     * @param monitoringGroupId 监控组ID
     * @return 影响的行数
     */
    int deleteById(Long monitoringGroupId);
    
    /**
     * 根据ID更新监控组记录
     * 
     * @param record 监控组实体
     * @return 影响的行数
     */
    int updateById(MonitoringGroupEntity record);
    
    /**
     * 根据ID查询监控组记录
     * 
     * @param monitoringGroupId 监控组ID
     * @return 监控组实体
     */
    MonitoringGroupEntity selectById(Long monitoringGroupId);
    
    /**
     * 查询所有监控组记录
     * 
     * @return 监控组实体列表
     */
    List<MonitoringGroupEntity> selectAll();
    
    /**
     * 根据名称模糊查询监控组记录
     * 
     * @param name 监控组名称(模糊匹配)
     * @return 监控组实体列表
     */
    List<MonitoringGroupEntity> selectByNameLike(@Param("name") String name);
    
    /**
     * 根据状态查询监控组记录
     * 
     * @param status 状态：0-正常 1-禁用
     * @return 监控组实体列表
     */
    List<MonitoringGroupEntity> selectByStatus(Integer status);
    
    /**
     * 更新监控组状态
     * 
     * @param monitoringGroupId 监控组ID
     * @param status 状态：0-正常 1-禁用
     * @return 影响的行数
     */
    int updateStatus(@Param("monitoringGroupId") Long monitoringGroupId, @Param("status") Integer status);
    
    /**
     * 批量插入监控组记录
     * 
     * @param records 监控组实体列表
     * @return 影响的行数
     */
    int batchInsert(List<MonitoringGroupEntity> records);
    
    /**
     * 批量删除监控组记录
     * 
     * @param monitoringGroupIds 监控组ID列表
     * @return 影响的行数
     */
    int batchDeleteByIds(List<Long> monitoringGroupIds);
    
    /**
     * 查询包含特定关键词的监控组记录
     * 
     * @param keyword 关键词
     * @return 监控组实体列表
     */
    List<MonitoringGroupEntity> selectByKeyword(@Param("keyword") String keyword);
    
    /**
     * 查询包含指定MIS ID的监控组记录
     * 
     * @param misId MIS ID
     * @return 监控组实体列表
     */
    List<MonitoringGroupEntity> selectByMisId(@Param("misId") String misId);
} 