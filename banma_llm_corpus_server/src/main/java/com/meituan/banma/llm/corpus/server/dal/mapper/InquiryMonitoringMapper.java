package com.meituan.banma.llm.corpus.server.dal.mapper;

import com.meituan.banma.llm.corpus.server.dal.entity.InquiryMonitoringEntity;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface InquiryMonitoringMapper{
    @Insert("INSERT INTO inquiry_monitoring (monitor_group_id, questioner_mis_id, questioner_emp_id, " +
            "questioner_org_id, questioner_org_name, raw_question_msg, summarized_question, " +
            "question_type, reply_status, from_message_id, message_cts, dx_group_id, ctime, utime) " +
            "VALUES (#{monitorGroupId}, #{questionerMisId}, #{questionerEmpId}, " +
            "#{questionerOrgId}, #{questionerOrgName}, #{rawQuestionMsg}, #{summarizedQuestion}, " +
            "#{questionType}, #{replyStatus}, #{fromMessageId}, #{messageCts}, #{dxGroupId}, " +
            "#{ctime}, #{utime}) ON DUPLICATE KEY UPDATE id=LAST_INSERT_ID(id)")
    int insert(InquiryMonitoringEntity po);

    @Insert("<script>" +
            "INSERT INTO inquiry_monitoring (monitor_group_id, questioner_mis_id, questioner_emp_id, " +
            "questioner_org_id, questioner_org_name, raw_question_msg, summarized_question, " +
            "question_type, reply_status, from_message_id, message_cts, dx_group_id, ctime, utime) VALUES " +
            "<foreach collection='poList' item='item' separator=','>" +
            "(#{item.monitorGroupId}, #{item.questionerMisId}, #{item.questionerEmpId}, " +
            "#{item.questionerOrgId}, #{item.questionerOrgName}, #{item.rawQuestionMsg}, #{item.summarizedQuestion}, " +
            "#{item.questionType}, #{item.replyStatus}, #{item.fromMessageId}, #{item.messageCts}, #{item.dxGroupId}, " +
            "#{item.ctime}, #{item.utime})" +
            "</foreach>" +
            " ON DUPLICATE KEY UPDATE " +
            "questioner_mis_id = VALUES(questioner_mis_id), " +
            "questioner_emp_id = VALUES(questioner_emp_id), " +
            "questioner_org_id = VALUES(questioner_org_id), " +
            "questioner_org_name = VALUES(questioner_org_name), " +
            "raw_question_msg = VALUES(raw_question_msg), " +
            "summarized_question = VALUES(summarized_question), " +
            "question_type = VALUES(question_type), " +
            "reply_status = VALUES(reply_status), " +
            "message_cts = VALUES(message_cts), " +
            "dx_group_id = VALUES(dx_group_id), " +
            "utime = VALUES(utime)" +
            "</script>")
    int batchInsert(@Param("poList") List<InquiryMonitoringEntity> poList);

    @Select("SELECT * FROM inquiry_monitoring WHERE monitor_group_id = #{monitorGroupId} " +
            "AND from_message_id = #{msgId} LIMIT 1")
    InquiryMonitoringEntity selectByFromMessageId(@Param("monitorGroupId") Long monitorGroupId,
                                                 @Param("msgId") String msgId);

    @Select("<script>" +
            "SELECT * FROM inquiry_monitoring WHERE monitor_group_id = #{monitorGroupId} " +
            "AND from_message_id IN " +
            "<foreach collection='msgIdList' item='msgId' open='(' separator=',' close=')'>" +
            "#{msgId}" +
            "</foreach>" +
            "</script>")
    List<InquiryMonitoringEntity> selectByFromMessageIdList(@Param("monitorGroupId") Long monitorGroupId,
                                                           @Param("msgIdList") List<String> msgIdList);

    /**
     * 根据监控组ID列表和时间范围查询数据
     * 
     * @param monitorGroupIds 监控组ID列表
     * @param startTime 开始时间戳（毫秒）
     * @param endTime 结束时间戳（毫秒）
     * @return 符合条件的监控记录列表
     */
    @Select("<script>" +
            "SELECT * FROM inquiry_monitoring WHERE message_cts BETWEEN #{startTime} AND #{endTime} " +
            "AND monitor_group_id IN " +
            "<foreach collection='monitorGroupIds' item='groupId' open='(' separator=',' close=')'>" +
            "#{groupId}" +
            "</foreach>" +
            " ORDER BY message_cts DESC" +
            "</script>")
    List<InquiryMonitoringEntity> selectByMonitorGroupIdsAndTimeRange(
            @Param("monitorGroupIds") List<Long> monitorGroupIds,
            @Param("startTime") Long startTime,
            @Param("endTime") Long endTime);
}
