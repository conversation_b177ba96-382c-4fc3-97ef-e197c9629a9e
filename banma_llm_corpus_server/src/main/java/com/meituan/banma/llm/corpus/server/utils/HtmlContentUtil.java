package com.meituan.banma.llm.corpus.server.utils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.meituan.banma.llm.corpus.server.common.domain.dto.LatestContentDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.Set;

@Slf4j
@Component
public class HtmlContentUtil {
    
    @Autowired
    private TagsUtil tagsUtil;
    
    public static final String TYPE_TITLE = "title";
    public static final String TYPE_PARAGRAPH = "paragraph";
    public static final String TYPE_HEADING = "heading";
    public static final String TYPE_NOTE = "note";
    public static final String TYPE_TABLE = "table";
    public static final String TYPE_ORDERED_LIST = "ordered_list";
    public static final String TYPE_BULLET_LIST = "bullet_list";
    public static final String TYPE_CATALOG = "catalog";
    public static final String TYPE_BLOCKQUOTE = "blockquote";

    public String createHtmlContent(List<LatestContentDTO> contents) {
        StringBuilder htmlBuilder = new StringBuilder();
        htmlBuilder.append("<html>\n");
        htmlBuilder.append("<head>\n");
        htmlBuilder.append("<title>语料内容</title>\n");
        htmlBuilder.append("</head>\n");
        htmlBuilder.append("<body>\n");

        // 预先收集所有标签ID并批量查询
        Map<String, List<String>> tagsIdsToNamesMap = buildTagsIdsToNamesMap(contents);

        for (LatestContentDTO latestContentDTO : contents) {
            // 获取标题并用<h1>标签包裹
            String question = latestContentDTO.getTitle();
            // 判断question是否是以【开头
            if (!question.startsWith("【")) {
                question = "【"+latestContentDTO.getTicketId()+"】" + question;
            }
            htmlBuilder.append("<h1>").append(question).append("</h1>\n");

            // 获取内容
            String content = latestContentDTO.getContent();
            
            // 将标签信息添加到内容的开头并用span包裹
            StringBuilder contentWithTags = new StringBuilder();
            if (latestContentDTO.getTagsIds() != null && !latestContentDTO.getTagsIds().trim().isEmpty()) {
                List<String> tagNames = tagsIdsToNamesMap.get(latestContentDTO.getTagsIds());
                if (!CollectionUtils.isEmpty(tagNames)) {
                    contentWithTags.append("标签: <span>");
                    for (int i = 0; i < tagNames.size(); i++) {
                        String tagName = tagNames.get(i);
                        contentWithTags.append(tagName);
                        if (i < tagNames.size() - 1) {
                            contentWithTags.append(",");
                        }
                    }
                    contentWithTags.append("</span>");
                }
            }
            
            contentWithTags.append(content);
            htmlBuilder.append("<p>").append(contentWithTags.toString()).append("</p>\n");
        }

        htmlBuilder.append("</body>\n");
        htmlBuilder.append("</html>");

        return htmlBuilder.toString();
    }

    /**
     * 构建标签IDs字符串到标签名称列表的映射关系
     * 优化性能：避免在循环中重复查询数据库
     * 
     * @param contents 内容列表
     * @return 标签IDs字符串到标签名称列表的映射Map
     */
    private Map<String, List<String>> buildTagsIdsToNamesMap(List<LatestContentDTO> contents) {
        Map<String, List<String>> tagsIdsToNamesMap = new HashMap<>();
        
        if (CollectionUtils.isEmpty(contents)) {
            return tagsIdsToNamesMap;
        }
        
        // 收集所有唯一的tagsIds字符串
        Set<String> uniqueTagsIds = new HashSet<>();
        for (LatestContentDTO content : contents) {
            if (content.getTagsIds() != null && !content.getTagsIds().trim().isEmpty()) {
                uniqueTagsIds.add(content.getTagsIds().trim());
            }
        }
        
        // 如果没有标签ID，直接返回空Map
        if (uniqueTagsIds.isEmpty()) {
            return tagsIdsToNamesMap;
        }
        
        // 批量查询每个唯一的tagsIds对应的标签名称
        for (String tagsIds : uniqueTagsIds) {
            try {
                List<String> tagNames = tagsUtil.getTagsNamesByIds(tagsIds);
                tagsIdsToNamesMap.put(tagsIds, tagNames != null ? tagNames : new ArrayList<>());
            } catch (Exception e) {
                log.warn("获取标签名称时发生异常, tagsIds: {}, 异常: {}", tagsIds, e.getMessage());
                // 出现异常时设置为空列表
                tagsIdsToNamesMap.put(tagsIds, new ArrayList<>());
            }
        }
        
        return tagsIdsToNamesMap;
    }

    public static String testString() {
        return "测试字符串";
    }

    public static String testRandomString() {
        // 生成随机字符串
        String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
        StringBuilder result = new StringBuilder();
        Random random = new Random();
        for (int i = 0; i < 10; i++) {
            result.append(chars.charAt(random.nextInt(chars.length())));
        }
        return result.toString();
    }

    /**
     * 清理并简化JSON格式的内容字符串
     *
     * @param jsonString JSON格式的内容字符串
     * @return 简化后的文本内容
     */
    public static String cleanAndSimplify(String jsonString) {
        StringBuilder simplifiedContent = new StringBuilder();

        if (jsonString == null || jsonString.isEmpty()) {
            log.warn("cleanAndSimplify: 输入的JSON字符串为空");
            return "";
        }

        try {
            JSONObject root = JSONObject.parseObject(jsonString);
            if (root == null) {
                log.warn("cleanAndSimplify: JSON解析失败，返回空字符串");
                return "";
            }

            JSONArray contentArray = root.getJSONArray("content");
            if (contentArray == null || contentArray.isEmpty()) {
                log.warn("cleanAndSimplify: content数组为空");
                return "";
            }

            for (int i = 0; i < contentArray.size(); i++) {
                JSONObject item = contentArray.getJSONObject(i);
                if (item != null) {
                    processItem(item, simplifiedContent);
                }
            }

        } catch (Exception e) {
            log.error("cleanAndSimplify处理JSON时发生异常: ", e);
            return "";
        }

        String result = simplifiedContent.toString();
        // 移除多余的空行和空格
        result = result.replaceAll("\\s*\n\\s*", "\n").trim();
        return result.replace("\\\\", "\\").replace("\\\"", "\"");
    }

    private static void processItem(JSONObject item, StringBuilder builder) {
        String type = item.getString("type");
        if (type == null || builder == null) {
            return;
        }

        switch (type) {
            case TYPE_TITLE:
            case TYPE_PARAGRAPH:
            case TYPE_HEADING:
                extractTextContent(item, builder, type.toUpperCase());
                break;

            case TYPE_NOTE:
                processNoteContent(item, builder);
                break;

            case TYPE_TABLE:
                processTableContent(item, builder);
                break;

            case TYPE_ORDERED_LIST:
            case TYPE_BULLET_LIST:
                processListContent(item, builder, type.toUpperCase());
                break;

            case TYPE_CATALOG:
                extractTextContent(item, builder, "CATALOG");
                break;

            case TYPE_BLOCKQUOTE:
                extractTextContent(item, builder, "BLOCKQUOTE");
                break;

            default:
                log.warn("Unhandled item type: " + type);
        }
    }

    private static void extractTextContent(JSONObject item, StringBuilder builder, String prefix) {
        if (item == null || builder == null || prefix == null) {
            return;
        }

        JSONArray texts = item.getJSONArray("content");
        if (texts == null) {
            return;
        }

        for (int t = 0; t < texts.size(); t++) {
            JSONObject textObj = texts.getJSONObject(t);
            if (textObj == null) {
                continue;
            }

            String textType = textObj.getString("type");
            if ("text".equals(textType) || "link".equals(textType)) {
                String text = textObj.getString("text");
                if (text != null) {
                    builder.append(prefix).append(": ").append(text);

                    if ("link".equals(textType) && textObj.containsKey("attrs")) {
                        JSONObject attrs = textObj.getJSONObject("attrs");
                        String href = attrs.getString("href");
                        if (href != null) {
                            builder.append(" (Link: ").append(href).append(")");
                        }
                    }
                    builder.append("\n");
                }
            }
        }
    }

    private static void processNoteContent(JSONObject item, StringBuilder builder) {
        JSONArray noteContent = item.getJSONArray("content");
        if (noteContent != null) {
            for (int k = 0; k < noteContent.size(); k++) {
                JSONObject subItem = noteContent.getJSONObject(k);
                if (subItem != null && subItem.containsKey("type") && "note_content".equals(subItem.getString("type"))) {
                    extractTextContent(subItem, builder, "NOTE");
                }
            }
        }
    }

    private static void processTableContent(JSONObject item, StringBuilder builder) {
        JSONArray rows = item.getJSONArray("content");
        if (rows != null) {
            builder.append("TABLE:\n");
            for (int r = 0; r < rows.size(); r++) {
                JSONObject row = rows.getJSONObject(r);
                if (row != null) {
                    JSONArray cells = row.getJSONArray("content");
                    if (cells != null) {
                        for (int c = 0; c < cells.size(); c++) {
                            JSONObject cell = cells.getJSONObject(c);
                            if (cell != null) {
                                extractTextContent(cell, builder, "");
                            }
                        }
                        builder.append("\n");
                    }
                }
            }
        }
    }

    private static void processListContent(JSONObject item, StringBuilder builder, String prefix) {
        JSONArray listItems = item.getJSONArray("content");
        if (listItems != null) {
            builder.append(prefix).append(":\n");
            for (int i = 0; i < listItems.size(); i++) {
                JSONObject listItem = listItems.getJSONObject(i);
                if (listItem != null) {
                    builder.append("- ");
                    extractTextContent(listItem, builder, "");
                }
            }
            builder.append("\n");
        }
    }

    public static void main(String[] args) {
        // Example usage
        String jsonString = "你的JSON内容";
        String result = cleanAndSimplify(jsonString);
        System.out.println(result);
    }
}
