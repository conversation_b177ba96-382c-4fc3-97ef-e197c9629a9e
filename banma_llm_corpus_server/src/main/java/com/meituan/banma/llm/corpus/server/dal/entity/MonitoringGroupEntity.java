package com.meituan.banma.llm.corpus.server.dal.entity;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 监控组主表实体类
 */
@Data
public class MonitoringGroupEntity {
    
    /**
     * 监控组ID
     */
    private Long monitoringGroupId;
    
    /**
     * 监控组名称
     */
    private String monitoringGroupName;
    
    /**
     * 监控组描述
     */
    private String monitoringGroupDesc;
    
    /**
     * 监控组所有者misId列表，逗号分隔，如"daili07,user1,user2"
     */
    private String monitoringGroupOwner;
    
    /**
     * DX群组ID列表，逗号分隔，如"67131758204,64853872187"
     */
    private String dxGroupIds;
    
    /**
     * 监控组织ID列表，逗号分隔
     */
    private String monitoredOrgIds;
    
    /**
     * 监控MIS账号列表，逗号分隔
     */
    private String monitoredMisIds;
    
    /**
     * 关键词列表，逗号分隔，如"配送范围,配送费,餐损"
     */
    private String keywords;
    
    /**
     * 监控时间范围类型
     */
    private Integer monitoringTimeRangeType;
    
    /**
     * 状态：0-正常 1-禁用
     */
    private Integer status;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
    
    /**
     * 问题类型列表（非数据库字段，用于传输数据）
     */
    private transient List<QuestionTypeEntity> questionTypes;
    
    // 以下是一些辅助方法，帮助处理复杂字段
    
    /**
     * 获取监控组所有者misId列表
     */
    public String[] getMonitoringGroupOwnerArray() {
        return monitoringGroupOwner != null ? monitoringGroupOwner.split(",") : new String[0];
    }
    
    /**
     * 获取DX群组ID列表
     */
    public Long[] getDxGroupIdArray() {
        if (dxGroupIds == null || dxGroupIds.isEmpty()) {
            return new Long[0];
        }
        
        String[] strArray = dxGroupIds.split(",");
        Long[] result = new Long[strArray.length];
        
        for (int i = 0; i < strArray.length; i++) {
            try {
                result[i] = Long.parseLong(strArray[i].trim());
            } catch (NumberFormatException e) {
                result[i] = null;
            }
        }
        
        return result;
    }
    
    /**
     * 获取监控组织ID列表
     */
    public String[] getMonitoredOrgIdArray() {
        return monitoredOrgIds != null ? monitoredOrgIds.split(",") : new String[0];
    }
    
    /**
     * 获取监控MIS账号列表
     */
    public String[] getMonitoredMisIdArray() {
        return monitoredMisIds != null ? monitoredMisIds.split(",") : new String[0];
    }
    
    /**
     * 获取关键词列表
     */
    public String[] getKeywordsArray() {
        return keywords != null ? keywords.split(",") : new String[0];
    }
    
    /**
     * 设置监控组所有者misId列表
     */
    public void setMonitoringGroupOwnerArray(String[] ownerArray) {
        if (ownerArray != null && ownerArray.length > 0) {
            this.monitoringGroupOwner = String.join(",", ownerArray);
        } else {
            this.monitoringGroupOwner = "";
        }
    }
    
    /**
     * 设置DX群组ID列表
     */
    public void setDxGroupIdArray(Long[] groupIds) {
        if (groupIds != null && groupIds.length > 0) {
            this.dxGroupIds = String.join(",", 
                java.util.Arrays.stream(groupIds)
                    .filter(id -> id != null)
                    .map(String::valueOf)
                    .toArray(String[]::new));
        } else {
            this.dxGroupIds = "";
        }
    }
} 