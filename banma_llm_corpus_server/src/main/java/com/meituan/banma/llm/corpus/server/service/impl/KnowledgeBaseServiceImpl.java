package com.meituan.banma.llm.corpus.server.service.impl;

import com.alibaba.fastjson.JSON;
import com.meituan.banma.llm.corpus.api.constants.BizCode;
import com.meituan.banma.llm.corpus.api.exception.LlmCorpusException;
import com.meituan.banma.llm.corpus.server.common.MtConfigService;
import com.meituan.banma.llm.corpus.server.common.constants.enums.ConvertTaskPlatformId;
import com.meituan.banma.llm.corpus.server.common.constants.enums.MergeTriggerSourceEnum;
import com.meituan.banma.llm.corpus.server.common.constants.enums.ModelOutputTaskStatusEnum;
import com.meituan.banma.llm.corpus.server.common.domain.dto.*;
import com.meituan.banma.llm.corpus.server.dal.entity.KnowledgeBaseVersionEntity;
import com.meituan.banma.llm.corpus.server.dal.entity.ModelOutputTaskEntity;
import com.meituan.banma.llm.corpus.server.dal.mapper.ModelOutputMapper;
import com.meituan.banma.llm.corpus.server.dal.mapper.ReviewMapper;
import com.meituan.banma.llm.corpus.server.rpc.friday.dto.LlmKmToCorpusDTO;
import com.meituan.banma.llm.corpus.server.service.IDxGroupChatService;
import com.meituan.banma.llm.corpus.server.service.IFridayService;
import com.meituan.banma.llm.corpus.server.service.IKnowledgeBaseService;
import com.meituan.banma.llm.corpus.server.service.mapper.KnowledgeBaseMapper;
import com.meituan.banma.llm.corpus.server.utils.AsyncTaskUtils;
import com.meituan.banma.llm.corpus.server.utils.RgQueryUtil;
import com.meituan.mafka.client.bean.MafkaProducer;
import com.meituan.mafka.client.producer.ProducerResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;
import java.util.Objects;

import static com.meituan.banma.llm.corpus.server.service.impl.TicketQueryServiceImpl.parseJsonToRgInfoDTOList;

@Slf4j
@Service
public class KnowledgeBaseServiceImpl implements IKnowledgeBaseService {
    @Autowired
    IFridayService fridayService;

    @Autowired
    IDxGroupChatService dxGroupChatService;

    @Autowired
    ModelOutputMapper modelOutputMapper;

    @Autowired
    RgQueryUtil rgQueryUtil;

    KnowledgeBaseMapper knowledgeBaseMapper = KnowledgeBaseMapper.get();

    @Autowired
    @Qualifier("llmCorpusConvertTaskTopic")
    private MafkaProducer sendClient;
    
    @Autowired
    private ReviewMapper reviewMapper;

    @Autowired
    private MtConfigService mtConfigService;

    private static final String MERGE_CORPUS_ID_PREFIX = "m-";
    @Override
    public boolean validateUserPermission(String misId, Long rgId) {
        if (StringUtils.isBlank(misId) || rgId == null) {
            return false;
        }
        List<RgInfoDTO> rgInfoDTOS = getRgInfoDTOList(misId);
        return rgInfoDTOS.stream().anyMatch(rgInfoDTO -> rgInfoDTO.getId() == rgId);
    }

    private List<RgInfoDTO> getRgInfoDTOList(String misId) {
        String rgInfoDTOListJSONString = rgQueryUtil.queryMyRgList(misId, 1, 200);
        if (StringUtils.isBlank(rgInfoDTOListJSONString)) {
            return Collections.emptyList();
        }
        return parseJsonToRgInfoDTOList(rgInfoDTOListJSONString);
    }

    @Override
    public String createConvertTtToKnowledgeTask(ConvertTtToKnowledgeParam param,TTInfoDTO ttInfoDTO) throws LlmCorpusException {
        // 1. 查询大象群聊天记录
        // 2. 创建异步任务，调用friday接口转换
        // 3. 任务信息落数据库
        // 4. 更新任务状态
        boolean permission = validateUserPermission(param.getMisId(),param.getRgId());
        if (!permission) {
            throw new LlmCorpusException(BizCode.NO_PERMISSION.getCode(), "用户无对应值班组权限");
        }
        String taskId = UUID.randomUUID().toString();

        //根据GroupId==-1来判断是群聊调用还是单聊调用。卡片选择值班组
        List<DxChatMessageRecord> chatRecordList;
        if(ttInfoDTO.getGroupId()==-1L){
            chatRecordList = dxGroupChatService.getSingleMergeChat(param.getCreatorDxUserId());
        }else {
            chatRecordList = dxGroupChatService.getTTChatInfo(param.getDxGroupId());
        }
        ExecutorService executor = AsyncTaskUtils.getLlmCorpusConvertTaskThreadPool();
        // 初始化任务记录
        initTaskRecord(taskId, param);

        executor.execute(() -> {
            try {
                TtChatCorpusDTO ttChatCorpusDTO = fridayService.convertTtChatToCorups(
                        param.getMisId(), ttInfoDTO, chatRecordList);
                if (ttChatCorpusDTO == null) {
                    log.error("#createConvertTtToKnowledgeTask# friday接口返回结果为空,param:{}", param);
                    throw new LlmCorpusException(BizCode.FRIDAY_CONVERT_TT_CHAT_TO_CORPUS_ERROR.getCode(),
                            "friday接口返回结果为空");
                }
                if (StringUtils.isBlank(ttChatCorpusDTO.getQuestion())) {
                    log.error("#createConvertTtToKnowledgeTask# friday接口返回结果标题为空,param:{},response:{}", param,
                            ttChatCorpusDTO);
                    throw new LlmCorpusException(BizCode.FRIDAY_CONVERT_TT_CHAT_TO_CORPUS_ERROR.getCode(),
                            "friday接口返回结果标题为空");
                }
                if (StringUtils.isBlank(ttChatCorpusDTO.getDocs())) {
                    log.error("#createConvertTtToKnowledgeTask# friday接口返回结果内容为空,param:{},response:{}", param,
                            ttChatCorpusDTO);
                    throw new LlmCorpusException(BizCode.FRIDAY_CONVERT_TT_CHAT_TO_CORPUS_ERROR.getCode(),
                            "friday接口返回结果内容为空");
                }
                llmTaskSuccess(taskId, ttChatCorpusDTO, param);
            } catch (LlmCorpusException e) {
                llmTaskFail(taskId,e.getMessage(), param);
                log.warn("#createConvertTtToKnowledgeTask# 异步任务执行失败, param:{}", param, e);
            } catch (Exception e) {
                llmTaskFail(taskId,"内部异常，任务执行失败！" , param);
                log.error("#createConvertTtToKnowledgeTask# 异步任务执行失败, param:{}", param, e);
            }
        });
        return taskId;
    }

    private void initTaskRecord(String taskId, ConvertTtToKnowledgeParam param) {
        ModelOutputTaskEntity entity = new ModelOutputTaskEntity();
        entity.setTaskId(taskId);
        entity.setTaskStatus(ModelOutputTaskStatusEnum.PROCESSING.getCode());
        entity.setTicketId(param.getTtId());
        entity.setCreatorDxId(param.getCreatorDxUserId());
        entity.setDxGroupId(param.getDxGroupId());
        entity.setCreatorMisId(param.getMisId());
        entity.setCreatorUserName(param.getCreatorUserName());
        entity.setRgId(param.getRgId());
        entity.setTitle("");
        entity.setContent("");
        entity.setPlatformId(param.getPlatformId().getCode());
        entity.setTagsIds("");
        modelOutputMapper.insertModelOutputTask(entity);
    }
    private void initTaskRecordForMerge(String taskId,String corpusId, KnowledgeMergeParam param) {
        ModelOutputTaskEntity entity = new ModelOutputTaskEntity();
        entity.setTaskId(taskId);
        entity.setTaskStatus(ModelOutputTaskStatusEnum.PROCESSING.getCode());
        entity.setTicketId(corpusId);
        entity.setCreatorDxId(0L);
        entity.setDxGroupId(0L);
        entity.setCreatorMisId(param.getMisId());
        entity.setCreatorUserName(param.getMisId());
        entity.setRgId(param.getRgId());
        entity.setTitle("");
        entity.setContent("");
        entity.setPlatformId(ConvertTaskPlatformId.WEB_MERGE.getCode());
        entity.setTagsIds("");
        modelOutputMapper.insertModelOutputTask(entity);
    }
    private void llmTaskSuccess(String taskId, TtChatCorpusDTO ttChatCorpusDTO, ConvertTtToKnowledgeParam param) {
        ModelOutputTaskEntity entity = new ModelOutputTaskEntity();
        try {
            entity.setTaskId(taskId);
            entity.setTaskStatus(ModelOutputTaskStatusEnum.SUCCESS.getCode());
            entity.setTitle(String.format("【%s】%s",param.getTtId(),ttChatCorpusDTO.getQuestion()));
            entity.setContent(ttChatCorpusDTO.getDocs());
            entity.setTaskMissingInfo(JSON.toJSONString(ttChatCorpusDTO.getMissingInfo()));
            entity.setTagsIds(ttChatCorpusDTO.getTagsIds());
            modelOutputMapper.updateModelOutputTaskStatusByTaskId(entity);
        } catch (Exception e) {
            log.error("#llmTaskSuccess#error,更新状态失败，entity:{}", entity,e);
        }
        LlmCorpusConvertTaskMessageDTO messageDTO = knowledgeBaseMapper.trans2LlmCorpusConvertTaskMessageDTO(taskId,
                ModelOutputTaskStatusEnum.SUCCESS.getCode(), param, "任务执行成功");
        sendEventMessage(messageDTO);
    }
    private void llmTaskSuccessForMerge(String taskId, String corpusId,TtChatCorpusDTO ttChatCorpusDTO, KnowledgeMergeParam param) {
        ModelOutputTaskEntity entity = new ModelOutputTaskEntity();
        try {
            entity.setTaskId(taskId);
            entity.setTaskStatus(ModelOutputTaskStatusEnum.SUCCESS.getCode());
            entity.setTitle(String.format("【%s】%s", corpusId, ttChatCorpusDTO.getQuestion()));
            entity.setContent(ttChatCorpusDTO.getDocs());
            entity.setTaskMissingInfo(JSON.toJSONString(ttChatCorpusDTO.getMissingInfo()));
            entity.setTagsIds(ttChatCorpusDTO.getTagsIds());
            modelOutputMapper.updateModelOutputTaskStatusByTaskId(entity);
        } catch (Exception e) {
            log.error("#llmTaskSuccessForMerge#error,更新状态失败，entity:{}", entity,e);
        }
    }

    @Override
    public void llmTaskFail(String taskId, String message, ConvertTtToKnowledgeParam param) {
        ModelOutputTaskEntity entity = new ModelOutputTaskEntity();
        try {
            entity.setTaskId(taskId);
            entity.setContent("");
            entity.setTaskMessage(message);
            entity.setTaskStatus(ModelOutputTaskStatusEnum.FAIL.getCode());
            modelOutputMapper.updateModelOutputTaskStatusByTaskId(entity);
        } catch (Exception e) {
            log.error("#llmTaskFail#error,更新状态失败，entity:{}", entity, e);
        }
        LlmCorpusConvertTaskMessageDTO messageDTO = knowledgeBaseMapper.trans2LlmCorpusConvertTaskMessageDTO(taskId,
                ModelOutputTaskStatusEnum.FAIL.getCode(), param, message);
        sendEventMessage(messageDTO);
    }

    private void llmTaskFailForMerge(String taskId, String message, KnowledgeMergeParam param) {
        ModelOutputTaskEntity entity = new ModelOutputTaskEntity();
        try {
            entity.setTaskId(taskId);
            entity.setContent("");
            entity.setTaskMessage(message);
            entity.setTaskStatus(ModelOutputTaskStatusEnum.FAIL.getCode());
            modelOutputMapper.updateModelOutputTaskStatusByTaskId(entity);
        } catch (Exception e) {
            log.error("#llmTaskFailForMerge#error,更新状态失败，entity:{}", entity, e);
        }
    }
    private void sendEventMessage(LlmCorpusConvertTaskMessageDTO messageDTO) {
        // 如果是web端，不发信息
        if (messageDTO.getPlatformId() == ConvertTaskPlatformId.WEB.getCode()) {
            return;
        }
        try {
            ProducerResult producerResult = sendClient.sendMessage(JSON.toJSONString(messageDTO));
            log.info(
                    "#KnowledgeBaseServiceImpl.sendEventMessage#info sendMessageClient:{},jsonMessage:{},producerResult:{}",
                    (sendClient != null ? sendClient.getTopic() : ""), messageDTO, JSON.toJSONString(producerResult));
        } catch (Exception e) {
            log.error("#KnowledgeBaseServiceImpl.sendEventMessage#error,jsonMessage:{}", JSON.toJSONString(messageDTO),
                    e);
        }
    }

    @Override
    public TtToCorpusTaskInfo queryConvertTtToKnowledgeTask(String taskId) throws LlmCorpusException {
        // 1. 查询db
        ModelOutputTaskEntity entity = modelOutputMapper.queryModelOutputByTaskId(taskId);
        if (entity == null) {
            log.warn("#queryConvertTtToKnowledgeTask# 查询结果为空, taskId:{}", taskId);
            return null;
        }
        return knowledgeBaseMapper.trans(entity);
    }

    /**
     * 根据rgId查询所有相关的转换任务信息
     *
     * @param rgId 值班组ID
     * @return 转换任务信息列表
     */
    @Override
    public PageDTO<TtToCorpusTaskInfo> queryConvertTtToKnowledgeTasksByRgId(Long rgId, int pageNum, int pageSize) {
        if (pageNum < 1) {
            pageNum = 1;
        }
        if (pageSize < 1) {
            pageSize = 10;
        }

        // 计算偏移量
        int offset = (pageNum - 1) * pageSize;

        // 获取总记录数
        int totalCount = modelOutputMapper.countModelOutputsByRgId(rgId);

        // 如果没有记录，返回空分页结果
        if (totalCount == 0) {
            log.warn("#queryConvertTtToKnowledgeTasksByRgIdWithPage# 查询结果为空, rgId:{}", rgId);
            return PageDTO.<TtToCorpusTaskInfo>builder()
                    .pageNum(pageNum)
                    .pageSize(pageSize)
                    .totalCount(0)
                    .totalPage(0)
                    .data(Collections.emptyList())
                    .build();
        }

        // 查询分页数据
        List<ModelOutputTaskEntity> entities = modelOutputMapper.queryModelOutputsByRgId(rgId, pageSize, offset);

        // 转换为TtToCorpusTaskInfo列表
        List<TtToCorpusTaskInfo> taskInfoList = entities.stream()
                .map(knowledgeBaseMapper::trans)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        // 计算总页数
        int totalPage = (int) Math.ceil((double) totalCount / pageSize);

        // 构建分页结果
        return PageDTO.<TtToCorpusTaskInfo>builder()
                .pageNum(pageNum)
                .pageSize(pageSize)
                .totalCount(totalCount)
                .totalPage(totalPage)
                .data(taskInfoList)
                .build();
    }

    /**
     * 根据rgId和misId查询所有相关的转换任务信息
     *
     * @param rgId 值班组ID
     * @param misId 用户MIS ID
     * @return 转换任务信息列表
     */
    @Override
    public PageDTO<TtToCorpusTaskInfo> queryConvertTtToKnowledgeTasksByRgIdAndMisId(Long rgId, String misId, int pageNum, int pageSize) {
        if (pageNum < 1) {
            pageNum = 1;
        }
        if (pageSize < 1) {
            pageSize = 10;
        }

        // 计算偏移量
        int offset = (pageNum - 1) * pageSize;

        // 获取总记录数
        int totalCount = modelOutputMapper.countModelOutputsByRgIdAndMisId(rgId, misId);

        // 如果没有记录，返回空分页结果
        if (totalCount == 0) {
            log.warn("#queryConvertTtToKnowledgeTasksByRgIdAndMisIdWithPage# 查询结果为空, rgId:{}, misId:{}", rgId, misId);
            return PageDTO.<TtToCorpusTaskInfo>builder()
                    .pageNum(pageNum)
                    .pageSize(pageSize)
                    .totalCount(0)
                    .totalPage(0)
                    .data(Collections.emptyList())
                    .build();
        }

        // 查询分页数据
        List<ModelOutputTaskEntity> entities = modelOutputMapper.queryModelOutputsByRgIdAndMisId(rgId, misId, pageSize, offset);

        // 转换为TtToCorpusTaskInfo列表
        List<TtToCorpusTaskInfo> taskInfoList = entities.stream()
                .map(knowledgeBaseMapper::trans)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        // 计算总页数
        int totalPage = (int) Math.ceil((double) totalCount / pageSize);

        // 构建分页结果
        return PageDTO.<TtToCorpusTaskInfo>builder()
                .pageNum(pageNum)
                .pageSize(pageSize)
                .totalCount(totalCount)
                .totalPage(totalPage)
                .data(taskInfoList)
                .build();
    }

    @Override
    public TtToCorpusTaskInfo queryConvertTtToKnowledgeTaskByTtId(String ttId) {
        List<ModelOutputTaskEntity> entities = modelOutputMapper.queryModelOutputByTicketId(ttId);
        if (entities == null) {
            log.warn("#queryConvertTtToKnowledgeTaskByTtId# 查询结果为空, ttId:{}", ttId);
            return null;
        }
        return knowledgeBaseMapper.trans(entities.get(0));
    }

    @Override
    public List<KnowledgeSimilarityRecord> queryKnowledgeSimilarity(Long rgId, String query) throws LlmCorpusException {
        // 1. 调用friday查询工作流
        if (rgId == null || rgId <= 0) {
            throw new LlmCorpusException(BizCode.ILLEGAL_ARGUMENT.getCode(), "rgId参数错误");
        }
        if (StringUtils.isBlank(query)) {
            throw new LlmCorpusException(BizCode.ILLEGAL_ARGUMENT.getCode(), "query参数错误");
        }
        List<KnowledgeSimilarityRecord> result = fridayService.getSimilarityRecord(rgId, query);
        if (CollectionUtils.isEmpty(result)) {
            log.warn("queryKnowledgeSimilarity# 查询结果为空, rgId:{}, query:{}", rgId, query);
            return Collections.emptyList();
        }
        return result;
    }

    @Override
    public List<KnowledgeSimilarityRecordWithScore> queryKnowledgeSimilarityWithScore(Long rgId, String query) throws LlmCorpusException {
        // 1. 调用friday查询工作流
        if (rgId == null || rgId <= 0) {
            throw new LlmCorpusException(BizCode.ILLEGAL_ARGUMENT.getCode(), "rgId参数错误");
        }
        if (StringUtils.isBlank(query)) {
            throw new LlmCorpusException(BizCode.ILLEGAL_ARGUMENT.getCode(), "query参数错误");
        }
        // 检索相似语料接口更新
        List<KnowledgeSimilarityRecordWithScore> result = fridayService.getSimilarityRecordWithScore(rgId, query);
        if (CollectionUtils.isEmpty(result)) {
            log.warn("queryKnowledgeSimilarityWithScore# 查询结果为空, rgId:{}, query:{}", rgId, query);
            return Collections.emptyList();
        }
        return result;
    }

    /**
     * 创建合并语料任务
     * Tips
     * 1. 如果source 是 review page 则 使用当前 ttid 做为合并后的id
     * 2. 如果source 是 web list 则 使用id list中第一条id进行加工 => "m-"+id 得到合并后的新语料的id
     * @param param
     * @return
     * @throws LlmCorpusException
     */
    @Override
    public String createKnowledgeMergeTask(KnowledgeMergeParam param) throws LlmCorpusException {
        if (param == null) {
            log.error("#createKnowledgeMergeTask# 参数为空");
            throw new LlmCorpusException(BizCode.ILLEGAL_ARGUMENT.getCode(), "参数错误");
        }
        if (param.getRgId() == null || param.getRgId() <= 0) {
            log.error("#createKnowledgeMergeTask# rgId为空");
            throw new LlmCorpusException(BizCode.ILLEGAL_ARGUMENT.getCode(), "rgId参数错误");
        }
        if (CollectionUtils.isEmpty(param.getCorpusIdList())) {
            log.error("#createKnowledgeMergeTask# CorpusIdList为空");
            throw new LlmCorpusException(BizCode.ILLEGAL_ARGUMENT.getCode(), "CorpusIdList参数错误");
        }
        String targetId = param.getCorpusIdList().get(0);
        if (!targetId.startsWith(MERGE_CORPUS_ID_PREFIX)){
            targetId = MERGE_CORPUS_ID_PREFIX + param.getCorpusIdList().get(0);
        }
        if (MergeTriggerSourceEnum.REVIEW_PAGE.getCode() == param.getTriggerSource()){
            // review页面，应该要传id
            if (StringUtils.isBlank(param.getTicketId())){
                log.error("#createKnowledgeMergeTask#error,合并语料来源为评审页，ticketId为空");
                throw LlmCorpusException.buildWithMsg(BizCode.ILLEGAL_ARGUMENT.getCode(), "合并语料来源为评审页，ticketId为空");
            }
            targetId = param.getTicketId();
        }
        // 默认最大支持10条
        if (param.getCorpusIdList().size() > mtConfigService.getMergeCorpusMaxLimit()){
            log.error("#createKnowledgeMergeTask#error,合并语料超过最大限制");
            throw LlmCorpusException.buildWithMsg(BizCode.ILLEGAL_ARGUMENT.getCode(), "合并语料超过最大限制，最大支持"+mtConfigService.getMergeCorpusMaxLimit()+"条");
        }
        boolean permission = validateUserPermission(param.getMisId(), param.getRgId());
        if (!permission) {
            throw new LlmCorpusException(BizCode.NO_PERMISSION.getCode(), "用户无对应值班组权限");
        }
        String taskId = UUID.randomUUID().toString();
        ExecutorService executor = AsyncTaskUtils.getMergeCorpusThreadPool();
        // 初始化任务记录
        initTaskRecordForMerge(taskId, targetId, param);
        
        List<KnowledgeBaseVersionEntity> knowledgeBaseVersionEntities = new ArrayList<>();
        try{
            knowledgeBaseVersionEntities = reviewMapper.findCorpusInfoByTTidRgids(param.getRgId(), param.getCorpusIdList());
        }catch (Exception e){
            log.error("#createKnowledgeMergeTask#error,查询数据失败，param:{}",param, e);
            throw LlmCorpusException.buildWithMsg(BizCode.MERGE_CORPUS_FAILED, "查询选中数据失败");
        }
        if (knowledgeBaseVersionEntities.isEmpty()){
            log.error("#createKnowledgeMergeTask#error,查询数据为空，param:{}",param);
            throw LlmCorpusException.buildWithMsg(BizCode.MERGE_CORPUS_FAILED, "查询选中数据为空");
        }
        List<String> corpusList = new ArrayList<>(param.getCorpusList());
        for (KnowledgeBaseVersionEntity entity : knowledgeBaseVersionEntities){
            corpusList.add(entity.getContent());
        }
        FridayMergeCorpusParam fridayMergeCorpusParam = FridayMergeCorpusParam.builder()
                .corpusList(corpusList)
                .triggerSource(param.getTriggerSource())
                .misId(param.getMisId())
                .rgId(param.getRgId())
                .build();
        String finalCorpusId = targetId;
        executor.execute(() -> {
            try {
                TtChatCorpusDTO ttChatCorpusDTO = fridayService.mergeCorpus(fridayMergeCorpusParam);
                if (ttChatCorpusDTO == null) {
                    log.error("#createKnowledgeMergeTask# friday接口返回结果为空,param:{}", param);
                    throw new LlmCorpusException(BizCode.FRIDAY_CONVERT_TT_CHAT_TO_CORPUS_ERROR.getCode(),
                            "friday接口返回结果为空");
                }
                if (StringUtils.isBlank(ttChatCorpusDTO.getQuestion())) {
                    log.error("#createKnowledgeMergeTask# friday接口返回结果标题为空,param:{},response:{}", param,
                            ttChatCorpusDTO);
                    throw new LlmCorpusException(BizCode.FRIDAY_CONVERT_TT_CHAT_TO_CORPUS_ERROR.getCode(),
                            "friday接口返回结果标题为空");
                }
                if (StringUtils.isBlank(ttChatCorpusDTO.getDocs())) {
                    log.error("#createKnowledgeMergeTask# friday接口返回结果内容为空,param:{},response:{}", param,
                            ttChatCorpusDTO);
                    throw new LlmCorpusException(BizCode.FRIDAY_CONVERT_TT_CHAT_TO_CORPUS_ERROR.getCode(),
                            "friday接口返回结果内容为空");
                }
                llmTaskSuccessForMerge(taskId, finalCorpusId, ttChatCorpusDTO, param);
            } catch (LlmCorpusException e) {
                llmTaskFailForMerge(taskId,e.getMessage(), param);
                log.warn("#createKnowledgeMergeTask# 异步任务执行失败, param:{}", param, e);
            } catch (Exception e) {
                llmTaskFailForMerge(taskId,"内部异常，任务执行失败！" , param);
                log.error("#createKnowledgeMergeTask# 异步任务执行失败, param:{}", param, e);
            }
        });
        return taskId;
    }

    /**
     * 统计指定值班组和用户下task_status为0的任务数量
     *
     * @param rgId 值班组ID
     * @param misId 用户MIS ID
     * @return 任务数量
     */
    @Override
    public int countModelOutputsWithAskStatusZero(Long rgId, String misId) {
        if (rgId == null || misId == null) {
            log.warn("#countModelOutputsWithAskStatusZero# 参数无效, rgId:{}, misId:{}", rgId, misId);
            return 0;
        }

        try {
            int count = modelOutputMapper.countModelOutputsWithAskStatusZero(rgId, misId);
            log.info("#countModelOutputsWithAskStatusZero# 统计完成, rgId:{}, misId:{}, count:{}", rgId, misId, count);
            return count;
        } catch (Exception e) {
            log.error("#countModelOutputsWithAskStatusZero# 统计异常, rgId:{}, misId:{}", rgId, misId, e);
            return 0;
        }
    }

    @Override
    public ContentQualityDTO getContentQualityAssessment(String query) throws LlmCorpusException {
        // 1. 调用friday服务评估语料质量
        if (StringUtils.isBlank(query)) {
            throw new LlmCorpusException(BizCode.ILLEGAL_ARGUMENT.getCode(), "query参数错误");
        }
        try {
            ContentQualityDTO result = fridayService.getContentQualityAssessment(query);
            log.info("getContentQualityAssessment# 评估完成, query:{}, result:{}", query, JSON.toJSONString(result));
            return result;
        } catch (Exception e) {
            log.error("getContentQualityAssessment# 评估异常, query:{}", query, e);
            throw LlmCorpusException.build(BizCode.FRIDAY_CONTENT_QUALITY_ASSESSMENT_ERROR);
        }
    }

    @Override
    public List<KmToQaCorpusDTO> queryConvertKmToQaAsList(String taskId) throws LlmCorpusException {
        // 1. 查询db
        ModelOutputTaskEntity entity = modelOutputMapper.queryModelOutputByTaskId(taskId);
        if (entity == null) {
            log.warn("#queryConvertKmToQaAsList# 查询结果为空, taskId:{}", taskId);
            return null;
        }
        List<KmToQaCorpusDTO> result = new ArrayList<>();
        try {
            String allContent = entity.getContent();
            LlmKmToCorpusDTO.LlmKmToCorpusTaskResultDTO data = null;
            try {
                data = JSON.parseObject(allContent, LlmKmToCorpusDTO.LlmKmToCorpusTaskResultDTO.class);
            } catch (Exception e) {
                log.error("#queryConvertKmToQaAsList# JSON解析失败，实际内容: {}, taskId: {}, 错误: {}", allContent, taskId, e.getMessage(), e);
                KmToQaCorpusDTO dto = new KmToQaCorpusDTO();
                dto.setTitle(entity.getTitle());
                dto.setContent(entity.getContent());
                dto.setTagsIds(entity.getTagsIds());
                result.add(dto);
                log.info("#queryConvertKmToQaAsList# 转换失败，当前知识无法转换语料, taskId:{}, size:{}", taskId, result.size());
                return result;
            }
            data.getKmQaList().forEach(item -> {
                KmToQaCorpusDTO dto = new KmToQaCorpusDTO();
                dto.setTitle(item.getResultDoc().getQuestion());
                dto.setContent(item.getResultDoc().getDocs());
                dto.setTaskMissingInfo(item.getMissingInfo());
                dto.setTagsIds(entity.getTagsIds());
                result.add(dto);
            });
            log.info("#queryConvertKmToQaAsList# 转换完成, taskId:{}, size:{}", taskId, result.size());
            return result;
        } catch (Exception e) {
            log.error("#queryConvertKmToQaAsList# 转换异常, taskId:{}", taskId, e);
            return result;
        }
    }

}
