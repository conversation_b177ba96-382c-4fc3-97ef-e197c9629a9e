package com.meituan.banma.llm.corpus.server.utils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.meituan.banma.llm.corpus.server.common.MtConfigService;
import com.meituan.banma.llm.corpus.server.rpc.dx.XmAuthRpcService;
import com.sankuai.dxenterprise.open.gateway.service.citadel.api.CitadelService;
import com.sankuai.dxenterprise.open.gateway.service.citadel.api.req.GetAttachmentS3UrlReq;
import com.sankuai.dxenterprise.open.gateway.service.citadel.api.resp.GetAttachmentS3UrlResp;
import com.sankuai.xm.openplatform.common.entity.RespStatus;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.nodes.Node;
import org.jsoup.nodes.TextNode;
import org.jsoup.select.Elements;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * JSON转HTML工具类
 */
@Component
public class JsonToHtmlConverterUtil implements ApplicationContextAware {
    
    private static ApplicationContext applicationContext;
    
    @Override
    public void setApplicationContext(ApplicationContext context) {
        applicationContext = context;
    }
    
    /**
     * 获取JsonToHtmlConverterUtil实例
     * @return JsonToHtmlConverterUtil实例
     */
    public static JsonToHtmlConverterUtil getInstance() {
        return applicationContext.getBean(JsonToHtmlConverterUtil.class);
    }

    @Autowired
    private XmAuthRpcService xmAuthRpcService;

    @Autowired
    private CitadelService citadelService;
    
    @Autowired
    private MtConfigService mtConfigService;

    /**
     * 将JSON字符串转换为HTML字符串
     * @param jsonString JSON字符串
     * @return HTML字符串
     */
    public String convertJsonToHtml(String jsonString) {
        if (jsonString == null || jsonString.isEmpty()) {
            return "";
        }

        JSONObject jsonObject = JSONObject.parseObject(jsonString);
        StringBuilder htmlBuilder = new StringBuilder();

        convertNodeToHtml(jsonObject, htmlBuilder);

        return htmlBuilder.toString();
    }

    /**
     * 递归将JSON节点转换为HTML
     * @param node JSON节点
     * @param htmlBuilder HTML构建器
     */
    private void convertNodeToHtml(JSONObject node, StringBuilder htmlBuilder) {
        String type = node.getString("type");
        JSONArray content = node.getJSONArray("content");
        JSONObject attrs = node.getJSONObject("attrs");
        String accessToken = xmAuthRpcService.getToken();
        String kmSsoidToken = xmAuthRpcService.getKmSsoidToken();
        switch (type) {
            case "doc":
                if (content != null) {
                    for (int i = 0; i < content.size(); i++) {
                        convertNodeToHtml(content.getJSONObject(i), htmlBuilder);
                    }
                }
                break;
            case "title":
                htmlBuilder.append("<h1>");
                appendContent(content, htmlBuilder);
                htmlBuilder.append("</h1>");
                break;
            case "paragraph":
                htmlBuilder.append("<p>");
                appendContent(content, htmlBuilder);
                htmlBuilder.append("</p>");
                break;
            case "heading":
                int level = attrs != null ? attrs.getIntValue("level") : 1;
                htmlBuilder.append("<h").append(level).append(">");
                appendContent(content, htmlBuilder);
                htmlBuilder.append("</h").append(level).append(">");
                break;
            case "link":
                if (attrs != null) {
                    String href = attrs.getString("href");
                    htmlBuilder.append("<a href='").append(href).append("'>");
                    appendContent(content, htmlBuilder);
                    htmlBuilder.append("</a>");
                }
                break;
            case "image":
                if (attrs != null) {
                    String src = attrs.getString("origin"); // Using 'origin' for the original image link
                    String s3Url = src;
                    if(src != null && mtConfigService != null && src.startsWith(mtConfigService.getKmImagePrefix())){
                        String regex = ".*/(\\d+)\\?";
                        Pattern pattern = Pattern.compile(regex);
                        Matcher matcher = pattern.matcher(src);
                        String extractedNumber = "";
                        if (matcher.find()) {
                            extractedNumber = matcher.group(1);
                            GetAttachmentS3UrlReq req = new GetAttachmentS3UrlReq();
                            GetAttachmentS3UrlReq.Request request = new GetAttachmentS3UrlReq.Request();
                            request.setAttachmentId(Long.parseLong(extractedNumber));
                            req.setRequest(request);
                            GetAttachmentS3UrlResp attachmentS3Url = null;
                            try {
                                attachmentS3Url = citadelService.getAttachmentS3Url(accessToken, kmSsoidToken, req);
                            } catch (Exception e) {
                                throw new RuntimeException(e);
                            }
                            RespStatus status = attachmentS3Url.getStatus();
                            if(status.getCode() == 0) {
                                s3Url = attachmentS3Url.getData().getS3Url();
                            }
                        }
                    }
                    String width = String.valueOf(attrs.getIntValue("width"));
                    String height = String.valueOf(attrs.getIntValue("height"));
                    htmlBuilder.append("<img src='").append(s3Url).append("' width='").append(width).append("' height='").append(height).append("' alt='image' />");
                }
                break;
            case "table":
                htmlBuilder.append("<table>");
                if (content != null) {
                    for (int i = 0; i < content.size(); i++) {
                        htmlBuilder.append("<tr>");
                        JSONObject row = content.getJSONObject(i);
                        JSONArray cells = row.getJSONArray("content");
                        if (cells != null) {
                            for (int j = 0; j < cells.size(); j++) {
                                htmlBuilder.append("<td>");
                                convertNodeToHtml(cells.getJSONObject(j), htmlBuilder);
                                htmlBuilder.append("</td>");
                            }
                        }
                        htmlBuilder.append("</tr>");
                    }
                }
                htmlBuilder.append("</table>");
                break;
            default:
                appendContent(content, htmlBuilder);
                break;
        }
    }

    /**
     * 添加内容到HTML构建器
     * @param content JSON内容数组
     * @param htmlBuilder HTML构建器
     */
    private void appendContent(JSONArray content, StringBuilder htmlBuilder) {
        if (content != null) {
            for (int i = 0; i < content.size(); i++) {
                JSONObject contentItem = content.getJSONObject(i);
                if (contentItem.containsKey("text")) {
                    htmlBuilder.append(contentItem.getString("text"));
                } else {
                    convertNodeToHtml(contentItem, htmlBuilder);
                }
            }
        }
    }

    /**
     * 将HTML根据标题转换为字符串列表
     * @param html HTML字符串
     * @return 字符串列表
     */
    public List<String> convertHtmlToList(String html) {
        List<String> result = new ArrayList<>();
        Document doc = Jsoup.parse(html);
        Elements elements = doc.body().children();

        Map<Integer, String> titlesMap = new HashMap<>();
        boolean hasContent = false;
        StringBuilder accumulatedContent = new StringBuilder();
        String lastTag = "";

        for (Element element : elements) {
            if (element.tagName().matches("h\\d")) {
                int level = Integer.parseInt(element.tagName().substring(1));

                // 在处理标题前，先处理积累的内容
                if (accumulatedContent.length() > 0) {
                    appendTitles(accumulatedContent, titlesMap);
                    result.add(accumulatedContent.toString().trim());
                    accumulatedContent.setLength(0);
                }

                if (!hasContent) {
                    // 没有正文内容时，拼接同级标题
                    titlesMap.merge(level, element.text(), (oldValue, newValue) -> oldValue + "/" + newValue);
                } else {
                    // 有正文内容时，替换同级标题并移除低等级标题
                    titlesMap.put(level, element.text());
                    titlesMap.keySet().removeIf(key -> key > level);
                    hasContent = false;
                }

                lastTag = "h";
            } else if ("p".equals(element.tagName()) || "table".equals(element.tagName())) {
                appendContentWithLinksAndTable(element, accumulatedContent);
                hasContent = true;
                lastTag = element.tagName();
            }
        }

        if (accumulatedContent.length() > 0) {
            appendTitles(accumulatedContent, titlesMap);
            result.add(accumulatedContent.toString().trim());
        }

        return result;
    }

    private static void appendElementContent(Element element, StringBuilder builder) {
        for (Node child : element.childNodes()) {
            if (child instanceof TextNode) {
                builder.append(((TextNode) child).text());
            } else if (child instanceof Element) {
                Element childElement = (Element) child;
                if ("a".equals(childElement.tagName())) {
                    appendLinkText(childElement, builder);
                } else if ("table".equals(childElement.tagName())) {
                    appendTableContent(childElement, builder);
                } else if ("img".equals(childElement.tagName())) {  // 新增图片处理
                    appendImageText(childElement, builder);
                } else {
                    appendElementContent(childElement, builder);
                }
            }
        }
    }

    private static void appendContentWithLinksAndTable(Element element, StringBuilder builder) {
        if ("table".equals(element.tagName())) {
            appendTableContent(element, builder); // 表格有自己的换行逻辑
        } else {
            appendElementContent(element, builder);
            if ("p".equals(element.tagName())) {
                builder.append("\n"); // 仅段落添加换行
            }
        }
    }

    private static void appendLinkText(Element element, StringBuilder builder) {
        String href = element.attr("href");
        if (href.isEmpty()) {
            builder.append(element.text());
        } else {
            builder.append("[链接: ").append(element.text()).append(" (").append(href).append(")]");
        }
    }

    private static void appendImageText(Element element, StringBuilder builder) {
        String src = element.attr("src");
        String alt = element.attr("alt");
        if (src.isEmpty()) {
            builder.append("[图片: 无效地址]");
        } else {
            builder.append("[图片: ").append(alt.isEmpty() ? "无描述" : alt).append(" (").append(src).append(")]");
        }
    }

    private static void appendTableContent(Element table, StringBuilder builder) {
        for (Element row : table.select("tr")) {
            Elements cells = row.select("th, td");
            StringBuilder rowBuilder = new StringBuilder();
            for (Element cell : cells) {
                // 独立处理单元格内容，避免受外部递归影响
                StringBuilder cellContent = new StringBuilder();
                appendPlainContent(cell, cellContent); // 仅提取文本和链接，不处理段落换行
                rowBuilder.append(cellContent.toString().trim()).append(" | ");
            }
            if (!cells.isEmpty()) {
                rowBuilder.setLength(rowBuilder.length() - 3); // 移除末尾 " | "
            }
            builder.append(rowBuilder.toString()).append("\n"); // 强制换行
        }
    }

    // 仅提取文本和链接，不处理段落换行
    private static void appendPlainContent(Element element, StringBuilder builder) {
        for (Node child : element.childNodes()) {
            if (child instanceof TextNode) {
                builder.append(((TextNode) child).text());
            } else if (child instanceof Element) {
                Element childElement = (Element) child;
                if ("a".equals(childElement.tagName())) {
                    appendLinkText(childElement, builder);
                } else if ("img".equals(childElement.tagName())) {  // 新增图片处理
                    appendImageText(childElement, builder);
                } else {
                    appendPlainContent(childElement, builder);
                }
            }
        }
    }

    private static void appendTitles(StringBuilder builder, Map<Integer, String> titlesMap) {
        if (!titlesMap.isEmpty()) {
            String titles = titlesMap.entrySet().stream()
                    .sorted(Map.Entry.comparingByKey())
                    .map(Map.Entry::getValue)
                    .collect(Collectors.joining("/"));
            builder.insert(0, titles + "\n");
        }
    }

    /**
     * 解析HTML中的表格
     * @param html HTML字符串
     * @return 表格内容列表
     */
    public List<String> parseTables(String html) {
        List<String> result = new ArrayList<>();
        Document doc = Jsoup.parse(html);
        Elements tables = doc.select("table");
        for (Element table : tables) {
            parseTable(table, result, "");
        }
        return result;
    }

    private static void parseTable(Element table, List<String> result, String parentHeaders) {
        // 处理表头（兼容无表头场景）
        Elements headerRow = table.select("tr:has(th)");
        if (headerRow.isEmpty()) {
            headerRow = table.select("tr:first-child");
        }
        Elements headerCells = headerRow.select("th, td");
        String headers = parentHeaders + headerCells.stream()
                .map(cell -> parseCellContent(cell))
                .collect(Collectors.joining(" | "));

        // 处理数据行
        Elements rows = table.select("tr");
        for (int i = 1; i < rows.size(); i++) { // 跳过表头行
            Element row = rows.get(i);
            Elements cells = row.select("td, th");
            List<String> currentRowValues = new ArrayList<>();
            boolean hasNestedTable = false;

            for (Element cell : cells) {
                // 提取单元格内容（含图片、链接）
                String cellContent = parseCellContent(cell);
                currentRowValues.add(cellContent);

                // 检查嵌套表格
                Elements nestedTables = cell.select("table");
                if (!nestedTables.isEmpty()) {
                    hasNestedTable = true;
                    for (Element nestedTable : nestedTables) {
                        parseTable(nestedTable, result, headers + "\n" + String.join(" | ", currentRowValues) + " | ");
                    }
                }
            }

            if (!hasNestedTable) {
                String rowData = String.join(" | ", currentRowValues);
                result.add(headers + "\n" + rowData);
            }
        }
    }

    // 递归解析单元格内容（支持图片、链接）
    private static String parseCellContent(Element cell) {
        StringBuilder builder = new StringBuilder();
        for (Node node : cell.childNodes()) {
            if (node instanceof TextNode) {
                builder.append(((TextNode) node).text());
            } else if (node instanceof Element) {
                Element childElement = (Element) node;
                if ("a".equals(childElement.tagName())) {
                    // 处理链接
                    String href = childElement.attr("href");
                    String text = childElement.text();
                    builder.append("[链接->")
                            .append(text)
                            .append(" (")
                            .append(href)
                            .append(")]");
                } else if ("img".equals(childElement.tagName())) {
                    // 处理图片
                    String alt = childElement.attr("alt");
                    String src = childElement.attr("src");
                    builder.append("[图片: ").append(alt).append(" (").append(src).append(")]");
                } else if ("table".equals(childElement.tagName())) {
                    // 嵌套表格已在 parseTable 中处理，此处跳过
                } else {
                    // 递归处理其他元素（如 div、span 等）
                    builder.append(parseCellContent(childElement));
                }
            }
        }
        return builder.toString().trim();
    }
}
