package com.meituan.banma.llm.corpus.server.common.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class MonitoringGroupConfig {
    private Long monitoringGroupId;
    private String monitoringGroupName;
    private String monitoringGroupDesc;
    private List<String> monitoringGroupOwner;
    private List<Long> dxGroupIds;
    private List<String> monitoredOrgIds;
    private List<String> monitoredMisIds;
    private List<String> keywords;
    private List<QuestionType> questionTypes;
    /**
     * 监控时间范围类型 0 前一天 1 前一周 2 前七天
     */
    private int monitoringTimeRangeType;
}
