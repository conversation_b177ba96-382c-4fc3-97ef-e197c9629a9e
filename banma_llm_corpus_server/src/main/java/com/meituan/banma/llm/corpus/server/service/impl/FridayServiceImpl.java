package com.meituan.banma.llm.corpus.server.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.dianping.lion.client.util.CollectionUtils;
import com.google.common.collect.Lists;
import com.meituan.banma.llm.corpus.api.constants.BizCode;
import com.meituan.banma.llm.corpus.api.exception.LlmCorpusException;
import com.meituan.banma.llm.corpus.server.common.MtConfigService;
import com.meituan.banma.llm.corpus.server.common.constants.enums.FridayUtteranceTypeEnum;
import com.meituan.banma.llm.corpus.server.common.domain.dto.*;
import com.meituan.banma.llm.corpus.server.common.domain.dto.stat.AppStatusDTO;
import com.meituan.banma.llm.corpus.server.dal.entity.KnowledgeBaseVersionEntity;
import com.meituan.banma.llm.corpus.server.dal.mapper.ReviewMapper;
import com.meituan.banma.llm.corpus.server.dal.mapper.RgBackgroundKnowledgeMapper;
import com.meituan.banma.llm.corpus.server.dal.mapper.RgSopMapper;
import com.meituan.banma.llm.corpus.server.dal.mapper.RgRuleMapper;
import com.meituan.banma.llm.corpus.server.dal.entity.RgBackgroundKnowledgeEntity;
import com.meituan.banma.llm.corpus.server.dal.entity.RgSopEntity;
import com.meituan.banma.llm.corpus.server.dal.entity.RgRuleEntity;
import com.meituan.banma.llm.corpus.server.rpc.friday.FridayRpcService;
import com.meituan.banma.llm.corpus.server.rpc.friday.dto.*;
import com.meituan.banma.llm.corpus.server.service.IFridayService;
import com.meituan.banma.llm.corpus.server.service.mapper.FridayMapper;
import com.meituan.banma.llm.corpus.server.utils.AsyncTaskUtils;
import com.meituan.banma.llm.corpus.server.utils.DegradeUtils;
import com.meituan.banma.llm.corpus.server.utils.TagsUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
@Service
public class FridayServiceImpl implements IFridayService {
    @Autowired
    FridayRpcService fridayRpcService;

    @Autowired
    MtConfigService mtConfigService;

    @Autowired
    private ReviewMapper reviewMapper;

    @Autowired
    private TagsUtil tagsUtil;

    @Autowired
    private RgBackgroundKnowledgeMapper rgBackgroundKnowledgeMapper;

    @Autowired
    private RgSopMapper rgSopMapper;

    @Autowired
    private RgRuleMapper rgRuleMapper;

    private final static Pattern TT_ID_PATTERN = Pattern.compile("【([a-zA-Z0-9-]+)】");

    FridayMapper fridayMapper = FridayMapper.get();
    @Override
    public TtChatCorpusDTO convertTtChatToCorups(String misId, TTInfoDTO ttInfo, List<DxChatMessageRecord> chatRecord) throws LlmCorpusException {
        //TODO 降级开关
        try{
            FridayConversationConfig fridayConversationConfig = mtConfigService.getFridayConversationConfig();
            String token = fridayRpcService.getFridayAccessToken();
            FridayConversationParams params = new FridayConversationParams();
            params.setUserId(misId);
            params.setUserType(fridayConversationConfig.getDefaultUserType());
            params.setStream(false);
            params.setDebug(false);
            params.setMultiTurn(false);
            params.setRedo(false);
            params.setAccessToken(token);
            params.setAppId(fridayConversationConfig.getAppId());
            FridayConversationUtterance utterance = fridayMapper.trans2FridayConversationUtterance(ttInfo, chatRecord);
            params.setUtterances(Lists.newArrayList(JSON.toJSONString(utterance)));
            FridayConversationResponseDataDTO fridayConversationResponseDTO = fridayRpcService.conversation(params);
            if (fridayConversationResponseDTO == null || CollectionUtils.isEmpty(fridayConversationResponseDTO.getContents())) {
                log.warn("convertTTChatToWiki#error,fridayConversationResponseDTO为空 ttInfo:{}, chatRecord:{}，fridayConversationResponseDTO:{}", ttInfo, chatRecord,fridayConversationResponseDTO);
                return null;
            }
            
            String responseText = fridayConversationResponseDTO.getContents().get(0).getText();
            log.info("#convertTTChatToWiki# 准备解析JSON内容: {}", responseText);
            
            LlmAnswerDTO answerDTO = null;
            try {
                answerDTO = JSON.parseObject(responseText, LlmAnswerDTO.class);
            } catch (Exception e) {
                log.error("#convertTTChatToWiki# JSON解析失败，实际内容: {}, 错误: {}", responseText, e.getMessage(), e);
                throw LlmCorpusException.buildWithMsg(BizCode.FRIDAY_CONVERT_TT_CHAT_TO_CORPUS_ERROR, "JSON解析失败: " + e.getMessage());
            }
            
            if (answerDTO == null) {
                log.warn("convertTTChatToWiki#error, ttInfo:{}, chatRecord:{},fridayConversationResponseDTO:{}", ttInfo, chatRecord,fridayConversationResponseDTO);
                throw LlmCorpusException.buildWithMsg(BizCode.FRIDAY_CONVERT_TT_CHAT_TO_CORPUS_ERROR, "模型返回结果为空");
            }
            if (answerDTO.getCode() != 0 || answerDTO.getData() == null) {
                log.warn("convertTTChatToWiki#error, ttInfo:{}, chatRecord:{},fridayConversationResponseDTO:{}", ttInfo, chatRecord,fridayConversationResponseDTO);
                throw LlmCorpusException.buildWithMsg(BizCode.FRIDAY_CONVERT_TT_CHAT_TO_CORPUS_ERROR, answerDTO.getMessage()==null?"模型未返回结果":answerDTO.getMessage());
            }
            return fridayMapper.trans2TtChatCorpusDTO(answerDTO);
        } catch (LlmCorpusException e){
            throw e;
        } catch (Exception e) {
            log.error("#KnowledgeBaseServiceImpl.convertTTChatToWiki#error, ttInfo:{}, chatRecord:{}",ttInfo,chatRecord, e);
            throw LlmCorpusException.build(BizCode.FRIDAY_CONVERT_TT_CHAT_TO_CORPUS_ERROR);
        }
    }

    @Override
    public List<KnowledgeSimilarityRecord> getSimilarityRecord(Long rgId, String query) throws LlmCorpusException {
        // TODO 降级
        try{
            FridayConversationConfig fridayConversationConfig = mtConfigService.getFridaySimilarityConversationConfig();
            String token = fridayRpcService.getFridayAccessToken();
            FridayConversationParams params = new FridayConversationParams();
            params.setUserId(fridayConversationConfig.getDefaultUserId());
            params.setUserType(fridayConversationConfig.getDefaultUserType());
            params.setStream(false);
            params.setDebug(false);
            params.setMultiTurn(false);
            params.setRedo(false);
            params.setAccessToken(token);
            params.setAppId(fridayConversationConfig.getAppId());
            FridayQuerySimilarityConversationUtterance utterance = fridayMapper.trans2FridayQuerySimilarityConversationUtterance(rgId, query);
            params.setUtterances(Lists.newArrayList(JSON.toJSONString(utterance)));
            FridayConversationResponseDataDTO fridayConversationResponseDTO = fridayRpcService.conversation(params);

            if (fridayConversationResponseDTO == null || CollectionUtils.isEmpty(fridayConversationResponseDTO.getContents())) {
                log.warn("getSimilarityRecord#error,fridayConversationResponseDTO为空 query:{}, rgId:{}，fridayConversationResponseDTO:{}", query, rgId, fridayConversationResponseDTO);
                return null;
            }
            if (fridayConversationResponseDTO.getMetaInfo() == null || CollectionUtils.isEmpty(fridayConversationResponseDTO.getMetaInfo().getWorkflow())) {
                log.warn("getSimilarityRecord#error,fridayConversationResponseDTO metaInfo为空,resp:{}", fridayConversationResponseDTO);
                return null;
            }
            FridayConversationResponseDataDTO.Workflow workflows = fridayConversationResponseDTO.getMetaInfo().getWorkflow().get(0);
            if (CollectionUtils.isEmpty(workflows.getOutputs())){
                log.warn("getSimilarityRecord#error,fridayConversationResponseDTO workflow outputs为空,resp:{}", fridayConversationResponseDTO);
                return null;
            }
            FridayConversationResponseDataDTO.Output output = workflows.getOutputs().get(0);
            if (output == null || CollectionUtils.isEmpty(output.getValue())){
                log.warn("getSimilarityRecord#error,fridayConversationResponseDTO output 为空,resp:{}", fridayConversationResponseDTO);
                return null;
            }
            return fridayMapper.trans2KnowledgeSimilarityRecordList(output.getValue());
        }catch (Exception e) {
            log.error("#getSimilarityRecord#error, query:{}, rgId:{}", query, rgId, e);
            throw LlmCorpusException.build(BizCode.FRIDAY_QUERY_SIMILARITY_ERROR);
        }
    }

    @Override
    public List<KnowledgeSimilarityRecordWithScore> getSimilarityRecordWithScore(Long rgId, String query) throws LlmCorpusException {
        try {
            // 调用 getSimilarityRecord 方法得到Friday处理后的结果
            List<KnowledgeSimilarityRecord> records = getSimilarityRecord(rgId, query);
            if (records == null || records.isEmpty()) {
                log.warn("getSimilarityRecordWithScore#error, records为空 query:{}, rgId:{}, records:{}", query, rgId, records);
                return Collections.emptyList();
            }

            log.info("getSimilarityRecordWithScore#成功获取到 records: {}", records);

            // 提取 ticketId 和 score
            List<String> ticketIds = new ArrayList<>();
            List<Double> scores = new ArrayList<>();
            for (KnowledgeSimilarityRecord record : records) {
                String text = record.getText();
                double score = record.getScore();

                // 从 text 中提取 ttId
                String ticketId = extractTtId(text);
                if (ticketId == null) {
                    log.warn("getSimilarityRecordWithScore#error, 未提取到 ttId text:{}", text);
                    continue;
                }

                // 添加到列表
                ticketIds.add(ticketId);
                scores.add(score);
            }

            // 先根据 rgId 和 ticketIds 列表批量查询语料信息
            List<KnowledgeBaseVersionEntity> corpusInfoEntities = reviewMapper.findCorpusInfoByTTidRgids(rgId, ticketIds);
            if (CollectionUtils.isEmpty(corpusInfoEntities)) {
                log.warn("getSimilarityRecordWithScore#error, 未查询到任何语料信息 rgId:{}, ticketIds:{}", rgId, ticketIds);
                return Collections.emptyList();
            }

            // 获取查询到的语料所对应的实际ticketId列表
            List<String> actualTicketIds = corpusInfoEntities.stream()
                    .map(KnowledgeBaseVersionEntity::getTicketId)
                    .collect(Collectors.toList());
            
            // 根据实际的ticketId列表查询创建时间
            List<Timestamp> createTimeList = reviewMapper.findCreateTimestampListByTicketId(actualTicketIds, rgId);

            // 建立ticketId到创建时间的映射
            Map<String, Timestamp> ticketIdToCreateTimeMap = new HashMap<>();
            for (int i = 0; i < Math.min(actualTicketIds.size(), createTimeList.size()); i++) {
                ticketIdToCreateTimeMap.put(actualTicketIds.get(i), createTimeList.get(i));
            }

            // 建立ticketId到相似度得分的映射
            Map<String, Double> ticketIdToScoreMap = new HashMap<>();
            for (int i = 0; i < ticketIds.size(); i++) {
                ticketIdToScoreMap.put(ticketIds.get(i), scores.get(i));
            }

            // 将实体转换为 CorpusInfoDTO 并构建返回结果
            List<KnowledgeSimilarityRecordWithScore> result = new ArrayList<>();
            for (KnowledgeBaseVersionEntity entity : corpusInfoEntities) {
                String ticketId = entity.getTicketId();
                Double score = ticketIdToScoreMap.get(ticketId);
                
                if (score == null) {
                    log.warn("getSimilarityRecordWithScore#warn, 未找到对应的score，使用默认值0 ticketId:{}", ticketId);
                    score = 0.0;
                }
                
                // 创建对象
                KnowledgeSimilarityRecordWithScore recordWithScore = new KnowledgeSimilarityRecordWithScore();
                recordWithScore.setTicketId(ticketId);
                recordWithScore.setTitle(entity.getTitle());
                recordWithScore.setContent(entity.getContent());
                recordWithScore.setType(entity.getType());
                recordWithScore.setSource(entity.getSource());
                recordWithScore.setMisId(entity.getMisId());
                recordWithScore.setCreateTime(ticketIdToCreateTimeMap.get(ticketId));
                recordWithScore.setUpdateTime(entity.getTimestamp());
                recordWithScore.setScore(score);
                
                // 获取并设置标签名称
                try {
                    List<String> tagNames = tagsUtil.getTagsNamesByIds(entity.getTagsIds());
                    recordWithScore.setTagsname(tagNames);
                } catch (Exception e) {
                    log.warn("getSimilarityRecordWithScore# 获取标签名称失败, ticketId: {}, tagsIds: {}, 异常: {}", 
                             ticketId, entity.getTagsIds(), e.getMessage());
                    recordWithScore.setTagsname(new ArrayList<>());
                }

                // 根据ID查询对应的backgroundKnowledge、sop、rule内容
                if (entity.getBackgroundKnowledgeId() != null) {
                    try {
                        RgBackgroundKnowledgeEntity bgEntity = rgBackgroundKnowledgeMapper.queryById(entity.getBackgroundKnowledgeId());
                        recordWithScore.setBackgroundKnowledge(bgEntity != null ? bgEntity.getKnowledgeContent() : null);
                    } catch (Exception e) {
                        log.warn("getSimilarityRecordWithScore# 查询背景知识失败, ticketId: {}, id: {}, 异常: {}", 
                                 ticketId, entity.getBackgroundKnowledgeId(), e.getMessage());
                        recordWithScore.setBackgroundKnowledge(null);
                    }
                }

                if (entity.getCorpusSopId() != null) {
                    try {
                        RgSopEntity sopEntity = rgSopMapper.queryById(entity.getCorpusSopId());
                        recordWithScore.setSop(sopEntity != null ? sopEntity.getSop() : null);
                    } catch (Exception e) {
                        log.warn("getSimilarityRecordWithScore# 查询SOP失败, ticketId: {}, id: {}, 异常: {}", 
                                 ticketId, entity.getCorpusSopId(), e.getMessage());
                        recordWithScore.setSop(null);
                    }
                }

                if (entity.getRuleId() != null) {
                    try {
                        RgRuleEntity ruleEntity = rgRuleMapper.queryById(entity.getRuleId());
                        recordWithScore.setRule(ruleEntity != null ? ruleEntity.getRule() : null);
                    } catch (Exception e) {
                        log.warn("getSimilarityRecordWithScore# 查询Rule失败, ticketId: {}, id: {}, 异常: {}", 
                                 ticketId, entity.getRuleId(), e.getMessage());
                        recordWithScore.setRule(null);
                    }
                }
                
                result.add(recordWithScore);
            }

            return result;
        } catch (Exception e) {
            log.error("#getSimilarityRecordWithScore#error, query:{}, rgId:{}", query, rgId, e);
            throw LlmCorpusException.build(BizCode.FRIDAY_QUERY_SIMILARITY_ERROR2);
        }
    }

    /**
     * 从 text 中提取 ttId
     *
     * @param text 包含 ttId 的文本
     * @return 提取到的 ttId，如果未找到则返回 null
     */
    @Override
    public String extractTtId(String text) throws LlmCorpusException {
        if (text == null || text.isEmpty()) {
            return null;
        }

        Matcher matcher = TT_ID_PATTERN.matcher(text);
        if (matcher.find()) {
            try {
                return matcher.group(1);
            } catch (Exception e) {
                log.error("#extractTtId#error, text:{}", text, e);
                throw LlmCorpusException.build(BizCode.TTID_EXTRACT_ERROR);
            }
        }
        return null;
    }


    @Override
    public ImageToTextResultDTO convertImageToText(String misId, String message, String imageUrl) throws LlmCorpusException {
        ImageToTextResultDTO resultDTO = new ImageToTextResultDTO();
        resultDTO.setUrl(imageUrl);
        resultDTO.setText("识别图片内容失败。");
        if (DegradeUtils.degradeImageToText()){
            log.warn("convertImageToText#degrade,mis:{},message:{},imageUrl:{}", misId, message, imageUrl);
            return resultDTO;
        }
        try{
            FridayConversationConfig fridayConversationConfig = mtConfigService.getFridayImageToTextConversationConfig();
            String token = fridayRpcService.getFridayAccessToken();
            FridayConversationParams params = new FridayConversationParams();
            params.setUserId(fridayConversationConfig.getDefaultUserId());
            if (StringUtils.isNotBlank(misId)){
                params.setUserId(misId);
            }
            params.setUserType(fridayConversationConfig.getDefaultUserType());
            params.setStream(false);
            params.setDebug(false);
            params.setMultiTurn(false);
            params.setRedo(false);
            params.setAccessToken(token);
            params.setAppId(fridayConversationConfig.getAppId());
            List<Object> utterances = Lists.newArrayList();
            FridayConversationCommonUtterance imageUtterance = FridayConversationCommonUtterance.builder()
                    .type(FridayUtteranceTypeEnum.IMAGE.getType())
                    .content(imageUrl)
                    .build();
            utterances.add(imageUtterance);
            FridayConversationCommonUtterance textUtterance = FridayConversationCommonUtterance.builder()
                    .type(FridayUtteranceTypeEnum.TEXT.getType())
                    .content(message)
                    .build();
            utterances.add(textUtterance);
            params.setUtterances(utterances);
            FridayConversationResponseDataDTO fridayConversationResponseDTO = fridayRpcService.conversation(params);
            if (fridayConversationResponseDTO == null || CollectionUtils.isEmpty(fridayConversationResponseDTO.getContents())) {
                log.warn("convertImageToText#error,fridayConversationResponseDTO为空 message:{}, image:{}，fridayConversationResponseDTO:{}", message, imageUrl,fridayConversationResponseDTO);
                return resultDTO;
            }
            FridayConversationResponseDataDTO.Content content = fridayConversationResponseDTO.getContents().get(0);
            if (content == null || content.getText() == null) {
                log.warn("convertImageToText#error,fridayConversationResponseDTO content为空,resp:{}", fridayConversationResponseDTO);
                return resultDTO;
            }
            resultDTO.setText(content.getText());
            return resultDTO;
        }catch (Exception e) {
            log.error("#convertImageToText#error, message:{}, image:{}", message, imageUrl, e);
            throw LlmCorpusException.build(BizCode.FRIDAY_IMAGE_TO_TEXT_ERROR);
        }

    }

    @Override
    public List<ImageToTextResultDTO> convertImageToTextBatch(String misId, String message, List<String> imageUrls) {
        ExecutorService batchImageToTextExecutor = AsyncTaskUtils.getBatchImageToTextThreadPool();
        if (CollectionUtils.isEmpty(imageUrls)) {
            return Lists.newArrayList();
        }
        
        List<ImageToTextResultDTO> results = Lists.newArrayList();
        List<java.util.concurrent.Future<ImageToTextResultDTO>> futures = Lists.newArrayList();
        
        // 提交所有图片处理任务
        for (String imageUrl : imageUrls) {
            futures.add(batchImageToTextExecutor.submit(() -> {
                try {
                    return convertImageToText(misId, message, imageUrl);
                } catch (Exception e) {
                    log.error("convertImageToTextBatch#error, misId:{}, message:{}, imageUrl:{}", misId, message, imageUrl, e);
                    ImageToTextResultDTO errorResult = new ImageToTextResultDTO();
                    errorResult.setUrl(imageUrl);
                    errorResult.setText("图片处理失败");
                    return errorResult;
                }
            }));
        }
        int timeout = mtConfigService.getImageToTextFeatureTimeout();
        // 收集所有任务结果
        for (java.util.concurrent.Future<ImageToTextResultDTO> future : futures) {
            try {
                ImageToTextResultDTO result = future.get(timeout, TimeUnit.MILLISECONDS);
                results.add(result);
            } catch (Exception e) {
                log.warn("convertImageToTextBatch#getFuture#warn", e);
            }
        }
        
        return results;
    }

    @Override
    public LlmDxGroupMonitoringTaskResultDTO summarizeDxGroupChatQuestions(String misId, LlmDxGroupMonitoringTaskMessageDTO messageDTO) throws LlmCorpusException {
        //TODO 降级开关
        try{
            FridayConversationConfig fridayConversationConfig = mtConfigService.getFridayDxMonitoringConversationConfig();
            String token = fridayRpcService.getFridayAccessToken();
            FridayConversationParams params = new FridayConversationParams();
            params.setUserId(misId);
            params.setUserType(fridayConversationConfig.getDefaultUserType());
            params.setStream(false);
            params.setDebug(false);
            params.setMultiTurn(false);
            params.setRedo(false);
            params.setAccessToken(token);
            params.setAppId(fridayConversationConfig.getAppId());
            params.setUtterances(Lists.newArrayList(JSON.toJSONString(messageDTO)));
            FridayConversationResponseDataDTO fridayConversationResponseDTO = fridayRpcService.conversation(params);
            if (fridayConversationResponseDTO == null || CollectionUtils.isEmpty(fridayConversationResponseDTO.getContents())) {
                log.warn("summarizeDxGroupChatQuestions#error,fridayConversationResponseDTO为空 messageDTO:{}，fridayConversationResponseDTO:{}", messageDTO,fridayConversationResponseDTO);
                return null;
            }
            LlmDxMonitoringAnwserDTO answerDTO = JSON.parseObject(fridayConversationResponseDTO.getContents().get(0).getText(), LlmDxMonitoringAnwserDTO.class);
            if (answerDTO == null) {
                log.warn("summarizeDxGroupChatQuestions#error,messageDTO:{},fridayConversationResponseDTO:{}", messageDTO, fridayConversationResponseDTO);
                throw LlmCorpusException.buildWithMsg(BizCode.FRIDAY_SUMMARIZE_QUESTION_ERROR, "模型返回结果为空");
            }
            if (answerDTO.getCode() != 0 || answerDTO.getData() == null) {
                log.warn("summarizeDxGroupChatQuestions#error, messageDTO:{},fridayConversationResponseDTO:{}", messageDTO,fridayConversationResponseDTO);
                throw LlmCorpusException.buildWithMsg(BizCode.FRIDAY_SUMMARIZE_QUESTION_ERROR, answerDTO.getMessage()==null?"模型未返回结果":answerDTO.getMessage());
            }
            return answerDTO.getData();
        } catch (LlmCorpusException e){
            throw e;
        } catch (Exception e) {
            log.error("summarizeDxGroupChatQuestions#error, messageDTOd:{}",messageDTO, e);
            throw LlmCorpusException.build(BizCode.FRIDAY_SUMMARIZE_QUESTION_ERROR);
        }
    }

    @Override
    public LlmKmToCorpusDTO.LlmKmToCorpusTaskResultDTO convertKmToCorups(String body, String misId, boolean flag, long rgId) throws LlmCorpusException {
        try {
            FridayConversationConfig fridayConversationConfig;
            if(flag){
                fridayConversationConfig = mtConfigService.getFridayQaToCorpusConversationConfig();
            }else {
                fridayConversationConfig = mtConfigService.getFridayKmToCorpusConversationConfig();
            }
            String token = fridayRpcService.getFridayAccessToken();
            FridayConversationParams params = new FridayConversationParams();
            params.setUserId(misId);
            params.setUserType(fridayConversationConfig.getDefaultUserType());
            params.setStream(false);
            params.setDebug(false);
            params.setMultiTurn(false);
            params.setRedo(false);
            params.setAccessToken(token);
            params.setAppId(fridayConversationConfig.getAppId());
            FridayConversationKmUtterance utterance = fridayMapper.trans2FridayConversationKmUtterance(body, rgId);
            params.setUtterances(Lists.newArrayList(JSON.toJSONString(utterance)));
            FridayConversationResponseDataDTO fridayConversationResponseDTO = fridayRpcService.conversation(params);
            if (fridayConversationResponseDTO == null || CollectionUtils.isEmpty(fridayConversationResponseDTO.getContents())) {
                log.warn("convertKmToCorpus#error,fridayConversationResponseDTO为空 fridayConversationResponseDTO:{}",fridayConversationResponseDTO);
                return null;
            }

            StringBuilder contentBuilder = new StringBuilder();
            // 拼接返回结果
            for (FridayConversationResponseDataDTO.Content content : fridayConversationResponseDTO.getContents()) {
                if (content == null || (StringUtils.isBlank(content.getText()) && StringUtils.isBlank(content.getHref()))) {
                    continue;
                }
                switch (content.getType()) {
                    case "TEXT":
                        contentBuilder.append(content.getText());
                        break;
                    case "LINK":
                        contentBuilder.append(content.getHref());
                        break;
                    default:
                        break;
                }
            }
            String responseContent = contentBuilder.toString();
            log.info("#convertKmToCorpus# 准备解析JSON内容: {}", responseContent);
            
            LlmKmToCorpusDTO answerDTO = null;
            try {
                answerDTO = JSON.parseObject(responseContent, LlmKmToCorpusDTO.class);
            } catch (Exception e) {
                log.error("#convertKmToCorpus# JSON解析失败，实际内容: {}, 错误: {}", responseContent, e.getMessage(), e);
                throw LlmCorpusException.buildWithMsg(BizCode.FRIDAY_KM_TO_CORPUS_ERROR, "JSON解析失败: " + e.getMessage());
            }
            
            if (answerDTO == null) {
                log.warn("convertKmToCorpus#error,body:{},fridayConversationResponseDTO:{}", body, fridayConversationResponseDTO);
                throw LlmCorpusException.buildWithMsg(BizCode.FRIDAY_KM_TO_CORPUS_ERROR, "模型返回结果为空");
            }
            if (answerDTO.getCode() != 0 || answerDTO.getData() == null) {
                log.warn("convertKmToCorpus#error, body:{},fridayConversationResponseDTO:{}", body,fridayConversationResponseDTO);
                throw LlmCorpusException.buildWithMsg(BizCode.FRIDAY_KM_TO_CORPUS_ERROR, answerDTO.getMessage()==null?"模型未返回结果":answerDTO.getMessage());
            }
            return answerDTO.getData();
        } catch (LlmCorpusException e) {
            throw e;
        } catch (Exception e) {
            log.error("convertKmToCorpus#error", e);
            throw LlmCorpusException.build(BizCode.FRIDAY_KM_TO_CORPUS_ERROR);
        }
    }

    @Override
    public TtChatCorpusDTO mergeCorpus(FridayMergeCorpusParam fridayMergeCorpusParam) throws LlmCorpusException {
        if (CollectionUtils.isEmpty(fridayMergeCorpusParam.getCorpusList())){
            throw LlmCorpusException.build(BizCode.ILLEGAL_ARGUMENT, "corpusList为空");
        }
        try{
            FridayConversationConfig fridayConversationConfig = mtConfigService.getFridayMergeCorpusConversationConfig();
            String token = fridayRpcService.getFridayAccessToken();
            FridayConversationParams params = new FridayConversationParams();
            params.setUserId(fridayMergeCorpusParam.getMisId());
            params.setUserType(fridayConversationConfig.getDefaultUserType());
            params.setStream(false);
            params.setDebug(false);
            params.setMultiTurn(false);
            params.setRedo(false);
            params.setAccessToken(token);
            params.setAppId(fridayConversationConfig.getAppId());

            MergeConversationUtterance mergeConversationUtterance = MergeConversationUtterance.builder().
                    triggerSource(fridayMergeCorpusParam.getTriggerSource())
                    .orgId(fridayMergeCorpusParam.getRgId())
                    .sopList(fridayMergeCorpusParam.getCorpusList())
                    .build();
//            List<Object> utterances = Lists.newArrayList();
//            FridayConversationCommonUtterance textUtterance = FridayConversationCommonUtterance.builder()
//                    .type(FridayUtteranceTypeEnum.TEXT.getType())
//                    .content(JSON.toJSONString(mergeConversationUtterance))
//                    .build();
//            utterances.add(textUtterance);
            params.setUtterances(Lists.newArrayList(JSON.toJSONString(mergeConversationUtterance)));
            FridayConversationResponseDataDTO fridayConversationResponseDTO = fridayRpcService.conversation(params);
            if (fridayConversationResponseDTO == null || CollectionUtils.isEmpty(fridayConversationResponseDTO.getContents())) {
                log.warn("mergeCorpus#error,fridayConversationResponseDTO为空 param:{}，fridayConversationResponseDTO:{}", fridayMergeCorpusParam, fridayConversationResponseDTO);
                return null;
            }
            
            String responseText = fridayConversationResponseDTO.getContents().get(0).getText();
            log.info("#mergeCorpus# 准备解析JSON内容: {}", responseText);
            
            LlmAnswerDTO answerDTO = null;
            try {
                answerDTO = JSON.parseObject(responseText, LlmAnswerDTO.class);
            } catch (Exception e) {
                log.error("#mergeCorpus# JSON解析失败，实际内容: {}, 错误: {}", responseText, e.getMessage(), e);
                throw LlmCorpusException.buildWithMsg(BizCode.MERGE_CORPUS_FAILED, "JSON解析失败: " + e.getMessage());
            }
            
            if (answerDTO == null) {
                log.warn("mergeCorpus#error, param:{} ,fridayConversationResponseDTO:{}", fridayMergeCorpusParam, fridayConversationResponseDTO);
                throw LlmCorpusException.buildWithMsg(BizCode.MERGE_CORPUS_FAILED, "模型返回结果为空");
            }
            if (answerDTO.getCode() != 0 || answerDTO.getData() == null) {
                log.warn("mergeCorpus#error, param:{},fridayConversationResponseDTO:{}", fridayMergeCorpusParam,fridayConversationResponseDTO);
                throw LlmCorpusException.buildWithMsg(BizCode.MERGE_CORPUS_FAILED, answerDTO.getMessage()==null?"模型未返回结果":answerDTO.getMessage());
            }
            return fridayMapper.trans2TtChatCorpusDTO(answerDTO);
        } catch (LlmCorpusException e){
            throw e;
        } catch (Exception e) {
            log.error("#mergeCorpus.convertTTChatToWiki#error, param:{}", fridayMergeCorpusParam, e);
            throw LlmCorpusException.build(BizCode.MERGE_CORPUS_FAILED);
        }
    }

    @Override
    public FridayAutoReplyResultDto autoReply(FridayAutoReplyConfig fridayAutoReplyConfig, String misId, TTInfoDTO ttInfoDTO) throws LlmCorpusException {
        FridayAutoReplyResultDto resultDto = new FridayAutoReplyResultDto();
        if (fridayAutoReplyConfig == null) {
            log.error("#autoReply#error, fridayAutoReplyConfig is null, misId:{}", misId);
            throw LlmCorpusException.buildWithMsg(BizCode.FRIDAY_AUTO_REPLY_ERROR, "配置为空");
        }
        if (StringUtils.isBlank(fridayAutoReplyConfig.getFridayAppId())) {
            log.error("#autoReply#error, fridayAppId is null, misId:{}, fridayAutoReplyConfig:{}", misId, fridayAutoReplyConfig);
            throw LlmCorpusException.buildWithMsg(BizCode.FRIDAY_AUTO_REPLY_ERROR, "Friday应用ID为空");
        }
        try {
            String token = fridayRpcService.getFridayAccessToken();
            FridayConversationParams params = new FridayConversationParams();
            params.setUserId(misId);
            params.setUserType("MIS");
            params.setStream(false);
            params.setDebug(false);
            params.setMultiTurn(false);
            params.setRedo(false);
            params.setAccessToken(token);
            params.setAppId(fridayAutoReplyConfig.getFridayAppId());
            params.setUtterances(Lists.newArrayList(JSON.toJSONString(ttInfoDTO)));
            FridayConversationResponseDataDTO fridayConversationResponseDTO = fridayRpcService.conversationWithoutPostProcess(params);

            if (fridayConversationResponseDTO == null || CollectionUtils.isEmpty(fridayConversationResponseDTO.getContents())) {
                log.warn("autoReply#warn,fridayConversationResponseDTO为空 fridayAutoReplyConfig:{}, misId:{}，ttInfoDTO:{}", fridayAutoReplyConfig, misId, ttInfoDTO);
                return resultDto;
            }
            StringBuilder contentBuilder = new StringBuilder();
            // 拼接返回结果
            for (FridayConversationResponseDataDTO.Content content : fridayConversationResponseDTO.getContents()) {
                if (content == null || (StringUtils.isBlank(content.getText()) && StringUtils.isBlank(content.getHref()))) {
                    continue;
                }
                switch (content.getType()) {
                    case "TEXT":
                        contentBuilder.append(content.getText());
                        break;
                    case "LINK":
                        contentBuilder.append(content.getHref());
                        break;
                    default:
                        break;
                }
            }
            if (contentBuilder.length() == 0) {
                log.warn("autoReply#warn,fridayConversationResponseDTO content为空,resp:{}", fridayConversationResponseDTO);
                return resultDto;
            }
            resultDto.setText(contentBuilder.toString());
            return resultDto;
        } catch (Exception e) {
            log.error("#autoReply#error, misId:{}, fridayAutoReplyConfig:{}", misId, fridayAutoReplyConfig, e);
            throw LlmCorpusException.build(BizCode.FRIDAY_AUTO_REPLY_ERROR);
        }
    }

    @Override
    public List<FridayQuestionClusteringItemDTO> questionClustering(List<AppStatusDTO.Question> questions) throws LlmCorpusException {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(questions)) {
            log.warn("questionClustering#warn, questions is empty");
            return Lists.newArrayList();
        }
        try{
            FridayConversationConfig fridayConversationConfig = mtConfigService.getFridayQuestionClusteringConversationConfig();
            String token = fridayRpcService.getFridayAccessToken();
            FridayConversationParams params = new FridayConversationParams();
            params.setUserId(fridayConversationConfig.getDefaultUserId());
            params.setUserType(fridayConversationConfig.getDefaultUserType());
            params.setStream(false);
            params.setDebug(false);
            params.setMultiTurn(false);
            params.setRedo(false);
            params.setAccessToken(token);
            params.setAppId(fridayConversationConfig.getAppId());

            FridayClusteringParam fridayClusteringParam = new FridayClusteringParam();
            fridayClusteringParam.setBackgroundKnowledge("");
            fridayClusteringParam.setQuestionList(questions);
            params.setUtterances(Lists.newArrayList(JSON.toJSONString(fridayClusteringParam)));
            FridayConversationResponseDataDTO fridayConversationResponseDTO = fridayRpcService.conversation(params);
            if (fridayConversationResponseDTO == null || CollectionUtils.isEmpty(fridayConversationResponseDTO.getContents())) {
                log.warn("questionClustering#warn,fridayConversationResponseDTO为空 questions:{}，fridayConversationResponseDTO:{}", questions,fridayConversationResponseDTO);
                return Lists.newArrayList();
            }
            // fridayConversationResponseDTO.getContents().get(0).getText()
            // 解析返回结果 为LlmAnswerCommonDTO<FridayQuestionClusteringDTO>
            LlmAnswerCommonDTO<FridayQuestionClusteringDTO> answerCommonDTO = JSON.parseObject(fridayConversationResponseDTO.getContents().get(0).getText(), new TypeReference<LlmAnswerCommonDTO<FridayQuestionClusteringDTO>>(){});
            if (answerCommonDTO == null) {
                log.warn("questionClustering#warn,questions:{},fridayConversationResponseDTO:{}", questions, fridayConversationResponseDTO);
                throw LlmCorpusException.buildWithMsg(BizCode.FRIDAY_QUESTION_CLUSTERING_ERROR, "问题聚类失败，模型返回结果为空");
            }
            if (answerCommonDTO.getCode() != 0 || answerCommonDTO.getData() == null) {
                log.warn("questionClustering#warn, questions:{},fridayConversationResponseDTO:{}", questions,fridayConversationResponseDTO);
                throw LlmCorpusException.buildWithMsg(BizCode.FRIDAY_QUESTION_CLUSTERING_ERROR, answerCommonDTO.getMessage()==null?"问题聚类失败，模型未返回结果":answerCommonDTO.getMessage());
            }
            FridayQuestionClusteringDTO data = answerCommonDTO.getData();
            if (CollectionUtils.isEmpty(data.getClusteredQuestions())) {
                log.warn("questionClustering#warn, questions:{},fridayConversationResponseDTO:{}", questions,fridayConversationResponseDTO);
                throw LlmCorpusException.buildWithMsg(BizCode.FRIDAY_QUESTION_CLUSTERING_ERROR, "问题聚类失败，模型返回结果为空");
            }
            List<FridayQuestionClusteringItemDTO> result = data.getClusteredQuestions();
            return result;
        }catch (LlmCorpusException e){
            throw e;
        } catch (Exception e){
            log.error("questionClustering#error, questions:{}", questions, e);
            throw LlmCorpusException.build(BizCode.SERVER_INTERNAL_ERROR);
        }
    }

    /**
     * 获取语料质量评分
     *
     * @param query 待评估的语料内容
     * @return 质量评分DTO
     */
    @Override
    public ContentQualityDTO getContentQualityAssessment(String query) throws LlmCorpusException {
        try {
            FridayConversationConfig fridayConversationConfig = mtConfigService.getFridayContentQualityAssessmentConfig();
            String token = fridayRpcService.getFridayAccessToken();
            FridayConversationParams params = new FridayConversationParams();
            params.setUserId(fridayConversationConfig.getDefaultUserId());
            params.setUserType(fridayConversationConfig.getDefaultUserType());
            params.setStream(false);
            params.setDebug(false);
            params.setMultiTurn(false);
            params.setRedo(false);
            params.setAccessToken(token);
            params.setAppId(fridayConversationConfig.getAppId());
            params.setUtterances(Lists.newArrayList(query));
            log.info("getContentQualityAssessment# 请求参数构建完成, params:{}", JSON.toJSONString(params));

            FridayConversationResponseDataDTO fridayConversationResponseDTO = fridayRpcService.conversation(params);
            if (fridayConversationResponseDTO == null || CollectionUtils.isEmpty(fridayConversationResponseDTO.getContents())) {
                log.warn("getContentQualityAssessment#error,fridayConversationResponseDTO为空 query:{},fridayConversationResponseDTO:{}", query, fridayConversationResponseDTO);
                return new ContentQualityDTO();
            }

            String responseText = fridayConversationResponseDTO.getContents().get(0).getText();
            log.info("getContentQualityAssessment# 获取响应文本:{}", responseText);

            // 解析响应文本为ContentQualityDTO对象
            ContentQualityDTO result = fridayMapper.trans2ContentQualityDTO(responseText);
            if (result == null) {
                log.warn("getContentQualityAssessment#error, query:{}, response:{}", query, responseText);
                return new ContentQualityDTO();
            }

            return result;
        } catch (LlmCorpusException e) {
            throw e;
        } catch (Exception e) {
            log.error("getContentQualityAssessment# 处理异常, query:{}", query, e);
            throw LlmCorpusException.build(BizCode.FRIDAY_CONTENT_QUALITY_ASSESSMENT_ERROR);
        }
    }

    @Override
    public AiopsAutoReplyResultDto aiopsAutoReply(AiopsAutoReplyConfig aiopsAutoReplyConfig, String misId, String message) throws LlmCorpusException {
        AiopsAutoReplyResultDto resultDto = new AiopsAutoReplyResultDto();
        if (aiopsAutoReplyConfig == null) {
            log.error("#aiopsAutoReply#error, aiopsAutoReplyConfig is null, misId:{}", misId);
            throw LlmCorpusException.buildWithMsg(BizCode.AIOPS_AUTO_REPLY_ERROR, "配置为空");
        }
        if (StringUtils.isBlank(aiopsAutoReplyConfig.getAiopsAppId())) {
            log.error("#aiopsAutoReply#error, aiopsAppId is null, misId:{}, aiopsAutoReplyConfig:{}", misId, aiopsAutoReplyConfig);
            throw LlmCorpusException.buildWithMsg(BizCode.AIOPS_AUTO_REPLY_ERROR, "Aiops应用ID为空");
        }
        if (StringUtils.isBlank(message)) {
            log.error("#aiopsAutoReply#error, message is empty, misId:{}, aiopsAutoReplyConfig:{}", misId, aiopsAutoReplyConfig);
            throw LlmCorpusException.buildWithMsg(BizCode.AIOPS_AUTO_REPLY_ERROR, "消息内容为空");
        }
        try {
            String token = fridayRpcService.getFridayAccessToken();
            FridayConversationParams params = new FridayConversationParams();
            params.setUserId(misId);
            params.setUserType("MIS");
            params.setStream(false);
            params.setDebug(false);
            params.setMultiTurn(false);
            params.setRedo(false);
            params.setAccessToken(token);
            params.setAppId(aiopsAutoReplyConfig.getAiopsAppId());
            params.setUtterances(Lists.newArrayList(message));
            FridayConversationResponseDataDTO fridayConversationResponseDTO = fridayRpcService.conversation(params);

            if (fridayConversationResponseDTO == null || CollectionUtils.isEmpty(fridayConversationResponseDTO.getContents())) {
                log.warn("#aiopsAutoReply#warn, fridayConversationResponseDTO为空 aiopsAutoReplyConfig:{}, misId:{}, message:{}", aiopsAutoReplyConfig, misId, message);
                return resultDto;
            }

            StringBuilder contentBuilder = new StringBuilder();
            for (FridayConversationResponseDataDTO.Content content : fridayConversationResponseDTO.getContents()) {
                if (content == null || (StringUtils.isBlank(content.getText()) && StringUtils.isBlank(content.getHref()))) {
                    continue;
                }
                switch (content.getType()) {
                    case "TEXT":
                        contentBuilder.append(content.getText());
                        break;
                    case "LINK":
                        contentBuilder.append(content.getHref());
                        break;
                    default:
                        break;
                }
            }

            if (contentBuilder.length() == 0) {
                log.warn("#aiopsAutoReply#warn, fridayConversationResponseDTO content为空, resp:{}", fridayConversationResponseDTO);
                return resultDto;
            }

            resultDto.setText(contentBuilder.toString());
            return resultDto;
        } catch (Exception e) {
            log.error("#aiopsAutoReply#error, misId:{}, aiopsAutoReplyConfig:{}, message:{}", misId, aiopsAutoReplyConfig, message, e);
            throw LlmCorpusException.build(BizCode.AIOPS_AUTO_REPLY_ERROR);
        }
    }

    /**
     * 判断问题是否已解决
     * @param questionAnswerPairs
     * @return
     * @throws LlmCorpusException
     */
    @Override
    public List<FridayQuestionResolveStateItem> judgeQuestionResolveState(List<QuestionAnswerPairDTO> questionAnswerPairs) throws LlmCorpusException {
        if (CollectionUtils.isEmpty(questionAnswerPairs)) {
            log.warn("#judgeQuestionResolveState#warn, questionMessageIds is empty");
            return Lists.newArrayList();
        }
        
        try {
            // 获取配置的批处理大小
            int batchSize = mtConfigService.getQuestionResolveStateBatchSize();
            ExecutorService executorService = AsyncTaskUtils.getQuestionResolveStateThreadPool();
            
            // 计算需要多少批次处理
            int totalSize = questionAnswerPairs.size();
            int batchCount = (totalSize + batchSize - 1) / batchSize;
            List<Future<List<FridayQuestionResolveStateItem>>> futures = new ArrayList<>(batchCount);
            log.info("#judgeQuestionResolveState#totalSize:{},batchSize:{}, 需要处理{}个批次", totalSize, batchSize,batchCount);
            // 提交每个批次的任务
            for (int i = 0; i < batchCount; i++) {
                int startIndex = i * batchSize;
                int endIndex = Math.min((i + 1) * batchSize, totalSize);
                List<QuestionAnswerPairDTO> batchQuestionAnswerPairs = questionAnswerPairs.subList(startIndex, endIndex);
                
                Future<List<FridayQuestionResolveStateItem>> future = AsyncTaskUtils.submitWithThreadLocal(executorService, 
                        () -> processBatchQuestionResolveState(batchQuestionAnswerPairs));
                futures.add(future);
            }
            
            // 收集所有批次的结果
            List<FridayQuestionResolveStateItem> results = new ArrayList<>();
            for (Future<List<FridayQuestionResolveStateItem>> future : futures) {
                try {
                    // 获取任务超时配置
                    int taskTimeout = mtConfigService.getQuestionResolveStateTaskTimeout();
                    List<FridayQuestionResolveStateItem> batchResult = future.get(taskTimeout, TimeUnit.MILLISECONDS);
                    if (batchResult != null) {
                        results.addAll(batchResult);
                    }
                } catch (java.util.concurrent.TimeoutException e) {
                    log.error("#judgeQuestionResolveState#error, Task execution timed out", e);
                } catch (Exception e) {
                    log.error("#judgeQuestionResolveState#error, Failed to get batch result", e);
                }
            }
            
            return results;
        } catch (Exception e) {
            log.error("#judgeQuestionResolveState#error, questionAnswerPairs:{}", questionAnswerPairs, e);
            throw LlmCorpusException.build(BizCode.SERVER_INTERNAL_ERROR);
        }
    }

    /**
     * 处理单个批次的问题解决状态判断
     * @param batchQuestionAnswerPairs 单个批次的问题答案对列表
     * @return 问题解决状态列表
     * @throws LlmCorpusException 如果处理过程中发生错误
     */
    private List<FridayQuestionResolveStateItem> processBatchQuestionResolveState(List<QuestionAnswerPairDTO> batchQuestionAnswerPairs) throws LlmCorpusException {
        List<FridayConversationQuestionSolvingStateJudgeUtteranceItem> utterances = Lists.newArrayList();
        for (QuestionAnswerPairDTO questionAnswerPair : batchQuestionAnswerPairs) {
            FridayConversationQuestionSolvingStateJudgeUtteranceItem item = new FridayConversationQuestionSolvingStateJudgeUtteranceItem();
            item.setQuestionMessageId(questionAnswerPair.getQuestion().getQuestionMessageId());
            item.setQuestion(questionAnswerPair.getQuestion().getQuestion());
            item.setAnswer(questionAnswerPair.getAnswer());
            utterances.add(item);
        }
        Map<String, List<FridayConversationQuestionSolvingStateJudgeUtteranceItem>> questionAnswerPairMap = new HashMap<>();
        questionAnswerPairMap.put("questionAndAnswerPairs", utterances);
        FridayConversationCommonUtterance commonUtterance = FridayConversationCommonUtterance.builder().type("TEXT").content(JSON.toJSONString(questionAnswerPairMap)).build();
        
        try {
            FridayConversationConfig fridayConversationConfig = mtConfigService.getFridayQuestionResolveStateConfig();
            String token = fridayRpcService.getFridayAccessToken();
            FridayConversationParams params = new FridayConversationParams();
            params.setUserId(fridayConversationConfig.getDefaultUserId());
            params.setUserType(fridayConversationConfig.getDefaultUserType());
            params.setAccessToken(token);
            params.setAppId(fridayConversationConfig.getAppId());
            params.setStream(false);
            params.setDebug(false);
            params.setMultiTurn(false);
            params.setRedo(false);
            params.setUtterances(Lists.newArrayList(commonUtterance));
            FridayConversationResponseDataDTO fridayConversationResponseDTO = fridayRpcService.conversation(params);
            if (fridayConversationResponseDTO == null || CollectionUtils.isEmpty(fridayConversationResponseDTO.getContents())) {
                log.warn("processBatchQuestionResolveState#warn,fridayConversationResponseDTO为空 questionAnswerPairs:{}，fridayConversationResponseDTO:{}", batchQuestionAnswerPairs, fridayConversationResponseDTO);
                return Lists.newArrayList();
            }
            String responseText = fridayConversationResponseDTO.getContents().get(0).getText();
            log.info("processBatchQuestionResolveState# 获取响应文本:{}", responseText);

            // 解析响应文本为List<FridayQuestionResolveStateItem>对象
            LlmAnswerCommonDTO<List<FridayQuestionResolveStateItem>> answerCommonDTO = JSON.parseObject(fridayConversationResponseDTO.getContents().get(0).getText(), new TypeReference<LlmAnswerCommonDTO<List<FridayQuestionResolveStateItem>>>(){});
            if (answerCommonDTO == null) {
                log.warn("processBatchQuestionResolveState#error, questionAnswerPairs:{},fridayConversationResponseDTO:{}", batchQuestionAnswerPairs, fridayConversationResponseDTO);
                throw LlmCorpusException.buildWithMsg(BizCode.FRIDAY_QUESTION_RESOLVE_STATE_ERROR, "问题解决状态判断失败，模型返回结果为空");
            }
            if (answerCommonDTO.getCode() != 0 || answerCommonDTO.getData() == null) {
                log.warn("processBatchQuestionResolveState#error, questionAnswerPairs:{},fridayConversationResponseDTO:{}", batchQuestionAnswerPairs, fridayConversationResponseDTO);
                throw LlmCorpusException.buildWithMsg(BizCode.FRIDAY_QUESTION_RESOLVE_STATE_ERROR, answerCommonDTO.getMessage()==null?"问题解决状态判断失败，模型未返回结果":answerCommonDTO.getMessage());
            }
            return answerCommonDTO.getData();
        } catch (LlmCorpusException e) {
            throw e;
        } catch (Exception e) {
            log.error("processBatchQuestionResolveState#error, questionAnswerPairs:{}", batchQuestionAnswerPairs, e);
            throw LlmCorpusException.build(BizCode.SERVER_INTERNAL_ERROR);
        }
    }

    @Override
    public List<FridayEmbeddingResDTO> getEmbedding(String misId, List<String> input) throws LlmCorpusException {
        if (StringUtils.isBlank(misId)) {
            log.error("#getEmbedding#error, misId is null, input:{}", input);
            throw LlmCorpusException.buildWithMsg(BizCode.ILLEGAL_REQUEST, "misId为空");
        }
        if (CollectionUtils.isEmpty(input)) {
            log.error("#getEmbedding#error, input is empty, misId:{}", misId);
            throw LlmCorpusException.buildWithMsg(BizCode.ILLEGAL_REQUEST, "输入为空");
        }
        FridayEmbeddingConfig embeddingConfig = mtConfigService.getFridayEmbeddingModel();
        if (embeddingConfig == null) {
            log.error("#getEmbedding#error, embeddingConfig is null, misId:{}", misId);
            throw LlmCorpusException.buildWithMsg(BizCode.ILLEGAL_REQUEST, "模型配置为空");
        }
        if (StringUtils.isBlank(embeddingConfig.getModelName())) {
            log.error("#getEmbedding#error, modelName is null, misId:{}", misId);
            throw LlmCorpusException.buildWithMsg(BizCode.ILLEGAL_REQUEST, "模型名称为空");
        }
        int batchSize = 4;
        if (embeddingConfig.getBatchSize() > 0){
            batchSize = embeddingConfig.getBatchSize();
        }
        List<FridayEmbeddingResDTO> res = Lists.newArrayList();
        int totalSize = input.size();
        int batchCount = (totalSize + batchSize - 1) / batchSize;
        List<Future<List<FridayEmbeddingResDTO>>> futures = new ArrayList<>(batchCount);
        log.info("#getEmbedding#totalSize:{},batchSize:{}, 需要处理{}个批次", totalSize, batchSize,batchCount);
        
        ExecutorService executorService = AsyncTaskUtils.getEmbeddingThreadPool();
        
        try {
            // 提交任务到线程池
            for (int i = 0; i < batchCount; i++) {
                int startIndex = i * batchSize;
                int endIndex = Math.min((i + 1) * batchSize, totalSize);
                List<String> batchInput = input.subList(startIndex, endIndex);
                Future<List<FridayEmbeddingResDTO>> future = AsyncTaskUtils.submitWithThreadLocal(executorService, 
                        () -> fridayRpcService.modelFactoryEmbedding(misId, batchInput, embeddingConfig.getModelName()));
                futures.add(future);
            }
            
            // 收集结果
            int timeout = mtConfigService.getFridayConversationTimeout();
            for (Future<List<FridayEmbeddingResDTO>> future : futures) {
                try {
                    List<FridayEmbeddingResDTO> batchResult = future.get(timeout, TimeUnit.MILLISECONDS);
                    if (batchResult != null) {
                        res.addAll(batchResult);
                    }
                } catch (java.util.concurrent.TimeoutException e) {
                    log.error("#getEmbedding#error, Task execution timed out", e);
                } catch (Exception e) {
                    log.error("#getEmbedding#error, Failed to get batch result", e);
                }
            }
            
            return res;
        } catch (Exception e) {
            log.error("#getEmbedding#error, misId:{}, input size:{}", misId, input.size(), e);
            throw LlmCorpusException.build(BizCode.SERVER_INTERNAL_ERROR);
        }
    }

    @Override
    public FridayQuestionClusterNamingResDTO questionClusterNaming(List<AppStatusDTO.Question> questionList) throws LlmCorpusException {
        if (CollectionUtils.isEmpty(questionList)) {
            log.error("#questionClusterNaming#error, questionList is empty");
            throw LlmCorpusException.buildWithMsg(BizCode.ILLEGAL_REQUEST, "问题列表为空");
        }
        FridayConversationConfig config = mtConfigService.getFridayQuestionPatternSumConversationConfig();
        if (config == null) {
            log.error("#questionClusterNaming#error, config is null");
            throw LlmCorpusException.buildWithMsg(BizCode.ILLEGAL_REQUEST, "配置为空");
        }
        try {
            String token = fridayRpcService.getFridayAccessToken();
            FridayConversationParams params = new FridayConversationParams();
            params.setUserId(config.getDefaultUserId());
            params.setUserType(config.getDefaultUserType());
            params.setStream(false);
            params.setDebug(false);
            params.setMultiTurn(false);
            params.setRedo(false);
            params.setAccessToken(token);
            params.setAppId(config.getAppId());
            
            // 构建参数
            FridayClusteringParam fridayClusteringParam = new FridayClusteringParam();
            fridayClusteringParam.setBackgroundKnowledge("");
            fridayClusteringParam.setQuestionList(questionList);
            params.setUtterances(Lists.newArrayList(JSON.toJSONString(fridayClusteringParam)));
            
            // 调用Friday服务
            FridayConversationResponseDataDTO fridayConversationResponseDTO = fridayRpcService.conversation(params);
            if (fridayConversationResponseDTO == null || CollectionUtils.isEmpty(fridayConversationResponseDTO.getContents())) {
                log.warn("#questionClusterNaming#warn,fridayConversationResponseDTO为空 questionList:{}，fridayConversationResponseDTO:{}", questionList, fridayConversationResponseDTO);
                return new FridayQuestionClusterNamingResDTO();
            }
            
            // 解析返回结果
            String responseText = fridayConversationResponseDTO.getContents().get(0).getText();
            log.info("#questionClusterNaming# 获取响应文本:{}", responseText);
            
            // 解析响应文本为FridayQuestionClusterNamingResDTO对象
            LlmAnswerCommonDTO<FridayQuestionClusterNamingResDTO> answerCommonDTO = JSON.parseObject(
                responseText, 
                new TypeReference<LlmAnswerCommonDTO<FridayQuestionClusterNamingResDTO>>(){}
            );
            
            if (answerCommonDTO == null) {
                log.warn("#questionClusterNaming#error, questionList:{}, fridayConversationResponseDTO:{}", questionList, fridayConversationResponseDTO);
                throw LlmCorpusException.buildWithMsg(BizCode.FRIDAY_QUESTION_PATTERN_SUM_ERROR, "问题聚类命名失败，模型返回结果为空");
            }
            
            if (answerCommonDTO.getCode() != 0 || answerCommonDTO.getData() == null) {
                log.warn("#questionClusterNaming#error, questionList:{}, fridayConversationResponseDTO:{}", questionList, fridayConversationResponseDTO);
                throw LlmCorpusException.buildWithMsg(
                    BizCode.FRIDAY_QUESTION_PATTERN_SUM_ERROR, 
                    answerCommonDTO.getMessage() == null ? "问题聚类命名失败，模型未返回结果" : answerCommonDTO.getMessage()
                );
            }
            
            return answerCommonDTO.getData();
        } catch (LlmCorpusException e) {
            throw e;
        } catch (Exception e) {
            log.error("#questionClusterNaming#error, questionList:{}", questionList, e);
            throw LlmCorpusException.build(BizCode.FRIDAY_QUESTION_PATTERN_SUM_ERROR);
        }
    }

    @Override
    public List<String> summarizeQuestionsIntoTypeDefinition(List<String> questionList) throws LlmCorpusException {
        if (CollectionUtils.isEmpty(questionList)) {
            log.error("#summarizeQuestionsIntoTypeDefinition#error, questionList is empty");
            throw LlmCorpusException.buildWithMsg(BizCode.ILLEGAL_REQUEST, "问题列表为空");
        }
        FridayConversationConfig config = mtConfigService.getFridaySummarizeQuestionsIntoTypeDefinitionConversationConfig();
        if (config == null) {
            log.error("#summarizeQuestionsIntoTypeDefinition#error, config is null");
            throw LlmCorpusException.buildWithMsg(BizCode.ILLEGAL_REQUEST, "配置为空");
        }
        try {
            String token = fridayRpcService.getFridayAccessToken();
            FridayConversationParams params = new FridayConversationParams();
            params.setUserId(config.getDefaultUserId());
            params.setUserType(config.getDefaultUserType());
            params.setStream(false);
            params.setDebug(false);
            params.setMultiTurn(false);
            params.setRedo(false);
            params.setAccessToken(token);
            params.setAppId(config.getAppId());
            Map<String,List<String>> map = new HashMap<>();
            map.put("questionList", questionList);
            params.setUtterances(Lists.newArrayList(JSON.toJSONString(map)));
            FridayConversationResponseDataDTO fridayConversationResponseDTO = fridayRpcService.conversation(params);
            if (fridayConversationResponseDTO == null || CollectionUtils.isEmpty(fridayConversationResponseDTO.getContents())) {
                log.warn("#summarizeQuestionsIntoTypeDefinition#warn,fridayConversationResponseDTO为空 questionList:{}，fridayConversationResponseDTO:{}", questionList, fridayConversationResponseDTO);
                return null;
            }
            String responseText = fridayConversationResponseDTO.getContents().get(0).getText();
            log.info("#summarizeQuestionsIntoTypeDefinition# 获取响应文本:{}", responseText);
            LlmAnswerCommonDTO<List<String>> answerCommonDTO = JSON.parseObject(
                responseText, 
                new TypeReference<LlmAnswerCommonDTO<List<String>>>(){}
            );
            if (answerCommonDTO == null) {
                log.warn("#summarizeQuestionsIntoTypeDefinition#error, questionList:{}, fridayConversationResponseDTO:{}", questionList, fridayConversationResponseDTO);
                throw LlmCorpusException.buildWithMsg(BizCode.FRIDAY_QUESTION_PATTERN_SUM_ERROR, "问题聚类命名失败，模型返回结果为空");
            }
            if (answerCommonDTO.getCode() != 0 || answerCommonDTO.getData() == null) {
                log.warn("#summarizeQuestionsIntoTypeDefinition#error, questionList:{}, fridayConversationResponseDTO:{}", questionList, fridayConversationResponseDTO);
                throw LlmCorpusException.buildWithMsg(BizCode.FRIDAY_QUESTION_PATTERN_SUM_ERROR, answerCommonDTO.getMessage() == null ? "问题聚类命名失败，模型未返回结果" : answerCommonDTO.getMessage());
            }
            return answerCommonDTO.getData();
        }catch (Exception e) {
            log.error("#summarizeQuestionsIntoTypeDefinition#error, questionList:{}", questionList, e);
            throw LlmCorpusException.build(BizCode.SERVER_INTERNAL_ERROR);
        }
    }

    @Override
    public List<FridayQuestionClassficationItemDTO> allocateQuestionsToClusters(AllocateQuestionsToClustersRequestParam allocateQuestionsToClustersRequestParam) throws LlmCorpusException {
        if (allocateQuestionsToClustersRequestParam == null) {
            log.warn("allocateQuestionsToClusters#warn, allocateQuestionsToClustersRequestParam is null");
            throw LlmCorpusException.buildWithMsg(BizCode.ILLEGAL_REQUEST, "请求参数为空");
        }
        List<AppStatusDTO.Question> questionList = allocateQuestionsToClustersRequestParam.getQuestions();
        if (CollectionUtils.isEmpty(questionList)) {
            log.warn("allocateQuestionsToClusters#warn, questionList is empty");
            return Lists.newArrayList();
        }
        try{
            FridayConversationConfig fridayConversationConfig = mtConfigService.getFridayAllocateQuestionsToClustersConversationConfig();
            String token = fridayRpcService.getFridayAccessToken();
            FridayConversationParams params = new FridayConversationParams();
            params.setUserId(fridayConversationConfig.getDefaultUserId());
            params.setUserType(fridayConversationConfig.getDefaultUserType());
            params.setStream(false);
            params.setDebug(false);
            params.setMultiTurn(false);
            params.setRedo(false);
            params.setAccessToken(token);
            params.setAppId(fridayConversationConfig.getAppId());

            params.setUtterances(Lists.newArrayList(JSON.toJSONString(allocateQuestionsToClustersRequestParam)));
            
            FridayConversationResponseDataDTO fridayConversationResponseDTO = fridayRpcService.conversation(params);
            if (fridayConversationResponseDTO == null || CollectionUtils.isEmpty(fridayConversationResponseDTO.getContents())) {
                log.warn("allocateQuestionsToClusters#warn,fridayConversationResponseDTO为空 questionList:{}，fridayConversationResponseDTO:{}", questionList,fridayConversationResponseDTO);
                return Lists.newArrayList();
            }

            LlmAnswerCommonDTO<FridayQuestionClassficationDTO> answerCommonDTO = JSON.parseObject(fridayConversationResponseDTO.getContents().get(0).getText(), new TypeReference<LlmAnswerCommonDTO<FridayQuestionClassficationDTO>>(){});
            if (answerCommonDTO == null) {
                log.warn("allocateQuestionsToClusters#warn,questionList:{},fridayConversationResponseDTO:{}", questionList, fridayConversationResponseDTO);
                throw LlmCorpusException.buildWithMsg(BizCode.FRIDAY_QUESTION_CLUSTERING_ERROR, "问题聚类失败，模型返回结果为空");
            }
            if (answerCommonDTO.getCode() != 0 || answerCommonDTO.getData() == null) {
                log.warn("allocateQuestionsToClusters#warn, questionList:{},fridayConversationResponseDTO:{}", questionList,fridayConversationResponseDTO);
                throw LlmCorpusException.buildWithMsg(BizCode.FRIDAY_QUESTION_CLUSTERING_ERROR, answerCommonDTO.getMessage()==null?"问题聚类失败，模型未返回结果":answerCommonDTO.getMessage());
            }
            FridayQuestionClassficationDTO data = answerCommonDTO.getData();
            if (CollectionUtils.isEmpty(data.getClusteredQuestions())) {
                log.warn("allocateQuestionsToClusters#warn, questionList:{},fridayConversationResponseDTO:{}", questionList,fridayConversationResponseDTO);
                throw LlmCorpusException.buildWithMsg(BizCode.FRIDAY_QUESTION_CLUSTERING_ERROR, "问题聚类失败，模型返回结果为空");
            }
            List<FridayQuestionClassficationItemDTO> result = data.getClusteredQuestions();
            return result;
        }catch (LlmCorpusException e){
            throw e;
        } catch (Exception e){
            log.error("allocateQuestionsToClusters#error, questionList:{}", questionList, e);
            throw LlmCorpusException.build(BizCode.SERVER_INTERNAL_ERROR);
        }
    }
}
