package com.meituan.banma.llm.corpus.server.controller;

import com.meituan.banma.llm.corpus.server.common.CommonResult;
import com.meituan.banma.llm.corpus.server.common.domain.dto.TicketRangeResultDTO;
import com.meituan.banma.llm.corpus.server.common.domain.dto.DynamicTemplateResultDTO;
import com.meituan.banma.llm.corpus.server.service.ITicketQueryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * 工单查询API控制器
 * 提供工单相关查询的对外API接口
 */
@Slf4j
@RestController
@RequestMapping("/api/1.0/ticket")
public class TicketQueryApiController {

    @Resource
    private ITicketQueryService ticketQueryService;

    /**
     * 查询范围工单信息
     * @param misId 用户ID
     * @param createdAtStart 创建开始时间，格式：yyyy-MM-dd
     * @param createdAtEnd 创建结束时间，格式：yyyy-MM-dd
     * @param state 工单状态，多个状态以逗号分隔
     * @return 范围工单列表
     */
    @RequestMapping("/queryRangeTickets")
    public Object queryRangeTickets(@RequestParam(value = "misId", required = true) String misId,
                                    @RequestParam(value = "createdAtStart", required = false) String createdAtStart,
                                    @RequestParam(value = "createdAtEnd", required = false) String createdAtEnd,
                                    @RequestParam(value = "state", required = false) String state) {
        try {
            TicketRangeResultDTO rangeResult = ticketQueryService.queryRangeTicketsInfo(misId, createdAtStart, createdAtEnd, state);

            // 封装结果
            Map<String, Object> result = new HashMap<>();
            result.put("total", rangeResult.getTotalCount());
            result.put("data", rangeResult.getTicketList());
            result.put("excelDownloadLink", rangeResult.getExcelDownloadLink());
            result.put("taskId", rangeResult.getTaskId());

            return CommonResult.success("success", result);
        } catch (Exception e) {
            log.error("queryRangeTickets# 执行失败, misId:{}, createdAtStart:{}, createdAtEnd:{}, state:{}, 异常原因:{}", 
                    misId, createdAtStart, createdAtEnd, state, e);
            return CommonResult.error(String.format("执行失败,异常原因=%s", e.getMessage()));
        }
    }
    
    /**
     * 根据模板名称查询范围工单信息
     * @param misId 用户ID
     * @param createdAtStart 创建开始时间，格式：yyyy-MM-dd
     * @param createdAtEnd 创建结束时间，格式：yyyy-MM-dd
     * @param state 工单状态，多个状态以逗号分隔
     * @param template 模板名称
     * @return 范围工单列表
     */
    @RequestMapping("/queryRangeTicketsByTemplate")
    public Object queryRangeTicketsByTemplate(@RequestParam(value = "misId", required = true) String misId,
                                    @RequestParam(value = "createdAtStart", required = false) String createdAtStart,
                                    @RequestParam(value = "createdAtEnd", required = false) String createdAtEnd,
                                    @RequestParam(value = "state", required = false) String state,
                                    @RequestParam(value = "template", required = true) String template) {
        try {
            DynamicTemplateResultDTO resultDTO = ticketQueryService.queryRangeTicketsInfoByTemplate(misId, createdAtStart, createdAtEnd, state, template);

            // 封装结果
            Map<String, Object> result = new HashMap<>();
            result.put("total", resultDTO.getTotalCount());
            result.put("data", resultDTO.getTicketList());
            result.put("excelDownloadLink", resultDTO.getExcelDownloadLink());
            result.put("taskId", resultDTO.getTaskId());

            return CommonResult.success("success", result);
        } catch (Exception e) {
            log.error("queryRangeTicketsByTemplate# 执行失败, misId:{}, createdAtStart:{}, createdAtEnd:{}, state:{}, template:{}, 异常原因:{}", 
                    misId, createdAtStart, createdAtEnd, state, template, e);
            return CommonResult.error(String.format("执行失败,异常原因=%s", e.getMessage()));
        }
    }
} 