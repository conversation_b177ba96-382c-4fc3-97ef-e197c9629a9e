package com.meituan.banma.llm.corpus.server.common.config;

import com.meituan.banma.llm.corpus.server.common.interceptor.MisIdInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Web MVC 配置类，用于注册拦截器
 */
@Configuration
public class WebMvcConfig implements WebMvcConfigurer {

    @Autowired
    private MisIdInterceptor misIdInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 注册 MisId 拦截器，拦截所有请求
        registry.addInterceptor(misIdInterceptor)
                .addPathPatterns("/**")  // 拦截所有请求
                .excludePathPatterns(
                    "/error",            // 排除错误页面
                    "/favicon.ico",      // 排除 favicon
                    "/static/**",        // 排除静态资源
                    "/public/**",        // 排除公共资源
                    "/swagger-ui/**",    // 排除 Swagger UI
                    "/swagger-resources/**", // 排除 Swagger 资源
                    "/v3/api-docs/**",   // 排除 API 文档
                    "/webjars/**"        // 排除 WebJars
                );
    }
} 