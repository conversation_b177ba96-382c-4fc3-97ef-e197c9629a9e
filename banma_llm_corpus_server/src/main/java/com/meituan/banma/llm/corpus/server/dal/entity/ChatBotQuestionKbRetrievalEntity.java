package com.meituan.banma.llm.corpus.server.dal.entity;

import lombok.Data;

import java.sql.Timestamp;

@Data
public class ChatBotQuestionKbRetrievalEntity {
    private Long id;
    private String questionId;
    /**
     * 知识库文档id，对应KnowledgeBaseVersion.ticketId
     */
    private String knowledgeBaseSliceId;
    private String knowledgeBaseName;
    private Integer retrievalScore = 0;
    private Timestamp ctime;
    private Timestamp utime;
}