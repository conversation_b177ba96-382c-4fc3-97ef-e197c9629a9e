package com.meituan.banma.llm.corpus.server.dal.entity;

import lombok.Data;
import lombok.ToString;

import java.sql.Timestamp;

/**
 * chat_bot_question
 */
@Data
@ToString
public class ChatBotQuestionEntity {
    /**
     * id
     */
    private Long id;
    /**
     * 问题id
     */
    private String questionId;
    /**
     * 问题原文
     */
    private String question;
    /**
     * 提问人misId
     */
    private String misId;
    /**
     * 机器人名称
     */
    private String botName;
    /**
     * 召回切片总数量
     */
    private Integer retrievalTotalCount;

    /**
     * 系统生成的切片被召回数量
     */
    private Integer retrievalGeneratedCount;

    /**
     * 创建时间
     */
    private Timestamp ctime;
    /**
     * 更新时间
     */
    private Timestamp utime;
}
