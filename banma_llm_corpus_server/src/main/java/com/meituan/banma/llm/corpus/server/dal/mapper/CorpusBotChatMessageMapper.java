package com.meituan.banma.llm.corpus.server.dal.mapper;

import com.meituan.banma.llm.corpus.server.dal.entity.CorpusBotChatMessageEntity;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface CorpusBotChatMessageMapper {
    /**
     * 插入一条聊天消息记录
     *
     * @param entity 聊天消息实体
     * @return 影响的行数
     */
    int insert(CorpusBotChatMessageEntity entity);

    /**
     * 批量插入聊天消息记录
     *
     * @param entities 聊天消息实体列表
     * @return 影响的行数
     */
    int batchInsert(List<CorpusBotChatMessageEntity> entities);

    /**
     * 根据ID更新聊天消息
     *
     * @param entity 聊天消息实体
     * @return 影响的行数
     */
    int updateById(CorpusBotChatMessageEntity entity);

    /**
     * 根据ID删除聊天消息
     *
     * @param id 主键ID
     * @return 影响的行数
     */
    int deleteById(@Param("id") Long id);

    /**
     * 根据消息ID删除聊天消息
     *
     * @param msgId 消息ID
     * @return 影响的行数
     */
    int deleteByMsgId(@Param("msgId") String msgId);

    /**
     * 根据ID查询聊天消息
     *
     * @param id 主键ID
     * @return 聊天消息实体
     */
    CorpusBotChatMessageEntity selectById(@Param("id") Long id);

    /**
     * 根据消息ID查询聊天消息
     *
     * @param msgId 消息ID
     * @return 聊天消息实体
     */
    CorpusBotChatMessageEntity selectByMsgId(@Param("msgId") String msgId);

    /**
     * 根据群组ID查询聊天消息列表
     *
     * @param gid 群组ID
     * @return 聊天消息实体列表
     */
    List<CorpusBotChatMessageEntity> selectByGid(@Param("gid") String gid);

    /**
     * 根据发送者ID查询聊天消息列表
     *
     * @param fromUid 发送者ID
     * @return 聊天消息实体列表
     */
    List<CorpusBotChatMessageEntity> selectByFromUid(@Param("fromUid") String fromUid);

    /**
     * 根据群组ID和时间范围查询聊天消息列表
     *
     * @param gid      群组ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 聊天消息实体列表
     */
    List<CorpusBotChatMessageEntity> selectByGidAndTimeRange(
            @Param("gid") String gid,
            @Param("startTime") Long startTime,
            @Param("endTime") Long endTime);

    /**
     * 根据时间范围查询聊天消息列表
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 聊天消息实体列表
     */
    List<CorpusBotChatMessageEntity> selectByTimeRange(
            @Param("startTime") Long startTime,
            @Param("endTime") Long endTime);
} 