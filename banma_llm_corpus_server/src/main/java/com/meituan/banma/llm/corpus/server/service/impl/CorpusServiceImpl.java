package com.meituan.banma.llm.corpus.server.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.meituan.banma.llm.corpus.api.constants.BizCode;
import com.meituan.banma.llm.corpus.api.exception.LlmCorpusException;
import com.meituan.banma.llm.corpus.server.common.constants.enums.CorpusSourceEnum;
import com.meituan.banma.llm.corpus.server.common.constants.enums.CorpusStatusEnum;
import com.meituan.banma.llm.corpus.server.common.domain.dto.*;
import com.meituan.banma.llm.corpus.server.controller.request.corpus.CorpusFormRequest;
import com.meituan.banma.llm.corpus.server.controller.request.corpus.SopFormRequest;
import com.meituan.banma.llm.corpus.server.dal.entity.KnowledgeBaseVersionEntity;
import com.meituan.banma.llm.corpus.server.dal.entity.RgRuleEntity;
import com.meituan.banma.llm.corpus.server.dal.entity.RgSopEntity;
import com.meituan.banma.llm.corpus.server.dal.entity.RgBackgroundKnowledgeEntity;
import com.meituan.banma.llm.corpus.server.dal.entity.RgTagsEntity;
import com.meituan.banma.llm.corpus.server.dal.mapper.ReviewMapper;
import com.meituan.banma.llm.corpus.server.dal.mapper.RgRuleMapper;
import com.meituan.banma.llm.corpus.server.dal.mapper.RgSopMapper;
import com.meituan.banma.llm.corpus.server.dal.mapper.RgBackgroundKnowledgeMapper;
import com.meituan.banma.llm.corpus.server.service.ICorpusService;
import com.meituan.banma.llm.corpus.server.service.IKnowledgeBaseService;
import com.meituan.banma.llm.corpus.server.service.IReviewService;
import com.meituan.banma.llm.corpus.server.service.IRgTagsService;
import com.meituan.banma.llm.corpus.server.utils.ExcelExportUtil;
import com.meituan.banma.llm.corpus.server.common.MtConfigService;
import com.meituan.banma.llm.corpus.server.common.domain.dto.SopDefaultConfig;
import com.sankuai.security.sdk.SecSdk;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.io.ByteArrayOutputStream;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import com.meituan.banma.llm.corpus.server.utils.TagsUtil;
import com.meituan.banma.llm.corpus.server.common.domain.dto.ContentLengthLimitConfig;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class CorpusServiceImpl implements ICorpusService {

    @Autowired
    private ReviewMapper reviewMapper;
    @Autowired
    private RgSopMapper rgSopMapper;
    @Autowired
    private RgBackgroundKnowledgeMapper rgBackgroundKnowledgeMapper;
    @Autowired
    private RgRuleMapper rgRuleMapper;

    @Autowired
    private IReviewService reviewService;
    @Autowired
    private IKnowledgeBaseService knowledgeBaseService;
    @Autowired
    private MtConfigService mtConfigService;
    @Autowired
    private IRgTagsService rgTagsService;
    @Autowired
    private TagsUtil tagsUtil;



    @Override
    public boolean addCorpus(CorpusFormRequest corpusForm, long rgId, String misId) throws LlmCorpusException {
        boolean permission = knowledgeBaseService.validateUserPermission(misId, rgId);
        if (!permission) {
            throw new LlmCorpusException(BizCode.NO_PERMISSION.getCode(), "用户无对应值班组权限");
        }
        // 生成UUID并获取其字符串形式
        String uuid = UUID.randomUUID().toString();

        // 提取UUID的前8位
        String uuidPrefix = uuid.substring(0, 8);

        // 创建ticketId
        String ticketId;
        Integer source;
        if(corpusForm.getContentId()!=0L){
            ticketId = "K" + uuidPrefix;
            source = CorpusSourceEnum.KM.getCode();
        }else {
            ticketId = "A" + uuidPrefix;
            source = CorpusSourceEnum.MANUAL.getCode();
        }
        RelatedIdsDTO relatedIds = reviewService.getRelatedIds(rgId);
        
        // 处理标签ID，如果为空则使用默认标签
        String processedTagsIds = tagsUtil.processTagsIdsWithDefault(corpusForm.getTagsIds());
        
        KnowledgeBaseVersionEntity newOutput = KnowledgeBaseVersionEntity.builder()
                .ticketId(ticketId)
                .title(SecSdk.encodeForHTML(corpusForm.getTitle()))
                .content(corpusForm.getContent())
                .version(0)
                .misId(misId)
                .corpusStatus(CorpusStatusEnum.VALID.getCode())
                .rgId(rgId)
                .source(source)
                .timestamp(new Timestamp(System.currentTimeMillis()))
                .type(-1)
                .contentId(corpusForm.getContentId())
                .taskId(corpusForm.getTaskId())
                .backgroundKnowledgeId(relatedIds.getBackgroundKnowledgeId())
                .corpusSopId(relatedIds.getCorpusSopId())
                .ruleId(relatedIds.getRuleId())
                .tagsIds(processedTagsIds)
                .build();

        int result = reviewMapper.insertModifiedOutputWithVersionCheck(newOutput);
        if (result != 0) {
            reviewService.refreshKbDocument(rgId, misId);
            return true;
        } else {
            log.warn("#CorpusServiceImpl.addCorpus# 插入新记录失败,重试: ticketId={}, currentVersion={}", ticketId, 0);
            return false;
        }
    }

    @Override
    public CorpusModifyDTO queryCorpusByTicketIdRgId(String ticketId, long rgId, String misId) throws LlmCorpusException {
        boolean permission = knowledgeBaseService.validateUserPermission(misId, rgId);
        if (!permission) {
            throw new LlmCorpusException(BizCode.NO_PERMISSION.getCode(), "用户无对应值班组权限");
        }
        CorpusModifyDTO entity = reviewMapper.findModifyContentByTicketIdRgId(ticketId, rgId);
        if (entity == null) {
            return null;
        }
        return entity;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean modifyCorpus(CorpusFormRequest corpusForm, String ticketId, long rgId, String misId) throws LlmCorpusException {
        boolean permission = knowledgeBaseService.validateUserPermission(misId, rgId);
        if (!permission) {
            throw new LlmCorpusException(BizCode.NO_PERMISSION.getCode(), "用户无对应值班组权限");
        }
        // 查询当前的最新内容和版本号
        try {
            KnowledgeBaseVersionEntity latestOutput = reviewMapper.findLatestContentByTicketIdWithRgId(ticketId, rgId);
            if (latestOutput == null||latestOutput.getRgId()!=rgId||latestOutput.getCorpusStatus().intValue() == CorpusStatusEnum.REMOVED.getCode().intValue()) {
                throw new RuntimeException("未找到对应语料");
            }
            int currentVersion = latestOutput.getVersion();
            int newVersion = currentVersion + 1;

            RelatedIdsDTO relatedIds = reviewService.getRelatedIds(rgId);
            
            // 处理标签ID，如果为空则使用默认标签
            String processedTagsIds = tagsUtil.processTagsIdsWithDefault(corpusForm.getTagsIds());
            
            KnowledgeBaseVersionEntity newOutput = KnowledgeBaseVersionEntity.builder()
                    .ticketId(ticketId)
                    .title(SecSdk.encodeForHTML(corpusForm.getTitle()))
                    .content(corpusForm.getContent())
                    .version(newVersion)
                    .corpusStatus(CorpusStatusEnum.VALID.getCode())
                    .misId(misId)
                    .rgId(rgId)
                    .source(latestOutput.getSource())
                    .timestamp(new Timestamp(System.currentTimeMillis()))
                    .contentId(latestOutput.getContentId())
                    .taskId(corpusForm.getTaskId())
                    .type(-1)
                    .backgroundKnowledgeId(relatedIds.getBackgroundKnowledgeId())
                    .corpusSopId(relatedIds.getCorpusSopId())
                    .ruleId(relatedIds.getRuleId())
                    .tagsIds(processedTagsIds)
                    .build();

            int result = reviewMapper.insertModifiedOutputWithVersionCheck(newOutput);
            if (result != 0) {
                reviewService.refreshKbDocument(rgId, misId);
                return true;
            } else {
                log.warn("#CorpusServiceImpl.modifyCorpus# 修改后插入新版本记录失败,重试: ticketId={}, currentVersion={}", ticketId, currentVersion);
                return false;
            }
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteCorpusByTicketIds(List<String> ticketIds, long rgId, String misId) throws LlmCorpusException {
        boolean permission = knowledgeBaseService.validateUserPermission(misId, rgId);
        if (!permission) {
            throw new LlmCorpusException(BizCode.NO_PERMISSION.getCode(), "用户无对应值班组权限");
        }
        try {
            if(reviewMapper.findByTicketIds(ticketIds, rgId).size() != ticketIds.size()){
                throw new RuntimeException("批量删除的ticketIds中有不存在的语料");
            }
            reviewMapper.deleteByTicketIds(ticketIds, rgId, misId);
        } catch (Exception e) {
            log.warn("#ReviewServiceImpl.deleteCorpusByTicketIds# 批量删除失败, 重试:{}", ticketIds.toString());
            throw new RuntimeException(e.getMessage());
        }
        reviewService.refreshKbDocument(rgId, misId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveMergeCorpus(SaveMergeCorpusParam request) throws LlmCorpusException {
        // Tips
        // 1. 执行保存语料
        // 2. 将被合并的语料指向新生成的语料
        // 3. 如果有指向当前被合并的语料的语料，将其指向新语料
        // 4. 刷新知识库
        validateMergeCorpusRequest(request);
        KnowledgeBaseVersionEntity ttCorpus = null;
        try{
            ttCorpus = reviewMapper.findLatestContentByTicketIdWithRgIdIncludeDeleted(request.getTicketId(), request.getRgId());
        }catch (Exception e){
            log.info("#saveMergeCorpus#info,未找到对应语料，该tt为首次保存");
        }

        // 生成合并ID，格式为 m-K加上UUID的前8位，所有实体共用同一个ID
        String uuid = UUID.randomUUID().toString();
        String uuidPrefix = uuid.substring(0, 8);
        String mergedId = "m-K" + uuidPrefix;

        try{
            int version = 0;
            if ( ttCorpus != null ){
                version = ttCorpus.getVersion() + 1;
            }
            RelatedIdsDTO relatedIds = reviewService.getRelatedIds(request.getRgId());
            
            // 处理标签ID，如果为空则使用默认标签
            String processedTagsIds = tagsUtil.processTagsIdsWithDefault(request.getTagsIds());
            
            KnowledgeBaseVersionEntity knowledgeBaseVersionEntity = KnowledgeBaseVersionEntity.builder()
                    .ticketId(mergedId)
                    .title(request.getTitle())
                    .content(request.getContent())
                    .version(version)
                    .misId(request.getMisId())
                    .rgId(request.getRgId())
                    .source(CorpusSourceEnum.MERGE.getCode())
                    .type(request.getType())
                    .corpusStatus(CorpusStatusEnum.VALID.getCode())
                    .timestamp(new Timestamp(System.currentTimeMillis()))
                    .backgroundKnowledgeId(relatedIds.getBackgroundKnowledgeId())
                    .corpusSopId(relatedIds.getCorpusSopId())
                    .ruleId(relatedIds.getRuleId())
                    .taskId(request.getTaskId())
                    .tagsIds(processedTagsIds)
                    .build();
            int result = reviewMapper.insertModifiedOutputWithVersionCheck(knowledgeBaseVersionEntity);
            if (result <= 0) {
                throw LlmCorpusException.buildWithMsg(BizCode.SAVE_MERGE_CORPUS_FAILED, "保存语料失败");
            }
            List<KnowledgeBaseVersionEntity> knowledgeBaseVersionEntityList = reviewMapper.findByTicketIds(request.getCorpusIdList(), request.getRgId());
            if (knowledgeBaseVersionEntityList.size() != request.getCorpusIdList().size()) {
                throw LlmCorpusException.buildWithMsg(BizCode.SAVE_MERGE_CORPUS_FAILED, "待合并的部分语料不存在");
            }

            for (KnowledgeBaseVersionEntity entity : knowledgeBaseVersionEntityList) {
                entity.setVersion(entity.getVersion() + 1);
                entity.setMisId(request.getMisId());
                entity.setTimestamp(new Timestamp(System.currentTimeMillis()));
                entity.setMergedToId(mergedId);
                entity.setCorpusStatus(CorpusStatusEnum.MERGED.getCode());

                int res = reviewMapper.insertModifiedOutputWithVersionCheck(entity);
                if (res <= 0) {
                    throw LlmCorpusException.buildWithMsg(BizCode.SAVE_MERGE_CORPUS_FAILED, "更新语料指向合并语料失败");
                }
            }

            // 实现tips 3: 处理指向被合并语料的其他语料
            List<KnowledgeBaseVersionEntity> pointingEntities = reviewMapper.findByMergedToIds(request.getCorpusIdList(), request.getRgId());
            for (KnowledgeBaseVersionEntity pointingEntity : pointingEntities) {
                pointingEntity.setVersion(pointingEntity.getVersion() + 1);
                pointingEntity.setMisId(request.getMisId());
                pointingEntity.setTimestamp(new Timestamp(System.currentTimeMillis()));
                pointingEntity.setMergedToId(mergedId);

                int res = reviewMapper.insertModifiedOutputWithVersionCheck(pointingEntity);
                if (res <= 0) {
                    throw LlmCorpusException.buildWithMsg(BizCode.SAVE_MERGE_CORPUS_FAILED, "更新指向被合并语料的语料失败");
                }
            }
        } catch (LlmCorpusException e){
            throw e;
        }catch (Exception e){
            throw LlmCorpusException.buildWithMsg(BizCode.SERVER_INTERNAL_ERROR, e.getMessage());
        }
        reviewService.refreshKbDocument(request.getRgId(), request.getMisId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addIgnoreCorpus(CorpusFormRequest corpusForm, long rgId, String misId, String ticketId) throws LlmCorpusException {
        boolean permission = knowledgeBaseService.validateUserPermission(misId, rgId);
        if (!permission) {
            throw new LlmCorpusException(BizCode.NO_PERMISSION.getCode(), "用户无对应值班组权限");
        }
        try {
            KnowledgeBaseVersionEntity latestOutput = reviewMapper.findLatestContentByTicketIdWithRgId(ticketId, rgId);
            if (latestOutput != null) {
                throw new RuntimeException("该语料已添加");
            }

            RelatedIdsDTO relatedIds = reviewService.getRelatedIds(rgId);
            
            // 处理标签ID，如果为空则使用默认标签
            String processedTagsIds = tagsUtil.processTagsIdsWithDefault(corpusForm.getTagsIds());
            
            KnowledgeBaseVersionEntity newOutput = KnowledgeBaseVersionEntity.builder()
                    .ticketId(ticketId)
                    .title(SecSdk.encodeForHTML(corpusForm.getTitle()))
                    .content(corpusForm.getContent())
                    .version(0)
                    .corpusStatus(CorpusStatusEnum.MARKED_AS_IGNORE.getCode())
                    .misId(misId)
                    .rgId(rgId)
                    .source(CorpusSourceEnum.TT.getCode())
                    .timestamp(new Timestamp(System.currentTimeMillis()))
                    .contentId(corpusForm.getContentId())
                    .type(-1)
                    .backgroundKnowledgeId(relatedIds.getBackgroundKnowledgeId())
                    .corpusSopId(relatedIds.getCorpusSopId())
                    .ruleId(relatedIds.getRuleId())
                    .tagsIds(processedTagsIds)
                    .build();

            int result = reviewMapper.insertModifiedOutputWithVersionCheck(newOutput);
            if (result != 0) {
                return true;
            } else {
                log.warn("#corpusServiceImpl.addIgnoreCorpus# 修改后插入无需入库记录失败,重试: ticketId={}, currentVersion={}", ticketId, 0);
                return false;
            }
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage());
        }
    }

    @Override
    public CorpusInfoDTO queryCorpusAllByTicketIdRgId(String ticketId, long rgId, String misId) throws LlmCorpusException {
        boolean permission = knowledgeBaseService.validateUserPermission(misId, rgId);
        if (!permission) {
            throw new LlmCorpusException(BizCode.NO_PERMISSION.getCode(), "用户无对应值班组权限");
        }
        KnowledgeBaseVersionEntity entity = reviewMapper.findAllInfoByTicketIdRgId(ticketId, rgId);
        if (entity == null) {
            return null;
        }
        List<String> ticketIds = new ArrayList<>();
        ticketIds.add(ticketId);
        List<Timestamp> createTimestampListByTicketId = reviewMapper.findCreateTimestampListByTicketId(ticketIds, rgId);

        CorpusInfoDTO corpusInfoDTO = new CorpusInfoDTO();
        corpusInfoDTO.setTicketId(entity.getTicketId());
        corpusInfoDTO.setTitle(entity.getTitle());
        corpusInfoDTO.setContent(entity.getContent());
        corpusInfoDTO.setSource(entity.getSource());
        corpusInfoDTO.setType(entity.getType());
        corpusInfoDTO.setCreateTime(createTimestampListByTicketId.get(0));
        corpusInfoDTO.setUpdateTime(entity.getTimestamp());
        corpusInfoDTO.setMisId(entity.getMisId());
        corpusInfoDTO.setContentId(entity.getContentId());

        // 根据ID查询对应的内容，注意处理ID为空的情况
        if (entity.getBackgroundKnowledgeId() != null) {
            try {
                RgBackgroundKnowledgeEntity bgEntity = rgBackgroundKnowledgeMapper.queryById(entity.getBackgroundKnowledgeId());
                corpusInfoDTO.setBackgroundKnowledge(bgEntity != null ? bgEntity.getKnowledgeContent() : null);
            } catch (Exception e) {
                log.warn("查询背景知识失败, id: {}, 异常: {}", entity.getBackgroundKnowledgeId(), e.getMessage());
                corpusInfoDTO.setBackgroundKnowledge(null);
            }
        }

        if (entity.getCorpusSopId() != null) {
            try {
                RgSopEntity sopEntity = rgSopMapper.queryById(entity.getCorpusSopId());
                corpusInfoDTO.setSop(sopEntity != null ? sopEntity.getSop() : null);
            } catch (Exception e) {
                log.warn("查询SOP失败, id: {}, 异常: {}", entity.getCorpusSopId(), e.getMessage());
                corpusInfoDTO.setSop(null);
            }
        }

        if (entity.getRuleId() != null) {
            try {
                RgRuleEntity ruleEntity = rgRuleMapper.queryById(entity.getRuleId());
                corpusInfoDTO.setRule(ruleEntity != null ? ruleEntity.getRule() : null);
            } catch (Exception e) {
                log.warn("查询Rule失败, id: {}, 异常: {}", entity.getRuleId(), e.getMessage());
                corpusInfoDTO.setRule(null);
            }
        }

        // 根据tagsIds获取标签名称
        corpusInfoDTO.setTagsname(tagsUtil.getTagsNamesByIds(entity.getTagsIds()));

        return corpusInfoDTO;
    }

    @Override
    public boolean updateSop(SopFormRequest sopFormRequest, long rgId, String misId) throws LlmCorpusException {
        try {
            // 验证SOP内容长度
            validateContentLength(sopFormRequest.getSop(), "sop");
            
            RgSopEntity rgSopEntity = new RgSopEntity();
            List<RgSopEntity> rgSopEntityList = rgSopMapper.queryRgSopByRgId(rgId, 1, 0);
            int currentVersion = -1;
            if(!rgSopEntityList.isEmpty()){
                currentVersion = rgSopEntityList.get(0).getVersion();
            }
            int newVersion = currentVersion + 1;
            rgSopEntity.setRgId(rgId);
            rgSopEntity.setSop(sopFormRequest.getSop());
            rgSopEntity.setVersion(newVersion);
            rgSopEntity.setMisId(misId);
            rgSopEntity.setUpdateTime(new Timestamp(System.currentTimeMillis()));
            int result = rgSopMapper.insertSopWithVersionCheck(rgSopEntity);
            if (result != 0) {
                return true;
            } else {
                log.warn("#CorpusServiceImpl.updateSop# 插入新记录失败,重试: rgId={}, currentVersion={}", rgId, currentVersion);
                return false;
            }
        }catch (Exception e){
            throw LlmCorpusException.buildWithMsg(BizCode.CORPUS_SOP_UPDATE_FAIL, e.getMessage());
        }
    }

    @Override
    public PageDTO<SopDTO> querySopByRgId(long rgId, String misId, int pageNum, int pageSize) {
        if (pageNum < 1) {
            pageNum = 1;
        }
        int offset = (pageNum - 1) * pageSize;
        int totalSize = rgSopMapper.countSopByRgId(rgId);
        // 获取当前页数据
        List<RgSopEntity> rgSopEntityList = rgSopMapper.queryRgSopByRgId(rgId, pageSize, offset);
        List<SopDTO> sopDTOList = new ArrayList<>();
        for (RgSopEntity rgSopEntity : rgSopEntityList) {
            SopDTO sopDTO = new SopDTO();
            sopDTO.setSop(rgSopEntity.getSop());
            sopDTO.setUpdateTime(rgSopEntity.getUpdateTime());
            sopDTO.setMisId(rgSopEntity.getMisId());
            sopDTOList.add(sopDTO);
        }
        // 构建PageDTO
        return PageDTO.<SopDTO>builder()
                .pageNum(pageNum)
                .pageSize(pageSize)
                .totalCount(totalSize)
                .totalPage(totalSize / pageSize + (totalSize % pageSize == 0 ? 0 : 1))
                .data(sopDTOList)
                .build();
    }

    @Override
    public String queryLatestSopByRgId(long rgId) {
        List<RgSopEntity> rgSopEntityList = rgSopMapper.queryRgSopByRgId(rgId, 1, 0);
        if(rgSopEntityList.isEmpty()){
            // 获取 我们定的rgId的最新版本数据
            SopDefaultConfig sopDefaultConfig = mtConfigService.getSopDefaultConfig();
            List<RgSopEntity> defaultRgSopEntityList = rgSopMapper.queryRgSopByRgId(sopDefaultConfig.getDefaultRgId(), 1, 0);
            if(defaultRgSopEntityList.isEmpty()){
                // 返回默认模板
                return sopDefaultConfig.getDefaultTemplate();
            }else {
                return defaultRgSopEntityList.get(0).getSop();
            }
        }else {
            return rgSopEntityList.get(0).getSop();
        }
    }


    /**
     * 根据值班组ID和ContentId查询语料列表
     * @param rgId 值班组ID
     * @param misId 用户ID
     * @param contentId 内容ID
     * @param pageNum 页码
     * @param pageSize 页大小
     * @param strMatch 匹配字符串
     * @return 语料分页列表
     * @throws LlmCorpusException 业务异常
     */
    @Override
    public PageDTO<CorpusInfoDTO> queryCorpusListByRgIdContentId(long rgId, String misId, long contentId, int pageNum, int pageSize, String strMatch) throws LlmCorpusException {
        boolean permission = knowledgeBaseService.validateUserPermission(misId, rgId);
        if (!permission) {
            throw new LlmCorpusException(BizCode.NO_PERMISSION.getCode(), "用户无对应值班组权限");
        }
        if (pageNum < 1) {
            pageNum = 1;
        }
        int offset = (pageNum - 1) * pageSize;
        int totalSize = reviewMapper.countCorpusByRgIdContentId(rgId, contentId, strMatch);
        // 获取当前页数据
        List<KnowledgeBaseVersionEntity> entityLatestList = reviewMapper.findLatestKnowledgeListByRgIdContentId(rgId, contentId, pageSize, offset, strMatch);

        // 提取ticketId列表
        List<String> ticketIdList = entityLatestList.stream().map(KnowledgeBaseVersionEntity::getTicketId).collect(Collectors.toList());
        //查询创建时间
        Map<String, Timestamp> entityCreateMap = new HashMap<>();
        if (!ticketIdList.isEmpty()) {
            reviewMapper.findCreateKnowledgeListByTicketId(ticketIdList, rgId).forEach(entity -> entityCreateMap.put(entity.getTicketId(), entity.getTimestamp()));
        }

        // 创建DTO列表
        List<CorpusInfoDTO> corpusInfoDTOList = new ArrayList<>();
        for (int i = 0; i < entityLatestList.size(); i++) {
            KnowledgeBaseVersionEntity latestEntity = entityLatestList.get(i);

            CorpusInfoDTO dto = new CorpusInfoDTO();
            dto.setTicketId(latestEntity.getTicketId());
            dto.setTitle(latestEntity.getTitle());
            dto.setContent(latestEntity.getContent());
            dto.setType(-1);
            dto.setSource(latestEntity.getSource());
            dto.setMisId(latestEntity.getMisId());
            dto.setCreateTime(entityCreateMap.get(latestEntity.getTicketId()));
            dto.setUpdateTime(latestEntity.getTimestamp());
            dto.setContentId(latestEntity.getContentId());

            // 根据ID查询对应的内容，注意处理ID为空的情况
            if (latestEntity.getBackgroundKnowledgeId() != null) {
                try {
                    RgBackgroundKnowledgeEntity bgEntity = rgBackgroundKnowledgeMapper.queryById(latestEntity.getBackgroundKnowledgeId());
                    dto.setBackgroundKnowledge(bgEntity != null ? bgEntity.getKnowledgeContent() : null);
                } catch (Exception e) {
                    log.warn("查询背景知识失败, id: {}, 异常: {}", latestEntity.getBackgroundKnowledgeId(), e.getMessage());
                    dto.setBackgroundKnowledge(null);
                }
            }

            if (latestEntity.getCorpusSopId() != null) {
                try {
                    RgSopEntity sopEntity = rgSopMapper.queryById(latestEntity.getCorpusSopId());
                    dto.setSop(sopEntity != null ? sopEntity.getSop() : null);
                } catch (Exception e) {
                    log.warn("查询SOP失败, id: {}, 异常: {}", latestEntity.getCorpusSopId(), e.getMessage());
                    dto.setSop(null);
                }
            }

            if (latestEntity.getRuleId() != null) {
                try {
                    RgRuleEntity ruleEntity = rgRuleMapper.queryById(latestEntity.getRuleId());
                    dto.setRule(ruleEntity != null ? ruleEntity.getRule() : null);
                } catch (Exception e) {
                    log.warn("查询Rule失败, id: {}, 异常: {}", latestEntity.getRuleId(), e.getMessage());
                    dto.setRule(null);
                }
            }

            // 根据tagsIds获取标签名称
            dto.setTagsname(tagsUtil.getTagsNamesByIds(latestEntity.getTagsIds()));

            corpusInfoDTOList.add(dto);
        }
        // 计算总页数
        int totalPage = (int) Math.ceil((double) totalSize / pageSize);

        // 构建PageDTO
        return PageDTO.<CorpusInfoDTO>builder()
                .pageNum(pageNum)
                .pageSize(pageSize)
                .totalCount(totalSize)
                .totalPage(totalPage)
                .data(corpusInfoDTOList)
                .build();
    }

    @Override
    public PageDTO<CorpusInfoDTO> queryCorpusListByCondition(long rgId, String misId, String ticketId, 
            String title, String content, Integer source, String creator,
            String startTime, String endTime, String strMatch, String tagsIds, int pageNum, int pageSize) throws LlmCorpusException {
        // 参数校验
        if (pageNum < 1) {
            pageNum = 1;
        }

        // 处理日期查询范围，让 startTime 精确到当天 0 点，endTime 精确到当天 24 点
        String formattedStartTime = startTime;
        String formattedEndTime = endTime;
        if (startTime != null && !startTime.isEmpty() && startTime.length() <= 10) {
            formattedStartTime = startTime + " 00:00:00";
        }
        if (endTime != null && !endTime.isEmpty() && endTime.length() <= 10) {
            formattedEndTime = endTime + " 23:59:59";
        }
        
        // 计算分页偏移量
        int offset = (pageNum - 1) * pageSize;
        
        // 获取符合条件的总记录数
        int totalSize = reviewMapper.countCorpusByCondition(rgId, ticketId, title, content, 
                source != null ? source.toString() : null, creator, formattedStartTime, formattedEndTime, strMatch, tagsIds);
        
        // 获取当前页数据
        List<KnowledgeBaseVersionEntity> entityLatestList = reviewMapper.findLatestKnowledgeListByCondition(
                rgId, ticketId, title, content, source != null ? source.toString() : null, 
                creator, formattedStartTime, formattedEndTime, strMatch, tagsIds, pageSize, offset);

        // 提取ticketId列表
        List<String> ticketIdList = entityLatestList.stream()
                .map(KnowledgeBaseVersionEntity::getTicketId)
                .collect(Collectors.toList());
                
        // 查询创建时间
        Map<String, Timestamp> entityCreateMap = new HashMap<>();
        if (!ticketIdList.isEmpty()) {
            reviewMapper.findCreateKnowledgeListByTicketId(ticketIdList, rgId)
                    .forEach(entity -> entityCreateMap.put(entity.getTicketId(), entity.getTimestamp()));
        }

        // 创建DTO列表
        List<CorpusInfoDTO> corpusInfoDTOList = new ArrayList<>();
        for (KnowledgeBaseVersionEntity latestEntity : entityLatestList) {
            CorpusInfoDTO dto = new CorpusInfoDTO();
            dto.setTicketId(latestEntity.getTicketId());
            dto.setTitle(latestEntity.getTitle());
            dto.setContent(latestEntity.getContent());
            dto.setType(-1);
            dto.setSource(latestEntity.getSource());
            dto.setMisId(latestEntity.getMisId());
            dto.setCreateTime(entityCreateMap.get(latestEntity.getTicketId()));
            dto.setUpdateTime(latestEntity.getTimestamp());
            dto.setContentId(latestEntity.getContentId());

            // 根据ID查询对应的内容，注意处理ID为空的情况
            if (latestEntity.getBackgroundKnowledgeId() != null) {
                try {
                    RgBackgroundKnowledgeEntity bgEntity = rgBackgroundKnowledgeMapper.queryById(latestEntity.getBackgroundKnowledgeId());
                    dto.setBackgroundKnowledge(bgEntity != null ? bgEntity.getKnowledgeContent() : null);
                } catch (Exception e) {
                    log.warn("查询背景知识失败, id: {}, 异常: {}", latestEntity.getBackgroundKnowledgeId(), e.getMessage());
                    dto.setBackgroundKnowledge(null);
                }
            }

            if (latestEntity.getCorpusSopId() != null) {
                try {
                    RgSopEntity sopEntity = rgSopMapper.queryById(latestEntity.getCorpusSopId());
                    dto.setSop(sopEntity != null ? sopEntity.getSop() : null);
                } catch (Exception e) {
                    log.warn("查询SOP失败, id: {}, 异常: {}", latestEntity.getCorpusSopId(), e.getMessage());
                    dto.setSop(null);
                }
            }

            if (latestEntity.getRuleId() != null) {
                try {
                    RgRuleEntity ruleEntity = rgRuleMapper.queryById(latestEntity.getRuleId());
                    dto.setRule(ruleEntity != null ? ruleEntity.getRule() : null);
                } catch (Exception e) {
                    log.warn("查询Rule失败, id: {}, 异常: {}", latestEntity.getRuleId(), e.getMessage());
                    dto.setRule(null);
                }
            }

            // 根据tagsIds获取标签名称
            dto.setTagsname(tagsUtil.getTagsNamesByIds(latestEntity.getTagsIds()));

            corpusInfoDTOList.add(dto);
        }

        // 计算总页数
        int totalPage = (int) Math.ceil((double) totalSize / pageSize);

        // 构建PageDTO
        return PageDTO.<CorpusInfoDTO>builder()
                .pageNum(pageNum)
                .pageSize(pageSize)
                .totalCount(totalSize)
                .totalPage(totalPage)
                .data(corpusInfoDTOList)
                .build();
    }

    private void validateMergeCorpusRequest(SaveMergeCorpusParam request) throws LlmCorpusException {
        if (request == null) {
            throw LlmCorpusException.buildWithMsg(BizCode.ILLEGAL_ARGUMENT,"请求参数为空");
        }
        if (CollectionUtils.isEmpty(request.getCorpusIdList())) {
            throw LlmCorpusException.buildWithMsg(BizCode.ILLEGAL_ARGUMENT,"corpus 列表为空");
        }
        if (request.getRgId() == null) {
            throw LlmCorpusException.buildWithMsg(BizCode.ILLEGAL_ARGUMENT,"rgId为空");
        }
        if (request.getSource() == null) {
            throw LlmCorpusException.buildWithMsg(BizCode.ILLEGAL_ARGUMENT,"source为空");
        }
        if (StringUtils.isBlank(request.getTitle())) {
            throw LlmCorpusException.buildWithMsg(BizCode.ILLEGAL_ARGUMENT,"标题为空");
        }
        if (StringUtils.isBlank(request.getContent())) {
            throw LlmCorpusException.buildWithMsg(BizCode.ILLEGAL_ARGUMENT,"内容为空");
        }
        if (StringUtils.isBlank(request.getMisId())) {
            throw LlmCorpusException.buildWithMsg(BizCode.ILLEGAL_ARGUMENT,"misId为空");
        }
        if(StringUtils.isBlank(request.getTicketId())){
            throw LlmCorpusException.buildWithMsg(BizCode.ILLEGAL_ARGUMENT,"ticketId为空");
        }
    }

    /**
     * 验证内容长度是否超出限制
     * @param content 待验证的内容
     * @param contentType 内容类型："rule", "sop", "knowledge"
     * @throws LlmCorpusException 如果内容长度超出限制
     */
    private void validateContentLength(String content, String contentType) throws LlmCorpusException {
        if (content == null) {
            return; // null值由其他校验处理
        }
        
        ContentLengthLimitConfig config = mtConfigService.getContentLengthLimitConfig();
        int maxLength;
        String typeName;
        
        switch (contentType) {
            case "rule":
                maxLength = config.getRuleMaxLength();
                typeName = "规则";
                break;
            case "sop":
                maxLength = config.getSopMaxLength();
                typeName = "SOP";
                break;
            case "knowledge":
                maxLength = config.getKnowledgeMaxLength();
                typeName = "背景知识";
                break;
            default:
                throw new LlmCorpusException(BizCode.ILLEGAL_ARGUMENT.getCode(), "未知的内容类型");
        }
        
        if (content.length() > maxLength) {
            throw new LlmCorpusException(BizCode.ILLEGAL_ARGUMENT.getCode(), 
                String.format("%s内容长度超出限制，最大允许%d字符，当前%d字符", 
                    typeName, maxLength, content.length()));
        }
    }

    // 更新自定义背景知识
    @Override
    public boolean updateBackgroundKnowledge(long rgId, String misId, String knowledgeContent) throws LlmCorpusException {
        try {
            // 验证背景知识内容长度
            validateContentLength(knowledgeContent, "knowledge");
            
            boolean permission = knowledgeBaseService.validateUserPermission(misId, Long.valueOf(rgId));
            if (!permission) {
                throw new LlmCorpusException(BizCode.NO_PERMISSION.getCode(), "用户无对应值班组权限");
            }
            // 查询当前最新版本
            RgBackgroundKnowledgeEntity latest = rgBackgroundKnowledgeMapper.queryLatestByRgId(rgId);
            long currentVersion = latest != null ? latest.getVersion() : -1;
            long newVersion = currentVersion + 1;

            RgBackgroundKnowledgeEntity entity = new RgBackgroundKnowledgeEntity();
            entity.setRgId(rgId);
            entity.setKnowledgeContent(knowledgeContent);
            entity.setVersion(newVersion);
            entity.setMisId(misId);
            entity.setUpdateTime(new Timestamp(System.currentTimeMillis()));

            int result = rgBackgroundKnowledgeMapper.insertWithVersionCheck(entity);
            return result != 0;
        } catch (Exception e) {
            throw LlmCorpusException.buildWithMsg(BizCode.KNOWLEDGE_UPDATE_FAIL, e.getMessage());
        }
    }

    // 分页查询自定义背景知识
    @Override
    public PageDTO<BackgroundKnowledgeDTO> queryBackgroundKnowledgeByRgId(long rgId, String misId, int pageNum, int pageSize) {
        if (pageNum < 1) {
            pageNum = 1;
        }
        int offset = (pageNum - 1) * pageSize;
        int totalSize = rgBackgroundKnowledgeMapper.countByRgId(rgId);
        List<RgBackgroundKnowledgeEntity> entityList = rgBackgroundKnowledgeMapper.queryByRgId(rgId, pageSize, offset);
        List<BackgroundKnowledgeDTO> dtoList = new ArrayList<>();
        for (RgBackgroundKnowledgeEntity entity : entityList) {
            BackgroundKnowledgeDTO dto = new BackgroundKnowledgeDTO();
            dto.setId(entity.getId());
            dto.setRgId(entity.getRgId());
            dto.setKnowledgeContent(entity.getKnowledgeContent());
            dto.setVersion(entity.getVersion());
            dto.setUpdateTime(entity.getUpdateTime());
            dto.setMisId(entity.getMisId());
            dtoList.add(dto);
        }
        return PageDTO.<BackgroundKnowledgeDTO>builder()
                .pageNum(pageNum)
                .pageSize(pageSize)
                .totalCount(totalSize)
                .totalPage(totalSize / pageSize + (totalSize % pageSize == 0 ? 0 : 1))
                .data(dtoList)
                .build();
    }

    // 查询最新自定义背景知识
    @Override
    public String queryLatestBackgroundKnowledgeByRgId(long rgId) {
        RgBackgroundKnowledgeEntity latest = rgBackgroundKnowledgeMapper.queryLatestByRgId(rgId);
        if (latest == null || latest.getKnowledgeContent() == null || latest.getKnowledgeContent().isEmpty()) {
            return "";
        } else {
            return latest.getKnowledgeContent();
        }
    }

    // 更新自定义规则
    @Override
    public boolean updateRule(long rgId, String misId, String rule) throws LlmCorpusException {
        try {
            // 验证规则内容长度
            validateContentLength(rule, "rule");
            
            boolean permission = knowledgeBaseService.validateUserPermission(misId, Long.valueOf(rgId));
            if (!permission) {
                throw new LlmCorpusException(BizCode.NO_PERMISSION.getCode(), "用户无对应值班组权限");
            }
            // 查询当前最新版本
            RgRuleEntity latest = rgRuleMapper.queryLatestByRgId(rgId);
            long currentVersion = latest != null ? latest.getVersion() : -1;
            long newVersion = currentVersion + 1;

            RgRuleEntity entity = new RgRuleEntity();
            entity.setRgId(rgId);
            entity.setRule(rule);
            entity.setVersion(newVersion);
            entity.setMisId(misId);
            entity.setUpdateTime(new Timestamp(System.currentTimeMillis()));

            int result = rgRuleMapper.insertWithVersionCheck(entity);
            return result != 0;
        } catch (Exception e) {
            throw LlmCorpusException.buildWithMsg(BizCode.RULE_UPDATE_FAIL, e.getMessage());
        }
    }

    // 分页查询自定义规则
    @Override
    public PageDTO<RuleDTO> queryRuleByRgId(long rgId, String misId, int pageNum, int pageSize) {
        if (pageNum < 1) {
            pageNum = 1;
        }
        int offset = (pageNum - 1) * pageSize;
        int totalSize = rgRuleMapper.countByRgId(rgId);
        List<RgRuleEntity> entityList = rgRuleMapper.queryByRgId(rgId, pageSize, offset);
        List<RuleDTO> dtoList = new ArrayList<>();
        for (RgRuleEntity entity : entityList) {
            RuleDTO dto = new RuleDTO();
            dto.setId(entity.getId());
            dto.setRgId(entity.getRgId());
            dto.setRule(entity.getRule());
            dto.setVersion(entity.getVersion());
            dto.setUpdateTime(entity.getUpdateTime());
            dto.setMisId(entity.getMisId());
            dtoList.add(dto);
        }
        return PageDTO.<RuleDTO>builder()
                .pageNum(pageNum)
                .pageSize(pageSize)
                .totalCount(totalSize)
                .totalPage(totalSize / pageSize + (totalSize % pageSize == 0 ? 0 : 1))
                .data(dtoList)
                .build();
    }

    // 查询最新自定义规则
    @Override
    public String queryLatestRuleByRgId(long rgId) {
        RgRuleEntity latest = rgRuleMapper.queryLatestByRgId(rgId);
        if (latest == null || latest.getRule() == null || latest.getRule().isEmpty()) {
            return "";
        } else {
            return latest.getRule();
        }
    }

    /**
     * 将语料全量数据列表转换为Excel字节数组
     * @param dataList 语料数据列表
     * @param entityCreateMap ticketId->创建时间
     * @return Excel内容的字节数组
     */
    private byte[] convertCorpusToExcelContent(List<KnowledgeBaseVersionEntity> dataList, Map<String, Timestamp> entityCreateMap) {
        if (CollectionUtils.isEmpty(dataList)) {
            log.warn("语料数据列表为空，无法转换为Excel内容");
            return null;
        }
        ByteArrayOutputStream outputStream = null;
        try {
            outputStream = new ByteArrayOutputStream();
            // 定义表头
            List<List<String>> headList = new ArrayList<>();
            headList.add(Collections.singletonList("语料Id"));
            headList.add(Collections.singletonList("标题"));
            headList.add(Collections.singletonList("内容"));
            headList.add(Collections.singletonList("类型"));
            headList.add(Collections.singletonList("来源"));
            headList.add(Collections.singletonList("操作人"));
            headList.add(Collections.singletonList("标签"));
            headList.add(Collections.singletonList("创建时间"));
            headList.add(Collections.singletonList("更新时间"));

            // 创建表头和内容样式
            WriteCellStyle headStyle = new WriteCellStyle();
            WriteFont headFont = new WriteFont();
            headFont.setFontHeightInPoints((short) 12);
            headFont.setBold(true);
            headStyle.setWriteFont(headFont);
            headStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
            headStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            headStyle.setBorderBottom(BorderStyle.THIN);
            headStyle.setBorderLeft(BorderStyle.THIN);
            headStyle.setBorderRight(BorderStyle.THIN);
            headStyle.setBorderTop(BorderStyle.THIN);
            headStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());

            WriteCellStyle contentStyle = new WriteCellStyle();
            contentStyle.setWrapped(true);
            contentStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            contentStyle.setBorderBottom(BorderStyle.THIN);
            contentStyle.setBorderLeft(BorderStyle.THIN);
            contentStyle.setBorderRight(BorderStyle.THIN);
            contentStyle.setBorderTop(BorderStyle.THIN);

            HorizontalCellStyleStrategy styleStrategy = new HorizontalCellStyleStrategy(headStyle, contentStyle);

            SimpleDateFormat dateFormat = new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            List<List<Object>> dataRows = new ArrayList<>();
            for (KnowledgeBaseVersionEntity entity : dataList) {
                if (entity == null) {
                    continue;
                }
                List<Object> row = new ArrayList<>();
                row.add(entity.getTicketId());
                row.add(entity.getTitle());
                row.add(entity.getContent());
                row.add("-1");
                row.add(entity.getSource() != null ? entity.getSource().toString() : "");
                row.add(entity.getMisId());
                // 根据tagsIds获取标签名称并转换为字符串
                List<String> tagNames = tagsUtil.getTagsNamesByIds(entity.getTagsIds());
                String tagsStr = tagNames.isEmpty() ? "" : String.join(", ", tagNames);
                row.add(tagsStr);
                Timestamp createTime = entityCreateMap.get(entity.getTicketId());
                row.add(createTime != null ? dateFormat.format(createTime) : "");
                row.add(entity.getTimestamp() != null ? dateFormat.format(entity.getTimestamp()) : "");
                dataRows.add(row);
            }
            EasyExcel.write(outputStream)
                    .head(headList)
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .registerWriteHandler(styleStrategy)
                    .sheet("语料全量导出")
                    .doWrite(dataRows);
            return outputStream.toByteArray();
        } catch (Exception e) {
            log.error("语料转换为Excel内容异常: {}", e.getMessage(), e);
            return null;
        } finally {
            if (outputStream != null) {
                try {
                    outputStream.close();
                } catch (java.io.IOException e) {
                    log.error("关闭Excel输出流异常: {}", e.getMessage());
                }
            }
        }
    }

    /**
     * 导出语料全量数据为Excel并上传S3，返回下载链接
     */
    @Override
    public String exportAllCorpusToExcelAndUploadToS3(long rgId, String misId, String ticketId,
            String title, String content, Integer source, String creator,
            String startTime, String endTime, String strMatch, String tagsIds) throws LlmCorpusException {
        Assert.notNull(rgId, "rgId不能为空");
        boolean permission = knowledgeBaseService.validateUserPermission(misId, rgId);
        if (!permission) {
            throw new LlmCorpusException(BizCode.NO_PERMISSION.getCode(), "用户无对应值班组权限");
        }

        // 处理日期查询范围，让 startTime 精确到当天 0 点，endTime 精确到当天 24 点
        String formattedStartTime = startTime;
        String formattedEndTime = endTime;
        if (startTime != null && !startTime.isEmpty() && startTime.length() <= 10) {
            formattedStartTime = startTime + " 00:00:00";
        }
        if (endTime != null && !endTime.isEmpty() && endTime.length() <= 10) {
            formattedEndTime = endTime + " 23:59:59";
        }

        try {
            // 查询全量数据，使用多条件筛选
            List<KnowledgeBaseVersionEntity> entityLatestList = reviewMapper.findAllLatestKnowledgeListByRgId(
                rgId, ticketId, title, content,
                source != null ? source.toString() : null,
                creator, formattedStartTime, formattedEndTime, strMatch, tagsIds);

            if (entityLatestList == null || entityLatestList.isEmpty()) {
                log.warn("查询结果为空，无法生成Excel文件");
                return null;
            }

            // 查询创建时间
            List<String> ticketIdList = entityLatestList.stream().map(KnowledgeBaseVersionEntity::getTicketId).collect(Collectors.toList());
            Map<String, Timestamp> entityCreateMap = new HashMap<>();
            if (!ticketIdList.isEmpty()) {
                reviewMapper.findCreateKnowledgeListByTicketId(ticketIdList, rgId)
                        .forEach(entity -> entityCreateMap.put(entity.getTicketId(), entity.getTimestamp()));
            }

            // 构建Excel数据
            byte[] excelContent = convertCorpusToExcelContent(entityLatestList, entityCreateMap);

            if (excelContent == null || excelContent.length == 0) {
                log.warn("生成Excel内容失败");
                return null;
            }

            // 生成任务ID用于文件名
            String taskId = "corpus_export_" + UUID.randomUUID().toString().replace("-", "");

            // 上传到S3并获取下载链接
            String downloadLink = ExcelExportUtil.uploadContentToS3(excelContent, taskId, ".xlsx");

            if (downloadLink != null) {
                log.info("语料Excel文件上传成功，下载链接: {}", downloadLink);
                return downloadLink;
            } else {
                log.warn("语料Excel文件上传失败，无法生成下载链接");
                return null;
            }
        } catch (Exception e) {
            log.error("语料全量导出Excel并上传到S3异常: rgId={}, error={}", rgId, e.getMessage(), e);
            throw new LlmCorpusException(BizCode.SERVER_INTERNAL_ERROR.getCode(),
                    "语料全量导出Excel并上传到S3异常: " + e.getMessage());
        }
    }
}