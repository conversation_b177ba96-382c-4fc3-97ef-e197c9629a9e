package com.meituan.banma.llm.corpus.server.dal.mapper;

import com.meituan.banma.llm.corpus.server.dal.entity.StatisticsReportEntity;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 统计报表Mapper接口
 */
@Repository
public interface StatisticsReportMapper {
    
    /**
     * 插入报表数据
     *
     * @param entity 报表实体
     * @return 影响行数
     */
    int insert(StatisticsReportEntity entity);
    
    /**
     * 根据应用ID和时间范围查询报表
     *
     * @param appId     应用ID
     * @param startTime 开始时间戳
     * @param endTime   结束时间戳
     * @return 报表实体
     */
    StatisticsReportEntity selectByAppIdAndTimeRange(
            @Param("appId") String appId,
            @Param("startTime") Long startTime,
            @Param("endTime") Long endTime);
    
    /**
     * 根据应用ID和时间范围更新报表数据
     *
     * @param entity 报表实体
     * @return 影响行数
     */
    int updateByAppIdAndTimeRange(StatisticsReportEntity entity);
    
    /**
     * 删除指定应用ID的所有报表数据
     *
     * @param appId 应用ID
     * @return 影响行数
     */
    int deleteByAppId(@Param("appId") String appId);
    
    /**
     * 批量插入报表数据
     *
     * @param entities 报表实体列表
     * @return 影响行数
     */
    int batchInsert(List<StatisticsReportEntity> entities);
} 