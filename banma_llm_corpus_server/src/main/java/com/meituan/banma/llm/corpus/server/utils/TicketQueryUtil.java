package com.meituan.banma.llm.corpus.server.utils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.meituan.banma.llm.corpus.api.constants.BizCode;
import com.meituan.banma.llm.corpus.api.exception.LlmCorpusException;
import com.meituan.banma.llm.corpus.server.common.MtConfigService;
import com.meituan.banma.llm.corpus.server.common.constants.enums.DeliveryTypeEnum;
import com.meituan.banma.llm.corpus.server.common.domain.dto.CustomFieldValuesItem;
import com.meituan.banma.llm.corpus.server.common.domain.dto.DynamicTemplateTicketDTO;
import com.meituan.banma.llm.corpus.server.common.domain.dto.TTInfoDTO;
import com.meituan.banma.llm.corpus.server.common.domain.dto.TemplateConfig;
import com.meituan.banma.llm.corpus.server.common.domain.dto.TicketRangeDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/12/4
 */

@Slf4j
@Component
public class TicketQueryUtil {
    @Resource
    private MtConfigService mtConfigService;

    @Resource
    private DeliveryTypeEnum deliveryTypeEnum;

    private static final String FILTER_QUERY_METHOD = "/api/1.0/ticket/filter/query";

    private static final String DX_GROUP_QUERY_METHOD = "/api/1.0/chat/already/created";

    private static final String TICKET_INFO_QUERY_METHOD = "/api/1.0/ticket/";

    private static final String INVITE_USERS_TO_DX_GROUP_METHOD = "/api/1.0/chat/user/invite";

    private static final List<String> RESOLVED_STATES = Arrays.asList("已解决", "已关闭");

    /**
     * 获取已解决状态列表
     * @return 已解决状态列表
     */
    public static List<String> getResolvedStates() {
        return RESOLVED_STATES;
    }

    /**
     * 根据值班组查询tt列表（全量、有筛选条件）
     * @param misId 用户ID
     * @param rgId 组织id
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @param createdAtStart 创建开始时间
     * @param createdAtEnd 创建结束时间
     * @return 响应结果
     */
    public String queryByRgIdALL(String misId, Long rgId, int pageNum, int pageSize, Long createdAtStart, Long createdAtEnd) {
        if (Objects.isNull(rgId)) {
            throw new IllegalArgumentException("rgId is null");
        }

        Map<String, Object> bodyMap = new HashMap<>();
        bodyMap.put("rgIds", Collections.singletonList(rgId));
        bodyMap.put("pageNum", pageNum);
        bodyMap.put("pageSize", pageSize);

        if (createdAtStart != null) {
            bodyMap.put("createdAtStart", createdAtStart);
        }
        if (createdAtEnd != null) {
            bodyMap.put("createdAtEnd", createdAtEnd);
        }
        // 拼接URL参数
        Map<String, String> params = new HashMap<>();
        params.put("cn", String.valueOf(pageNum));
        params.put("sn", String.valueOf(pageSize));
        params.put("orderKind", "DESC");
        String urlWithParams = buildUrlWithParams(mtConfigService.getTtQueryDomain() + FILTER_QUERY_METHOD, params);

        return HttpUtil.doPost(urlWithParams, getCommonHeaders(misId), bodyMap);
    }

    public String queryByRgId(String misId, Long rgId, int pageNum, int pageSize, Long createdAtStart, Long createdAtEnd) {
        if (Objects.isNull(rgId)) {
            throw new IllegalArgumentException("rgId is null");
        }

        // 根据rgIds查询并只查询已解决和已关闭的
        Map<String, Object> bodyMap = new HashMap<>();
        bodyMap.put("rgIds", Collections.singletonList(rgId));
        bodyMap.put("state", RESOLVED_STATES);
        if (createdAtStart != null) {
            bodyMap.put("createdAtStart", createdAtStart);
        }
        if (createdAtEnd != null) {
            bodyMap.put("createdAtEnd", createdAtEnd);
        }

        // 拼接URL参数
        Map<String, String> params = new HashMap<>();
        params.put("cn", String.valueOf(pageNum));
        params.put("sn", String.valueOf(pageSize));
        params.put("orderKind", "DESC");
        String urlWithParams = buildUrlWithParams(mtConfigService.getTtQueryDomain() + FILTER_QUERY_METHOD, params);

        return HttpUtil.doPost(urlWithParams, getCommonHeaders(misId), bodyMap);
    }
    public String queryByRgIdWithDateFilter(String misId, Long rgId, int pageNum, int pageSize, Long startTime, Long endTime) {
        if (Objects.isNull(rgId)) {
            throw new IllegalArgumentException("rgId is null");
        }

        // 根据rgIds查询并只查询已解决和已关闭的
        Map<String, Object> bodyMap = new HashMap<>();
        bodyMap.put("rgIds", Collections.singletonList(rgId));
        bodyMap.put("createdAtStart", String.valueOf(startTime));
        bodyMap.put("createdAtEnd", String.valueOf(endTime));
        // 拼接URL参数
        Map<String, String> params = new HashMap<>();
        params.put("cn", String.valueOf(pageNum));
        params.put("sn", String.valueOf(pageSize));
        params.put("orderKind", "DESC");
        String urlWithParams = buildUrlWithParams(mtConfigService.getTtQueryDomain() + FILTER_QUERY_METHOD, params);

        return HttpUtil.doPost(urlWithParams, getCommonHeaders(misId), bodyMap);
    }
    /**
     * 根据ttId查询tt详情
     * 
     * @param ttId ticket id
     * @return TTInfoDTO
     *         https://km.sankuai.com/collabpage/1310169407#id-ticket%E8%AF%A6%E6%83%85
     */
    public TTInfoDTO getTtInfo(String misId, String ttId, long groupId) {
        if (Objects.isNull(ttId)) {
            throw new IllegalArgumentException("ttId is null");
        }

        TTInfoDTO ttInfoDTO = new TTInfoDTO();

        String urlWithTicketId = mtConfigService.getTtQueryDomain() + TICKET_INFO_QUERY_METHOD + ttId;

        String jsonString = HttpUtil.doGet(urlWithTicketId, getCommonHeaders(misId));
        // 解析响应结果，拿到TicketDetailDTO的列表
        JSONObject respObj = JSONObject.parseObject(jsonString).getJSONObject("data");
        if (respObj == null) {
            return ttInfoDTO;
        }
        TTInfoDTO infoDTO = null;

        try{
            infoDTO = respObj.toJavaObject(TTInfoDTO.class);
            infoDTO.setGroupId(groupId);
            infoDTO.setRgId(respObj.getLong("rgId"));
            infoDTO.setTicketId(ttId);
            infoDTO.setItemName(respObj.getString("itemName"));
            infoDTO.setTypeName(respObj.getString("typeName"));
            infoDTO.setName(respObj.getString("name"));
            infoDTO.setSla(respObj.getString("sla"));
            infoDTO.setTicketType(respObj.getString("ticketType"));
            infoDTO.setAssigned(respObj.getString("assigned"));
            JSONObject stateObject = respObj.getJSONObject("state");
            infoDTO.setState(stateObject.getString("name"));
            infoDTO.setCategoryName(respObj.getString("categoryName"));
            infoDTO.setCustomFieldValues(respObj.getObject("customFieldValues", new TypeReference<List<CustomFieldValuesItem>>(){}));
        }catch (Exception e){
            log.error("TicketQueryUtil.getTtInfo error, jsonString:{}", jsonString, e);
        }

        return infoDTO;
    }

    /**
     * 通过ticketId查询大象群信息
     * 参考文档：https://km.sankuai.com/collabpage/2552597188
     * 
     * @param ticketId tt id
     * @return json
     */
    public String queryDxInfoByTicketId(String misId, String ticketId) {
        if (StringUtils.isBlank(ticketId)) {
            throw new IllegalArgumentException("ticketId is blank");
        }

        Map<String, String> headers = getCommonHeaders(misId);
        Map<String, String> params = new HashMap<>();
        params.put("ticketId", ticketId);
        String urlWithParams = buildUrlWithParams(mtConfigService.getTtQueryDomain() + DX_GROUP_QUERY_METHOD, params);
        return HttpUtil.doGet(urlWithParams, headers);
    }

    /**
     * 邀请用户加入大象群
     * 参考文档：https://km.sankuai.com/collabpage/2552597188
     *
     * @param misId 用户ID
     * @param ticketId ticket的id
     * @param groupId 群组的id
     * @param userList 加入大象群的用户列表
     * @return 是否邀请成功
     */
    public void inviteUsersToDxGroup(String misId, String ticketId, Long groupId, List<String> userList) throws LlmCorpusException {
        if (StringUtils.isBlank(ticketId)) {
            throw new IllegalArgumentException("ticketId is blank");
        }
        if (Objects.isNull(groupId)) {
            throw new IllegalArgumentException("groupId is null");
        }
        if (userList == null || userList.isEmpty()) {
            throw new IllegalArgumentException("userList is empty");
        }
        if (userList.size() > 10) {
            throw new IllegalArgumentException("userList size cannot exceed 10");
        }
        
        try {
            
            // 构建请求体
            Map<String, Object> bodyMap = new HashMap<>();
            bodyMap.put("ticketId", Long.parseLong(ticketId));
            bodyMap.put("groupId", groupId);
            bodyMap.put("userList", userList);

            // 发送请求
            String url = mtConfigService.getTtQueryDomain() + INVITE_USERS_TO_DX_GROUP_METHOD;
            String response = HttpUtil.doPost(url, getCommonHeaders(misId), bodyMap);
            
            // 解析响应结果
            JSONObject respObj = JSONObject.parseObject(response);
            if (respObj != null && respObj.getInteger("code") == 200) {
                log.info("邀请用户加入大象群成功, ticketId:{}, groupId:{}, userList:{}", ticketId, groupId, userList);
            } else {
                // 检查是否是改版前创建的大象群错误
                if (respObj != null && respObj.getInteger("code") == 400) {
                    JSONObject data = respObj.getJSONObject("data");
                    if (data != null && data.getInteger("errorCode") == 10098) {
                        String errorMsg = "改版前创建的大象群暂不支持此功能，敬请谅解";
                        log.error("邀请用户加入大象群失败, ticketId:{}, groupId:{}, userList:{}, 错误信息:{}", 
                                ticketId, groupId, userList, errorMsg);
                        throw LlmCorpusException.buildWithMsg(BizCode.DX_GROUP_OPERATION_ERROR.getCode(), errorMsg);
                    }
                }
                log.error("邀请用户加入大象群失败, ticketId:{}, groupId:{}, userList:{}, response:{}", 
                         ticketId, groupId, userList, response);
            }
        } catch (NumberFormatException e) {
            log.error("邀请用户加入大象群失败, ticketId格式错误:{}", ticketId, e);
        } catch (LlmCorpusException e) {
            throw e;
        } catch (Exception e) {
            log.error("邀请用户加入大象群异常, ticketId:{}, groupId:{}, userList:{}", ticketId, groupId, userList, e);
        }
    }

    /**
     * 请求头标准：https://km.sankuai.com/collabpage/137606233
     * 
     * @param username mis账号&操作人（有权限判断）
     * @return
     */
    public Map<String, String> getCommonHeaders(String username) {
        if (StringUtils.isBlank(username)) {
            throw new IllegalArgumentException("Username is blank");
        }
        Map<String, String> headers = new HashMap<>();
        headers.put("USERNAME", username);
        headers.put("Authorization", mtConfigService.getTicketBaAuth());
        headers.put("Content-Type", "application/json");
        return headers;
    }

    public String buildUrlWithParams(String baseUrl, Map<String, String> params) {
        if (params == null || params.isEmpty()) {
            return baseUrl;
        }

        StringBuilder urlBuilder = new StringBuilder(baseUrl);
        boolean isFirstParam = !baseUrl.contains("?");

        for (Map.Entry<String, String> entry : params.entrySet()) {
            if (isFirstParam) {
                urlBuilder.append("?");
                isFirstParam = false;
            } else {
                urlBuilder.append("&");
            }
            urlBuilder.append(entry.getKey()).append("=").append(entry.getValue());
        }

        return urlBuilder.toString();
    }

    /**
     * 查询范围工单信息收集
     *、
     * @param createdAtStart 创建开始时间戳
     * @param createdAtEnd 创建结束时间戳
     * @param state 工单状态，多个状态以逗号分隔
     * @return 工单查询结果JSON字符串
     */
    public String queryRangeTickets(Long rgId, Long createdAtStart, Long createdAtEnd, String state, String misId) {

        Map<String, Object> bodyMap = new HashMap<>();
        bodyMap.put("rgIds", Collections.singletonList(rgId));

        if (createdAtStart != null) {
            bodyMap.put("createdAtStart", createdAtStart);
        }
        if (createdAtEnd != null) {
            bodyMap.put("createdAtEnd", createdAtEnd);
        }
        
        // 处理工单状态参数，按逗号分隔转换为List
        if (StringUtils.isNotBlank(state)) {
            List<String> states = Arrays.stream(state.split(","))
                    .map(String::trim)
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.toList());
            if (!states.isEmpty()) {
                bodyMap.put("state", states);
            }
        }
        
        // 拼接URL参数
        Map<String, String> params = new HashMap<>();
        params.put("orderKind", "DESC");
        params.put("sn", mtConfigService.getTicketRangeQueryConfig().getSn());
        String urlWithParams = buildUrlWithParams(mtConfigService.getTtQueryDomain() + FILTER_QUERY_METHOD, params);

        return HttpUtil.doPost(urlWithParams, getCommonHeaders(misId), bodyMap);
    }

    /**
     * 获取工单范围详细信息
     *
     * @param rgId 组织ID
     * @param ttId 工单ID
     * @return 工单范围信息DTO
     */
    public TicketRangeDTO getTicketRangeInfo(Long rgId, String ttId, String misId) {
        if (Objects.isNull(ttId)) {
            throw new IllegalArgumentException("ttId is null");
        }

        TicketRangeDTO rangeDTO = new TicketRangeDTO();
        
        String urlWithTicketId = mtConfigService.getTtQueryDomain() + TICKET_INFO_QUERY_METHOD + ttId;
        String jsonString = HttpUtil.doGet(urlWithTicketId, getCommonHeaders(misId));
        
        // 解析响应结果
        JSONObject respObj = JSONObject.parseObject(jsonString);
        if (respObj == null || respObj.getInteger("code") != 200) {
            log.error("获取工单信息失败, ttId:{}, response:{}", ttId, jsonString);
            return rangeDTO;
        }
        
        JSONObject data = respObj.getJSONObject("data");
        if (data == null) {
            log.error("工单数据为空, ttId:{}", ttId);
            return rangeDTO;
        }
        
        // 判断是否是范围工单表单
        Integer customFormId = data.getInteger("customFormId");
        String configCustomFormIds = mtConfigService.getTicketRangeQueryConfig().getCustomFormId();
        
        // 将配置的表单ID分割为列表
        List<Integer> formIdList = Arrays.stream(configCustomFormIds.split(","))
                .map(String::trim)
                .filter(StringUtils::isNotBlank)
                .map(Integer::parseInt)
                .collect(Collectors.toList());
        
        // 判断当前工单是否属于配置的表单
        if (!formIdList.contains(customFormId)) {
            log.info("非范围工单表单, ttId:{}, customFormId:{}, configCustomFormIds:{}", ttId, customFormId, configCustomFormIds);
            return rangeDTO;
        }
        
        try {
            rangeDTO.setRgId(rgId);
            rangeDTO.setTicketId(ttId);

            Long createdAt = data.getLong("createdAt");
            if (createdAt != null) {
                rangeDTO.setDate(new Timestamp(createdAt));
            }

            Map<String, String> customFields = new HashMap<>();
            JSONArray customFieldValues = data.getJSONArray("customFieldValues");
            if (customFieldValues != null) {
                for (int i = 0; i < customFieldValues.size(); i++) {
                    JSONObject field = customFieldValues.getJSONObject(i);
                    String name = field.getString("name");
                    String value = field.getString("value");
                    customFields.put(name, value);
                }
            }

            String merchantId = customFields.get("商家ID");
            rangeDTO.setMerchantId(merchantId);

            String timeSlot = customFields.get("异常时段");
            rangeDTO.setTimeSlot(timeSlot);

            String reason = customFields.get("问题分类");
            rangeDTO.setReason(reason);

            String typeName = customFields.get("配送方式");
            rangeDTO.setTypeName(typeName);

            rangeDTO.setTypeId(deliveryTypeEnum.getTypeIdByName(typeName));
                     
        } catch (Exception e) {
            log.error("解析工单范围信息异常, ttId:{}, error:{}", ttId, e.getMessage(), e);
        }
        
        return rangeDTO;
    }

    /**
     * 根据模板配置获取工单信息
     *
     * @param ttId 工单ID
     * @param misId 用户MIS ID
     * @param templateConfig 模板配置
     * @return 动态工单DTO，如果工单不属于指定模板则返回null
     */
    public DynamicTemplateTicketDTO getTicketTemplateInfo(String ttId, String misId, TemplateConfig templateConfig) {
        if (Objects.isNull(ttId)) {
            throw new IllegalArgumentException("ttId is null");
        }
        if (Objects.isNull(templateConfig)) {
            throw new IllegalArgumentException("templateConfig is null");
        }

        String urlWithTicketId = mtConfigService.getTtQueryDomain() + TICKET_INFO_QUERY_METHOD + ttId;
        String jsonString = HttpUtil.doGet(urlWithTicketId, getCommonHeaders(misId));
        
        // 解析响应结果
        JSONObject respObj = JSONObject.parseObject(jsonString);
        if (respObj == null || respObj.getInteger("code") != 200) {
            log.error("获取工单信息失败, ttId:{}, response:{}", ttId, jsonString);
            return null;
        }
        
        JSONObject data = respObj.getJSONObject("data");
        if (data == null) {
            log.error("工单数据为空, ttId:{}", ttId);
            return null;
        }
        
        // 判断是否是指定的表单
        Integer customFormId = data.getInteger("customFormId");
        String templateId = templateConfig.getTemplateId();
        
        // 检查当前工单的表单ID是否与模板ID匹配
        if (StringUtils.isNotBlank(templateId) && !templateId.equals(customFormId.toString())) {
            log.info("工单不属于指定模板, ttId:{}, customFormId:{}, templateId:{}", 
                    ttId, customFormId, templateId);
            return null;
        }
        
        // 工单属于指定模板，创建DTO对象
        DynamicTemplateTicketDTO ticketDTO = new DynamicTemplateTicketDTO();
        ticketDTO.setTicketId(ttId);
        ticketDTO.setTemplateName(templateConfig.getTemplateName());
        ticketDTO.setTemplateId(templateConfig.getTemplateId());
        
        try {
            // 提取基本字段
            Long createdAt = data.getLong("createdAt");
            if (createdAt != null) {
                ticketDTO.addField("createdAt", new Timestamp(createdAt));
            }
            
            String title = data.getString("title");
            if (StringUtils.isNotBlank(title)) {
                ticketDTO.addField("title", title);
            }
            
            String status = data.getString("status");
            if (StringUtils.isNotBlank(status)) {
                ticketDTO.addField("status", status);
            }
            
            // 处理特殊字段：问题描述或描述字段
            String name = data.getString("name");
            if (StringUtils.isNotBlank(name)) {
                List<String> configFields = templateConfig.getFields();
                if (configFields != null) {
                    if (configFields.contains("问题描述")) {
                        ticketDTO.addField("问题描述", name);
                    }
                    if (configFields.contains("描述")) {
                        ticketDTO.addField("描述", name);
                    }
                }
            }
            
            // 提取自定义字段
            Map<String, String> customFields = new HashMap<>();
            JSONArray customFieldValues = data.getJSONArray("customFieldValues");
            if (customFieldValues != null) {
                for (int i = 0; i < customFieldValues.size(); i++) {
                    JSONObject field = customFieldValues.getJSONObject(i);
                    String fieldName = field.getString("name");
                    String fieldValue = field.getString("value");
                    customFields.put(fieldName, fieldValue);

                    if (StringUtils.isNotBlank(fieldName) && fieldValue != null) {
                        ticketDTO.addField(fieldName, fieldValue);
                    }
                }
            }
            
            // 根据模板配置的字段添加值
            List<String> configFields = templateConfig.getFields();
            if (configFields != null) {
                for (String field : configFields) {
                    if (!ticketDTO.hasField(field)) {
                        // 检查是否是data对象中的基本字段
                        if (data.containsKey(field)) {
                            Object value = data.get(field);
                            if (value != null) {
                                ticketDTO.addField(field, value);
                            }
                        }
                    }
                }
            }
            
        } catch (Exception e) {
            log.error("解析工单模板信息异常, ttId:{}, templateName:{}, error:{}", 
                    ttId, templateConfig.getTemplateName(), e.getMessage(), e);
        }
        
        return ticketDTO;
    }
}
