package com.meituan.banma.llm.corpus.server.common.domain.dto;

import lombok.Data;

/**
 * Class TicketRangeQueryConfig
 * Project banma_llm_corpus_process_server
 *
 * Description 工单范围查询配置
 */

@Data
public class TicketRangeQueryConfig {
    /**
     * 组织ID
     */
    private Long rgId;

    /**
     * 自定义表单ID
     */
    private String customFormId;
    
    /**
     * S3预签名URL过期时间（秒）
     * 默认为3600秒（1小时）
     */
    private Long s3UrlExpirationSeconds;
    
    /**
     * S3存储桶名称
     */
    private String s3BucketName;
    
    /**
     * S3 URL过期时间（分钟）
     */
    private Integer s3UrlExpirationMinutes;
    
    /**
     * S3应用KEY
     */
    private String s3AppKey;
    
    /**
     * S3服务端点
     */
    private String s3Endpoint;

    private String sn;
} 