package com.meituan.banma.llm.corpus.server.common.constants.enums;

import lombok.Getter;

@Getter
public enum ImGeneralMessageTypeEnum {

    RICH_TEXT(100, "富文本"),
    OLD_CARD(11, "旧卡片【已停止接入】"),
    CARD(12, "卡片"),
    ;

    private final Integer code;
    private final String desc;

    ImGeneralMessageTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ImGeneralMessageTypeEnum findByCode(Integer code) {
        for (ImGeneralMessageTypeEnum item : ImGeneralMessageTypeEnum.values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return null;
    }
}
