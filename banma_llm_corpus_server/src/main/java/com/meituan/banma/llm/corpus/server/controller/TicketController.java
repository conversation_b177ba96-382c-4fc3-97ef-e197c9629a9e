package com.meituan.banma.llm.corpus.server.controller;

import com.dianping.lion.shade.org.apache.curator.shaded.com.google.common.base.Preconditions;
import com.meituan.banma.llm.corpus.server.common.domain.dto.QueryTTInfoDTO;
import com.meituan.banma.llm.corpus.server.common.domain.dto.RgInfoDTO;
import com.meituan.banma.llm.corpus.server.service.ITicketQueryService;
import com.meituan.banma.llm.corpus.server.common.CommonResult;
import com.sankuai.meituan.auth.util.UserUtils;
import com.sankuai.meituan.auth.vo.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.util.CollectionUtils;
import com.meituan.banma.llm.corpus.api.exception.LlmCorpusException;
import com.meituan.banma.llm.corpus.server.common.domain.dto.BatchAddTTContentResultDTO;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/12/4
 * 页面选择TT触发 相关接口
 */
@Controller
@Slf4j
@RequestMapping("/ticket")
@ResponseBody
public class TicketController {

    @Resource
    private ITicketQueryService ticketQueryService;

    /**
     * 根据当前用户查询用户所有值班组
     * @return List<DutyGroupDTO> 值班组列表
     */
    @RequestMapping("/queryRGList")
    public Object queryRGList(@RequestParam(value = "pageNum", defaultValue = "1") int pageNum,
                              @RequestParam(value = "pageSize", defaultValue = "20") int pageSize) {
        try {
            User user = UserUtils.getUser();
            List<RgInfoDTO> dutyGroupDTOList = ticketQueryService.queryRGList(user.getLogin(), pageNum, pageSize);
            return CommonResult.success("success", dutyGroupDTOList);
        } catch (Exception e) {
            log.error("queryRGList# 执行失败, 异常原因:{}", e);
            return CommonResult.error((String.format("执行失败,异常原因=%s", e.getMessage())));
        }
    }

    /**
     * 根据值班组查询 tt 列表（无筛选条件、全量）
     * @return PageDTO<TicketDetailDTO> tt列表
     */
    @RequestMapping("/queryTTListByRgIdALL")
    public Object queryTTListByRgIdALL(@RequestParam(value = "misId", defaultValue = "0") String misId,
                                    @RequestParam(value = "rgId", defaultValue = "0")  long rgId,
                                    @RequestParam(value = "pageNum", defaultValue = "1") int pageNum,
                                    @RequestParam(value = "pageSize", defaultValue = "100") int pageSize,
                                    @RequestParam(value = "createdAtStart", required = false) Long createdAtStart,
                                    @RequestParam(value = "createdAtEnd", required = false) Long createdAtEnd) {
        try {
            Preconditions.checkArgument(rgId > 0, "rgId 必须大于0");
            QueryTTInfoDTO ttInfoDTO = ticketQueryService.queryTTListByRgIdALL(misId, rgId, pageNum, pageSize, createdAtStart, createdAtEnd);
            // 封装页数进去
            Map<String, Object> result = new HashMap<>();
            result.put("currentPage", pageNum);
            result.put("pageSize", pageSize);
            result.put("total", ttInfoDTO.getTotalNum());
            result.put("data", ttInfoDTO.getTicketDetailDTOList());
            return CommonResult.success("success", result);
        } catch (Exception e) {
            log.error("queryTTListByRgIdALL# 执行失败, rgId:{}, 异常原因:{}", rgId, e);
            return CommonResult.error((String.format("执行失败,异常原因=%s", e.getMessage())));
        }
    }

    /**
     * 根据值班组查询 tt 列表（附加筛选条件：TT状态为已解决/已关闭 or 未创建大象群）
     * @return PageDTO<TicketDetailDTO> tt列表
     */
    @RequestMapping("/queryTTListByRgId")
    public Object queryTTListByRgId(@RequestParam(value = "misId", defaultValue = "0") String misId,
                                    @RequestParam(value = "rgId", defaultValue = "0")  long rgId,
                                    @RequestParam(value = "pageNum", defaultValue = "1") int pageNum,
                                    @RequestParam(value = "pageSize", defaultValue = "100") int pageSize,
                                    @RequestParam(value = "createdAtStart", required = false) Long createdAtStart,
                                    @RequestParam(value = "createdAtEnd", required = false) Long createdAtEnd) {
        try {
            Preconditions.checkArgument(rgId > 0, "rgId 必须大于0");
            QueryTTInfoDTO ttInfoDTO = ticketQueryService.queryTTListByRgId(misId, rgId, pageNum, pageSize, createdAtStart, createdAtEnd);
            // 封装页数进去
            Map<String, Object> result = new HashMap<>();
            result.put("currentPage", pageNum);
            result.put("pageSize", pageSize);
            result.put("total", ttInfoDTO.getTotalNum());
            result.put("data", ttInfoDTO.getTicketDetailDTOList());
            return CommonResult.success("success", result);
        } catch (Exception e) {
            log.error("queryTTListByGroupId# 执行失败, rgId:{}, 异常原因:{}", rgId, e);
            return CommonResult.error((String.format("执行失败,异常原因=%s", e.getMessage())));
        }
    }

    /**
     * 根据TT获取对应大象群ID
     * @return String 大象群ID
     */
    @Deprecated
    @RequestMapping("/queryDxGroupIdByTT")
    public Object queryDxGroupIdByTT(@RequestParam(value = "misId", defaultValue = "0") String misId,
                                     @RequestParam(value = "ticketId", defaultValue = "0") String ticketId) {
        try {
            Preconditions.checkArgument(StringUtils.isNotBlank(ticketId), "ticketId 不能为空");
            Long dxGroupId = ticketQueryService.getDxGroupIdByTicketId(misId, ticketId);
            return CommonResult.success("success", dxGroupId);
        } catch (Exception e) {
            log.error("queryDxGroupIdByTT# 执行失败, ticketId:{}, 异常原因:{}", ticketId, e);
            return CommonResult.error((String.format("执行失败,异常原因=%s", e.getMessage())));
        }
    }

    /**
     * 根据TTid添加指定TT内容
     * web端手动添加入口
     */
    @RequestMapping("/addTTContentByTTId")
    public Object addTTContentByTTId(@RequestParam(value = "empId", defaultValue = "0") long empId,
                                     @RequestParam(value = "misId", defaultValue = "0") String misId,
                                     @RequestParam(value = "ticketId", defaultValue = "0") String ticketId) {
        try {
            Preconditions.checkArgument(StringUtils.isNotBlank(ticketId), "ticketId 不能为空");
            String taskId = ticketQueryService.addTTContentByTTId(empId, misId, ticketId);
            return CommonResult.success("success", taskId);
        } catch (Exception e) {
            log.error("addTTContentByTTId# 执行失败, ticketId:{}, 异常原因:{}", ticketId, e);
            return CommonResult.error((String.format("执行失败,异常原因=%s", e.getMessage())));
        }
    }

    /**
     * 批量根据TTid添加指定TT内容
     * web端批量手动添加入口
     */
    @RequestMapping("/batchAddTTContentByTTIds")
    public Object batchAddTTContentByTTIds(@RequestParam(value = "empId", defaultValue = "0") long empId,
                                           @RequestParam(value = "misId", defaultValue = "0") String misId,
                                           @RequestParam(value = "ticketIds") List<String> ticketIds) {
        try {
            Preconditions.checkArgument(!CollectionUtils.isEmpty(ticketIds), "ticketIds 不能为空");

            BatchAddTTContentResultDTO result = ticketQueryService.batchAddTTContentByTTIds(empId, misId, ticketIds);
            return CommonResult.success(result);
        } catch (LlmCorpusException e) {
            log.error("batchAddTTContentByTTIds# 执行失败, ticketIds:{}, 异常原因:{}", ticketIds, e.getMessage());
            return CommonResult.error(String.format("执行失败,异常原因=%s", e.getMessage()));
        } catch (Exception e) {
            log.error("batchAddTTContentByTTIds# 执行失败, ticketIds:{}, 异常原因:{}", ticketIds, e);
            return CommonResult.error(String.format("执行失败,异常原因=%s", e.getMessage()));
        }
    }
}
