package com.meituan.banma.llm.corpus.server.common.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * Represents an embedding returned by the embedding api
 * <a href="https://platform.openai.com/docs/api-reference/classifications/create">api-reference<a/>
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class FridayEmbeddingResDTO {
    /**
     * The position of this embedding in the list
     */
    private Integer index;
    /**
     * The embedding vector
     */
    private List<Double> embedding;
    /**
     * The type of object returned, should be "embedding"
     */
    private String object;
}
