package com.meituan.banma.llm.corpus.server.dal.mapper;

import com.meituan.banma.llm.corpus.server.dal.entity.RgDatasetDocumentEntity;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

@Mapper
public interface RgDatasetDocumentMapper {

    // 根据rgId查询记录，只查询status=0（正常）的记录
    @Select("SELECT * FROM rg_dataset_document WHERE rg_id = #{rgId} AND status = 0 ORDER BY timestamp DESC")
    List<RgDatasetDocumentEntity> findByRgId(@Param("rgId") Long rgId);
    
    // 根据rgId和spaceId查询记录，只查询status=0（正常）的记录
    @Select("SELECT * FROM rg_dataset_document WHERE rg_id = #{rgId} AND space_id = #{spaceId} AND status = 0 LIMIT 1")
    RgDatasetDocumentEntity findByRgIdAndSpaceId(@Param("rgId") Long rgId, @Param("spaceId") String spaceId);
    
    // 根据rgId和spaceId查询记录，包括status=1（已删除）的记录
    @Select("SELECT * FROM rg_dataset_document WHERE rg_id = #{rgId} AND space_id = #{spaceId} LIMIT 1")
    RgDatasetDocumentEntity findByRgIdAndSpaceIdIncludeDeleted(@Param("rgId") Long rgId, @Param("spaceId") String spaceId);

    // 插入新的记录，默认status为0
    @Insert("INSERT INTO rg_dataset_document (rg_id, space_id, space_name, dataset_id, document_id, timestamp, access_key, app_secret, status) VALUES (#{rgId}, #{spaceId}, #{spaceName}, #{datasetId}, #{documentId}, #{timestamp}, #{accessKey}, #{appSecret}, 0)")
    void insertRgDatasetDocumentAndSpaceId(RgDatasetDocumentEntity rgDatasetDocumentEntity);

    // 更新现有记录
    @Update("UPDATE rg_dataset_document SET dataset_id = #{entity.datasetId}, document_id = #{entity.documentId}, timestamp = NOW(), access_key = #{entity.accessKey}, app_secret = #{entity.appSecret}, status = #{entity.status} WHERE rg_id = #{entity.rgId} AND space_id = #{entity.spaceId}")
    void updateAndSpaceId(@Param("entity") RgDatasetDocumentEntity entity);

    // 虚拟删除记录：将status更新为1，而不是实际删除
    @Update("UPDATE rg_dataset_document SET status = 1 WHERE rg_id = #{rgId} AND space_id = #{spaceId} AND status = 0")
    void deleteByRgIdAndSpaceId(@Param("rgId") Long rgId, @Param("spaceId") String spaceId);
    
    // 恢复已删除的记录：将status更新为0
    @Update("UPDATE rg_dataset_document SET status = 0 WHERE rg_id = #{rgId} AND space_id = #{spaceId} AND status = 1")
    void restoreByRgIdAndSpaceId(@Param("rgId") Long rgId, @Param("spaceId") String spaceId);
    
    // 恢复已删除的记录：将status更新为0，同时更新spaceName
    @Update("UPDATE rg_dataset_document SET status = 0, space_name = #{spaceName} WHERE rg_id = #{rgId} AND space_id = #{spaceId} AND status = 1")
    void restoreByRgIdAndSpaceIdWithName(@Param("rgId") Long rgId, @Param("spaceId") String spaceId, @Param("spaceName") String spaceName);

    /**
     * 查询所有不重复的Friday空间信息
     *
     * @return 所有空间的基本信息列表
     */
    @Select("SELECT DISTINCT space_id, space_name, MIN(timestamp) as timestamp " +
            "FROM rg_dataset_document " +
            "WHERE status = 0 AND space_id IS NOT NULL " +
            "GROUP BY space_id, space_name " +
            "ORDER BY timestamp ASC")
    List<RgDatasetDocumentEntity> findAllDistinctSpaces();

    /**
     * 根据spaceId查询绑定的所有rgId
     *
     * @param spaceId Friday空间ID
     * @return 绑定该空间的rgId列表
     */
    @Select("SELECT DISTINCT rg_id FROM rg_dataset_document " +
            "WHERE space_id = #{spaceId} AND status = 0 " +
            "ORDER BY rg_id")
    List<Long> findRgIdsBySpaceId(@Param("spaceId") String spaceId);

    // 清空document_id字段，将其设置为null
    @Update("UPDATE rg_dataset_document SET document_id = NULL, timestamp = NOW() WHERE rg_id = #{rgId} AND space_id = #{spaceId} AND status = 0")
    void clearDocumentIdByRgIdAndSpaceId(@Param("rgId") Long rgId, @Param("spaceId") String spaceId);

    // 查询所有状态正常的记录
    @Select("SELECT * FROM rg_dataset_document ORDER BY rg_id, timestamp DESC")
    List<RgDatasetDocumentEntity> findAll();
}

