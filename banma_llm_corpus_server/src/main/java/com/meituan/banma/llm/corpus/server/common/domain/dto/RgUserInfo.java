package com.meituan.banma.llm.corpus.server.common.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class RgUserInfo {
    private String orgName;
    private String role;
    private String identify;
    private Integer rgId;
    private String displayName;
    private String i18nDisplayName;
    private Boolean active;
    private String type;
    private Long endAt;
    private String buName;
    private Integer oncallId;
    private Long createdAt;
    private Integer sequence;
    private Boolean external;
    private Boolean isOncall;
    private Integer id;
    private String bgName;
    private Long startAt;
}
