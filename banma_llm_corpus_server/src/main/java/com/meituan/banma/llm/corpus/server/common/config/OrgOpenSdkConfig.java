package com.meituan.banma.llm.corpus.server.common.config;

import com.meituan.banma.llm.corpus.server.common.MtConfigService;
import com.sankuai.meituan.org.opensdk.client.RemoteServiceClient;
import com.sankuai.meituan.org.opensdk.service.CompService;
import com.sankuai.meituan.org.opensdk.service.DictService;
import com.sankuai.meituan.org.opensdk.service.EmpService;
import com.sankuai.meituan.org.opensdk.service.JobCodeService;
import com.sankuai.meituan.org.opensdk.service.OrgService;
import com.sankuai.meituan.org.opensdk.service.SiteCodeService;
import com.sankuai.meituan.org.opensdk.service.impl.CompServiceImpl;
import com.sankuai.meituan.org.opensdk.service.impl.DictServiceImpl;
import com.sankuai.meituan.org.opensdk.service.impl.EmpServiceImpl;
import com.sankuai.meituan.org.opensdk.service.impl.JobCodeServiceImpl;
import com.sankuai.meituan.org.opensdk.service.impl.OrgServiceImpl;
import com.sankuai.meituan.org.opensdk.service.impl.SiteCodeServiceImpl;
import com.sankuai.meituan.org.queryservice.domain.param.DataScope;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Arrays;

@Configuration
public class OrgOpenSdkConfig {
    @Autowired
    private MtConfigService mtConfigService;
    private final static String REMOTE_APP_KEY = "com.sankuai.hrmdm.org.gateway";
    //不再使用，默认空字符即可。
    private final static String APP_SECRET = "";
    private final static Integer APP_TENANT_ID = 1;//如需查询其它租户数据，请设置成其它租户ID
    private final static String APP_SOURCE = "MT";//设置租户下对应的source.如果设置为"ALL"，则查tenantId下所有sources。


    @Bean
    public RemoteServiceClient remoteServiceClient() throws Exception {
        String clientAppkey = mtConfigService.getOrgAppKey();

        // 设置App默认的数据访问范围。如下设置，App默认所有的请求是针对美团租户下“MT”数据域的ORG数据
        DataScope dataScope = new DataScope();
        dataScope.setTenantId(APP_TENANT_ID);
        dataScope.setSources(Arrays.asList(APP_SOURCE));

        RemoteServiceClient remoteServiceClient = new RemoteServiceClient(clientAppkey, APP_SECRET, REMOTE_APP_KEY, dataScope);
        return remoteServiceClient;
    }

    @Bean
    public CompService compService(RemoteServiceClient remoteServiceClient) {
        CompServiceImpl compService = new CompServiceImpl(remoteServiceClient);
        return compService;
    }

    @Bean
    public DictService dictService(RemoteServiceClient remoteServiceClient) {
        DictServiceImpl dictService = new DictServiceImpl(remoteServiceClient);
        return dictService;
    }

    @Bean
    public EmpService empService(RemoteServiceClient remoteServiceClient) {
        EmpServiceImpl empService = new EmpServiceImpl(remoteServiceClient);
        return empService;
    }

    @Bean
    public JobCodeService jobCodeService(RemoteServiceClient remoteServiceClient) {
        JobCodeServiceImpl jobCodeService = new JobCodeServiceImpl(remoteServiceClient);
        return jobCodeService;
    }

    @Bean
    public OrgService orgService(RemoteServiceClient remoteServiceClient) {
        OrgServiceImpl orgService;
        orgService = new OrgServiceImpl(remoteServiceClient);
        return orgService;
    }

    @Bean
    public SiteCodeService siteCodeService(RemoteServiceClient remoteServiceClient) {
        SiteCodeServiceImpl siteCodeService = new SiteCodeServiceImpl(remoteServiceClient);
        return siteCodeService;
    }
}
