package com.meituan.banma.llm.corpus.server.common.domain.dto.stat;

import com.meituan.banma.llm.corpus.server.common.constants.enums.ClusterValidationMethod;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * K-means聚类算法的配置参数
 */
@Data
@Builder
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class KMeansConfig {
    /**
     * 聚类簇的数量，默认为0
     * 当k <= 0时，将自动使用肘部法则(Elbow Method)确定最佳K值
     * 当k > 0时，使用指定的K值
     */
    private Integer k = 0;
    
    /**
     * 最大迭代次数，默认为100
     */
    private Integer maxIterations = 100;
    
    /**
     * 收敛阈值，如果簇中心点移动距离小于该值则认为已收敛，默认为0.001
     */
    private Double convergenceThreshold = 0.001;
    
    /**
     * 向量维度，用于表示文本的向量维度，默认为768（对应BERT的特征维度）
     */
    private Integer vectorDimension = 768;
    
    /**
     * 随机种子，用于初始化簇中心点，默认为42
     */
    private Long randomSeed = 42L;
    
    /**
     * 是否使用KMeans++算法初始化簇中心点，默认为true
     */
    private Boolean useKMeansPlusPlus = true;
    
    /**
     * 重复运行次数，从多次运行中选择最佳结果，默认为3
     */
    private Integer numRuns = 3;
    
    /**
     * 是否允许极小簇存在，默认为true
     * 当设置为true时，即使簇中只有一个或极少数据点也会保留该簇
     * 当设置为false时，会尝试减少K值以避免极小簇
     */
    private Boolean allowSmallClusters = true;
    
    /**
     * 聚类有效性评估方法，默认为SILHOUETTE
     * 可选值：
     * - SILHOUETTE (轮廓系数法)：
     *   特性：衡量样本与自身所在簇的相似度与其他簇的差异度
     *   适用：形状规则、密度均匀的球状簇，适合大多数常见数据场景
     *   评估：值越大越好，范围[-1,1]，通常>0.5表示合理的聚类结构
     *   优势：直观、计算简单，对簇形状和密度不均匀有一定容忍度
     * 
     * - DB_INDEX (Davies-Bouldin指数法)：
     *   特性：评估簇内分散度与簇间距离的比率
     *   适用：分离良好的簇，当簇间边界明显时效果最佳
     *   评估：值越小越好，越接近0表示簇分离得越好
     *   优势：对噪音数据不敏感，能够识别紧凑且分离良好的簇
     * 
     * - CH_INDEX (Calinski-Harabasz指数法)：
     *   特性：方差比准则，评估簇间离散度与簇内离散度的比率
     *   适用：高密度、球状且大小相近的簇，适合高维数据
     *   评估：值越大越好，没有固定上限
     *   优势：计算快速，特别适合大规模数据集的聚类评估
     * 
     * - GAP_STATISTIC (Gap统计量法)：
     *   特性：比较观察数据与随机参考分布的聚类分散度差异
     *   适用：非球形、不规则形状簇，当簇数量不明确时效果最佳
     *   评估：寻找最大正Gap值或满足特定条件的最小k值
     *   优势：对真实数据结构敏感性高，能发现自然聚类结构
     * 
     * - COMBINED (组合评估法)：
     *   特性：结合轮廓系数和DB指数的评估结果
     *   适用：需要平衡多种指标评估的场景，减少单一指标可能带来的偏差
     *   评估：通过计算排名平均值选择最优K值
     *   优势：比单一指标更稳健，能够处理多种簇形状和密度的混合数据
     * 
     * - COMPREHENSIVE (综合评估法)：
     *   特性：采用投票机制，集成四种评估方法的结果
     *   适用：复杂、多样化的数据集，需要保守稳健的结果时
     *   评估：根据多种方法投票选出最佳K值
     *   优势：最大程度降低误判风险，提供最稳健可靠的聚类数量估计
     */
    private ClusterValidationMethod clusterValidationMethod = ClusterValidationMethod.SILHOUETTE;
    
    /**
     * Gap统计量计算时的参考数据集数量，默认为5
     * 仅在使用GAP_STATISTIC方法时有效
     */
    private Integer gapStatisticReferenceDatasets = 5;
} 