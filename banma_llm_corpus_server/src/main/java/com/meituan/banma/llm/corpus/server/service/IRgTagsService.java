package com.meituan.banma.llm.corpus.server.service;

import com.meituan.banma.llm.corpus.server.dal.entity.RgTagsEntity;

import java.util.List;
import java.util.Map;

/**
 * 语料标签服务接口
 * 提供标签的增删改查功能
 */
public interface IRgTagsService {

    /**
     * 新增标签
     * 
     * @param rgId 值班组ID
     * @param tagName 标签名
     * @param tagDesc 标签描述
     * @param misId 操作人misID
     * @return 创建的标签实体，失败返回null
     */
    RgTagsEntity addTag(Long rgId, String tagName, String tagDesc, String misId);

    /**
     * 根据id删除标签
     * 
     * @param id 标签ID
     * @return 删除结果，true表示成功，false表示失败
     */
    boolean deleteTagById(Long id);

    /**
     * 根据rgId和tagName删除标签
     * 
     * @param rgId 值班组ID
     * @param tagName 标签名
     * @return 删除结果，true表示成功，false表示失败
     */
    boolean deleteTagByRgIdAndTagName(Long rgId, String tagName);

    /**
     * 修改标签描述（仅修改描述）
     * 
     * @param id 标签ID
     * @param tagDesc 新的标签描述
     * @param misId 操作人misID
     * @return 修改结果，true表示成功，false表示失败
     */
    boolean updateTagDesc(Long id, String tagDesc, String misId);

    /**
     * 根据rgId查询标签列表
     * 
     * @param rgId 值班组ID
     * @return 标签列表
     */
    List<RgTagsEntity> getTagsByRgId(Long rgId);

    /**
     * 根据ids批量查询标签列表
     * 
     * @param ids 标签ID列表
     * @return 标签列表
     */
    List<RgTagsEntity> getTagsByIds(List<Long> ids);

    /**
     * 根据rgId查询该值班组下的标签和对应的描述
     * 
     * @param rgId 值班组ID
     * @return Map，key为标签名，value为标签描述
     */
    Map<String, String> getTagsMapByRgId(Long rgId);
} 