package com.meituan.banma.llm.corpus.server.common.domain.dto;
import lombok.Data;

import java.sql.Timestamp;
import java.util.List;

@Data
public class KnowledgeSimilarityRecordWithScore {

    // TT ID
    private String ticketId;

    // 问题标题
    private String title;

    // 知识库内容
    private String content;

    // 问题类型
    private Integer type;

    // 来源
    private Integer source;

    //操作人misId
    private String misId;

    //创建时间
    private Timestamp createTime;

    //更新时间
    private Timestamp updateTime;

    // 检索得到的得分
    private double score;

    // 标签名称列表
    private List<String> tagsname;

    // 背景知识
    private String backgroundKnowledge;

    // SOP
    private String sop;

    // Rule
    private String rule;
}
