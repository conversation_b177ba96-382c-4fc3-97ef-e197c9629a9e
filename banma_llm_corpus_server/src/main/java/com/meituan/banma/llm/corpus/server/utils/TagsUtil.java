package com.meituan.banma.llm.corpus.server.utils;

import com.meituan.banma.llm.corpus.server.common.MtConfigService;
import com.meituan.banma.llm.corpus.server.dal.entity.RgTagsEntity;
import com.meituan.banma.llm.corpus.server.dal.mapper.RgTagsMapper;
import com.meituan.banma.llm.corpus.server.service.IRgTagsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 标签处理工具类
 */
@Slf4j
@Component
public class TagsUtil {

    @Autowired
    private MtConfigService mtConfigService;
    
    @Autowired
    private RgTagsMapper rgTagsMapper;
    
    @Autowired
    private IRgTagsService rgTagsService;

    /**
     * 处理标签ID，如果为空则使用默认标签
     * @param tagsIds 原始标签ID字符串
     * @return 处理后的标签ID字符串
     */
    public String processTagsIdsWithDefault(String tagsIds) {
        // 如果tagsIds不为空，直接返回
        if (StringUtils.isNotBlank(tagsIds)) {
            return tagsIds;
        }
        
        try {
            // 获取默认标签的rgId
            Long defaultTagRgId = mtConfigService.getDefaultTagRgId();
            
            // 查询默认标签
            List<RgTagsEntity> defaultTags = rgTagsMapper.findByRgId(defaultTagRgId);
            
            if (CollectionUtils.isEmpty(defaultTags)) {
                log.warn("未找到默认标签, defaultTagRgId: {}", defaultTagRgId);
                return "";
            }
            
            // 将默认标签ID转换为逗号分隔的字符串
            List<String> defaultTagIds = defaultTags.stream()
                    .map(tag -> tag.getId().toString())
                    .collect(Collectors.toList());
            
            String result = String.join(",", defaultTagIds);
            log.info("使用默认标签ID: {}, defaultTagRgId: {}", result, defaultTagRgId);
            return result;
        } catch (Exception e) {
            log.error("获取默认标签失败, 异常: {}", e.getMessage(), e);
            return "";
        }
    }
    
    /**
     * 根据标签ID字符串获取标签名称列表
     * @param tagsIds 标签ID字符串，格式如"1,2,3"
     * @return 标签名称列表
     */
    public List<String> getTagsNamesByIds(String tagsIds) {
        if (StringUtils.isBlank(tagsIds)) {
            return new ArrayList<>();
        }
        
        try {
            String[] idArray = tagsIds.split(",");
            List<Long> idList = new ArrayList<>();
            
            // 将字符串ID转换为Long类型
            for (String idStr : idArray) {
                if (StringUtils.isNotBlank(idStr.trim())) {
                    idList.add(Long.parseLong(idStr.trim()));
                }
            }
            
            if (idList.isEmpty()) {
                return new ArrayList<>();
            }
            
            // 批量查询标签信息
            List<RgTagsEntity> tags = rgTagsService.getTagsByIds(idList);
            if (CollectionUtils.isEmpty(tags)) {
                return new ArrayList<>();
            }
            
            // 提取标签名称并返回列表
            return tags.stream()
                    .map(RgTagsEntity::getTagName)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.warn("解析标签ID失败, tagsIds: {}, 异常: {}", tagsIds, e.getMessage());
            return new ArrayList<>();
        }
    }
} 