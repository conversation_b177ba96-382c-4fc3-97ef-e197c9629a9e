package com.meituan.banma.llm.corpus.server.common.constants.enums;


import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum CorpusStatusEnum  {
    /**
     * 语料状态
     */
    VALID(1, "有效"),
    MERGED(2, "已合并"),
    REMOVED(3, "已删除"),
    MARKED_AS_IGNORE(4, "已标注为无需入库")
    ;

    private final Integer code;
    private final String desc;

    public static CorpusStatusEnum fromCode(int code){
        for (CorpusStatusEnum item: CorpusStatusEnum.values()){
            if (code == item.getCode()){
                return item;
            }
        }
        return null;
    }
}
