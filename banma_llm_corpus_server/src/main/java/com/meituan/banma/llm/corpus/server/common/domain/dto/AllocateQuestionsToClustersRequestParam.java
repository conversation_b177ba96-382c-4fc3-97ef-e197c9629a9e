package com.meituan.banma.llm.corpus.server.common.domain.dto;

import com.meituan.banma.llm.corpus.server.common.domain.dto.stat.AppStatusDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AllocateQuestionsToClustersRequestParam {
    private List<ClusterTypeDefinition> clusterTypeDefinitions;
    private List<AppStatusDTO.Question> questions;

    @Data
    public static class ClusterTypeDefinition {
        private Integer clusterId;
        private String clusterType;
    }
}
