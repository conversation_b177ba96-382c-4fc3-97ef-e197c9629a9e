package com.meituan.banma.llm.corpus.server.common.domain.dto.stat;

import lombok.Data;

/**
 * 机器人聊天消息统计项
 */
@Data
public class CorpusBotMessageItem {
    /**
     * 消息ID
     */
    private String msgId;
    
    /**
     * 发送者用户ID
     */
    private String fromUid;
    
    /**
     * 发送者公众号ID
     */
    private String fromPubId;
    
    /**
     * 群组ID
     */
    private String gid;
    
    /**
     * 创建时间戳
     */
    private long cts;
    
    /**
     * 消息类型
     */
    private int type;
    
    /**
     * 消息内容
     */
    private String message;
    
    /**
     * 发送者名称
     */
    private String fromName;
    
    /**
     * 消息扩展内容
     */
    private String msgExt;
    
    /**
     * 发送者MIS账号
     */
    private String fromMis;

    private String userOrgId;

    private String userOrgName;
    
    private String userOrgPath;

}
