package com.meituan.banma.llm.corpus.server.controller;

import com.alibaba.fastjson.JSON;
import com.meituan.banma.llm.corpus.api.exception.LlmCorpusException;
import com.meituan.banma.llm.corpus.server.common.CommonResult;
import com.meituan.banma.llm.corpus.server.common.MtConfigService;
import com.meituan.banma.llm.corpus.server.common.domain.dto.GrayscaleConfigDTO;
import com.meituan.banma.llm.corpus.server.common.interceptor.MisIdInterceptor;
import com.meituan.banma.llm.corpus.server.rpc.dx.XmAuthRpcService;
import com.meituan.banma.llm.corpus.server.service.IAccessKeyService;
import com.meituan.banma.llm.corpus.server.service.IDxGroupChatService;
import com.meituan.banma.llm.corpus.server.utils.TicketQueryUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * API测试控制器
 * 提供开发测试用的API接口
 * 注意：生产环境应该禁用这些接口
 */
@Slf4j
@RestController
@RequestMapping("/api/test")
public class ApiTestController {

    @Autowired
    private XmAuthRpcService xmAuthRpcService;

    @Autowired
    private TicketQueryUtil ticketQueryUtil;

    @Autowired
    private IDxGroupChatService dxGroupChatService;

    @Resource
    private MtConfigService mtConfigService;
    
    @Autowired
    private IAccessKeyService accessKeyService;

    /**
     * 测试获取SSO Token
     * 调用XmAuthRpcService的getSsoidToken方法，返回获取的token
     *
     * @return 包含token和状态的响应
     */
    @GetMapping("/sso-token")
    public Map<String, Object> testGetSsoidToken() {
        Map<String, Object> result = new HashMap<>();

        try {
            long startTime = System.currentTimeMillis();
            String token = xmAuthRpcService.getDxSsoidToken();
            long endTime = System.currentTimeMillis();

            result.put("success", true);
            result.put("token", token);
            result.put("timeMs", (endTime - startTime));

            // 添加token长度信息
            if (token != null) {
                result.put("tokenLength", token.length());
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("error", e.getMessage());
            result.put("errorType", e.getClass().getName());
        }

        return result;
    }

    /**
     * 测试邀请用户加入大象群
     * 调用TicketQueryUtil的inviteUsersToDxGroup方法，返回邀请结果
     *
     * @param ticketId 工单ID
     * @param roomId 群组ID
     * @param userList 用户列表，多个用户用逗号分隔
     * @return 包含邀请结果和状态的响应
     */
    @RequestMapping(value = "/invite-users", method = {RequestMethod.GET, RequestMethod.POST})
    public Map<String, Object> testInviteUsersToDxGroup(
            @RequestParam("misId") String misId,
            @RequestParam("ticketId") String ticketId,
            @RequestParam("roomId") Long roomId,
            @RequestParam("userList") String userList) {

        Map<String, Object> result = new HashMap<>();

        try {
            // 记录开始时间
            long startTime = System.currentTimeMillis();

            // 解析用户列表
            List<String> users = Arrays.asList(userList.split(","));

            // 调用邀请方法
            ticketQueryUtil.inviteUsersToDxGroup(misId, ticketId, roomId, users);

            // 记录结束时间
            long endTime = System.currentTimeMillis();

            // 组装返回结果
            result.put("success", true);
            result.put("ticketId", ticketId);
            result.put("roomId", roomId);
            result.put("userList", users);
            result.put("timeMs", (endTime - startTime));

        } catch (Exception e) {
            result.put("success", false);
            result.put("error", e.getMessage());
            result.put("errorType", e.getClass().getName());
        }

        return result;
    }

    /**
     * 测试检查指定用户是否在群组中
     * 调用DxGroupChatServiceImpl的isUserInGroup方法，返回检查结果
     *
     * @param groupId 群组ID
     * @param empId 员工ID
     * @return 包含检查结果和状态的响应
     */
    @GetMapping("/is-user-in-group")
    public Map<String, Object> testIsUserInGroup(
            @RequestParam("groupId") Long groupId,
            @RequestParam("empId") Long empId) {
        Map<String, Object> result = new HashMap<>();
        result.put("groupId", groupId);
        result.put("empId", empId);

        try {
            // 记录开始时间
            long startTime = System.currentTimeMillis();

            // 调用检查方法
            boolean isInGroup = dxGroupChatService.isUserInGroup(groupId, empId);

            // 记录结束时间
            long endTime = System.currentTimeMillis();

            // 组装返回结果
            result.put("success", true);
            result.put("isInGroup", isInGroup);
            result.put("timeMs", (endTime - startTime));

        } catch (LlmCorpusException e) {
            // 特殊处理业务异常
            result.put("success", false);
            result.put("error", e.getMessage());
            result.put("errorType", "业务异常");
            result.put("errorCode", e.getCode());
            result.put("solution", "请先将机器人拉入群聊后再试");
            log.error("业务异常: groupId={}, empId={}, errorCode={}, errorMsg={}", 
                    groupId, empId, e.getCode(), e.getMessage(), e);
        }

        return result;
    }

    @GetMapping("/sso-token-km")
    public Map<String, Object> testGetKmSsoidToken() {
        Map<String, Object> result = new HashMap<>();

        try {
            long startTime = System.currentTimeMillis();
            String token = xmAuthRpcService.getKmSsoidToken();
            long endTime = System.currentTimeMillis();

            result.put("success", true);
            result.put("token", token);
            result.put("timeMs", (endTime - startTime));

            // 添加token长度信息
            if (token != null) {
                result.put("tokenLength", token.length());
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("error", e.getMessage());
            result.put("errorType", e.getClass().getName());
        }


        return result;
    }

    /**
     * 测试添加机器人到群组
     *
     * @param botId 机器人ID
     * @param groupId 群组ID
     * @return 包含添加结果和状态的响应
     */
    @GetMapping("/add-bot-to-group")
    public Map<String, Object> testAddBotToGroup(
            @RequestParam("botId") Long botId,
            @RequestParam("groupId") Long groupId) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 记录开始时间
            long startTime = System.currentTimeMillis();

            // 调用添加机器人方法
            dxGroupChatService.addBotToGroup(botId, groupId);

            // 记录结束时间
            long endTime = System.currentTimeMillis();

            // 组装返回结果
            result.put("success", true);
            result.put("botId", botId);
            result.put("groupId", groupId);
            result.put("timeMs", (endTime - startTime));

        } catch (Exception e) {
            result.put("success", false);
            result.put("error", e.getMessage());
            result.put("errorType", e.getClass().getName());
            result.put("botId", botId);
            result.put("groupId", groupId);
        }

        return result;
    }

    /**
     * 获取灰度配置
     * 该接口从配置中心获取灰度配置信息，转换结构后返回给前端
     *
     * @return 灰度配置信息
     */
    @GetMapping("/grayscale-config")
    public Object getGrayscaleConfig() {
        try {
            // 获取配置中心的灰度配置JSON
            String grayscaleConfigJson = mtConfigService.getGrayscaleConfig();
            
            // 解析JSON并转换结构
            Map<String, Object> configMap = new HashMap<>();
            if (!StringUtils.isEmpty(grayscaleConfigJson)) {
                // 将JSON字符串解析为对象
                GrayscaleConfigDTO configDTO = JSON.parseObject(grayscaleConfigJson, GrayscaleConfigDTO.class);
                if (configDTO != null) {
                    // 将逗号分隔的字符串转换为列表
                    List<String> monitorGroupIds = configDTO.getMonitorGroupIdList();
                    List<String> misIds = configDTO.getMisIdList();
                    
                    // 放入结果Map
                    configMap.put("monitorGroupIds", monitorGroupIds != null ? monitorGroupIds : new ArrayList<>());
                    configMap.put("misIds", misIds != null ? misIds : new ArrayList<>());
                }
            }
            
            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("config", configMap);
            
            log.info("getGrayscaleConfig# 获取灰度配置成功");
            
            return CommonResult.success(result);
        } catch (Exception e) {
            log.error("getGrayscaleConfig# 获取灰度配置异常, 原因:{}", e.getMessage(), e);
            return CommonResult.error("获取灰度配置失败: " + e.getMessage());
        }
    }

//    /**
//     * 批量清理并重建所有文档
//     * 调用AccessKeyService的clearDocumentIdByRgIdAndSpaceId方法，删除所有文档并重新创建
//     *
//     * @return 处理结果
//     */
//    @GetMapping("/clear-rebuild-documents")
//    public Object clearAndRebuildDocuments() {
//        try {
//            // 调用服务处理
//            Map<String, Object> result = accessKeyService.clearDocumentIdByRgIdAndSpaceId();
//
//            // 记录完整的result结果
//            log.info("#clearAndRebuildDocuments 批量清理并重建文档结果详情: {}", result);
//
//            return CommonResult.success("处理完成", result);
//        } catch (Exception e) {
//            log.error("清理并重建文档失败", e);
//            return CommonResult.error("处理失败: " + e.getMessage());
//        }
//    }
} 