package com.meituan.banma.llm.corpus.server.service;

import com.meituan.banma.llm.corpus.api.exception.LlmCorpusException;
import com.meituan.banma.llm.corpus.server.common.domain.dto.DxChatMessageRecord;
import com.meituan.banma.llm.corpus.server.common.domain.dto.GroupInfoDto;
import com.meituan.banma.llm.corpus.server.common.domain.dto.NoticeDetailDto;
import com.sankuai.xm.openplatform.api.entity.UserDetail;
import org.apache.thrift.TException;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2024/12/10
 */
public interface IDxGroupChatService {
    /**
     * 根据大象群id获取聊天记录
     * 
     * @param groupId 大象群id
     * @return 聊天记录
     */
    List<DxChatMessageRecord> getTTChatInfo(long groupId, String... robotAppId);

    List<DxChatMessageRecord> getTtChatInfoByTime(long groupId, long startTime, long endTime);
    /**
     * 根据大象群id获取群名
     * @param groupId
     * @return
     */
    String getGroupNameByDxGroupId(long groupId);

    /**
     * 根据群名获取对应TT id
     * 
     * @param groupId 大象群id
     * @return TT id
     */
    String getTicketIdByDxGroupNotice(long groupId);

    NoticeDetailDto getNoticeDetailByDxGroupNotice(long groupId);

    /**
     * 向群中发送信息,并@发起人
     *
     * @param groupId  群id
     * @param uid      用户id
     * @param fromName 用户名
     * @param message  消息
     */
    void sendMessageToGroupWithAt(long groupId, long uid, String fromName, String message);

    void sendMessageToGroup(long groupId, String message);

    void sendMessageToSingle(long uid, String message);

    void sendMessageToGroupSpecifyRobot(long groupId, String message, String robotAppId, String robotAppSecret);

    /**
     * 根据uid获取misId
     * @param uid 用户id
     * @return misId
     */
    String getMisIdByUid(long uid);

    /**
     * 根据uid获取用户详情
     */
    UserDetail getUserDetailByUid(long uid);

    /**
     * 根据uid获取empId
     * @return
     */
    Set<Long> getEmpIdListByUidList(List<Long> uidList);

    /**
     * 根据单个empId获取uid
     * @param empId
     * @return
     */
    long getUidByEmpId(long empId);

    /**
     * 添加机器人到群组（这里是人拉机器人）
     * 参考文档：https://km.sankuai.com/collabpage/2552597188
     *
     * @param botId 机器人id
     * @param groupId 群组id
     */
    void addBotToGroup(Long botId, Long groupId);

    /**
     * 判断指定用户是否在群里
     * @param groupId 群组id
     * @param empId 员工id
     * @return 是否在群里
     * @throws LlmCorpusException 如果机器人不在群里，会抛出异常
     */
    boolean isUserInGroup(long groupId, long empId) throws LlmCorpusException;

    Object testGetMsg(long msgId, int chatType) throws TException;

    List<DxChatMessageRecord> getSingleMergeChat(Long creatorDxUserId);

    /**
     * 机器人添加群成员(这里是机器人拉机器人进群)
     * @param groupId 大象群id
     * @param botIds 机器人id列表
     */
    void addBotsToGroup(long groupId, List<Long> botIds);

    /**
     * 获取群名和发起人misId
     * @param groupId 大象群id
     * @return 包含群名和发起人misId的对象
     */
    GroupInfoDto getGroupNameAndCreatorMisId(long groupId);

    /**
     * 批量查询MIS列表对应的员工身份信息
     * 
     * @param misList MIS ID列表
     * @return 员工身份信息Map，key为misId，value为包含uid和empId的Map
     * @throws LlmCorpusException 如果查询失败，抛出异常
     */
    Map<String, Map<String, Long>> queryEmpIdentityByMisList(List<String> misList) throws LlmCorpusException;

    /**
     * 解析大象群公告获取雷达事件总指挥MIS ID
     *
     * @param groupId 大象群id
     * @return 总指挥MIS ID
     */
    String getRadarOwnerMisIdByDxGroupNotice(long groupId);
}
