package com.meituan.banma.llm.corpus.server.thrift;

import com.meituan.banma.llm.corpus.api.client.ExampleThriftService;
import com.meituan.banma.llm.corpus.api.exception.LlmCorpusException;
import com.meituan.banma.llm.corpus.api.request.ExampleRequestParam;
import com.meituan.banma.llm.corpus.server.common.MtConfigService;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftServer;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
@MdpThriftServer()
public class ExampleThriftServiceImpl implements ExampleThriftService {
    @Autowired
    private MtConfigService mtConfigService;
    @Override
    public String exampleMethod(ExampleRequestParam exampleRequestParam) throws TException, LlmCorpusException {
        return mtConfigService.getExampleConfig();
    }
}
