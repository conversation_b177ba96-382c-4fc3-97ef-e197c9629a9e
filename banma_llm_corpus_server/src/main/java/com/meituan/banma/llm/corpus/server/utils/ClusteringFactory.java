package com.meituan.banma.llm.corpus.server.utils;

import com.meituan.banma.llm.corpus.server.common.domain.dto.stat.HDBSCANConfig;
import com.meituan.banma.llm.corpus.server.common.domain.dto.stat.KMeansConfig;
import com.meituan.banma.llm.corpus.server.common.constants.enums.ClusteringAlgorithmType;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;

/**
 * 聚类算法工厂类
 * 用于根据配置选择使用哪种聚类算法
 */
@Slf4j
public class ClusteringFactory {

    /**
     * 根据指定的算法类型进行聚类
     *
     * @param vectors 向量列表
     * @param algorithmType 聚类算法类型
     * @param kMeansConfig K-means配置
     * @param hdbscanConfig HDBSCAN配置
     * @return 聚类结果
     */
    public static Map<Integer, List<Integer>> cluster(
            List<double[]> vectors,
            ClusteringAlgorithmType algorithmType,
            KMeansConfig kMeansConfig,
            HDBSCANConfig hdbscanConfig) {
        
        if (algorithmType == null) {
            log.info("未指定聚类算法类型，默认使用K-means算法");
            algorithmType = ClusteringAlgorithmType.KMEANS;
        }
        
        switch (algorithmType) {
            case HDBSCAN:
                log.info("使用HDBSCAN*聚类算法，数据点数量: {}", vectors.size());
                return HDBSCANClusteringUtil.cluster(vectors, hdbscanConfig);
            case KMEANS:
            default:
                log.info("使用K-means聚类算法，数据点数量: {}", vectors.size());
                return KMeansClusteringUtil.cluster(vectors, kMeansConfig);
        }
    }
} 