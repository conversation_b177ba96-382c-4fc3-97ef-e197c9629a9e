package com.meituan.banma.llm.corpus.server.service;

import com.meituan.banma.llm.corpus.api.exception.LlmCorpusException;

/**
 * 工作空间服务接口
 */
public interface WorkspaceService {
    
    /**
     * 验证新增工作空间条件
     * 检查用户提供的工作空间ID与密钥是否有效，以及是否有权限访问
     *
     * @param rgId 值班组ID
     * @param spaceId 工作空间ID
     * @param accessKey 访问密钥
     * @param appSecret 应用密钥
     * @return 验证结果，true表示符合添加条件，false表示不符合
     * @throws LlmCorpusException 业务异常
     */
    boolean validateWorkspaceAddCondition(Long rgId, String spaceId, String accessKey, String appSecret, String spaceName) throws LlmCorpusException;
    
    /**
     * 获取工作空间访问令牌
     *
     * @param accessKey 访问密钥
     * @param appSecret 应用密钥
     * @return 访问令牌
     * @throws LlmCorpusException 业务异常
     */
    String getWorkspaceAccessToken(String accessKey, String appSecret) throws LlmCorpusException;

    /**
     * 删除工作空间
     * 删除指定值班组下的指定工作空间及其所有文档
     *
     * @param rgId 值班组ID
     * @param spaceId 工作空间ID
     * @param misId 用户ID
     * @return 删除结果，true表示删除成功，false表示删除失败
     * @throws LlmCorpusException 业务异常
     */
    boolean deleteWorkspace(Long rgId, String spaceId, String misId) throws LlmCorpusException;
    
    /**
     * 为值班组创建默认工作空间配置
     * 如果指定值班组没有工作空间配置，将创建一个默认的工作空间配置
     *
     * @param rgId 值班组ID
     * @param misId 用户ID
     * @return 创建是否成功
     */
    boolean createDefaultWorkspace(Long rgId, String misId);
    
    /**
     * 判断指定misId是否具有默认工作空间的权限
     * 通过尝试创建并删除测试知识库来判断
     *
     * @param misId 用户ID
     * @return 是否具有权限
     */
    boolean checkDefaultWorkspacePermission(String misId);
    
    /**
     * 检查该值班组是否具有自定义空间
     * 如果用户没有默认工作空间的权限，并且该值班组除了默认工作空间没有其他工作空间，则认为无权限
     * 
     * @param modifier 用户ID
     * @param rgId 值班组ID
     * @return 是否具有权限
     */
    boolean checkUserRgPermission(String modifier, Long rgId);
} 