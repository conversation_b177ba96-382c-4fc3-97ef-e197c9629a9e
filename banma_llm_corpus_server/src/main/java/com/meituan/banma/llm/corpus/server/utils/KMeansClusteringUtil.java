package com.meituan.banma.llm.corpus.server.utils;

import com.meituan.banma.llm.corpus.server.common.domain.dto.stat.KMeansConfig;
import com.meituan.banma.llm.corpus.server.common.constants.enums.ClusterValidationMethod;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * K-means聚类算法工具类
 */
@Slf4j
public class KMeansClusteringUtil {

    /**
     * 使用K-means算法对文本向量进行聚类
     *
     * @param vectors        文本向量列表
     * @param config         K-means配置参数
     * @return 聚类结果，返回Map<Integer, List<Integer>>，key为簇的索引，value为该簇中的向量索引列表
     */
    public static Map<Integer, List<Integer>> cluster(List<double[]> vectors, KMeansConfig config) {
        if (vectors == null || vectors.isEmpty()) {
            return Collections.emptyMap();
        }
        
        // 如果K值设为0或负数，自动确定最佳K值
        int k = config.getK();
        if (k <= 0) {
            k = findOptimalK(vectors, config);
            log.info("自动确定最佳K值为: {}", k);
        } else {
            k = Math.min(k, vectors.size());
        }
        
        Random random = new Random(config.getRandomSeed());
        
        Map<Integer, List<Integer>> bestClusters = null;
        double bestScore = Double.MAX_VALUE;
        
        // 多次运行，选择最佳结果
        for (int run = 0; run < config.getNumRuns(); run++) {
            // 初始化簇中心点
            List<double[]> centroids = initializeCentroids(vectors, k, random, config.getUseKMeansPlusPlus());
            
            // 迭代直到收敛或达到最大迭代次数
            Map<Integer, List<Integer>> clusters = null;
            for (int iter = 0; iter < config.getMaxIterations(); iter++) {
                // 分配向量到最近的簇
                clusters = assignToClusters(vectors, centroids);
                
                // 更新簇中心点
                List<double[]> newCentroids = updateCentroids(vectors, clusters, centroids.get(0).length);
                
                // 检查是否收敛
                double shift = calculateCentroidShift(centroids, newCentroids);
                centroids = newCentroids;
                
                if (shift < config.getConvergenceThreshold()) {
                    log.info("K-means聚类在第{}次迭代后收敛, shift={}", iter + 1, shift);
                    break;
                }
            }
            
            // 计算当前聚类结果的评分（这里使用簇内距离平方和）
            double score = calculateScore(vectors, clusters, centroids);
            
            // 更新最佳结果
            if (score < bestScore) {
                bestScore = score;
                bestClusters = clusters;
            }
        }
        
        log.info("K-means聚类完成，最佳评分={}, 簇数量={}", bestScore, bestClusters.size());
        return bestClusters;
    }
    
    /**
     * 使用肘部法则(Elbow Method)自动确定最佳K值
     * 
     * @param vectors 数据向量
     * @param config K-means配置
     * @return 最佳K值
     */
    private static int findOptimalK(List<double[]> vectors, KMeansConfig config) {
        int n = vectors.size();
        if (n <= 2) {
            return Math.max(1, n);
        }
        
        // 确定要尝试的K值范围
        int maxK = Math.min(20, n / 2); // 最大不超过数据点数的二分之一，且不超过20
        int minK = 2; // 最小为2，至少有两个簇
        
        double[] inertias = new double[maxK + 1];
        Random random = new Random(config.getRandomSeed());
        
        log.info("开始计算不同K值的聚类惯性值 (K从{}到{})", minK, maxK);
        
        // 计算不同K值下的惯性值(簇内平方和)
        for (int k = minK; k <= maxK; k++) {
            // 初始化簇中心点
            List<double[]> centroids = initializeCentroids(vectors, k, random, config.getUseKMeansPlusPlus());
            
            // 迭代直到收敛或达到最大迭代次数
            for (int iter = 0; iter < config.getMaxIterations(); iter++) {
                // 分配向量到最近的簇
                Map<Integer, List<Integer>> clusters = assignToClusters(vectors, centroids);
                
                // 更新簇中心点
                List<double[]> newCentroids = updateCentroids(vectors, clusters, centroids.get(0).length);
                
                // 检查是否收敛
                double shift = calculateCentroidShift(centroids, newCentroids);
                centroids = newCentroids;
                
                if (shift < config.getConvergenceThreshold()) {
                    break;
                }
            }
            
            // 计算当前K值下的惯性值(簇内距离平方和)
            Map<Integer, List<Integer>> clusters = assignToClusters(vectors, centroids);
            inertias[k] = calculateScore(vectors, clusters, centroids);
            
            log.info("K={}, 惯性值={}", k, inertias[k]);
        }
        
        // 归一化惯性值，使其在0-1之间
        double maxInertia = inertias[minK];
        double minInertia = Double.MAX_VALUE;
        for (int k = minK; k <= maxK; k++) {
            minInertia = Math.min(minInertia, inertias[k]);
        }
        
        double range = maxInertia - minInertia;
        double[] normalizedInertias = new double[maxK + 1];
        for (int k = minK; k <= maxK; k++) {
            normalizedInertias[k] = (inertias[k] - minInertia) / range;
        }
        
        // 使用改进的肘部检测方法
        int bestK = findElbowPoint(normalizedInertias, minK, maxK);
        
        // 应用聚类有效性检验
        bestK = validateClusterNumber(vectors, bestK, minK, maxK, config);
        
        // 确保bestK至少为minK
        return Math.max(bestK, minK);
    }
    
    /**
     * 使用改进的方法找到肘部点
     * 采用最大曲率点方法，寻找曲线上曲率最大的点
     */
    private static int findElbowPoint(double[] normalizedInertias, int minK, int maxK) {
        // 构建(k, inertia)点集
        double[][] points = new double[maxK - minK + 1][2];
        for (int k = minK; k <= maxK; k++) {
            points[k - minK][0] = k;
            points[k - minK][1] = normalizedInertias[k];
        }
        
        // 计算每个点的曲率
        double[] curvatures = new double[maxK - minK + 1];
        for (int i = 1; i < points.length - 1; i++) {
            double x1 = points[i-1][0], y1 = points[i-1][1];
            double x2 = points[i][0], y2 = points[i][1];
            double x3 = points[i+1][0], y3 = points[i+1][1];
            
            // 计算两个向量
            double[] v1 = {x2 - x1, y2 - y1};
            double[] v2 = {x3 - x2, y3 - y2};
            
            // 归一化向量
            double v1Norm = Math.sqrt(v1[0] * v1[0] + v1[1] * v1[1]);
            double v2Norm = Math.sqrt(v2[0] * v2[0] + v2[1] * v2[1]);
            
            v1[0] /= v1Norm;
            v1[1] /= v1Norm;
            v2[0] /= v2Norm;
            v2[1] /= v2Norm;
            
            // 计算向量夹角的余弦值
            double dotProduct = v1[0] * v2[0] + v1[1] * v2[1];
            
            // 夹角的余弦值越小，曲率越大
            curvatures[i] = 1 - dotProduct;
        }
        
        // 找到曲率最大的点
        int maxCurvatureIndex = 1; // 初始化为第二个点(k=minK+1)
        double maxCurvature = curvatures[1];
        
        for (int i = 2; i < curvatures.length - 1; i++) {
            if (curvatures[i] > maxCurvature) {
                maxCurvature = curvatures[i];
                maxCurvatureIndex = i;
            }
        }
        
        int bestK = minK + maxCurvatureIndex;
        log.info("曲率最大点: K={}, 曲率={}", bestK, maxCurvature);
        
        return bestK;
    }
    
    /**
     * 验证聚类数量的有效性
     * 使用轮廓系数(Silhouette Coefficient)、Davies-Bouldin指数或组合评估方法
     */
    private static int validateClusterNumber(List<double[]> vectors, int initialK, int minK, int maxK, KMeansConfig config) {
        Random random = new Random(config.getRandomSeed());
        int bestK = initialK;
        
        // 存储不同K值的评估指标
        double[] silhouetteScores = new double[maxK + 1];
        double[] dbIndexScores = new double[maxK + 1];
        double[] chIndexScores = new double[maxK + 1];
        double[] gapStatisticScores = new double[maxK + 1];
        
        // 缓存每个K值的聚类结果，避免重复计算
        Map<Integer, ClusteringResult> clusteringResults = new HashMap<>();
        
        // 如果使用Gap统计量，需要生成参考数据集
        List<List<double[]>> referenceDatasets = null;
        if (config.getClusterValidationMethod() == ClusterValidationMethod.GAP_STATISTIC || 
            config.getClusterValidationMethod() == ClusterValidationMethod.COMPREHENSIVE) {
            int numReferences = config.getGapStatisticReferenceDatasets();
            referenceDatasets = generateReferenceDatasets(vectors, numReferences, random);
        }
        
        // 为候选K值计算评估指标
        int rangeEnd = Math.min(initialK + 2, maxK);
        if (config.getClusterValidationMethod() == ClusterValidationMethod.COMPREHENSIVE) {
            // 对于综合评估，扩大评估范围
            rangeEnd = Math.min(initialK + 4, maxK);
        }
        
        for (int k = minK; k <= rangeEnd; k++) {
            // 运行K-means
            List<double[]> centroids = initializeCentroids(vectors, k, random, config.getUseKMeansPlusPlus());
            
            for (int iter = 0; iter < config.getMaxIterations(); iter++) {
                Map<Integer, List<Integer>> clusters = assignToClusters(vectors, centroids);
                List<double[]> newCentroids = updateCentroids(vectors, clusters, centroids.get(0).length);
                double shift = calculateCentroidShift(centroids, newCentroids);
                centroids = newCentroids;
                
                if (shift < config.getConvergenceThreshold()) {
                    break;
                }
            }
            
            Map<Integer, List<Integer>> clusters = assignToClusters(vectors, centroids);
            double inertia = calculateScore(vectors, clusters, centroids);
            
            // 缓存聚类结果
            ClusteringResult result = new ClusteringResult(clusters, centroids, inertia);
            clusteringResults.put(k, result);
            
            // 计算轮廓系数（值越大越好）
            silhouetteScores[k] = calculateSilhouette(vectors, clusters);
            
            // 计算Davies-Bouldin指数（值越小越好）
            dbIndexScores[k] = calculateDaviesBouldinIndex(vectors, clusters, centroids);
            
            // 计算Calinski-Harabasz指数（值越大越好）
            chIndexScores[k] = calculateCalinskiHarabaszIndex(vectors, clusters, centroids);
            
            log.info("K={}, 轮廓系数={}, Davies-Bouldin指数={}, Calinski-Harabasz指数={}", 
                    k, silhouetteScores[k], dbIndexScores[k], chIndexScores[k]);
        }
        
        // 如果使用Gap统计量，需要额外计算
        if (config.getClusterValidationMethod() == ClusterValidationMethod.GAP_STATISTIC || 
            config.getClusterValidationMethod() == ClusterValidationMethod.COMPREHENSIVE) {
            
            calculateGapStatistic(vectors, clusteringResults, referenceDatasets, gapStatisticScores, minK, rangeEnd, config);
            
            for (int k = minK; k <= rangeEnd; k++) {
                log.info("K={}, Gap统计量={}", k, gapStatisticScores[k]);
            }
        }
        
        // 根据选择的评估方法确定最佳K值
        ClusterValidationMethod validationMethod = config.getClusterValidationMethod();
        if (validationMethod == null) {
            validationMethod = ClusterValidationMethod.SILHOUETTE; // 默认使用轮廓系数
        }
        
        switch (validationMethod) {
            case DB_INDEX:
                // 选择DB指数最小的K值
                double minDBIndex = Double.MAX_VALUE;
                for (int k = minK; k <= rangeEnd; k++) {
                    if (dbIndexScores[k] < minDBIndex) {
                        minDBIndex = dbIndexScores[k];
                        bestK = k;
                    }
                }
                log.info("使用Davies-Bouldin指数评估，最佳K值为: {}, DB指数={}", bestK, minDBIndex);
                break;
                
            case CH_INDEX:
                // 选择Calinski-Harabasz指数最大的K值
                double maxCHIndex = -1;
                for (int k = minK; k <= rangeEnd; k++) {
                    if (chIndexScores[k] > maxCHIndex) {
                        maxCHIndex = chIndexScores[k];
                        bestK = k;
                    }
                }
                log.info("使用Calinski-Harabasz指数评估，最佳K值为: {}, CH指数={}", bestK, maxCHIndex);
                break;
                
            case GAP_STATISTIC:
                // 选择Gap统计量最大且满足Gap(k) >= Gap(k+1) - standardError的K值
                bestK = findBestKByGapStatistic(gapStatisticScores, minK, rangeEnd);
                log.info("使用Gap统计量评估，最佳K值为: {}, Gap值={}", bestK, gapStatisticScores[bestK]);
                break;
                
            case COMPREHENSIVE:
                // 综合评估，结合多种方法的投票
                bestK = findBestKByComprehensiveEvaluation(
                        silhouetteScores, dbIndexScores, chIndexScores, gapStatisticScores, 
                        minK, rangeEnd);
                log.info("使用综合评估方法，最佳K值为: {}", bestK);
                break;
            
            case COMBINED:
                // 结合轮廓系数和DB指数，计算平均排名
                Map<Integer, Double> combinedRanks = new HashMap<>();
                
                // 计算轮廓系数的排名（越大越好）
                List<Integer> silhouetteRanking = IntStream.range(minK, rangeEnd + 1)
                        .boxed()
                        .sorted((i, j) -> Double.compare(silhouetteScores[j], silhouetteScores[i]))
                        .collect(Collectors.toList());
                
                // 计算DB指数的排名（越小越好）
                List<Integer> dbIndexRanking = IntStream.range(minK, rangeEnd + 1)
                        .boxed()
                        .sorted((i, j) -> Double.compare(dbIndexScores[i], dbIndexScores[j]))
                        .collect(Collectors.toList());
                
                // 计算平均排名
                for (int k = minK; k <= rangeEnd; k++) {
                    int silhouetteRank = silhouetteRanking.indexOf(k);
                    int dbIndexRank = dbIndexRanking.indexOf(k);
                    double avgRank = (silhouetteRank + dbIndexRank) / 2.0;
                    combinedRanks.put(k, avgRank);
                }
                
                // 选择平均排名最好的K值
                double bestRank = Double.MAX_VALUE;
                for (Map.Entry<Integer, Double> entry : combinedRanks.entrySet()) {
                    if (entry.getValue() < bestRank) {
                        bestRank = entry.getValue();
                        bestK = entry.getKey();
                    }
                }
                log.info("使用组合评估方法，最佳K值为: {}", bestK);
                break;
                
            case SILHOUETTE:
            default:
                // 选择轮廓系数最大的K值
                double maxSilhouette = -1;
                for (int k = minK; k <= rangeEnd; k++) {
                    if (silhouetteScores[k] > maxSilhouette) {
                        maxSilhouette = silhouetteScores[k];
                        bestK = k;
                    }
                }
                log.info("使用轮廓系数评估，最佳K值为: {}, 轮廓系数={}", bestK, maxSilhouette);
                break;
        }
        
        // 检查簇是否平衡
        ClusteringResult finalClusters = clusteringResults.get(bestK);
        if (finalClusters == null) {
            // 如果没有缓存结果，重新计算
            List<double[]> centroids = initializeCentroids(vectors, bestK, random, config.getUseKMeansPlusPlus());
            for (int iter = 0; iter < config.getMaxIterations(); iter++) {
                Map<Integer, List<Integer>> clusters = assignToClusters(vectors, centroids);
                List<double[]> newCentroids = updateCentroids(vectors, clusters, centroids.get(0).length);
                double shift = calculateCentroidShift(centroids, newCentroids);
                centroids = newCentroids;
                
                if (shift < config.getConvergenceThreshold()) {
                    break;
                }
            }
            
            finalClusters = new ClusteringResult(
                    assignToClusters(vectors, centroids), 
                    centroids, 
                    calculateScore(vectors, assignToClusters(vectors, centroids), centroids));
        }
        
        // 统计簇大小分布情况
        int[] clusterSizes = new int[finalClusters.clusters.size()];
        int idx = 0;
        for (List<Integer> clusterPoints : finalClusters.clusters.values()) {
            clusterSizes[idx++] = clusterPoints.size();
            log.info("簇 #{} 包含 {} 个数据点", idx-1, clusterPoints.size());
        }
        
        // 检查是否存在极小簇(比如只有1-2个点的簇)
        boolean hasVerySmallCluster = false;
        int minClusterSize = Math.max(1, vectors.size() / (bestK * 10)); // 最小簇大小阈值
        for (List<Integer> clusterPoints : finalClusters.clusters.values()) {
            if (clusterPoints.size() <= minClusterSize) {
                hasVerySmallCluster = true;
                break;
            }
        }
        
        // 只有当配置不允许小簇时才减少K值
        if (hasVerySmallCluster && bestK > minK && !config.getAllowSmallClusters()) {
            log.info("检测到极小簇，且不允许极小簇存在，将K值从{}减少到{}", bestK, bestK - 1);
            bestK -= 1;
        } else if (hasVerySmallCluster) {
            log.info("检测到极小簇，但配置允许极小簇存在，保留K值为{}", bestK);
        }
        
        return bestK;
    }
    
    /**
     * 使用Gap统计量找到最佳K值
     * 选择满足Gap(k) >= Gap(k+1) - standardError的最小k值
     * 
     * 特性：比较观察数据与随机参考分布的聚类分散度差异
     * 适用：非球形、不规则形状簇，当簇数量不明确时效果最佳
     * 优势：对真实结构的敏感性高，能发现自然聚类
     */
    private static int findBestKByGapStatistic(double[] gapStatisticScores, int minK, int maxK) {
        int bestK = minK;
        double maxGap = gapStatisticScores[minK];
        
        for (int k = minK + 1; k <= maxK; k++) {
            if (gapStatisticScores[k] > maxGap) {
                maxGap = gapStatisticScores[k];
                bestK = k;
            }
        }
        
        return bestK;
    }
    
    /**
     * 综合多种评估方法投票选出最佳K值
     * 
     * 特性：采用投票机制，集成四种评估方法的结果
     * 适用：复杂、多样化的数据集，需要保守稳健的结果
     * 优势：最大程度降低误判风险，提供稳健可靠的估计
     */
    private static int findBestKByComprehensiveEvaluation(
            double[] silhouetteScores, double[] dbIndexScores, double[] chIndexScores, 
            double[] gapStatisticScores, int minK, int maxK) {
        
        // 获取每种方法的最佳K值
        int bestKBySilhouette = minK;
        double maxSilhouette = silhouetteScores[minK];
        
        int bestKByDBIndex = minK;
        double minDBIndex = dbIndexScores[minK];
        
        int bestKByCHIndex = minK;
        double maxCHIndex = chIndexScores[minK];
        
        int bestKByGapStatistic = findBestKByGapStatistic(gapStatisticScores, minK, maxK);
        
        // 寻找每种方法的最佳K值
        for (int k = minK + 1; k <= maxK; k++) {
            if (silhouetteScores[k] > maxSilhouette) {
                maxSilhouette = silhouetteScores[k];
                bestKBySilhouette = k;
            }
            
            if (dbIndexScores[k] < minDBIndex) {
                minDBIndex = dbIndexScores[k];
                bestKByDBIndex = k;
            }
            
            if (chIndexScores[k] > maxCHIndex) {
                maxCHIndex = chIndexScores[k];
                bestKByCHIndex = k;
            }
        }
        
        // 统计每个K值的票数
        Map<Integer, Integer> votes = new HashMap<>();
        votes.put(bestKBySilhouette, votes.getOrDefault(bestKBySilhouette, 0) + 1);
        votes.put(bestKByDBIndex, votes.getOrDefault(bestKByDBIndex, 0) + 1);
        votes.put(bestKByCHIndex, votes.getOrDefault(bestKByCHIndex, 0) + 1);
        votes.put(bestKByGapStatistic, votes.getOrDefault(bestKByGapStatistic, 0) + 1);
        
        // 找出得票最多的K值
        int maxVotes = 0;
        int bestK = minK;
        
        for (Map.Entry<Integer, Integer> entry : votes.entrySet()) {
            if (entry.getValue() > maxVotes) {
                maxVotes = entry.getValue();
                bestK = entry.getKey();
            }
        }
        
        log.info("综合评估投票结果: 轮廓系数推荐K={}, DB指数推荐K={}, CH指数推荐K={}, Gap统计量推荐K={}, 最终选择K={} (得票{})",
                bestKBySilhouette, bestKByDBIndex, bestKByCHIndex, bestKByGapStatistic, bestK, maxVotes);
        
        return bestK;
    }
    
    /**
     * 计算Calinski-Harabasz指数(也称为方差比准则)
     * 值越大表示聚类效果越好
     * 
     * 特性：方差比准则，评估簇间离散度与簇内离散度的比率
     * 适用：高密度、球状且大小相近的簇，适合高维数据
     * 优势：计算快速，特别适合大规模数据集
     */
    private static double calculateCalinskiHarabaszIndex(List<double[]> vectors, Map<Integer, List<Integer>> clusters, List<double[]> centroids) {
        if (clusters.size() <= 1 || vectors.size() <= clusters.size()) {
            return 0.0; // 当簇数为1或样本数少于簇数时，指数无意义
        }
        
        int dimension = vectors.get(0).length;
        
        // 计算数据集中心点
        double[] globalCentroid = new double[dimension];
        for (double[] vector : vectors) {
            for (int i = 0; i < dimension; i++) {
                globalCentroid[i] += vector[i];
            }
        }
        for (int i = 0; i < dimension; i++) {
            globalCentroid[i] /= vectors.size();
        }
        
        // 计算簇间离散度
        double betweenClusterVariance = 0.0;
        for (int clusterId : clusters.keySet()) {
            List<Integer> clusterPoints = clusters.get(clusterId);
            if (clusterPoints.isEmpty()) {
                continue;
            }
            
            double distance = calculateDistance(centroids.get(clusterId), globalCentroid);
            betweenClusterVariance += clusterPoints.size() * distance * distance;
        }
        
        // 计算簇内离散度
        double withinClusterVariance = 0.0;
        for (int clusterId : clusters.keySet()) {
            List<Integer> clusterPoints = clusters.get(clusterId);
            
            for (int pointIdx : clusterPoints) {
                double distance = calculateDistance(vectors.get(pointIdx), centroids.get(clusterId));
                withinClusterVariance += distance * distance;
            }
        }
        
        // 当所有点都在各自簇的中心时，簇内方差为0，此时返回一个大值
        if (withinClusterVariance == 0) {
            return Double.MAX_VALUE;
        }
        
        // 计算Calinski-Harabasz指数: (簇间离散度 / 簇内离散度) * ((N - k) / (k - 1))
        // 其中N是样本数，k是簇数
        return (betweenClusterVariance / withinClusterVariance) * 
               ((vectors.size() - clusters.size()) / (double)(clusters.size() - 1));
    }
    
    /**
     * 为Gap统计量生成参考数据集
     */
    private static List<List<double[]>> generateReferenceDatasets(List<double[]> vectors, int numReferences, Random random) {
        int n = vectors.size();
        int dimension = vectors.get(0).length;
        
        // 计算数据的范围
        double[][] ranges = new double[dimension][2]; // [min, max]
        for (int d = 0; d < dimension; d++) {
            ranges[d][0] = Double.MAX_VALUE; // min
            ranges[d][1] = Double.MIN_VALUE; // max
        }
        
        for (double[] vector : vectors) {
            for (int d = 0; d < dimension; d++) {
                ranges[d][0] = Math.min(ranges[d][0], vector[d]);
                ranges[d][1] = Math.max(ranges[d][1], vector[d]);
            }
        }
        
        // 生成参考数据集
        List<List<double[]>> referenceDatasets = new ArrayList<>(numReferences);
        for (int i = 0; i < numReferences; i++) {
            List<double[]> reference = new ArrayList<>(n);
            for (int j = 0; j < n; j++) {
                double[] point = new double[dimension];
                for (int d = 0; d < dimension; d++) {
                    // 在范围内生成均匀分布的随机值
                    point[d] = ranges[d][0] + random.nextDouble() * (ranges[d][1] - ranges[d][0]);
                }
                reference.add(point);
            }
            referenceDatasets.add(reference);
        }
        
        return referenceDatasets;
    }
    
    /**
     * 计算Gap统计量
     * 
     * 特性：比较观察数据与随机参考分布的聚类分散度差异
     * 适用：非球形、不规则形状簇，当簇数量不明确时效果最佳
     * 优势：对真实结构的敏感性高，能发现自然聚类
     */
    private static void calculateGapStatistic(
            List<double[]> vectors, 
            Map<Integer, ClusteringResult> clusteringResults,
            List<List<double[]>> referenceDatasets,
            double[] gapStatisticScores,
            int minK, int maxK,
            KMeansConfig config) {
        
        Random random = new Random(config.getRandomSeed());
        
        // 计算实际数据的log(惯性值)
        double[] logInertias = new double[maxK + 1];
        for (int k = minK; k <= maxK; k++) {
            ClusteringResult result = clusteringResults.get(k);
            if (result == null) {
                // 如果没有缓存结果，重新计算
                List<double[]> centroids = initializeCentroids(vectors, k, random, config.getUseKMeansPlusPlus());
                for (int iter = 0; iter < config.getMaxIterations(); iter++) {
                    Map<Integer, List<Integer>> clusters = assignToClusters(vectors, centroids);
                    List<double[]> newCentroids = updateCentroids(vectors, clusters, centroids.get(0).length);
                    double shift = calculateCentroidShift(centroids, newCentroids);
                    centroids = newCentroids;
                    
                    if (shift < config.getConvergenceThreshold()) {
                        break;
                    }
                }
                
                Map<Integer, List<Integer>> clusters = assignToClusters(vectors, centroids);
                double inertia = calculateScore(vectors, clusters, centroids);
                logInertias[k] = Math.log(inertia);
            } else {
                logInertias[k] = Math.log(result.inertia);
            }
        }
        
        // 计算参考数据集的平均log(惯性值)
        double[][] refLogInertias = new double[referenceDatasets.size()][maxK + 1];
        
        for (int r = 0; r < referenceDatasets.size(); r++) {
            List<double[]> reference = referenceDatasets.get(r);
            
            for (int k = minK; k <= maxK; k++) {
                // 对参考数据集运行K-means
                List<double[]> centroids = initializeCentroids(reference, k, random, config.getUseKMeansPlusPlus());
                
                for (int iter = 0; iter < config.getMaxIterations(); iter++) {
                    Map<Integer, List<Integer>> clusters = assignToClusters(reference, centroids);
                    List<double[]> newCentroids = updateCentroids(reference, clusters, centroids.get(0).length);
                    double shift = calculateCentroidShift(centroids, newCentroids);
                    centroids = newCentroids;
                    
                    if (shift < config.getConvergenceThreshold()) {
                        break;
                    }
                }
                
                Map<Integer, List<Integer>> clusters = assignToClusters(reference, centroids);
                double inertia = calculateScore(reference, clusters, centroids);
                refLogInertias[r][k] = Math.log(inertia);
            }
        }
        
        // 计算参考数据集的平均log(惯性值)和标准差
        double[] avgRefLogInertias = new double[maxK + 1];
        double[] stdRefLogInertias = new double[maxK + 1];
        
        for (int k = minK; k <= maxK; k++) {
            double sum = 0.0;
            for (int r = 0; r < referenceDatasets.size(); r++) {
                sum += refLogInertias[r][k];
            }
            avgRefLogInertias[k] = sum / referenceDatasets.size();
            
            double sumSquaredDiff = 0.0;
            for (int r = 0; r < referenceDatasets.size(); r++) {
                double diff = refLogInertias[r][k] - avgRefLogInertias[k];
                sumSquaredDiff += diff * diff;
            }
            stdRefLogInertias[k] = Math.sqrt(sumSquaredDiff / referenceDatasets.size());
        }
        
        // 计算Gap统计量
        for (int k = minK; k <= maxK; k++) {
            gapStatisticScores[k] = avgRefLogInertias[k] - logInertias[k];
        }
    }
    
    /**
     * 聚类结果缓存类
     */
    private static class ClusteringResult {
        final Map<Integer, List<Integer>> clusters;
        final List<double[]> centroids;
        final double inertia;
        
        ClusteringResult(Map<Integer, List<Integer>> clusters, List<double[]> centroids, double inertia) {
            this.clusters = clusters;
            this.centroids = centroids;
            this.inertia = inertia;
        }
    }
    
    /**
     * 计算轮廓系数(Silhouette Coefficient)
     * 轮廓系数是衡量聚类质量的一个指标，取值范围[-1, 1]
     * 值越大表示聚类效果越好
     * 
     * 特性：衡量样本与自身所在簇的相似度与其他簇的差异度
     * 适用：形状规则、密度均匀的球状簇，适合大多数普通场景
     * 优势：直观、计算简单，对簇形状和密度不均匀有一定容忍度
     */
    private static double calculateSilhouette(List<double[]> vectors, Map<Integer, List<Integer>> clusters) {
        if (clusters.size() <= 1) {
            return 0.0; // 只有一个簇时，轮廓系数为0
        }
        
        double totalSilhouette = 0.0;
        int totalPoints = 0;
        
        // 计算每个点的轮廓系数
        for (int clusterId : clusters.keySet()) {
            List<Integer> clusterPoints = clusters.get(clusterId);
            if (clusterPoints.isEmpty()) {
                continue;
            }
            
            for (int pointIdx : clusterPoints) {
                // 计算a(i): 点到自己所在簇其他点的平均距离
                double a = 0.0;
                if (clusterPoints.size() > 1) {
                    double sum = 0.0;
                    for (int otherPointIdx : clusterPoints) {
                        if (pointIdx != otherPointIdx) {
                            sum += calculateDistance(vectors.get(pointIdx), vectors.get(otherPointIdx));
                        }
                    }
                    a = sum / (clusterPoints.size() - 1);
                }
                
                // 计算b(i): 点到最近的其他簇的平均距离
                double b = Double.MAX_VALUE;
                for (int otherClusterId : clusters.keySet()) {
                    if (otherClusterId != clusterId && !clusters.get(otherClusterId).isEmpty()) {
                        double sum = 0.0;
                        List<Integer> otherClusterPoints = clusters.get(otherClusterId);
                        for (int otherPointIdx : otherClusterPoints) {
                            sum += calculateDistance(vectors.get(pointIdx), vectors.get(otherPointIdx));
                        }
                        double avgDistance = sum / otherClusterPoints.size();
                        b = Math.min(b, avgDistance);
                    }
                }
                
                // 计算轮廓系数: s(i) = (b - a) / max(a, b)
                double s;
                if (a == 0 && b == 0) {
                    s = 0;
                } else {
                    s = (b - a) / Math.max(a, b);
                }
                
                totalSilhouette += s;
                totalPoints++;
            }
        }
        
        return totalPoints > 0 ? totalSilhouette / totalPoints : 0;
    }
    
    /**
     * 初始化簇中心点
     */
    private static List<double[]> initializeCentroids(List<double[]> vectors, int k, Random random, boolean useKMeansPlusPlus) {
        if (useKMeansPlusPlus) {
            return initializeCentroidsKMeansPlusPlus(vectors, k, random);
        } else {
            return initializeCentroidsRandom(vectors, k, random);
        }
    }
    
    /**
     * 随机初始化簇中心点
     */
    private static List<double[]> initializeCentroidsRandom(List<double[]> vectors, int k, Random random) {
        // 随机选择k个向量作为初始中心点
        List<double[]> centroids = new ArrayList<>(k);
        Set<Integer> selectedIndices = new HashSet<>();
        
        while (selectedIndices.size() < k) {
            int idx = random.nextInt(vectors.size());
            if (!selectedIndices.contains(idx)) {
                selectedIndices.add(idx);
                centroids.add(vectors.get(idx).clone());
            }
        }
        
        return centroids;
    }
    
    /**
     * 使用K-means++算法初始化簇中心点
     */
    private static List<double[]> initializeCentroidsKMeansPlusPlus(List<double[]> vectors, int k, Random random) {
        List<double[]> centroids = new ArrayList<>(k);
        
        // 随机选择第一个中心点
        int firstCentroidIdx = random.nextInt(vectors.size());
        centroids.add(vectors.get(firstCentroidIdx).clone());
        
        // 选择剩余的中心点
        for (int i = 1; i < k; i++) {
            // 计算每个点到最近中心点的距离
            double[] distances = new double[vectors.size()];
            double sum = 0;
            
            for (int j = 0; j < vectors.size(); j++) {
                double minDistance = Double.MAX_VALUE;
                for (double[] centroid : centroids) {
                    double distance = calculateDistance(vectors.get(j), centroid);
                    minDistance = Math.min(minDistance, distance);
                }
                
                distances[j] = minDistance * minDistance;
                sum += distances[j];
            }
            
            // 按距离加权随机选择下一个中心点
            double rand = random.nextDouble() * sum;
            double cumulativeSum = 0;
            int selectedIdx = 0;
            
            for (int j = 0; j < vectors.size(); j++) {
                cumulativeSum += distances[j];
                if (cumulativeSum >= rand) {
                    selectedIdx = j;
                    break;
                }
            }
            
            centroids.add(vectors.get(selectedIdx).clone());
        }
        
        return centroids;
    }
    
    /**
     * 将向量分配到最近的簇
     */
    private static Map<Integer, List<Integer>> assignToClusters(List<double[]> vectors, List<double[]> centroids) {
        Map<Integer, List<Integer>> clusters = new HashMap<>();
        for (int i = 0; i < centroids.size(); i++) {
            clusters.put(i, new ArrayList<>());
        }
        
        for (int i = 0; i < vectors.size(); i++) {
            double minDistance = Double.MAX_VALUE;
            int closestCentroidIdx = 0;
            
            for (int j = 0; j < centroids.size(); j++) {
                double distance = calculateDistance(vectors.get(i), centroids.get(j));
                if (distance < minDistance) {
                    minDistance = distance;
                    closestCentroidIdx = j;
                }
            }
            
            clusters.get(closestCentroidIdx).add(i);
        }
        
        return clusters;
    }
    
    /**
     * 更新簇中心点
     */
    private static List<double[]> updateCentroids(List<double[]> vectors, Map<Integer, List<Integer>> clusters, int dimension) {
        List<double[]> newCentroids = new ArrayList<>(clusters.size());
        
        for (int i = 0; i < clusters.size(); i++) {
            List<Integer> clusterPoints = clusters.get(i);
            
            if (clusterPoints.isEmpty()) {
                // 如果簇为空，保持原来的中心点
                newCentroids.add(new double[dimension]);
                continue;
            }
            
            double[] centroid = new double[dimension];
            
            // 计算簇中所有点的平均值
            for (int pointIdx : clusterPoints) {
                double[] point = vectors.get(pointIdx);
                for (int j = 0; j < dimension; j++) {
                    centroid[j] += point[j];
                }
            }
            
            for (int j = 0; j < dimension; j++) {
                centroid[j] /= clusterPoints.size();
            }
            
            newCentroids.add(centroid);
        }
        
        return newCentroids;
    }
    
    /**
     * 计算两个向量之间的欧几里得距离
     */
    private static double calculateDistance(double[] v1, double[] v2) {
        double sum = 0;
        for (int i = 0; i < v1.length; i++) {
            double diff = v1[i] - v2[i];
            sum += diff * diff;
        }
        return Math.sqrt(sum);
    }
    
    /**
     * 计算簇中心点的移动距离
     */
    private static double calculateCentroidShift(List<double[]> oldCentroids, List<double[]> newCentroids) {
        double maxShift = 0;
        for (int i = 0; i < oldCentroids.size(); i++) {
            double shift = calculateDistance(oldCentroids.get(i), newCentroids.get(i));
            maxShift = Math.max(maxShift, shift);
        }
        return maxShift;
    }
    
    /**
     * 计算聚类结果的评分（簇内距离平方和）
     */
    private static double calculateScore(List<double[]> vectors, Map<Integer, List<Integer>> clusters, List<double[]> centroids) {
        double score = 0;
        
        for (int i = 0; i < centroids.size(); i++) {
            List<Integer> clusterPoints = clusters.get(i);
            
            for (int pointIdx : clusterPoints) {
                double distance = calculateDistance(vectors.get(pointIdx), centroids.get(i));
                score += distance * distance;
            }
        }
        
        return score;
    }
    
    /**
     * 计算Davies-Bouldin指数(Davies-Bouldin Index)
     * Davies-Bouldin指数是衡量聚类质量的一个指标，值越小表示聚类效果越好
     * 
     * 特性：基于簇内分散度与簇间距离的比率
     * 适用：密度相似但形状多变的簇，适合检测紧密且分离良好的簇
     * 优势：对噪声不敏感，计算效率高
     */
    private static double calculateDaviesBouldinIndex(List<double[]> vectors, Map<Integer, List<Integer>> clusters, List<double[]> centroids) {
        if (clusters.size() <= 1) {
            return Double.MAX_VALUE; // 只有一个簇时，DB指数无意义，返回一个很大的值
        }
        
        // 计算每个簇的平均距离（簇内散布度）
        Map<Integer, Double> clusterScatter = new HashMap<>();
        for (int clusterId : clusters.keySet()) {
            List<Integer> clusterPoints = clusters.get(clusterId);
            if (clusterPoints.isEmpty()) {
                clusterScatter.put(clusterId, 0.0);
                continue;
            }
            
            double sumDistances = 0.0;
            for (int pointIdx : clusterPoints) {
                sumDistances += calculateDistance(vectors.get(pointIdx), centroids.get(clusterId));
            }
            double avgDistance = sumDistances / clusterPoints.size();
            clusterScatter.put(clusterId, avgDistance);
        }
        
        // 计算每对簇之间的Davies-Bouldin指数
        double dbSum = 0.0;
        for (int i : clusters.keySet()) {
            double maxRatio = 0.0;
            for (int j : clusters.keySet()) {
                if (i != j) {
                    // 计算簇中心之间的距离
                    double centerDistance = calculateDistance(centroids.get(i), centroids.get(j));
                    if (centerDistance > 0) {
                        // 计算相似度比率: (Si + Sj) / dij
                        double ratio = (clusterScatter.get(i) + clusterScatter.get(j)) / centerDistance;
                        maxRatio = Math.max(maxRatio, ratio);
                    }
                }
            }
            dbSum += maxRatio;
        }
        
        // 计算平均值作为最终的Davies-Bouldin指数
        return dbSum / clusters.size();
    }
    
    /**
     * 生成随机向量，用于测试
     */
    public static List<double[]> generateRandomVectors(int numVectors, int dimension, Random random) {
        List<double[]> vectors = new ArrayList<>(numVectors);
        for (int i = 0; i < numVectors; i++) {
            double[] vector = new double[dimension];
            for (int j = 0; j < dimension; j++) {
                vector[j] = random.nextDouble();
            }
            vectors.add(vector);
        }
        return vectors;
    }
} 