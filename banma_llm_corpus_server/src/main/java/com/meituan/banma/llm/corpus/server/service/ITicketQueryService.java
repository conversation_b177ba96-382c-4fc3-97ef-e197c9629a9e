package com.meituan.banma.llm.corpus.server.service;

import com.meituan.banma.llm.corpus.api.exception.LlmCorpusException;
import com.meituan.banma.llm.corpus.server.common.domain.dto.QueryTTInfoDTO;
import com.meituan.banma.llm.corpus.server.common.domain.dto.RgInfoDTO;
import com.meituan.banma.llm.corpus.server.common.domain.dto.RgListItemDTO;
import com.meituan.banma.llm.corpus.server.common.domain.dto.RgUserInfo;
import com.meituan.banma.llm.corpus.server.common.domain.dto.TTInfoDTO;
import com.meituan.banma.llm.corpus.server.common.domain.dto.TicketDetailDTO;
import com.meituan.banma.llm.corpus.server.common.domain.dto.BatchAddTTContentResultDTO;
import com.meituan.banma.llm.corpus.server.common.domain.dto.TicketRangeResultDTO;
import com.meituan.banma.llm.corpus.server.common.domain.dto.DynamicTemplateResultDTO;
import com.meituan.banma.llm.corpus.server.dal.entity.KnowledgeBaseVersionEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/4
 */
public interface ITicketQueryService {
    /**
     * 根据值班组查询 tt 列表
     * 
     * @param misId 用户ID
     * @param rgId 组织id
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @param createdAtStart 创建开始时间
     * @param createdAtEnd 创建结束时间
     * @return tt 列表分页结果
     */
    QueryTTInfoDTO queryTTListByRgIdALL(String misId, Long rgId, int pageNum, int pageSize, Long createdAtStart, Long createdAtEnd);

    QueryTTInfoDTO queryTTListByRgId(String misId, Long rgId, int pageNum, int pageSize, Long createdAtStart, Long createdAtEnd);

    /**
     * 查询范围工单信息
     *
     * @param misId 用户MIS ID
     * @param createdAtStart 创建开始时间，格式：yyyy-MM-dd
     * @param createdAtEnd 创建结束时间，格式：yyyy-MM-dd
     * @param state 工单状态，多个状态以逗号分隔
     * @return 工单范围查询结果DTO
     */
    TicketRangeResultDTO queryRangeTicketsInfo(String misId, String createdAtStart, String createdAtEnd, String state);

    /**
     * 根据模板名称查询范围工单信息
     *
     * @param misId 用户MIS ID
     * @param createdAtStart 创建开始时间，格式：yyyy-MM-dd
     * @param createdAtEnd 创建结束时间，格式：yyyy-MM-dd
     * @param state 工单状态，多个状态以逗号分隔
     * @param template 模板名称，例如"违规订单--餐损申诉"
     * @return 工单范围查询结果DTO
     */
    DynamicTemplateResultDTO queryRangeTicketsInfoByTemplate(String misId, String createdAtStart, String createdAtEnd, String state, String template);

    /**
     * 根据ticketId查询语料
     * @param ticketIds
     * @return
     */
    public List<KnowledgeBaseVersionEntity> queryModelOutputByTicketIds(List<String> ticketIds, Long rgId);

    /**
     * 根据ticket id获取TT大象群组id
     *
     * @param ticketId TT id
     * @return 大象群组id
     */
    Long getDxGroupIdByTicketId(String misId, String ticketId);

    /**
     * 查询当前用户所有值班组
     * 
     * @return 值班组列表
     */
    List<RgInfoDTO> queryRGList(String misId, int pageNum, int pageSize);

    RgListItemDTO queryRgInfoByRgId(String misId, Long rgId);
    /**
     * 根据ticket id获取TT信息
     * @param ticketId
     * @return
     */
    TTInfoDTO getTTInfoByTicketId(String misId, String ticketId, Long groupId);

    /**
     * 根据TT id添加TT内容
     * web端入口
     */
    String addTTContentByTTId(Long empId, String misId, String ticketId) throws Exception;

    /**
     * 批量根据TT id添加TT内容
     *
     * @param empId 员工ID
     * @param misId 用户MIS ID
     * @param ticketIds TT ID列表
     * @return 处理结果DTO，包含成功和失败的信息
     * @throws LlmCorpusException 处理异常
     */
    BatchAddTTContentResultDTO batchAddTTContentByTTIds(Long empId, String misId, List<String> ticketIds) throws LlmCorpusException;

    /**
     * 根据rgId查询管理员信息
     * @param misId
     * @param rgId
     * @return
     */
    List<RgUserInfo> queryRgAdminUserInfoByRgId(String misId, Long rgId);

    /**
     * 根据rgId查询管理员信息
     * @param misId
     * @param rgId
     * @return
     */
    List<RgUserInfo> queryRgNormalUserInfoByRgId(String misId, Long rgId);
    /**
     * 根据日期查询TT列表
     * @param misId
     * @param startDate
     * @param endDate
     * @return
     */
    List<TicketDetailDTO> queryTicketListWithDate(String misId,Long rgId, Long startDate, Long endDate);
}
