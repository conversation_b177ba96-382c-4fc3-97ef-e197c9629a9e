package com.meituan.banma.llm.corpus.server.service.impl;

import com.alibaba.fastjson.JSON;
import com.dianping.lion.shade.org.apache.curator.shaded.com.google.common.collect.Lists;
import com.meituan.banma.llm.corpus.api.constants.BizCode;
import com.meituan.banma.llm.corpus.api.exception.LlmCorpusException;
import com.meituan.banma.llm.corpus.server.common.MtConfigService;
import com.meituan.banma.llm.corpus.server.common.constants.enums.ConvertTaskPlatformId;
import com.meituan.banma.llm.corpus.server.common.constants.enums.ModelOutputTaskStatusEnum;
import com.meituan.banma.llm.corpus.server.common.domain.dto.PageDTO;
import com.meituan.banma.llm.corpus.server.common.domain.dto.RgListItemDTO;
import com.meituan.banma.llm.corpus.server.controller.request.km.SplitContentForm;
import com.meituan.banma.llm.corpus.server.dal.entity.ModelOutputTaskEntity;
import com.meituan.banma.llm.corpus.server.dal.entity.RgDatasetDocumentEntity;
import com.meituan.banma.llm.corpus.server.dal.entity.RgDocumentUrlEntity;
import com.meituan.banma.llm.corpus.server.dal.mapper.ModelOutputMapper;
import com.meituan.banma.llm.corpus.server.dal.mapper.RgDatasetDocumentMapper;
import com.meituan.banma.llm.corpus.server.dal.mapper.RgDocumentUrlMapper;
import com.meituan.banma.llm.corpus.server.rpc.dx.XmAuthRpcService;
import com.meituan.banma.llm.corpus.server.rpc.friday.FridayRpcService;
import com.meituan.banma.llm.corpus.server.rpc.friday.dto.LlmKmToCorpusDTO;
import com.meituan.banma.llm.corpus.server.service.IDocumentRetryService;
import com.meituan.banma.llm.corpus.server.service.IFridayService;
import com.meituan.banma.llm.corpus.server.service.IKmService;
import com.meituan.banma.llm.corpus.server.service.IKnowledgeBaseService;
import com.meituan.banma.llm.corpus.server.service.IReviewService;
import com.meituan.banma.llm.corpus.server.service.ITicketQueryService;
import com.meituan.banma.llm.corpus.server.service.WorkspaceService;
import com.meituan.banma.llm.corpus.server.utils.AsyncTaskUtils;
import com.meituan.banma.llm.corpus.server.utils.HtmlContentUtil;
import com.meituan.banma.llm.corpus.server.utils.JsonToHtmlConverterUtil;
import com.sankuai.dxenterprise.open.gateway.service.citadel.api.CitadelService;
import com.sankuai.dxenterprise.open.gateway.service.citadel.api.req.GetCollaborationContentBySsoReq;
import com.sankuai.dxenterprise.open.gateway.service.citadel.api.req.GetContentMetaBySsoReq;
import com.sankuai.dxenterprise.open.gateway.service.citadel.api.resp.GetCollaborationContentBySsoResp;
import com.sankuai.dxenterprise.open.gateway.service.citadel.api.resp.GetContentMetaBySsoResp;
import com.sankuai.xm.openplatform.common.entity.RespStatus;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.context.annotation.Lazy;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.sql.Timestamp;
import java.util.ArrayList;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class KmServiceImpl implements IKmService {
    @Autowired
    private RgDatasetDocumentMapper rgDatasetDocumentMapper;
    @Autowired
    private RgDocumentUrlMapper rgDocumentUrlMapper;
    @Autowired
    private CitadelService citadelService;
    @Autowired
    private XmAuthRpcService xmAuthRpcService;
    @Autowired
    private IFridayService fridayService;
    @Autowired
    private IKnowledgeBaseService knowledgeBaseService;
    @Autowired
    ModelOutputMapper modelOutputMapper;
    @Lazy
    @Autowired
    private IReviewService reviewService;
    @Autowired
    private FridayRpcService fridayRpcService;
    @Autowired
    private MtConfigService mtConfigService;
    @Lazy
    @Resource
    private WorkspaceService workspaceService;
    @Autowired
    private ITicketQueryService ticketQueryService;
    @Lazy
    @Autowired
    private IDocumentRetryService documentRetryService;
    /**
     * 异步检查URL及获取文档元数据
     * @param url 文档URL
     * @param rgId 值班组ID
     * @param misId 用户ID
     * @return CompletableFuture包含检查结果Map
     */
    @Override
    public CompletableFuture<Map<String, Object>> checkUrlAndGetMetaAsync(String url, long rgId, String misId) {
        ExecutorService checkUrlThreadPool = AsyncTaskUtils.getCheckUrlThreadPool();
        
        // 使用支持ThreadLocal传递的异步任务
        return AsyncTaskUtils.supplyAsyncWithThreadLocal(checkUrlThreadPool, () -> {
            Map<String, Object> result = new HashMap<>();
            try {
                // 检查用户权限
                boolean permission = knowledgeBaseService.validateUserPermission(misId, rgId);
                if (!permission) {
                    result.put("canAdd", Boolean.FALSE);
                    result.put("reason", "用户无对应值班组权限");
                    return result;
                }
                
                // 并行执行获取文档访问权限和元数据
                CompletableFuture<Boolean> hasAccessFuture = AsyncTaskUtils.supplyAsyncWithThreadLocal(
                    checkUrlThreadPool, () -> {
                        try {
                            return getDocAccessByUrl(url, rgId, misId);
                        } catch (Exception e) {
                            log.error("#checkUrlAndGetMetaAsync# getDocAccessByUrl异步执行失败, url:{}, rgId:{}, misId:{}, 异常原因:{}", 
                                    url, rgId, misId, e.getMessage(), e);
                            return false;
                        }
                    });
                
                CompletableFuture<GetContentMetaBySsoResp> kmContentFuture = AsyncTaskUtils.supplyAsyncWithThreadLocal(
                    checkUrlThreadPool, () -> {
                        try {
                            return getKmMetaByUrl(url, rgId, misId);
                        } catch (Exception e) {
                            log.error("#checkUrlAndGetMetaAsync# getKmMetaByUrl异步执行失败, url:{}, rgId:{}, misId:{}, 异常原因:{}", 
                                    url, rgId, misId, e.getMessage(), e);
                            return null;
                        }
                    });
                
                // 等待两个异步任务完成
                CompletableFuture.allOf(hasAccessFuture, kmContentFuture).join();
                
                // 处理结果
                
                GetContentMetaBySsoResp kmContent = kmContentFuture.join();
                if (kmContent != null && kmContent.getData() != null && kmContent.getData().getContentMetaDataDTO() != null) {
                    result.put("contentId", kmContent.getData().getContentMetaDataDTO().getContentId());
                    result.put("contentTitle", kmContent.getData().getContentMetaDataDTO().getContentTitle());
                    boolean hasAccess = hasAccessFuture.join();
                    if (!hasAccess) {
                        result.put("canAdd", Boolean.FALSE);
                        result.put("reason", "文档无管理权限, 无法添加");
                        return result;
                    } else {
                        result.put("canAdd", Boolean.TRUE);
                        result.put("reason", "文档url正确, 可以解析");
                        return result;
                    }
                } else {
                    result.put("contentId", null);
                    result.put("contentTitle", null);
                    result.put("canAdd", Boolean.FALSE);
                    result.put("reason", "解析学城文档基本信息失败，该文档可能不存在");
                }
            } catch (Exception e) {
                log.error("#checkUrlAndGetMetaAsync# 异步执行失败, url:{}, rgId:{}, misId:{}, 异常原因:{}", 
                          url, rgId, misId, e.getMessage(), e);
                result.put("canAdd", Boolean.FALSE);
                result.put("reason", e.getMessage());
            }
            return result;
        });
    }

    /**
     * 异步检查URL是否存在，并获取元数据
     * @param url 文档URL
     * @param rgId 值班组ID
     * @param misId 用户ID
     * @return CompletableFuture包含检查结果Map
     */
    @Override
    public CompletableFuture<Map<String, Object>> checkUrlExistsAsync(String url, long rgId, String misId) {
        ExecutorService checkUrlThreadPool = AsyncTaskUtils.getCheckUrlThreadPool();
        
        // 使用支持ThreadLocal传递的异步任务
        return AsyncTaskUtils.supplyAsyncWithThreadLocal(checkUrlThreadPool, () -> {
            Map<String, Object> result = new HashMap<>();
            try {
                // 验证权限
                boolean permission = knowledgeBaseService.validateUserPermission(misId, rgId);
                if (!permission) {
                    result.put("exists", Boolean.FALSE);
                    result.put("canAdd", Boolean.FALSE);
                    result.put("reason", "用户无对应值班组权限");
                    return result;
                }

                // 执行查询
                int existingCount = rgDocumentUrlMapper.findDistinctByUrlAndRgId(url, rgId);
                if (existingCount > 0) {
                    result.put("exists", Boolean.TRUE);
                    result.put("canAdd", Boolean.FALSE);
                    result.put("reason", "URL已存在,无法添加");
                    return result;
                }
                
                result.put("exists", Boolean.FALSE);
                
                // URL不存在，并行检查URL有效性和获取元数据
                CompletableFuture<GetContentMetaBySsoResp> kmContentFuture = AsyncTaskUtils.supplyAsyncWithThreadLocal(
                    checkUrlThreadPool, () -> {
                        try {
                            return getKmMetaByUrl(url, rgId, misId);
                        } catch (Exception e) {
                            log.error("#checkUrlExistsAsync# getKmMetaByUrl异步执行失败, url:{}, rgId:{}, misId:{}, 异常原因:{}", 
                                    url, rgId, misId, e.getMessage(), e);
                            return null;
                        }
                    });
                
                CompletableFuture<Boolean> accessFuture = AsyncTaskUtils.supplyAsyncWithThreadLocal(
                    checkUrlThreadPool, () -> {
                        try {
                            return getDocAccessByUrl(url, rgId, misId);
                        } catch (Exception e) {
                            log.error("#checkUrlExistsAsync# getDocAccessByUrl异步执行失败, url:{}, rgId:{}, misId:{}, 异常原因:{}", 
                                    url, rgId, misId, e.getMessage(), e);
                            return false;
                        }
                    });

                // 等待两个异步任务完成
                CompletableFuture.allOf(kmContentFuture, accessFuture).join();
                
                // 处理结果
                GetContentMetaBySsoResp kmContent = kmContentFuture.join();
                if (kmContent != null && kmContent.getData() != null && kmContent.getData().getContentMetaDataDTO() != null) {
                    result.put("contentId", kmContent.getData().getContentMetaDataDTO().getContentId());
                    result.put("contentTitle", kmContent.getData().getContentMetaDataDTO().getContentTitle());
                    
                    boolean docAccessByContentId = accessFuture.join();
                    if (docAccessByContentId) {
                        result.put("canAdd", Boolean.TRUE);
                        result.put("reason", "文档url正确, 可以添加");
                    } else {
                        result.put("canAdd", Boolean.FALSE);
                        result.put("reason", "文档无管理权限, 无法添加");
                    }
                } else {
                    result.put("contentId", 0L);
                    result.put("contentTitle", "");
                    result.put("canAdd", Boolean.FALSE);
                    result.put("reason", "解析学城文档基本信息失败，该文档可能不存在");
                }
            } catch (Exception e) {
                log.error("#checkUrlExistsAsync# 异步执行失败, url:{}, rgId:{}, misId:{}, 异常原因:{}", 
                          url, rgId, misId, e.getMessage(), e);
                result.put("exists", Boolean.FALSE);
                result.put("canAdd", Boolean.FALSE);
                result.put("reason", e.getMessage());
            }
            return result;
        });
    }

    @Override
    public GetContentMetaBySsoResp getKmMetaByUrl(String url, long rgId, String misId) throws LlmCorpusException {
        long contentId = 0L;
        try {
            contentId = extractNumberFromUrl(mtConfigService.getKmUrlRegex(), url);
            if (contentId == 0L) {
                throw LlmCorpusException.buildWithMsg(BizCode.META_DEAL_ERROR, "文档基本信息解析失败，学城文档url解析失败");
            }
            String accessToken = xmAuthRpcService.getToken();
            String kmSsoidToken = xmAuthRpcService.getKmSsoidToken();
            GetContentMetaBySsoReq req = new GetContentMetaBySsoReq();
            GetContentMetaBySsoReq.Request request = new GetContentMetaBySsoReq.Request();
            request.setContentId(contentId);
            req.setRequest(request);
            return citadelService.getContentMetaBySso(accessToken, kmSsoidToken, req);
        } catch (LlmCorpusException e) {
            throw e;
        } catch (Exception e) {
            log.error("#getKmMetaByContentId# 查询km元信息失败,contentId:{}", contentId, e);
            throw LlmCorpusException.buildWithMsg(BizCode.META_DEAL_ERROR, e.getMessage());
        }
    }


    private Object getKmByContentId(long contentId, long rgId, String misId) throws LlmCorpusException {
        try {
            String accessToken = xmAuthRpcService.getToken();
            String kmSsoidToken = xmAuthRpcService.getKmSsoidToken();
            GetCollaborationContentBySsoReq req = new GetCollaborationContentBySsoReq();
            req.setContentId(contentId);
            GetCollaborationContentBySsoResp resp = citadelService.getCollaborationContentBySso(accessToken, kmSsoidToken, req);
            RespStatus status = resp.getStatus();
            if (status.getCode() == 0) {
                return resp.getData().getContent();
            }else {
                String msg = status.getMsg();
                if(msg.isEmpty()){
                    msg= "此学城文档不存在";
                }
                log.error("#getKmByContentId# 查询km内容失败,contentId:{}", contentId);
                throw LlmCorpusException.buildWithMsg(BizCode.AUTHOR_ERROR, msg);
            }
        } catch (LlmCorpusException e){
            throw e;
        } catch (Exception e) {
            log.error("#getKmByContentId# 查询km内容失败,contentId:{}", contentId, e);
            throw LlmCorpusException.buildWithMsg(BizCode.GET_CONTENT_ERROR, e.getMessage());
        }
    }

    @Override
    public String testSimplfyBody(String body) {
        String simplifyBody = HtmlContentUtil.cleanAndSimplify(body);
        return simplifyBody;
    }

    @Override
    public boolean getDocAccessByUrl(String url, long rgId, String misId) throws LlmCorpusException {
        validateUserPermission(misId, rgId);
        try {
            long contentId = extractNumberFromUrl(mtConfigService.getKmUrlRegex(), url);
            String body = (String) getKmByContentId(contentId, rgId, misId);
            return !body.isEmpty();
        } catch (Exception e) {
            log.error("#getDocAccessByUrl# 执行失败, url无管理权限或为空, url:{}", url, e);
            throw LlmCorpusException.buildWithMsg(BizCode.GET_CONTENT_ERROR.getCode(), e.getMessage());
        }
    }

    @Override
    public Object getKmSplitByContentId(long contentId, long rgId, String misId, int isTable) throws LlmCorpusException {
        validateUserPermission(misId, rgId);
        try {
            String body = (String) getKmByContentId(contentId, rgId, misId);
            // 获取JsonToHtmlConverterUtil实例
            JsonToHtmlConverterUtil converter = JsonToHtmlConverterUtil.getInstance();
            String simplifyBody = converter.convertJsonToHtml(body);
            List<String> strings;
            if (isTable == 1) {
                strings = converter.parseTables(simplifyBody);
            }else {
                strings = converter.convertHtmlToList(simplifyBody);
            }
            return strings;
        } catch (Exception e) {
            log.error("#getKmSplitByContentId# 执行失败,contentId:{}", contentId, e);
            throw LlmCorpusException.buildWithMsg(BizCode.PARAM_ERROR, e.getMessage());
        }
    }

    @Override
    public Object getKmHtmlByContentId(long contentId, long rgId, String misId) throws LlmCorpusException {
        validateUserPermission(misId, rgId);
        try {
            String body = (String) getKmByContentId(contentId, rgId, misId);
            // 获取JsonToHtmlConverterUtil实例
            JsonToHtmlConverterUtil converter = JsonToHtmlConverterUtil.getInstance();
            String simplifyBody = converter.convertJsonToHtml(body);
            return simplifyBody;
        } catch (Exception e) {
            log.error("#getKmHtmlByContentId# 执行失败,contentId:{}", contentId, e);
            throw LlmCorpusException.buildWithMsg(BizCode.GET_CONTENT_ERROR.getCode(),e.getMessage());
        }
    }

    /**
     * 将km文档转换为语料,纯ai切分
     * @param contentId
     * @param rgId
     * @param misId
     * @return
     * @throws LlmCorpusException
     */
    @Override
    public String kmToCorpusByContentId(long contentId, long rgId, String misId) throws LlmCorpusException {
        validateUserPermission(misId, rgId);
        try {
            String body = (String) getKmByContentId(contentId, rgId, misId);
            String simplifyBody = HtmlContentUtil.cleanAndSimplify(body);
            return createConvertKmToKnowledgeTask(simplifyBody, rgId, misId, false);
        } catch (Exception e) {
            log.error("#kmToCorpusByContentId# 执行失败,contentId:{}", contentId, e);
            throw LlmCorpusException.buildWithMsg(BizCode.FRIDAY_KM_TO_CORPUS_ERROR,e.getMessage());
        }
    }

    /**
     * 将qa文档转换为语料,人工切分完毕
     * @param splitContentForm
     * @param rgId
     * @param misId
     * @return
     * @throws LlmCorpusException
     */
    @Override
    public String splitContentToCorpus(SplitContentForm splitContentForm, long rgId, String misId) throws LlmCorpusException {
        validateUserPermission(misId, rgId);
        long contentId = splitContentForm.getContentId();
        try {
            if (contentId == 0L) {
                throw new LlmCorpusException(BizCode.PARAM_ERROR.getCode(), "contentId为空");
            }
            List<String> splitContent = splitContentForm.getData();
            String splitBody = String.join("\n\n", splitContent);
            return createConvertKmToKnowledgeTask(splitBody, rgId, misId, true);
        } catch (Exception e) {
            log.error("#splitContentToCorpus# 执行失败,contentId:{}", contentId, e);
            throw new RuntimeException(e);
        }
    }

    public String createConvertKmToKnowledgeTask(String body, long rgId, String misId, boolean isSplit){
        // 1. 获取简化后的body内容，通过大模型两步将切分后的内容转换为语料
        // 2. 创建异步任务，调用friday接口转换
        // 3. 任务信息落数据库
        // 4. 更新任务状态
        String taskId = UUID.randomUUID().toString();
        ExecutorService executor = AsyncTaskUtils.getLlmCorpusConvertTaskThreadPool();
        // 初始化任务记录
        initTaskRecord(taskId, rgId, misId);

        executor.execute(() -> {
            try {
                LlmKmToCorpusDTO.LlmKmToCorpusTaskResultDTO llmKmToCorpusTaskResultDTO = fridayService.convertKmToCorups(body, misId, isSplit, rgId);
                if (llmKmToCorpusTaskResultDTO == null) {
                    log.error("#createConvertKmToKnowledgeTask# friday接口返回结果为空,body:{}", body);
                    throw new LlmCorpusException(BizCode.FRIDAY_KM_TO_CORPUS_ERROR.getCode(),
                            "friday接口返回结果为空");
                }

                llmTaskSuccessForKmQa(taskId, llmKmToCorpusTaskResultDTO);
            } catch (LlmCorpusException e) {
                llmTaskFailForKmQa(taskId,e.getMessage());
                log.warn("#createConvertKmToKnowledgeTask# 异步任务执行失败, body:{}", body, e);
            } catch (Exception e) {
                llmTaskFailForKmQa(taskId,"内部异常，任务执行失败！");
                log.error("#createConvertKmToKnowledgeTask# 异步任务执行失败, param:{}", body, e);
            }
        });
        return taskId;
    }

    private void llmTaskFailForKmQa(String taskId, String s) {
        ModelOutputTaskEntity entity = new ModelOutputTaskEntity();
        try {
            entity.setTaskId(taskId);
            entity.setTaskStatus(ModelOutputTaskStatusEnum.FAIL.getCode());
            entity.setTitle("学城FAQ转换处理失败");
            entity.setContent("学城FAQ转换处理失败");
            entity.setTaskMessage(s);
            entity.setTagsIds(null);
            modelOutputMapper.updateModelOutputTaskStatusByTaskId(entity);
        } catch (Exception e) {
            log.error("#llmTaskFailForKmQa# 更新任务状态失败,taskId:{}", taskId, e);
        }
    }

    private void llmTaskSuccessForKmQa(String taskId, LlmKmToCorpusDTO.LlmKmToCorpusTaskResultDTO llmKmToCorpusTaskResultDTO) {
        ModelOutputTaskEntity entity = new ModelOutputTaskEntity();
        try {
            entity.setTaskId(taskId);
            entity.setTaskStatus(ModelOutputTaskStatusEnum.SUCCESS.getCode());
            entity.setTitle("学城FAQ转换处理成功");
            entity.setContent(JSON.toJSONString(llmKmToCorpusTaskResultDTO));
            // 从kmQaList的第一个item中获取tagsIds，如果为空则使用null
            String tagsIds = null;
            if (llmKmToCorpusTaskResultDTO != null && 
                llmKmToCorpusTaskResultDTO.getKmQaList() != null && 
                !llmKmToCorpusTaskResultDTO.getKmQaList().isEmpty()) {
                tagsIds = llmKmToCorpusTaskResultDTO.getKmQaList().get(0).getTagsIds();
            }
            entity.setTagsIds(tagsIds);
            modelOutputMapper.updateModelOutputTaskStatusByTaskId(entity);
        } catch (Exception e) {
            log.error("#llmTaskSuccess# 更新任务状态失败,taskId:{}", taskId, e);
        }
    }

    private void initTaskRecord(String taskId, long rgId, String misId) {
        ModelOutputTaskEntity entity = new ModelOutputTaskEntity();
        entity.setTaskId(taskId);
        entity.setTaskStatus(ModelOutputTaskStatusEnum.PROCESSING.getCode());
        entity.setTicketId("0");
        entity.setCreatorDxId(-1L);
        entity.setDxGroupId(0L);
        entity.setCreatorMisId(misId);
        entity.setCreatorUserName(misId);
        entity.setRgId(rgId);
        entity.setTitle("");
        entity.setContent("");
        entity.setPlatformId(ConvertTaskPlatformId.KM_QA_UPLOADING.getCode());
        entity.setTagsIds("");
        modelOutputMapper.insertModelOutputTask(entity);
    }

    public static Long extractNumberFromUrl(String regexPre,String url) {
        // 定义正则表达式来匹配collabpage/后面的数字
        String finalRegex = regexPre + "(\\d+)";
        Pattern pattern = Pattern.compile(finalRegex);
        Matcher matcher = pattern.matcher(url);

        // 查找并返回匹配到的数字
        if (matcher.find()) {
            return Long.valueOf(matcher.group(1));
        }
        return null;
    }

    @Override
    public PageDTO<RgDocumentUrlEntity> queryDocumentByRgId(long rgId, String misId, String strMatch, int pageNum, int pageSize) throws LlmCorpusException {
        // 验证权限
        validateUserPermission(misId, rgId);
        
        // 参数校验与处理
        if (pageNum < 1) {
            pageNum = 1;
        }
        int offset = (pageNum - 1) * pageSize;
        
        // 获取符合条件的总记录数
        int totalCount = rgDocumentUrlMapper.countDistinctByRgId(rgId, strMatch);
        
        // 如果没有记录，返回空结果
        if (totalCount == 0) {
            return PageDTO.<RgDocumentUrlEntity>builder()
                    .pageNum(pageNum)
                    .pageSize(pageSize)
                    .totalCount(0)
                    .totalPage(0)
                    .data(Collections.emptyList())
                    .build();
        }
        
        // 查询当前页的数据
        List<RgDocumentUrlEntity> documentList = rgDocumentUrlMapper.findDistinctByRgId(rgId, strMatch, pageSize, offset);
        
        // 计算总页数
        int totalPage = (int) Math.ceil((double) totalCount / pageSize);
        
        // 构建并返回分页结果
        return PageDTO.<RgDocumentUrlEntity>builder()
                .pageNum(pageNum)
                .pageSize(pageSize)
                .totalCount(totalCount)
                .totalPage(totalPage)
                .data(documentList)
                .build();
    }

    @Override
    public boolean checkUrlExists(long rgId, String url, String misId) throws LlmCorpusException {
        // 验证权限
        validateUserPermission(misId, rgId);

        // 执行查询
        int existingDocs = rgDocumentUrlMapper.findDistinctByUrlAndRgId(url, rgId);
        return existingDocs > 0;
    }
    
    @Override
    public boolean checkNameExists(long rgId, String name, String misId) throws LlmCorpusException {
        // 验证权限
        validateUserPermission(misId, rgId);
        
        // 直接查询同名记录
        int existingDocs = rgDocumentUrlMapper.findDistinctByNameAndRgId(name, rgId);
        
        return existingDocs > 0;
    }
    
    @Override
    public Map<String, Object> checkDocumentCanAdd(long rgId, String url, String name, String misId) throws LlmCorpusException {
        // 验证权限
        validateUserPermission(misId, rgId);
        
        Map<String, Object> result = new HashMap<>();
        result.put("canAdd", true);
        result.put("reason", "");
        
        // 检查URL是否存在
        boolean checkUrlAndNameExist = checkUrlAndNameExist(rgId, url, name);
        if (checkUrlAndNameExist) {
            result.put("canAdd", false);
            result.put("reason", "已存在同url或同name文档");
            return result;
        }
        return result;
    }

    private boolean checkUrlAndNameExist(long rgId, String url, String name) {
        int byUrlAndNameAndRgId = rgDocumentUrlMapper.findDistinctByUrlNameAndRgId(url, name, rgId);
        return byUrlAndNameAndRgId > 0;
    }

    @Override
    public List<String> batchDeleteByDocumentIds(long rgId, List<String> documentIds, String misId) throws LlmCorpusException {
        // 验证权限
        validateUserPermission(misId, rgId);
        
        // 获取该值班组下的所有工作空间配置
        List<RgDatasetDocumentEntity> rgDatasetDocuments = rgDatasetDocumentMapper.findByRgId(rgId);
        if (rgDatasetDocuments == null || rgDatasetDocuments.isEmpty()) {
            throw new LlmCorpusException(BizCode.DOCUMENT_DEL_ERROR.getCode(), "未找到值班组对应知识库，无法删除");
        }
        
        // 检查用户是否有默认工作空间权限，如果没有，则从列表中移除默认工作空间
        rgDatasetDocuments = filterWorkspacesByPermission(rgDatasetDocuments, misId, rgId);
        
        if (documentIds == null || documentIds.isEmpty()) {
            log.warn("批量删除文档记录时传入空列表, rgId: {}, misId: {}", rgId, misId);
            throw new LlmCorpusException(BizCode.DOCUMENT_DEL_ERROR.getCode(), "documentId列表不能为空");
        }
        
        List<String> failList = Lists.newArrayList(documentIds);
        
        // 从数据库中查询文档的实际URL，并去重
        List<String> distinctUrls = new ArrayList<>();
        Map<String, String> documentIdToUrlMap = new HashMap<>();
        
        for (RgDatasetDocumentEntity workspace : rgDatasetDocuments) {
            String spaceId = workspace.getSpaceId();
            for (String documentId : documentIds) {
                // 根据rgId、spaceId和documentId查询记录
                RgDocumentUrlEntity document = rgDocumentUrlMapper.findByRgIdSpaceIdAndDocumentId(rgId, spaceId, documentId);
                if (document != null && document.getUrl() != null) {
                    // 记录documentId到URL的映射
                    documentIdToUrlMap.put(documentId, document.getUrl());
                    // 添加到去重列表（如果尚未包含）
                    if (!distinctUrls.contains(document.getUrl())) {
                        distinctUrls.add(document.getUrl());
                        log.info("找到文档URL: documentId={}, url={}", documentId, document.getUrl());
                    }
                    break; // 找到一个有效URL后就跳出工作空间循环
                }
            }
        }
        
        // 如果没有找到任何有效URL，记录警告并返回
        if (distinctUrls.isEmpty()) {
            log.warn("未找到任何文档的有效URL, rgId: {}, documentIds: {}", rgId, documentIds);
            return failList;
        }
        
        log.info("根据documentIds找到的去重URL列表: count={}, urls={}", distinctUrls.size(), distinctUrls);
        
        // 保存每个URL在每个工作空间中的删除状态
        Map<String, Map<String, Boolean>> urlDeleteStatus = new HashMap<>();
        for (String url : distinctUrls) {
            urlDeleteStatus.put(url, new HashMap<>());
            for (RgDatasetDocumentEntity document : rgDatasetDocuments) {
                urlDeleteStatus.get(url).put(document.getSpaceId(), false);
            }
        }
        
        // 对每个工作空间配置进行处理
        for (RgDatasetDocumentEntity rgDatasetDocument : rgDatasetDocuments) {
            String currentSpaceId = rgDatasetDocument.getSpaceId();
            String datasetId = rgDatasetDocument.getDatasetId();
            
            try {
                // 获取工作空间的访问令牌
                String accessToken = null;
                if (rgDatasetDocument.getAccessKey() != null && rgDatasetDocument.getAppSecret() != null) {
                    try {
                        accessToken = workspaceService.getWorkspaceAccessToken(
                        rgDatasetDocument.getAccessKey(), rgDatasetDocument.getAppSecret());
                    } catch (Exception e) {
                        log.error("获取工作空间令牌失败: rgId={}, spaceId={}, error={}", 
                                rgId, currentSpaceId, e.getMessage(), e);
                        // 获取令牌失败，该工作空间下的所有URL都标记为删除失败
                        for (String url : distinctUrls) {
                            urlDeleteStatus.get(url).put(currentSpaceId, false);
                        }
                        continue; // 跳过当前工作空间，处理下一个
                    }
                } else {
                    // 没有找到记录或记录中缺少密钥信息
                    log.error("工作空间配置缺少密钥信息: rgId={}, spaceId={}", rgId, currentSpaceId);
                    continue; // 跳过当前工作空间，处理下一个
                }
                
                // 对每个文档ID进行处理
                for (String documentId : documentIds) {
                    String url = documentIdToUrlMap.get(documentId);
                    if (url == null) {
                        log.warn("未找到documentId对应的URL: documentId={}", documentId);
                        continue;
                    }
                    
                    try {
                        // 根据URL查找数据库中的记录
                        RgDocumentUrlEntity entity = rgDocumentUrlMapper.findByRgIdSpaceIdAndUrl(rgId, currentSpaceId, url);
                        
                        if (entity != null) {
                            // 获取真实的documentId
                            String actualDocumentId = entity.getDocumentId();
                            
                            if (datasetId != null && actualDocumentId != null) {
                                // 调用Friday API删除文档
                                fridayRpcService.deleteDocument(datasetId, actualDocumentId, accessToken, misId, currentSpaceId);
                                
                                // 从数据库删除记录
                                int result = rgDocumentUrlMapper.deleteByRgIdSpaceIdAndDocumentId(rgId, currentSpaceId, actualDocumentId);
                                
                                if (result > 0) {
                                    log.info("成功删除文档记录: rgId={}, spaceId={}, documentId={}, url={}", 
                                            rgId, currentSpaceId, documentId, url);
                                    
                                    // 标记为在当前工作空间成功删除
                                urlDeleteStatus.get(url).put(currentSpaceId, true);
                                } else {
                                    log.warn("文档数据库记录删除失败: rgId={}, spaceId={}, documentId={}, url={}", 
                                            rgId, currentSpaceId, documentId, url);
                                    urlDeleteStatus.get(url).put(currentSpaceId, false);
                                }
                            } else {
                                log.warn("缺少删除文档所需的datasetId或actualDocumentId: rgId={}, spaceId={}, documentId={}, url={}", 
                                        rgId, currentSpaceId, documentId, url);
                                urlDeleteStatus.get(url).put(currentSpaceId, false);
                            }
                        } else {
                            // 在当前工作空间中未找到该URL的记录，认为删除成功
                            log.info("在工作空间中未找到文档记录，视为删除成功: rgId={}, spaceId={}, documentId={}, url={}", 
                                    rgId, currentSpaceId, documentId, url);
                            urlDeleteStatus.get(url).put(currentSpaceId, true);
                        }
                    } catch (Exception e) {
                        log.error("删除文档失败: rgId={}, spaceId={}, documentId={}, url={}, error={}", 
                                rgId, currentSpaceId, documentId, url, e.getMessage(), e);
                        urlDeleteStatus.get(url).put(currentSpaceId, false);
                    }
                }
            } catch (Exception e) {
                log.error("获取工作空间访问令牌失败, rgId: {}, spaceId: {}, error: {}", 
                        rgId, currentSpaceId, e.getMessage(), e);
                // 获取令牌失败，该工作空间下的所有URL都标记为删除失败
                for (String url : distinctUrls) {
                    urlDeleteStatus.get(url).put(currentSpaceId, false);
                }
            }
        }
        
        // 检查哪些URL在所有工作空间中都删除成功
        List<String> trulySuccessList = new ArrayList<>();
        for (String documentId : documentIds) {
            String url = documentIdToUrlMap.get(documentId);
            if (url == null) {
                log.warn("未找到documentId对应的URL: documentId={}", documentId);
                continue;
            }
            
            boolean allSuccess = true;
            Map<String, Boolean> spaceStatus = urlDeleteStatus.get(url);
            
            if (spaceStatus != null) {
            for (Boolean success : spaceStatus.values()) {
                if (!success) {
                    allSuccess = false;
                    break;
                }
            }
            
            if (allSuccess) {
                    // 所有工作空间中都删除成功
                    trulySuccessList.add(documentId);
                    log.info("文档在所有工作空间删除成功: rgId={}, documentId={}, url={}", 
                            rgId, documentId, url);
                } else {
                    log.warn("文档在部分工作空间删除失败: rgId={}, documentId={}, url={}", 
                            rgId, documentId, url);
                }
            }
        }

        failList.removeAll(trulySuccessList);
        
        return failList;
    }

    @Override
    public List<String> addBatchDocumentByNameAndUrl(long rgId, List<String> nameList, List<String> urlList, List<Integer> autoUpdateList, String misId) throws LlmCorpusException {
        // 验证权限
        validateUserPermission(misId, rgId);
        
        // 获取该值班组下的所有工作空间配置
        List<RgDatasetDocumentEntity> rgDatasetDocuments = rgDatasetDocumentMapper.findByRgId(rgId);
        if (rgDatasetDocuments == null || rgDatasetDocuments.isEmpty()) {
            log.info("addBatchDocumentByNameAndUrl# 未找到rgId={}的工作空间配置", rgId);
//            Long rgIdLong = Long.valueOf(rgId);
//            // 调用WorkspaceService创建默认工作空间
//            boolean success = workspaceService.createDefaultWorkspace(rgIdLong, misId);
//            if (!success) {
//                log.warn("MisIdInterceptor# rgId={}创建默认工作空间配置失败", rgId);
//                throw new LlmCorpusException(BizCode.WORKSPACE_NOT_FOUND.getCode(), "找不到对应的工作空间配置，请先添加工作空间");
//            }
        }
        
        // 检查用户是否有默认工作空间权限，如果没有，则从列表中移除默认工作空间
        rgDatasetDocuments = filterWorkspacesByPermission(rgDatasetDocuments, misId, rgId);

        log.info("开始批量添加文档记录, rgId: {}, misId: {}, 总数: {}, 工作空间数: {}",
                rgId, misId, nameList.size(), rgDatasetDocuments.size());

        List<String> failUrl = Lists.newArrayList(urlList);

        // 记录每个URL在各个工作空间的上传状态
        Map<String, Map<String, Boolean>> urlUploadStatus = new HashMap<>();
        for (String url : urlList) {
            urlUploadStatus.put(url, new HashMap<>());
            for (RgDatasetDocumentEntity document : rgDatasetDocuments) {
                urlUploadStatus.get(url).put(document.getSpaceId(), false);
            }
        }

        // 保存每个URL对应的documentId和name，用于后续查询
        Map<String, String> urlToDocumentId = new HashMap<>();
        Map<String, String> urlToName = new HashMap<>();
        for (int i = 0; i < urlList.size(); i++) {
            urlToName.put(urlList.get(i), nameList.get(i));
        }

        // 对每个工作空间配置进行处理
        for (RgDatasetDocumentEntity rgDatasetDocument : rgDatasetDocuments) {
            String currentSpaceId = rgDatasetDocument.getSpaceId();
            String datasetId = rgDatasetDocument.getDatasetId();

            try {
                // 获取工作空间的访问令牌
                String accessToken = workspaceService.getWorkspaceAccessToken(
                        rgDatasetDocument.getAccessKey(), rgDatasetDocument.getAppSecret());

                // 生成文档实体列表
                for (int i = 0; i < nameList.size(); i++) {
                    String name = nameList.get(i);
                    String url = urlList.get(i);
                    int autoUpdateGet = autoUpdateList.get(i);
                    boolean autoUpdate = autoUpdateGet == 1;

                    // 检查是否已存在相同URL或名称的文档
                    boolean checkUrlAndNameExist = checkUrlAndNameExistInSpace(rgId, url, name, currentSpaceId);
                    if (checkUrlAndNameExist) {
                        log.info("文档在当前工作空间已存在, 尝试重新上传: rgId={}, spaceId={}, url={}, name={}",
                                rgId, currentSpaceId, url, name);

                        try {
                            // 尝试重新上传文档
                            String documentIdStr = fridayRpcService.uploadDocument(
                                    datasetId, url, name, accessToken, misId, autoUpdate, currentSpaceId);

                            // 保存文档ID用于后续查询
                            urlToDocumentId.put(url, documentIdStr);

                            // 先查询已存在的记录
                            RgDocumentUrlEntity existingEntity = rgDocumentUrlMapper.findByRgIdSpaceIdAndUrlIncludeDeleted(rgId, currentSpaceId, url);
                            if (existingEntity != null) {
                                handleExistingDocument(existingEntity, documentIdStr, rgId, currentSpaceId,
                                        url, name, autoUpdateGet, misId, urlUploadStatus);
                            } else {
                                // 不存在记录则创建新记录
                                createNewDocument(documentIdStr, rgId, currentSpaceId, url, name, autoUpdateGet,
                                        misId, urlUploadStatus);
                            }
                        } catch (Exception e) {
                            // 检查是否是"知识库内已有重名知识"错误
                            if (!isKnowledgeAlreadyExistsError(e, rgId, currentSpaceId, url, name, urlUploadStatus)) {
                                // 确认不是知识库重名错误，尝试重新创建知识库
                                if (e instanceof RuntimeException && e.getMessage().contains("Failed to upload document") &&
                                        e.getCause() != null && e.getCause().getMessage() != null &&
                                        e.getCause().getMessage().contains("当前请求无相应地数据集权限")) {
                                    log.info("检测到知识库权限错误，尝试重新创建知识库: rgId={}, spaceId={}, url={}, name={}, error={}",
                                            rgId, currentSpaceId, url, name, e.getMessage());

                                    try {
                                        // 尝试重新创建知识库并重新上传文档
                                        String documentIdStr = recreateDatasetAndUploadDocument(
                                                rgId, currentSpaceId, url, name, accessToken, misId, autoUpdate, autoUpdateGet, urlUploadStatus);

                                        if (documentIdStr != null) {
                                            // 保存文档ID用于后续查询
                                            urlToDocumentId.put(url, documentIdStr);
                                            continue; // 上传成功，继续处理下一个URL
                                        }
                                    } catch (Exception createEx) {
                                        log.warn("尝试重新创建知识库或上传文档失败: rgId={}, spaceId={}, error={}",
                                                rgId, currentSpaceId, createEx.getMessage());
                                    }
                                } else if (isNetworkError(e)) {
                                    // 网络错误，记录到重试任务
                                    log.warn("检测到网络错误，记录到重试任务: rgId={}, spaceId={}, url={}, name={}, error={}",
                                            rgId, currentSpaceId, url, name, e.getMessage());
                                    documentRetryService.recordFailedTask(rgId, currentSpaceId, url, name, misId, autoUpdateGet, e.getMessage());
                                }

                                log.warn("添加文档失败: rgId={}, spaceId={}, url={}, name={}, error={}",
                                        rgId, currentSpaceId, url, name, e.getMessage());
                                urlUploadStatus.get(url).put(currentSpaceId, false);
                            }
                        }
                        continue;
                    }

                    // 调用上传文档API
                    try {
                        // 如果创建成功，则获得到documentIdStr
                        String documentIdStr = fridayRpcService.uploadDocument(
                                datasetId, url, name, accessToken, misId, autoUpdate, currentSpaceId);

                        // 保存文档ID用于后续查询
                        urlToDocumentId.put(url, documentIdStr);

                        // 先查询是否存在记录（包括已删除的记录）
                        RgDocumentUrlEntity existingEntity = rgDocumentUrlMapper.findByRgIdSpaceIdAndUrlIncludeDeleted(rgId, currentSpaceId, url);

                        if (existingEntity != null) {
                            handleExistingDocument(existingEntity, documentIdStr, rgId, currentSpaceId,
                                    url, name, autoUpdateGet, misId, urlUploadStatus);
                        } else {
                            // 创建新记录
                            createNewDocument(documentIdStr, rgId, currentSpaceId, url, name, autoUpdateGet,
                                    misId, urlUploadStatus);
                        }
                    } catch (Exception e) {
                        // 检查是否是"知识库内已有重名知识"错误
                        if (!isKnowledgeAlreadyExistsError(e, rgId, currentSpaceId, url, name, urlUploadStatus)) {
                            // 确认不是知识库重名错误，尝试重新创建知识库
                            if (e instanceof RuntimeException && e.getMessage().contains("Failed to upload document") &&
                                    e.getCause() != null && e.getCause().getMessage() != null &&
                                    e.getCause().getMessage().contains("当前请求无相应地数据集权限")) {
                                log.info("检测到知识库权限错误，尝试重新创建知识库: rgId={}, spaceId={}, url={}, name={}, error={}",
                                        rgId, currentSpaceId, url, name, e.getMessage());

                                try {
                                    // 尝试重新创建知识库并重新上传文档
                                    String documentIdStr = recreateDatasetAndUploadDocument(
                                            rgId, currentSpaceId, url, name, accessToken, misId, autoUpdate, autoUpdateGet, urlUploadStatus);

                                    if (documentIdStr != null) {
                                        // 保存文档ID用于后续查询
                                        urlToDocumentId.put(url, documentIdStr);
                                        continue; // 上传成功，继续处理下一个URL
                                    }
                                } catch (Exception createEx) {
                                    log.warn("尝试重新创建知识库或上传文档失败: rgId={}, spaceId={}, error={}",
                                            rgId, currentSpaceId, createEx.getMessage());
                                }
                            } else if (isNetworkError(e)) {
                                // 网络错误，记录到重试任务
                                log.warn("检测到网络错误，记录到重试任务: rgId={}, spaceId={}, url={}, name={}, error={}",
                                        rgId, currentSpaceId, url, name, e.getMessage());
                                documentRetryService.recordFailedTask(rgId, currentSpaceId, url, name, misId, autoUpdateGet, e.getMessage());
                            }

                            log.warn("添加文档失败: rgId={}, spaceId={}, url={}, name={}, error={}",
                                    rgId, currentSpaceId, url, name, e.getMessage());
                            urlUploadStatus.get(url).put(currentSpaceId, false);
                        }
                    }
                }
            } catch (Exception e) {
                log.error("获取工作空间访问令牌失败: rgId={}, spaceId={}, error={}",
                        rgId, currentSpaceId, e.getMessage(), e);
                // 获取令牌失败，该工作空间下的所有URL都标记为上传失败
                for (String url : urlList) {
                    urlUploadStatus.get(url).put(currentSpaceId, false);
                }
            }
        }

        // 检查哪些URL在所有工作空间都上传成功
        List<String> successUrlList = new ArrayList<>();
        for (String url : urlList) {
            boolean allSuccess = true;
            Map<String, Boolean> spaceStatus = urlUploadStatus.get(url);

            for (Boolean success : spaceStatus.values()) {
                if (!success) {
                    allSuccess = false;
                    break;
                }
            }

            if (allSuccess) {
                // 在所有工作空间中都上传成功
                successUrlList.add(url);
                log.info("文档在所有工作空间上传成功: rgId={}, url={}, name={}",
                        rgId, url, urlToName.get(url));
            } else {
                log.warn("文档在部分工作空间上传失败: rgId={}, url={}, name={}",
                        rgId, url, urlToName.get(url));
            }
        }

        // 从失败列表中移除成功的URL
        failUrl.removeAll(successUrlList);

        return failUrl;
    }

    @Override
    public boolean changeRefreshByDocumentId(long rgId, String url, int autoUpdate, String misId) throws LlmCorpusException {
        // 验证权限
        validateUserPermission(misId, rgId);
        
        // 获取该值班组下的所有工作空间配置
        List<RgDatasetDocumentEntity> rgDatasetDocuments = rgDatasetDocumentMapper.findByRgId(rgId);
        if (rgDatasetDocuments == null || rgDatasetDocuments.isEmpty()) {
            throw new LlmCorpusException(BizCode.DOCUMENT_DEL_ERROR.getCode(), "未找到值班组对应知识库，无法修改自动更新状态");
        }
        
        // 检查用户是否有默认工作空间权限，如果没有，则从列表中移除默认工作空间
        rgDatasetDocuments = filterWorkspacesByPermission(rgDatasetDocuments, misId, rgId);
        
        boolean autoUpdateBool = autoUpdate == 1;
        
        // 记录每个工作空间的更新状态
        Map<String, Boolean> workspaceUpdateStatus = new HashMap<>();
        for (RgDatasetDocumentEntity document : rgDatasetDocuments) {
            workspaceUpdateStatus.put(document.getSpaceId(), false);
        }
        
        // 对每个工作空间配置进行处理
        boolean found = false;
        for (RgDatasetDocumentEntity rgDatasetDocument : rgDatasetDocuments) {
            String currentSpaceId = rgDatasetDocument.getSpaceId();
            String datasetId = rgDatasetDocument.getDatasetId();
            
            try {
                // 获取工作空间的访问令牌
                String accessToken = workspaceService.getWorkspaceAccessToken(
                        rgDatasetDocument.getAccessKey(), rgDatasetDocument.getAppSecret());
                
                // 根据URL查找记录，只查找status=0的记录
                RgDocumentUrlEntity entity = rgDocumentUrlMapper.findByRgIdSpaceIdAndUrl(rgId, currentSpaceId, url);
                
                if (entity != null) {
                    // 获取实际的documentId
                    String documentId = entity.getDocumentId();
                    
                    // 调用API修改文档自动更新状态
                    fridayRpcService.changeRefreshDocument(datasetId, documentId, accessToken, misId, autoUpdateBool, currentSpaceId);
                    
                    // 更新数据库记录
                    entity.setAutoUpdate(autoUpdate);
                    int result = rgDocumentUrlMapper.updateEntity(entity);
                    
                    if (result > 0) {
                        log.info("修改文档自动更新状态成功: rgId={}, spaceId={}, url={}, documentId={}, autoUpdate={}", 
                                rgId, currentSpaceId, url, documentId, autoUpdate);
                        workspaceUpdateStatus.put(currentSpaceId, true);
                        found = true;
                    } else {
                        log.warn("修改文档自动更新状态数据库更新失败: rgId={}, spaceId={}, url={}, documentId={}, autoUpdate={}", 
                                rgId, currentSpaceId, url, documentId, autoUpdate);
                        // API调用成功但数据库更新失败，仍视为部分成功
                        workspaceUpdateStatus.put(currentSpaceId, true);
                        found = true;
                    }
                } else {
                    log.warn("未找到对应的记录: rgId={}, spaceId={}, url={}", 
                            rgId, currentSpaceId, url);
                    // 无法找到对应记录，标记为成功（跳过用户手动上传情况）
                    workspaceUpdateStatus.put(currentSpaceId, true);
                }
            } catch (Exception e) {
                log.error("修改文档自动更新状态失败: rgId={}, spaceId={}, url={}, error={}", 
                        rgId, currentSpaceId, url, e.getMessage(), e);
                workspaceUpdateStatus.put(currentSpaceId, false);
            }
        }
        
        if (!found) {
            log.error("在所有工作空间中均未找到URL对应的有效记录: rgId={}, url={}", rgId, url);
            return false;
        }
        
        // 检查是否所有工作空间都更新成功
        boolean allSuccess = true;
        for (Boolean success : workspaceUpdateStatus.values()) {
            if (!success) {
                allSuccess = false;
                break;
            }
        }
        
        if (allSuccess) {
            log.info("所有工作空间文档自动更新状态修改成功: rgId={}, url={}, autoUpdate={}", 
                    rgId, url, autoUpdate);
        } else {
            log.warn("部分工作空间文档自动更新状态修改失败: rgId={}, url={}, autoUpdate={}", 
                    rgId, url, autoUpdate);
        }
        
        return allSuccess;
    }

    /**
     * 检查特定工作空间中是否存在相同URL或名称的文档（仅检查状态为正常的记录）
     * @param rgId 值班组ID
     * @param url URL
     * @param name 名称
     * @param spaceId 工作空间ID
     * @return 是否存在
     */
    private boolean checkUrlAndNameExistInSpace(long rgId, String url, String name, String spaceId) {
        // 只检查状态为0的URL记录是否存在
        RgDocumentUrlEntity existingEntity = rgDocumentUrlMapper.findByRgIdSpaceIdAndUrl(rgId, spaceId, url);
        return existingEntity != null;
    }

    /**
     * 检查知识库内已有重名知识错误
     * 当出现此类错误时，将其视为成功上传
     * @param e 异常
     * @param rgId 值班组ID
     * @param spaceId 工作空间ID
     * @param url URL
     * @param name 文档名称
     * @param urlUploadStatus 上传状态映射
     * @return 是否为知识库内已有重名知识错误
     */
    private boolean isKnowledgeAlreadyExistsError(Exception e, long rgId, String spaceId, String url, String name, 
                                              Map<String, Map<String, Boolean>> urlUploadStatus) {
        if (e instanceof RuntimeException) {
            String errorMsg = e.getMessage();
            Throwable cause = e.getCause();
            
            // 检查错误消息或原因中是否包含"知识库内已有重名知识"
            if (errorMsg != null && errorMsg.contains("Failed to upload document") && 
                cause != null && cause.getMessage() != null && 
                (cause.getMessage().contains("知识库内已有重名知识") || 
                 cause.getMessage().contains("Request failed: 知识库内已有重名知识") ||
                 cause.getMessage().contains("Request failed with message: 知识库内已有重名知识"))) {
                
                log.info("检测到「知识库内已有重名知识」错误，视为上传成功: rgId={}, spaceId={}, url={}, name={}", 
                        rgId, spaceId, url, name);
                
                // 标记为在当前工作空间成功上传
                if (urlUploadStatus != null && urlUploadStatus.get(url) != null) {
                    urlUploadStatus.get(url).put(spaceId, true);
                }
                return true;
            }
        }
        return false;
    }
    
    /**
     * 处理文档上传，包括处理已删除和正常记录的情况
     * @param existingEntity 已存在的实体
     * @param documentIdStr 文档ID
     * @param rgId 值班组ID
     * @param currentSpaceId 工作空间ID
     * @param url URL
     * @param name 文档名称
     * @param autoUpdateGet 自动更新设置
     * @param misId 用户ID
     * @param urlUploadStatus 上传状态映射
     * @return 处理是否成功
     */
    public boolean handleExistingDocument(RgDocumentUrlEntity existingEntity, String documentIdStr,
                                        long rgId, String currentSpaceId, String url, String name, 
                                        int autoUpdateGet, String misId,
                                        Map<String, Map<String, Boolean>> urlUploadStatus) {
        if (existingEntity.getStatus() == 1) {
            // 如果是已删除的记录（status=1），则恢复并更新记录
            return restoreDeletedDocument(existingEntity, documentIdStr, rgId, currentSpaceId, 
                                       url, name, autoUpdateGet, misId, urlUploadStatus);
        } else {
            // 如果是正常记录（status=0），则更新记录
            return updateExistingDocument(existingEntity, documentIdStr, rgId, currentSpaceId, 
                                       url, name, autoUpdateGet, urlUploadStatus);
        }
    }
    
    /**
     * 恢复已删除的文档记录
     */
    private boolean restoreDeletedDocument(RgDocumentUrlEntity existingEntity, String documentIdStr, 
                                        long rgId, String currentSpaceId, String url, String name, 
                                        int autoUpdateGet, String misId,
                                        Map<String, Map<String, Boolean>> urlUploadStatus) {
        existingEntity.setDocumentId(documentIdStr);
        existingEntity.setName(name);
        existingEntity.setAutoUpdate(autoUpdateGet);
        existingEntity.setMisId(misId);
        int result = rgDocumentUrlMapper.restoreEntity(existingEntity);
        
        if (result > 0) {
            log.info("恢复已删除的文档记录: rgId={}, spaceId={}, url={}, name={}, documentId={}", 
                    rgId, currentSpaceId, url, name, documentIdStr);
            
            // 标记为在当前工作空间成功上传
            urlUploadStatus.get(url).put(currentSpaceId, true);
            return true;
        } else {
            log.warn("恢复已删除的文档记录失败: rgId={}, spaceId={}, url={}, name={}", 
                    rgId, currentSpaceId, url, name);
            urlUploadStatus.get(url).put(currentSpaceId, false);
            return false;
        }
    }
    
    /**
     * 更新已存在的文档记录
     */
    private boolean updateExistingDocument(RgDocumentUrlEntity existingEntity, String documentIdStr, 
                                        long rgId, String currentSpaceId, String url, String name, 
                                        int autoUpdateGet, 
                                        Map<String, Map<String, Boolean>> urlUploadStatus) {
        existingEntity.setDocumentId(documentIdStr);
        existingEntity.setName(name);
        existingEntity.setAutoUpdate(autoUpdateGet);
        int result = rgDocumentUrlMapper.updateEntity(existingEntity);
        
        if (result > 0) {
            log.info("成功更新文档记录: rgId={}, spaceId={}, url={}, name={}, documentId={}", 
                    rgId, currentSpaceId, url, name, documentIdStr);
            
            // 标记为在当前工作空间成功上传
            urlUploadStatus.get(url).put(currentSpaceId, true);
            return true;
        } else {
            log.warn("文档上传成功但数据库记录更新失败: rgId={}, spaceId={}, url={}, name={}", 
                    rgId, currentSpaceId, url, name);
            urlUploadStatus.get(url).put(currentSpaceId, false);
            return false;
        }
    }
    
    /**
     * 创建新的文档记录
     */
    public boolean createNewDocument(String documentIdStr, long rgId, String currentSpaceId,
                                     String url, String name, int autoUpdateGet, String misId,
                                     Map<String, Map<String, Boolean>> urlUploadStatus) {
        RgDocumentUrlEntity entity = RgDocumentUrlEntity.builder()
                .rgId(rgId)
                .documentId(documentIdStr)
                .spaceId(currentSpaceId)
                .name(name)
                .url(url)
                .misId(misId)
                .autoUpdate(autoUpdateGet)
                .createTime(new Timestamp(System.currentTimeMillis()))
                .build();
        
        int result = rgDocumentUrlMapper.insertEntity(entity);
        
        if (result > 0) {
            log.info("成功添加文档: rgId={}, spaceId={}, url={}, name={}, documentId={}", 
                    rgId, currentSpaceId, url, name, documentIdStr);
            
            // 标记为在当前工作空间成功上传
            urlUploadStatus.get(url).put(currentSpaceId, true);
            return true;
        } else {
            log.warn("文档数据库记录创建失败: rgId={}, spaceId={}, url={}, name={}", 
                    rgId, currentSpaceId, url, name);
            urlUploadStatus.get(url).put(currentSpaceId, false);
            return false;
        }
    }
    
    /**
     * 验证用户是否有对应的值班组权限
     * @param misId 用户ID
     * @param rgId 值班组ID
     * @throws LlmCorpusException 如果没有权限，抛出异常
     */
    private void validateUserPermission(String misId, long rgId) throws LlmCorpusException {
        boolean permission = knowledgeBaseService.validateUserPermission(misId, rgId);
        if (!permission) {
            throw new LlmCorpusException(BizCode.NO_PERMISSION.getCode(), "用户无对应值班组权限");
        }
    }

    private String recreateDatasetAndUploadDocument(long rgId, String spaceId, String url, String name, String accessToken, String misId, boolean autoUpdate, int autoUpdateGet, Map<String, Map<String, Boolean>> urlUploadStatus) throws LlmCorpusException {
        int retryCount = 0;
        String lastError = null;
        
        // 最多重试3次
        while (retryCount < 3) {
            try {
                // 获取值班组名称
                String rgName = "";
                try {
                    RgListItemDTO rgListItemDTO = ticketQueryService.queryRgInfoByRgId(misId, rgId);
                    if (rgListItemDTO != null) {
                        rgName = rgListItemDTO.getName();
                    }
                } catch (Exception ex) {
                    log.warn("获取值班组名称失败: rgId={}, error={}", rgId, ex.getMessage());
                }
                
                // 尝试重新创建知识库
                String SUF_NAME = "值班组知识库";
                String datasetName = rgId + "|" + rgName + SUF_NAME;
                String newDatasetId = fridayRpcService.createDataset(datasetName, 
                        accessToken, misId, datasetName, spaceId);
                
                log.info("成功重新创建知识库: rgId={}, spaceId={}, datasetId={}, datasetName={}", 
                        rgId, spaceId, newDatasetId, datasetName);
                
                // 更新数据库中的datasetId
                RgDatasetDocumentEntity rgDatasetDoc = rgDatasetDocumentMapper.findByRgIdAndSpaceId(rgId, spaceId);
                if (rgDatasetDoc != null) {
                    rgDatasetDoc.setDatasetId(newDatasetId);
                    rgDatasetDocumentMapper.updateAndSpaceId(rgDatasetDoc);
                    log.info("更新数据库中的知识库ID: rgId={}, spaceId={}, datasetId={}", 
                            rgId, spaceId, newDatasetId);
                }
                
                // 创建成功后再次尝试上传文档
                try {
                    String documentIdStr = fridayRpcService.uploadDocument(
                            newDatasetId, url, name, accessToken, misId, autoUpdate, spaceId);
                    
                    // 先查询是否存在记录（包括已删除的记录）
                    RgDocumentUrlEntity existingEntity = rgDocumentUrlMapper.findByRgIdSpaceIdAndUrlIncludeDeleted(rgId, spaceId, url);
                    
                    if (existingEntity != null) {
                        handleExistingDocument(existingEntity, documentIdStr, rgId, spaceId, 
                                            url, name, autoUpdateGet, misId, urlUploadStatus);
                    } else {
                        // 创建新记录
                        createNewDocument(documentIdStr, rgId, spaceId, url, name, autoUpdateGet, 
                                       misId, urlUploadStatus);
                    }
                    
                    return documentIdStr;
                } catch (Exception uploadEx) {
                    lastError = "重新创建知识库后上传文档失败: " + uploadEx.getMessage();
                    log.warn("重新创建知识库后上传文档失败: rgId={}, spaceId={}, url={}, name={}, error={}, 重试次数={}", 
                            rgId, spaceId, url, name, uploadEx.getMessage(), retryCount);
                    retryCount++;
                    
                    // 如果不是网络问题，直接退出重试
                    if (!isNetworkError(uploadEx)) {
                        break;
                    }
                    
                    // 短暂延迟后重试
                    Thread.sleep(1000);
                }
            } catch (Exception ex) {
                lastError = "尝试重新创建知识库失败: " + ex.getMessage();
                log.warn("尝试重新创建知识库失败: rgId={}, spaceId={}, error={}, 重试次数={}", 
                        rgId, spaceId, ex.getMessage(), retryCount);
                retryCount++;
                
                // 如果不是网络问题，直接退出重试
                if (!isNetworkError(ex)) {
                    break;
                }
                
                try {
                    // 短暂延迟后重试
                    Thread.sleep(1000);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                }
            }
        }
        
        // 达到最大重试次数或非网络错误，记录失败任务
        if (retryCount >= 3 || lastError != null) {
            log.error("重试创建知识库和上传文档失败，记录到重试任务: rgId={}, spaceId={}, url={}, 错误={}", 
                    rgId, spaceId, url, lastError);
            documentRetryService.recordFailedTask(rgId, spaceId, url, name, misId, autoUpdateGet, lastError);
        }
        
        return null;
    }
    
    /**
     * 判断是否为网络错误
     */
    private boolean isNetworkError(Exception e) {
        String message = e.getMessage();
        if (message == null) {
            return false;
        }
        
        // 检查是否包含常见网络错误关键字
        return message.contains("timeout") || 
               message.contains("connection") || 
               message.contains("网络") || 
               message.contains("network") || 
               message.contains("连接") ||
               message.contains("Connection refused") ||
               message.contains("Connection reset");
    }

    private List<RgDatasetDocumentEntity> filterWorkspacesByPermission(List<RgDatasetDocumentEntity> rgDatasetDocuments, String misId, long rgId) throws LlmCorpusException {
        // 检查用户是否有默认工作空间权限，如果没有，则从列表中移除默认工作空间
        boolean hasDefaultPermission = workspaceService.checkDefaultWorkspacePermission(misId);
        if (!hasDefaultPermission && rgDatasetDocuments != null && !rgDatasetDocuments.isEmpty()) {
            String defaultSpaceId = mtConfigService.getFridaySpaceId();
            List<RgDatasetDocumentEntity> filteredDocuments = new ArrayList<>();
            
            for (RgDatasetDocumentEntity doc : rgDatasetDocuments) {
                if (!defaultSpaceId.equals(doc.getSpaceId())) {
                    filteredDocuments.add(doc);
                } else {
                    log.info("filterWorkspacesByPermission# 用户无默认工作空间权限，移除默认工作空间: misId={}, rgId={}, defaultSpaceId={}", 
                            misId, rgId, defaultSpaceId);
                }
            }
            
            // 更新工作空间列表，排除默认工作空间
            rgDatasetDocuments = filteredDocuments;
            
            // 如果过滤后没有可用的工作空间，则抛出异常
            if (rgDatasetDocuments.isEmpty()) {
                log.warn("filterWorkspacesByPermission# 用户无默认工作空间权限，且无其他可用工作空间: misId={}, rgId={}", misId, rgId);
                throw new LlmCorpusException(BizCode.NO_PERMISSION.getCode(), "您没有该值班组的工作空间权限，请联系管理员添加权限或添加自定义工作空间");
            }
        }
        return rgDatasetDocuments;
    }
}
