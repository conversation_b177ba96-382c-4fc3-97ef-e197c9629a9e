package com.meituan.banma.llm.corpus.server.service.impl;

import com.meituan.banma.llm.corpus.server.common.domain.dto.UserInfoDTO;
import com.meituan.banma.llm.corpus.server.service.IUserService;
import com.sankuai.meituan.auth.util.UserUtils;
import com.sankuai.meituan.auth.vo.User;
import org.springframework.stereotype.Service;

import java.util.ArrayList;

/**
 * 用户服务类
 */
@Service
public class UserService implements IUserService {
    
    /**
     * 获取当前用户信息
     * @return 用户信息DTO
     */
    @Override
    public UserInfoDTO getCurrentUserInfo() {
        // 获取当前用户
        User user = UserUtils.getUser();
        if (user == null) {
            return null;
        }
        
        // 构建用户信息DTO
        UserInfoDTO userInfoDTO = new UserInfoDTO();
        userInfoDTO.setId(user.getId());
        userInfoDTO.setLogin(user.getLogin());
        userInfoDTO.setName(user.getName());
        userInfoDTO.setCode(user.getCode());
        userInfoDTO.setEmail(user.getEmail());
        userInfoDTO.setTenantId(user.getTenantId());
        userInfoDTO.setRoles(user.getRoles() != null ? user.getRoles() : new ArrayList<>());
        userInfoDTO.setIsVerified(user.isVerified);
        userInfoDTO.setVerifyType(user.verifyType);
        userInfoDTO.setVerifyExpireTime(user.verifyExpireTime);
        userInfoDTO.setPassport(user.getPassport());
        
        return userInfoDTO;
    }
} 