package com.meituan.banma.llm.corpus.server.common.constants.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum FridayConversationUserTypeEnum {

    USER("user", "用户"),
    ASSISTANT("assistan", "助手"),
    SYSTEM("system", "系统");

    private final String type;
    private final String desc;

    public static FridayConversationUserTypeEnum fromType(String type) {
        for (FridayConversationUserTypeEnum value : FridayConversationUserTypeEnum.values()) {
            if (value.getType().equals(type)) {
                return value;
            }
        }
        return null;
    }
}
