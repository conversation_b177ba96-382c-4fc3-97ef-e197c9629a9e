package com.meituan.banma.llm.corpus.server.common.domain.dto;

import lombok.Builder;
import lombok.Data;
import java.util.List;

/**
 * 动态模板工单查询结果DTO
 * 用于根据模板配置返回不同的字段结果
 */
@Data
@Builder
public class DynamicTemplateResultDTO {
    /**
     * 工单信息列表，使用动态字段
     */
    private List<DynamicTemplateTicketDTO> ticketList;
    
    /**
     * Excel文件下载链接
     */
    private String excelDownloadLink;
    
    /**
     * 任务ID
     */
    private String taskId;
    
    /**
     * 总工单数
     */
    private int totalCount;
    
    /**
     * 成功处理的工单数
     */
    private int successCount;
    
    /**
     * 模板名称
     */
    private String templateName;
    
    /**
     * 模板ID
     */
    private String templateId;
    
    /**
     * 字段列表
     */
    private List<String> fields;
} 