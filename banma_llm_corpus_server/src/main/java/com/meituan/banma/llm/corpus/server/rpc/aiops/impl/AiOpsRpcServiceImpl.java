package com.meituan.banma.llm.corpus.server.rpc.aiops.impl;

import com.meituan.banma.llm.corpus.server.rpc.aiops.AiOpsRpcService;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.banma.jarvis.aiops.api.request.ChatSummaryRequest;
import com.sankuai.banma.jarvis.aiops.api.service.AIOpsChatSummaryService;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * Class AiOpsRpcServiceImpl
 * Project banma_llm_corpus_process_server
 *
 * <AUTHOR>
 * @date 2025/5/8
 * Description 雷达群故障总结
 */
@Service
public class AiOpsRpcServiceImpl implements AiOpsRpcService {
    @Resource
    private AIOpsChatSummaryService aiOpsChatSummaryService;

    @MdpConfig("radar_group_summary_task_interval:1200")
    private int interval;

    @MdpConfig("radar_group_summary_task_period:1800")
    private int period;

    @Override
    public void submitRadarGroupSummaryTask(String misId, long groupId) throws TException {
        ChatSummaryRequest request = new ChatSummaryRequest();
        request.setGroupId(groupId);
        request.setMisId(misId);
        request.setTaskType(2);
        request.setTimeInterval(3600);
        request.setInterval(interval);
        request.setPeriod(period);
        aiOpsChatSummaryService.submitChatSummaryTask(request);
    }
}
