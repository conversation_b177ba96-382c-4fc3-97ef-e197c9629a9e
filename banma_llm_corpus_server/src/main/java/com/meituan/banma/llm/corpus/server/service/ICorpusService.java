package com.meituan.banma.llm.corpus.server.service;

import com.meituan.banma.llm.corpus.api.exception.LlmCorpusException;
import com.meituan.banma.llm.corpus.server.common.domain.dto.*;
import com.meituan.banma.llm.corpus.server.controller.request.corpus.CorpusFormRequest;
import com.meituan.banma.llm.corpus.server.controller.request.corpus.SopFormRequest;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ICorpusService {
    /**
     * 根据值班组ID和ContentId查询语料列表
     * @param rgId 值班组ID
     * @param misId 用户ID
     * @param contentId 内容ID
     * @param pageNum 页码
     * @param pageSize 页大小
     * @param strMatch 匹配字符串
     * @return 语料分页列表
     * @throws LlmCorpusException 业务异常
     */
    PageDTO<CorpusInfoDTO> queryCorpusListByRgIdContentId(long rgId, String misId, long contentId, int pageNum, int pageSize, String strMatch) throws LlmCorpusException;

    boolean addCorpus(CorpusFormRequest corpusForm, long rgId, String misId) throws LlmCorpusException;

    CorpusModifyDTO queryCorpusByTicketIdRgId(String ticketId, long rgId, String misId) throws LlmCorpusException;

    boolean modifyCorpus(CorpusFormRequest corpusForm, String ticketId, long rgId, String misId) throws LlmCorpusException;

    void deleteCorpusByTicketIds(List<String> ticketIds, long rgId, String misId) throws LlmCorpusException;

    void saveMergeCorpus(SaveMergeCorpusParam request) throws LlmCorpusException;

    boolean addIgnoreCorpus(CorpusFormRequest corpusForm, long rgId, String misId, String ticketId) throws LlmCorpusException;

    CorpusInfoDTO queryCorpusAllByTicketIdRgId(String ticketId, long rgId, String misId) throws LlmCorpusException;

    boolean updateSop(SopFormRequest sopFormRequest, long rgId, String misId) throws LlmCorpusException;

    PageDTO<SopDTO> querySopByRgId(long rgId, String misId, int pageNum, int pageSize);

    String queryLatestSopByRgId(long rgId);

    /**
     * 更新自定义背景知识
     * @param rgId 值班组ID
     * @param misId 操作人misId
     * @param knowledgeContent 背景知识内容
     * @return 是否成功
     */
    boolean updateBackgroundKnowledge(long rgId, String misId, String knowledgeContent) throws LlmCorpusException;

    /**
     * 分页查询自定义背景知识
     * @param rgId 值班组ID
     * @param misId 操作人misId
     * @param pageNum 页码
     * @param pageSize 页大小
     * @return 分页结果
     */
    PageDTO<BackgroundKnowledgeDTO> queryBackgroundKnowledgeByRgId(long rgId, String misId, int pageNum, int pageSize);

    /**
     * 查询最新自定义背景知识内容
     * @param rgId 值班组ID
     * @return 最新背景知识内容
     */
    String queryLatestBackgroundKnowledgeByRgId(long rgId);

    /**
     * 更新自定义规则
     * @param rgId 值班组ID
     * @param misId 操作人misId
     * @param rule 规则内容
     * @return 是否成功
     */
    boolean updateRule(long rgId, String misId, String rule) throws LlmCorpusException;

    /**
     * 分页查询自定义规则
     * @param rgId 值班组ID
     * @param misId 操作人misId
     * @param pageNum 页码
     * @param pageSize 页大小
     * @return 分页结果
     */
    PageDTO<RuleDTO> queryRuleByRgId(long rgId, String misId, int pageNum, int pageSize);

    /**
     * 查询最新自定义规则内容
     * @param rgId 值班组ID
     * @return 最新自定义规则内容
     */
    String queryLatestRuleByRgId(long rgId);

    /**
     * 导出语料全量数据为Excel并上传S3，返回下载链接
     * @param rgId 值班组ID
     * @param misId 操作人misId
     * @param ticketId 语料ID
     * @param title 标题
     * @param content 内容
     * @param source 来源
     * @param creator 创建人
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param strMatch 匹配字符串
     * @param tagsIds 标签ID列表，逗号分隔，如"7,6"
     * @return S3下载链接
     * @throws LlmCorpusException 业务异常
     */
    String exportAllCorpusToExcelAndUploadToS3(long rgId, String misId, String ticketId,
                                               String title, String content, Integer source,
                                               String creator, String startTime, String endTime,
                                               String strMatch, String tagsIds) throws LlmCorpusException;

    /**
     * 根据条件查询语料库列表
     *
     * @param rgId 值班组ID
     * @param misId 用户ID
     * @param ticketId 语料ID
     * @param title 标题
     * @param content 内容
     * @param source 来源
     * @param creator 创建人
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param strMatch 搜索关键词
     * @param tagsIds 标签ID列表，逗号分隔，如"7,6"
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @return 语料分页列表
     * @throws LlmCorpusException 业务异常
     */
    PageDTO<CorpusInfoDTO> queryCorpusListByCondition(long rgId, String misId, String ticketId,
            String title, String content, Integer source, String creator,
            String startTime, String endTime, String strMatch, String tagsIds, int pageNum, int pageSize) throws LlmCorpusException;
}