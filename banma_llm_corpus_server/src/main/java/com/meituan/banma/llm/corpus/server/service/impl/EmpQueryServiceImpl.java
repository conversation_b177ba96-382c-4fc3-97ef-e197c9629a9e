package com.meituan.banma.llm.corpus.server.service.impl;

import com.google.common.collect.Lists;
import com.meituan.banma.llm.corpus.server.rpc.emp.EmpQueryRpcService;
import com.meituan.banma.llm.corpus.server.service.IEmpQueryService;
import com.sankuai.meituan.org.opensdk.model.domain.Emp;
import com.sankuai.meituan.org.opensdk.service.EmpService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class EmpQueryServiceImpl implements IEmpQueryService {

    @Autowired
    private EmpQueryRpcService empQueryRpcService;

    @Override
    public Map<String, Emp> getEmpInfoByMisId(List<String> misIdList, Date date) {
        List<Emp> empList = Lists.newArrayList();
        try {
            // 判断misIdList是否为空
            if (CollectionUtils.isEmpty(misIdList)) {
                log.warn("#empService.batchQuery#warn, misIdList为空");
                return Collections.emptyMap();
            }
            
            // 调用远程服务获取员工信息
            empList = empQueryRpcService.batchQueryByMis(misIdList, null);
        } catch (Exception e) {
            log.error("#empService.batchQuery#error, misIdList:{}", misIdList, e);
        }
        return empList.stream().collect(Collectors.toMap(Emp::getMis, e -> e));
    }

    @Override
    public Map<String, Emp> getEmpInfoByEmpId(List<String> empIdList, Date date) {
        List<Emp> empList = Lists.newArrayList();
        try{
            empList = empQueryRpcService.batchQueryByEmpId(empIdList, null);
        } catch (Exception e){
            log.error("#empService.batchQuery#error, empIdList:{}", empIdList, e);
        }
        return empList.stream().collect(Collectors.toMap(Emp::getMis, e -> e));
    }
}
