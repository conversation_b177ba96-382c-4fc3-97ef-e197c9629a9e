package com.meituan.banma.llm.corpus.server.dal.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CorpusBotChatMessageEntity {
    private Long id;
    private String msgId;
    private String fromUid;
    private String fromPubId;
    private String gid;
    private long cts;
    private int type;
    /**
     * 参见https://km.sankuai.com/collabpage/635952308
     */
    private String message;
    /**
     * 真实姓名，需要查询转换后填充
     */
    private String fromName;
    private String msgExt;
    private String userOrgId;
    private String userOrgName;
    private String userOrgPath;
    private String fromMis;
    private Timestamp ctime;
    private Timestamp utime;
}
