package com.meituan.banma.llm.corpus.server.service.mapper;

import com.meituan.banma.llm.corpus.api.request.StatisticsReportRequestParam;
import com.meituan.banma.llm.corpus.server.common.domain.dto.StatisticsReportParam;
import lombok.extern.slf4j.Slf4j;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

@Mapper()
@Slf4j
public abstract class StatisticsMapper {
    private final static StatisticsMapper MAPPER = Mappers.getMapper(StatisticsMapper.class);
    public static StatisticsMapper get() {
        return MAPPER;
    }
    @Mappings({
            @Mapping(target = "docRecallItems", source = "docRecallItems"),
            @Mapping(target = "misId", source = "misId"),
            @Mapping(target = "botName", source = "botName"),
            @Mapping(target = "question", source = "question")
    })
    public abstract StatisticsReportParam toStatisticsReportParam(StatisticsReportRequestParam statisticsReportRequestParam);
}
