package com.meituan.banma.llm.corpus.server.service.impl;

import com.alibaba.fastjson.JSON;
import com.meituan.banma.llm.corpus.api.constants.BizCode;
import com.meituan.banma.llm.corpus.api.exception.LlmCorpusException;
import com.meituan.banma.llm.corpus.server.common.domain.dto.SaveCorpusBotChatMessageParam;
import com.meituan.banma.llm.corpus.server.dal.entity.CorpusBotChatMessageEntity;
import com.meituan.banma.llm.corpus.server.dal.mapper.CorpusBotChatMessageMapper;
import com.meituan.banma.llm.corpus.server.service.ICorpusBotChatService;
import com.meituan.banma.llm.corpus.server.service.IEmpQueryService;
import com.sankuai.meituan.org.opensdk.model.domain.Emp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class CorpusBotChatServiceImpl implements ICorpusBotChatService {

    @Autowired
    private CorpusBotChatMessageMapper corpusBotChatMessageMapper;
    
    @Autowired
    private IEmpQueryService empQueryService;

    @Override
    public void saveChatMessage(SaveCorpusBotChatMessageParam param) throws LlmCorpusException {
        if (param == null) {
            throw LlmCorpusException.buildWithMsg(BizCode.ILLEGAL_ARGUMENT, "参数为空");
        }
        
        // 构建实体对象
        CorpusBotChatMessageEntity entity = buildEntity(param);
        
        // 如果有MIS号，需要查询用户组织信息并填充
        if (StringUtils.isNotBlank(param.getFromMis())) {
            try {
                // 使用cts作为时间戳创建Date对象进行快照查询
                Date snapshotDate = new Date(param.getCts());
                
                // 调用EmpQueryService获取员工信息，使用snapshotDate作为时间参数
                Map<String, Emp> empMap = empQueryService.getEmpInfoByMisId(
                        Collections.singletonList(param.getFromMis()), snapshotDate);
                
                // 处理员工信息查询结果
                if (empMap == null || empMap.isEmpty()) {
                    log.warn("未查询到员工信息, mis:{}, cts:{}", param.getFromMis(), param.getCts());
                } else if (empMap.get(param.getFromMis()) == null) {
                    log.warn("未找到对应的员工信息, empMap:{}, mis:{}, cts:{}", JSON.toJSONString(empMap), param.getFromMis(), param.getCts());
                } else {
                    Emp emp = empMap.get(param.getFromMis());
                    // 填充用户信息
                    entity.setFromName(emp.getName());
                    entity.setUserOrgId(emp.getOrgId());
                    entity.setUserOrgName(emp.getOrgName());
                    entity.setUserOrgPath(emp.getOrgPath());
                }
            } catch (Exception e) {
                log.error("查询员工信息异常, mis:{}, cts:{}", param.getFromMis(), param.getCts(), e);
                // 查询失败不影响消息保存，只是组织信息为空
            }
        }
        try{
            corpusBotChatMessageMapper.insert(entity);
        } catch (Exception e) {
            log.error("保存消息异常, entity:{}", JSON.toJSONString(entity), e);
        }
    }
    
    /**
     * 构建实体对象
     *
     * @param param 参数
     * @return 实体对象
     */
    private CorpusBotChatMessageEntity buildEntity(SaveCorpusBotChatMessageParam param) {
        return CorpusBotChatMessageEntity.builder()
                .msgId(param.getMsgId())
                .fromUid(param.getFromUid())
                .fromPubId(param.getFromPubId())
                .gid(param.getGid())
                .cts(param.getCts())
                .type(param.getType())
                .message(param.getMessage())
                .msgExt(param.getMsgExt())
                .fromMis(param.getFromMis())
                .build();
    }
}
