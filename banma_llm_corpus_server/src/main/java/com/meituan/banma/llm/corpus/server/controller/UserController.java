package com.meituan.banma.llm.corpus.server.controller;

import com.meituan.banma.llm.corpus.server.common.CommonResult;
import com.meituan.banma.llm.corpus.server.common.domain.dto.UserInfoDTO;
import com.meituan.banma.llm.corpus.server.service.IUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 用户控制器
 */
@Slf4j
@RestController
@RequestMapping("/user")
public class UserController {

    @Autowired
    private IUserService userService;
    
    /**
     * 获取当前用户信息
     * @return 用户信息
     */
    @GetMapping("/current")
    public Object getCurrentUser() {
        try {
            UserInfoDTO userInfo = userService.getCurrentUserInfo();
            log.info("getCurrentUser# 获取当前用户信息成功, userInfo:{}", userInfo);
            return CommonResult.success(userInfo);
        } catch (Exception e) {
            log.error("getCurrentUser# 获取当前用户信息失败, 异常原因:{}", e.getMessage(), e);
            return CommonResult.error((String.format("获取当前用户信息失败,异常原因=%s", e.getMessage())));
        }
    }
} 