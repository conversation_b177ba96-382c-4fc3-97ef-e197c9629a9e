package com.meituan.banma.llm.corpus.server.service.impl;

import com.meituan.banma.llm.corpus.api.exception.LlmCorpusException;
import com.meituan.banma.llm.corpus.server.dal.entity.DocumentUploadRetryEntity;
import com.meituan.banma.llm.corpus.server.dal.entity.RgDatasetDocumentEntity;
import com.meituan.banma.llm.corpus.server.dal.entity.RgDocumentUrlEntity;
import com.meituan.banma.llm.corpus.server.dal.mapper.DocumentUploadRetryMapper;
import com.meituan.banma.llm.corpus.server.dal.mapper.RgDatasetDocumentMapper;
import com.meituan.banma.llm.corpus.server.dal.mapper.RgDocumentUrlMapper;
import com.meituan.banma.llm.corpus.server.rpc.friday.FridayRpcService;
import com.meituan.banma.llm.corpus.server.service.IDocumentRetryService;
import com.meituan.banma.llm.corpus.server.service.WorkspaceService;
import com.cip.crane.client.spring.annotation.Crane;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 文档上传重试服务实现
 */
@Slf4j
@Service
public class DocumentRetryServiceImpl implements IDocumentRetryService {
    
    @Autowired
    private DocumentUploadRetryMapper documentUploadRetryMapper;
    
    @Autowired
    private RgDatasetDocumentMapper rgDatasetDocumentMapper;
    
    @Autowired
    private RgDocumentUrlMapper rgDocumentUrlMapper;
    
    @Autowired
    private FridayRpcService fridayRpcService;
    
    @Autowired
    private WorkspaceService workspaceService;

    @Lazy
    @Autowired
    private KmServiceImpl kmService;
    
    /**
     * 最大重试次数
     */
    private static final int MAX_RETRY_COUNT = 3;
    
    /**
     * 每次处理的最大任务数
     */
    private static final int BATCH_SIZE = 20;
    
    @Override
    public void recordFailedTask(long rgId, String spaceId, String url, String name, String misId, int autoUpdate, String failReason) {
        try {
            // 查询是否已有相同任务
            DocumentUploadRetryEntity existingTask = documentUploadRetryMapper.findTask(rgId, spaceId, url);
            
            if (existingTask != null) {
                // 已有任务，更新重试次数和状态
                existingTask.setRetryCount(existingTask.getRetryCount() + 1);
                existingTask.setStatus(0); // 重置为待处理
                existingTask.setFailReason(failReason);
                documentUploadRetryMapper.updateTaskStatus(existingTask);
                log.info("更新文档上传重试任务: rgId={}, spaceId={}, url={}, retryCount={}", 
                        rgId, spaceId, url, existingTask.getRetryCount());
            } else {
                // 创建新任务
                DocumentUploadRetryEntity task = DocumentUploadRetryEntity.builder()
                        .rgId(rgId)
                        .spaceId(spaceId)
                        .url(url)
                        .name(name)
                        .retryCount(1)
                        .failReason(failReason)
                        .misId(misId)
                        .autoUpdate(autoUpdate)
                        .status(0) // 待处理
                        .build();
                documentUploadRetryMapper.insertRetryTask(task);
                log.info("创建文档上传重试任务: rgId={}, spaceId={}, url={}", rgId, spaceId, url);
            }
        } catch (Exception e) {
            log.error("记录文档上传失败任务出错: rgId={}, spaceId={}, url={}, error={}", 
                    rgId, spaceId, url, e.getMessage(), e);
        }
    }
    
    @Crane("document-upload-retry-task")
    @Override
    public void processRetryTasks() {
        log.info("开始处理文档上传重试任务");
        try {
            // 查询已经卡住的任务（处理中但超时）
            List<DocumentUploadRetryEntity> stuckTasks = documentUploadRetryMapper.findStuckTasks();
            for (DocumentUploadRetryEntity task : stuckTasks) {
                task.setStatus(0); // 重置为待处理
                documentUploadRetryMapper.updateTaskStatus(task);
                log.info("重置卡住的任务: id={}, rgId={}, spaceId={}", task.getId(), task.getRgId(), task.getSpaceId());
            }
            
            // 查询待处理的任务
            List<DocumentUploadRetryEntity> pendingTasks = documentUploadRetryMapper.findPendingTasks(BATCH_SIZE);
            if (pendingTasks.isEmpty()) {
                log.info("没有待处理的文档上传重试任务");
                return;
            }
            
            log.info("发现{}个待处理的文档上传重试任务", pendingTasks.size());
            
            for (DocumentUploadRetryEntity task : pendingTasks) {
                // 标记为处理中
                task.setStatus(1);
                documentUploadRetryMapper.updateTaskStatus(task);
                
                try {
                    // 执行重试
                    if (task.getRetryCount() > MAX_RETRY_COUNT) {
                        log.warn("文档上传重试任务超过最大重试次数: id={}, rgId={}, spaceId={}, url={}, retryCount={}",
                                task.getId(), task.getRgId(), task.getSpaceId(), task.getUrl(), task.getRetryCount());
                        task.setStatus(3); // 处理失败
                        task.setFailReason("超过最大重试次数");
                        documentUploadRetryMapper.updateTaskStatus(task);
                        continue;
                    }
                    
                    boolean success = retryUploadDocument(
                            task.getRgId(), task.getSpaceId(), task.getUrl(), 
                            task.getName(), task.getMisId(), task.getAutoUpdate());
                    
                    if (success) {
                        // 处理成功
                        task.setStatus(2);
                        documentUploadRetryMapper.updateTaskStatus(task);
                        log.info("文档上传重试任务处理成功: id={}, rgId={}, spaceId={}, url={}",
                                task.getId(), task.getRgId(), task.getSpaceId(), task.getUrl());
                    } else {
                        // 处理失败，增加重试次数
                        task.setStatus(0); // 重置为待处理
                        task.setRetryCount(task.getRetryCount() + 1);
                        task.setFailReason("重试上传失败");
                        documentUploadRetryMapper.updateTaskStatus(task);
                        log.warn("文档上传重试任务处理失败: id={}, rgId={}, spaceId={}, url={}, retryCount={}",
                                task.getId(), task.getRgId(), task.getSpaceId(), task.getUrl(), task.getRetryCount());
                    }
                } catch (Exception e) {
                    // 处理异常
                    task.setStatus(0); // 重置为待处理
                    task.setRetryCount(task.getRetryCount() + 1);
                    task.setFailReason(e.getMessage());
                    documentUploadRetryMapper.updateTaskStatus(task);
                    log.error("文档上传重试任务处理异常: id={}, rgId={}, spaceId={}, url={}, error={}",
                            task.getId(), task.getRgId(), task.getSpaceId(), task.getUrl(), e.getMessage(), e);
                }
            }
        } catch (Exception e) {
            log.error("处理文档上传重试任务出错: error={}", e.getMessage(), e);
        }
    }
    
    @Override
    public boolean retryUploadDocument(long rgId, String spaceId, String url, String name, String misId, int autoUpdate){
        int retryCount = 0;
        String lastError = null;
        
        // 最多重试3次
        while (retryCount < 3) {
            try {
                // 查询工作空间配置
                RgDatasetDocumentEntity rgDatasetDocument = rgDatasetDocumentMapper.findByRgIdAndSpaceId(rgId, spaceId);
                if (rgDatasetDocument == null) {
                    log.warn("未找到工作空间配置: rgId={}, spaceId={}", rgId, spaceId);
                    return false;
                }
                
                String datasetId = rgDatasetDocument.getDatasetId();
                
                // 获取访问令牌
                String accessToken = workspaceService.getWorkspaceAccessToken(
                        rgDatasetDocument.getAccessKey(), rgDatasetDocument.getAppSecret());
                
                // 执行上传文档
                String documentIdStr = fridayRpcService.uploadDocument(
                        datasetId, url, name, accessToken, misId, autoUpdate == 1, spaceId);
                
                // 检查是否已存在记录
                RgDocumentUrlEntity existingEntity = rgDocumentUrlMapper.findByRgIdSpaceIdAndUrlIncludeDeleted(rgId, spaceId, url);
                
                // 处理数据库记录
                Map<String, Map<String, Boolean>> urlUploadStatus = new HashMap<>();
                urlUploadStatus.put(url, new HashMap<>());
                urlUploadStatus.get(url).put(spaceId, false);
                
                boolean success;
                if (existingEntity != null) {
                    // 处理已存在的记录
                    success = kmService.handleExistingDocument(existingEntity, documentIdStr, rgId, 
                            spaceId, url, name, autoUpdate, misId, urlUploadStatus);
                } else {
                    // 创建新记录
                    success = kmService.createNewDocument(documentIdStr, rgId, spaceId, 
                            url, name, autoUpdate, misId, urlUploadStatus);
                }
                
                // 成功处理，直接返回
                return success;
            } catch (Exception e) {
                lastError = e.getMessage();
                retryCount++;
                
                log.warn("重试上传文档失败 (尝试 {}/3): rgId={}, spaceId={}, url={}, name={}, error={}",
                        retryCount, rgId, spaceId, url, name, e.getMessage());
                
                if (!isNetworkError(e)) {
                    // 如果不是网络错误，不再继续重试
                    log.error("非网络错误，中止重试: rgId={}, spaceId={}, url={}", rgId, spaceId, url);
                    break;
                }
                
                // 如果还有重试次数，短暂延迟后重试
                if (retryCount < 3) {
                    try {
                        Thread.sleep(1000 * retryCount); // 随着重试次数增加等待时间
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                    }
                }
            }
        }
        
        // 如果到这里，说明重试次数已用完或者遇到了非网络错误
        log.error("重试上传文档失败，已达到最大重试次数: rgId={}, spaceId={}, url={}, 最后错误={}",
                rgId, spaceId, url, lastError);
        return false;
    }
    
    /**
     * 判断是否为网络错误
     */
    private boolean isNetworkError(Exception e) {
        String message = e.getMessage();
        if (message == null) {
            return false;
        }
        
        // 检查是否包含常见网络错误关键字
        return message.contains("timeout") || 
               message.contains("connection") || 
               message.contains("网络") || 
               message.contains("network") || 
               message.contains("连接") ||
               message.contains("Connection refused") ||
               message.contains("Connection reset");
    }
} 