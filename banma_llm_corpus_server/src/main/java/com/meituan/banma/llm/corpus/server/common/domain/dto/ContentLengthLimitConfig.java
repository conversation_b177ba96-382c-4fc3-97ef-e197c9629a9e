package com.meituan.banma.llm.corpus.server.common.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 内容长度限制配置DTO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ContentLengthLimitConfig {
    /**
     * 规则内容长度限制
     */
    private Integer ruleMaxLength;
    
    /**
     * SOP内容长度限制
     */
    private Integer sopMaxLength;
    
    /**
     * 背景知识内容长度限制
     */
    private Integer knowledgeMaxLength;
    
    /**
     * 创建默认配置
     * @return 默认配置对象
     */
    public static ContentLengthLimitConfig createDefault() {
        return new ContentLengthLimitConfig(1000, 1000, 1000);
    }
} 