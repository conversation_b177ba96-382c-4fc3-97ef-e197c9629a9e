package com.meituan.banma.llm.corpus.server.controller.request.monitoring;

import lombok.Data;

import java.util.List;

/**
 * 监控组请求参数
 */
@Data
public class MonitoringGroupRequest {
    
    /**
     * 监控组ID
     */
    private Long monitoringGroupId;
    
    /**
     * 监控组名称
     */
    private String monitoringGroupName;
    
    /**
     * 监控组描述
     */
    private String monitoringGroupDesc;
    
    /**
     * 监控组所有者misId列表
     */
    private List<String> monitoringGroupOwner;
    
    /**
     * DX群组ID列表
     */
    private List<Long> dxGroupIds;
    
    /**
     * 监控组织ID列表
     */
    private List<String> monitoredOrgIds;
    
    /**
     * 监控MIS账号列表
     */
    private List<String> monitoredMisIds;
    
    /**
     * 关键词列表
     */
    private List<String> keywords;
    
    /**
     * 问题类型列表
     */
    private List<QuestionTypeRequest> questionTypes;
    
    /**
     * 监控时间范围类型
     */
    private Integer monitoringTimeRangeType;
    
    /**
     * 状态：0-正常 1-禁用
     */
    private Integer status;
    
    /**
     * 操作人misId
     */
    private String operatorMisId;
} 