package com.meituan.banma.llm.corpus.server.service;

import com.meituan.banma.llm.corpus.server.common.domain.dto.TicketRangeDTO;

import java.util.List;

/**
 * 工单范围信息服务接口
 */
public interface ITicketRangeService {
    
    /**
     * 保存工单范围信息（存在则更新，不存在则插入）
     * 
     * @param rangeDTO 工单范围信息DTO
     * @return 保存结果，成功返回true
     */
    boolean saveTicketRange(TicketRangeDTO rangeDTO);
    
    /**
     * 批量保存工单范围信息（存在则更新，不存在则插入）
     * 
     * @param rangeDTOList 工单范围信息DTO列表
     * @return 成功保存的记录数
     */
    int batchSaveTicketRange(List<TicketRangeDTO> rangeDTOList);
    
    /**
     * 根据工单ID查询工单范围信息
     * 
     * @param ticketId 工单ID
     * @return 工单范围信息，不存在返回null
     */
    TicketRangeDTO getByTicketId(String ticketId);
    
    /**
     * 根据工单ID和组织ID查询工单范围信息
     * 
     * @param ticketId 工单ID
     * @param rgId 组织ID
     * @return 工单范围信息，不存在返回null
     */
    TicketRangeDTO getByTicketIdAndRgId(String ticketId, Long rgId);
    
    /**
     * 根据多个工单ID批量查询工单范围信息
     * 
     * @param ticketIds 工单ID列表
     * @return 工单范围信息列表
     */
    List<TicketRangeDTO> getByTicketIds(List<String> ticketIds);
} 