package com.meituan.banma.llm.corpus.server.utils;

import org.apache.commons.lang.StringUtils;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;

public class CheckUtils {

    /**
     * 传入两个 date string 和 一个 天数 判断两个date的间隔天数是否小于等于传入天数
     * @param date1 string yyyyMMdd
     * @param date2 string yyyyMMdd
     * @param intervalDays int 7
     * @return boolean true false
     */
    public static boolean checkDateInterval(String date1, String date2, int intervalDays) {
        if (StringUtils.isBlank(date1) || StringUtils.isBlank(date2)) {
            return false;
        }
        LocalDate localDate1 = LocalDate.parse(date1, DateTimeFormatter.ofPattern("yyyy-MM-dd "));
        LocalDate localDate2 = LocalDate.parse(date2, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        return localDate1.until(localDate2, ChronoUnit.DAYS) <= intervalDays;
    }
}
