package com.meituan.banma.llm.corpus.server.rpc.venus.impl;

import com.meituan.banma.llm.corpus.server.common.MtConfigService;
import com.meituan.banma.llm.corpus.server.common.domain.dto.VenusMccConfig;
import com.meituan.banma.llm.corpus.server.rpc.venus.VenusRpcService;
import com.meituan.banma.llm.corpus.server.rpc.venus.dto.UploadImageBytesDTO;
import com.meituan.image.client.ImageUploadClient;
import com.meituan.image.client.impl.ImageUploadClientImpl;
import com.meituan.image.client.pojo.ImageRequest;
import com.meituan.image.client.pojo.ImageResult;
import com.sankuai.inf.octo.mns.model.HostEnv;
import com.sankuai.inf.octo.mns.util.ProcessInfoUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Slf4j
@Service
public class VenusRpcServiceImpl implements VenusRpcService {
    @Autowired
    private MtConfigService mtConfigService;
    @Override
    public String uploadImageBytes(UploadImageBytesDTO uploadImageBytesDTO) {
        if (Objects.isNull(uploadImageBytesDTO) || StringUtils.isBlank(uploadImageBytesDTO.getImageName()) || Objects.isNull(uploadImageBytesDTO.getImageBytes())) {
            log.error("#VenusRpcServiceImpl.uploadImageBytes# param error, uploadImageBytesDTO:{}", uploadImageBytesDTO);
            return null;
        }
        VenusMccConfig venusMccConfig = mtConfigService.getVenusConfig();
        if (Objects.isNull(venusMccConfig)) {
            log.error("#VenusRpcServiceImpl.uploadImageBytes# getVenusConfig error");
            return null;
        }
        try{
            ImageUploadClient client = new ImageUploadClientImpl(venusMccConfig.getBucket(), venusMccConfig.getClientId(), venusMccConfig.getClientSecret());
            if(HostEnv.TEST.equals(ProcessInfoUtil.getHostEnv())){
                client.setEnv(ImageUploadClient.Environment.TEST);
            }else {
                client.setEnv(ImageUploadClient.Environment.PROD);
            }
            ImageRequest request = new ImageRequest(ImageRequest.UploadTypeBytes, uploadImageBytesDTO.getImageBytes(),uploadImageBytesDTO.getImageName(),false);
            ImageResult res = client.postImage(request);
            if (Objects.nonNull(res) && res.isSuccess()) {
                return res.getOriginalLink();
            }else {
                log.error("#VenusRpcServiceImpl.uploadImageBytes# uploadImageBytes error, uploadImageBytesDTO:{}, res:{}", uploadImageBytesDTO, res);
                return null;
            }
        } catch (Exception e){
            log.error("#VenusRpcServiceImpl.uploadImageBytes# error, uploadImageBytesDTO:{}", uploadImageBytesDTO, e);
            return null;
        }
    }
}
