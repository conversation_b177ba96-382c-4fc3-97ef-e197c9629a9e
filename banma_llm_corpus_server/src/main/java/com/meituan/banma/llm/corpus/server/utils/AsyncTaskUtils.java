package com.meituan.banma.llm.corpus.server.utils;

import com.meituan.mtrace.thread.TraceRunnable;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.Callable;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.function.Supplier;

import com.meituan.mtrace.thread.pool.ExecutorServiceTraceWrapper;

@Slf4j
public class AsyncTaskUtils {
    private static ThreadPoolExecutor llmCorpusConvertTaskThreadPool = ThreadPoolUtils.getLlmCorpusConvertTaskThreadPool();
    private static ThreadPoolExecutor fridayConversationThreadPool = ThreadPoolUtils.getFridayConversationThreadPool();
    private static ThreadPoolExecutor batchImageToTextThreadPool = ThreadPoolUtils.getBatchImageToTextThreadPool();
    private static ThreadPoolExecutor refreshDocumentThreadPool = ThreadPoolUtils.getRefreshDocumentThreadPool();
    private static ThreadPoolExecutor statisticsReportThreadPool = ThreadPoolUtils.getStatisticsReportThreadPool();
    private static ThreadPoolExecutor dxMonitoringThreadPool = ThreadPoolUtils.getDxMonitoringThreadPool();
    private static ThreadPoolExecutor mergeCorpusThreadPool = ThreadPoolUtils.getMergeCorpusThreadPool();
    private static ThreadPoolExecutor batchProcessTtThreadPool = ThreadPoolUtils.getBatchProcessTtThreadPool();
    private static ThreadPoolExecutor checkUrlThreadPool = ThreadPoolUtils.getCheckUrlThreadPool();
    private static ThreadPoolExecutor questionResolveStateThreadPool = ThreadPoolUtils.getQuestionResolveStateThreadPool();
    private static ThreadPoolExecutor embeddingThreadPool = ThreadPoolUtils.getEmbeddingThreadPool();
    private static ThreadPoolExecutor clusterNamingThreadPool = ThreadPoolUtils.getClusterNamingThreadPool();


    public static void executeLlmCorpusConvertTask(Runnable task) {
        llmCorpusConvertTaskThreadPool.submit(new TraceRunnable(task));
    }

    public static ExecutorService getLlmCorpusConvertTaskThreadPool() {
        return llmCorpusConvertTaskThreadPool;
    }

    public static ExecutorService getFridayConversationThreadPool() {
        return fridayConversationThreadPool;
    }

    public static ExecutorService getBatchImageToTextThreadPool() { return batchImageToTextThreadPool; }

    public static ExecutorService getRefreshDocumentThreadPool() { return refreshDocumentThreadPool; }

    public static ExecutorService getStatisticsReportThreadPool() { return statisticsReportThreadPool; }

    public static ExecutorService getDxMonitoringThreadPool() { return dxMonitoringThreadPool; }

    public static ExecutorService getMergeCorpusThreadPool() { return mergeCorpusThreadPool; }

    public static ExecutorService getBatchProcessTtThreadPool() { return batchProcessTtThreadPool; }

    public static ExecutorService getCheckUrlThreadPool() { return checkUrlThreadPool; }
    
    public static ExecutorService getQuestionResolveStateThreadPool() { return questionResolveStateThreadPool; }
    
    public static ExecutorService getEmbeddingThreadPool() { return embeddingThreadPool; }
    
    public static ExecutorService getClusterNamingThreadPool() { return clusterNamingThreadPool; }
    
    /**
     * 提交一个可传递ThreadLocal值的Runnable任务
     * @param executor 要使用的线程池
     * @param task 要执行的任务
     * @return Future对象
     */
    public static Future<?> submitWithThreadLocal(ExecutorService executor, Runnable task) {
        // 捕获当前线程的ThreadLocal值
        ThreadLocalTransferUtil.ThreadLocalContext context = ThreadLocalTransferUtil.capture();
        
        return executor.submit(() -> {
            try {
                // 在新线程中应用捕获的ThreadLocal值
                ThreadLocalTransferUtil.apply(context);
                
                // 执行任务
                task.run();
            } finally {
                // 清理ThreadLocal值，避免内存泄漏
                ThreadLocalTransferUtil.clear();
            }
        });
    }
    
    /**
     * 提交一个可传递ThreadLocal值的Callable任务
     * @param executor 要使用的线程池
     * @param task 要执行的任务
     * @return Future对象
     */
    public static <T> Future<T> submitWithThreadLocal(ExecutorService executor, Callable<T> task) {
        // 捕获当前线程的ThreadLocal值
        ThreadLocalTransferUtil.ThreadLocalContext context = ThreadLocalTransferUtil.capture();
        
        return executor.submit(() -> {
            try {
                // 在新线程中应用捕获的ThreadLocal值
                ThreadLocalTransferUtil.apply(context);
                
                // 执行任务并返回结果
                return task.call();
            } finally {
                // 清理ThreadLocal值，避免内存泄漏
                ThreadLocalTransferUtil.clear();
            }
        });
    }
    
    /**
     * 创建一个可传递ThreadLocal值的CompletableFuture
     * @param executor 要使用的线程池
     * @param supplier 任务提供者
     * @return CompletableFuture对象
     */
    public static <T> CompletableFuture<T> supplyAsyncWithThreadLocal(
            ExecutorService executor, Supplier<T> supplier) {
        // 捕获当前线程的ThreadLocal值
        ThreadLocalTransferUtil.ThreadLocalContext context = ThreadLocalTransferUtil.capture();
        
        return CompletableFuture.supplyAsync(() -> {
            try {
                // 在新线程中应用捕获的ThreadLocal值
                ThreadLocalTransferUtil.apply(context);
                
                // 执行任务并返回结果
                return supplier.get();
            } finally {
                // 清理ThreadLocal值，避免内存泄漏
                ThreadLocalTransferUtil.clear();
            }
        }, executor);
    }
    
    /**
     * 创建一个可传递ThreadLocal值的CompletableFuture（无返回值）
     * @param executor 要使用的线程池
     * @param runnable 要执行的任务
     * @return CompletableFuture对象
     */
    public static CompletableFuture<Void> runAsyncWithThreadLocal(
            ExecutorService executor, Runnable runnable) {
        // 捕获当前线程的ThreadLocal值
        ThreadLocalTransferUtil.ThreadLocalContext context = ThreadLocalTransferUtil.capture();
        
        return CompletableFuture.runAsync(() -> {
            try {
                // 在新线程中应用捕获的ThreadLocal值
                ThreadLocalTransferUtil.apply(context);
                
                // 执行任务
                runnable.run();
            } finally {
                // 清理ThreadLocal值，避免内存泄漏
                ThreadLocalTransferUtil.clear();
            }
        }, executor);
    }
}
