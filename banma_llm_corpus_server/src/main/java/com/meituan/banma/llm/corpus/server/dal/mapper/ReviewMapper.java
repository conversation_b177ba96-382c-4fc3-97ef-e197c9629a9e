package com.meituan.banma.llm.corpus.server.dal.mapper;

import com.meituan.banma.llm.corpus.server.common.domain.dto.CorpusModifyDTO;
import com.meituan.banma.llm.corpus.server.common.domain.dto.LatestContentDTO;
import com.meituan.banma.llm.corpus.server.dal.entity.KnowledgeBaseVersionEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.sql.Timestamp;
import java.util.List;

@Mapper
public interface ReviewMapper {
    // 查询某个ticketId的最新版本
    @Select("SELECT id, version, rg_id FROM modified_output WHERE ticket_id = #{ticketId} AND corpus_status = 1 ORDER BY version DESC LIMIT 1")
    KnowledgeBaseVersionEntity findLatestContentByTicketId(String ticketId);

    // 查询某个rgId下所有ticketId的最新版本内容
    List<LatestContentDTO> findLatestContentsByRgId(Long rgId);

    // 插入新的版本记录，使用版本号检查来模拟乐观锁
    int insertModifiedOutputWithVersionCheck(KnowledgeBaseVersionEntity knowledgeBaseVersionEntity);

    // 查询rgId下匹配条件的所有最新数据列表（不分页）
    List<KnowledgeBaseVersionEntity> findAllLatestKnowledgeListByRgId(
            @Param("rgId") long rgId,
            @Param("ticketId") String ticketId,
            @Param("title") String title,
            @Param("content") String content,
            @Param("source") String source,
            @Param("creator") String creator,
            @Param("startTime") String startTime,
            @Param("endTime") String endTime,
            @Param("strMatch") String strMatch,
            @Param("tagsIds") String tagsIds);

    /**
     * 根据 ticketIds和 查询知识库信息
     *
     * @param ticketIds TT IDs, rgId
     * @return 知识库信息
     */

    List<KnowledgeBaseVersionEntity> findCorpusInfoByTTidRgids(@Param("rgId") Long rgId, @Param("ticketIds") List<String> ticketIds);

    /**
     * 根据ticketIds列表批量查询 取最新版本的一条
     * @param ticketIds
     * @return
     */
    @Select("<script>" +
            "SELECT mo.* FROM modified_output mo " +
            "INNER JOIN (" +
            "  SELECT ticket_id, MAX(version) as max_version " +
            "  FROM modified_output " +
            "  WHERE ticket_id IN " +
            "  <foreach item='item' index='index' collection='ticketIds' open='(' separator=',' close=')'>" +
            "    #{item}" +
            "  </foreach>" +
            "  GROUP BY ticket_id" +
            ") t ON mo.ticket_id = t.ticket_id AND mo.version = t.max_version" +
            " WHERE mo.rg_id = #{rgId}" +
            " AND corpus_status = 1 " +
            "</script>")
    List<KnowledgeBaseVersionEntity> findByTicketIds(@Param("ticketIds") List<String> ticketIds, @Param("rgId") Long rgId);

    @Select("<script>" +
            "SELECT mo.* FROM modified_output mo " +
            "INNER JOIN (" +
            "  SELECT ticket_id, MAX(version) as max_version " +
            "  FROM modified_output " +
            "  WHERE ticket_id IN " +
            "  <foreach item='item' index='index' collection='ticketIds' open='(' separator=',' close=')'>" +
            "    #{item}" +
            "  </foreach>" +
            "  GROUP BY ticket_id" +
            ") t ON mo.ticket_id = t.ticket_id AND mo.version = t.max_version" +
            " WHERE mo.rg_id = #{rgId}" +
            " AND corpus_status != 3 " +
            "</script>")
    List<KnowledgeBaseVersionEntity> findByTicketIdsExcludeDeletedOnly(@Param("ticketIds") List<String> ticketIds, @Param("rgId") Long rgId);
    // 根据ticketId和rgId查询需要修改的最新版本的主要内容
    @Select("SELECT title, content " +
            "FROM modified_output " +
            "WHERE ticket_id = #{ticketId} AND rg_id = #{rgId} " +
            "AND version = (SELECT MAX(version) FROM modified_output WHERE ticket_id = #{ticketId} AND rg_id = #{rgId}) " +
            "AND corpus_status != 3 ")
    CorpusModifyDTO findModifyContentByTicketIdRgId(@Param("ticketId") String ticketId, @Param("rgId")long rgId);
    //根据ticketId和rgId批量删除数据
    void deleteByTicketIds(@Param("ticketIds")List<String> ticketIds, @Param("rgId")long rgId, @Param("misId")String misId);

    // 根据ticketIdList查询最老版本的记录创建
    List<KnowledgeBaseVersionEntity> findCreateKnowledgeListByTicketId(@Param("ticketIdList")List<String> ticketIdList, @Param("rgId")long rgId);

    /**
     * 根据mergedToIds列表批量查询指向这些语料的记录 取最新版本
     * @param mergedToIds 被合并的语料ID列表
     * @param rgId 值班组ID
     * @return 最新版本的语料列表
     */
    List<KnowledgeBaseVersionEntity> findByMergedToIds(@Param("mergedToIds") List<String> mergedToIds, @Param("rgId") Long rgId);

    //根据ticketId和rgId查询单条记录所有信息（能找到有效的）
    @Select("SELECT * FROM modified_output WHERE ticket_id = #{ticketId} AND rg_id = #{rgId} " +
            "AND version = (SELECT MAX(version) FROM modified_output WHERE ticket_id = #{ticketId} AND rg_id = #{rgId}) " +
            "AND corpus_status = 1 LIMIT 1")
    KnowledgeBaseVersionEntity findLatestContentByTicketIdWithRgId(@Param("ticketId")String ticketId, @Param("rgId")long rgId);

    //根据ticketId和rgId查询单条记录所有信息（能找到合并的和有效的和删除的）
    @Select("SELECT * FROM modified_output WHERE ticket_id = #{ticketId} AND rg_id = #{rgId} " +
            "AND version = (SELECT MAX(version) FROM modified_output WHERE ticket_id = #{ticketId} AND rg_id = #{rgId}) LIMIT 1 FOR UPDATE")
    KnowledgeBaseVersionEntity findLatestContentByTicketIdWithRgIdIncludeDeleted(@Param("ticketId")String ticketId, @Param("rgId")long rgId);

    //根据ticketIdList查询创建时间timestamp
    List<Timestamp> findCreateTimestampListByTicketId(@Param("ticketIdList")List<String> ticketIdList, @Param("rgId")long rgId);

    //根据ticketId和rgId查询单条记录所有信息（能找到合并的和有效的）
    @Select("SELECT * " +
            "FROM modified_output " +
            "WHERE ticket_id = #{ticketId} AND rg_id = #{rgId} " +
            "AND version = (SELECT MAX(version) FROM modified_output WHERE ticket_id = #{ticketId} AND rg_id = #{rgId}) " +
            "AND corpus_status != 3 ")
    KnowledgeBaseVersionEntity findAllInfoByTicketIdRgId(@Param("ticketId") String ticketId, @Param("rgId")long rgId);

    /**
     * 查询rgId下指定ContentId的最新数据分页列表
     * @param rgId 值班组ID
     * @param contentId 内容ID
     * @param limit 分页大小
     * @param offset 偏移量
     * @param strMatch 匹配字符串
     * @return 最新版本的语料列表
     */
    List<KnowledgeBaseVersionEntity> findLatestKnowledgeListByRgIdContentId(@Param("rgId") long rgId,
                                                                   @Param("contentId") long contentId,
                                                                   @Param("limit") int limit,
                                                                   @Param("offset") int offset,
                                                                   @Param("strMatch") String strMatch);

    /**
     * 统计符合条件的rgId和contentId下的记录数
     * @param rgId 值班组ID
     * @param contentId 内容ID
     * @param strMatch 匹配字符串
     * @return 记录数
     */
    int countCorpusByRgIdContentId(@Param("rgId") long rgId,
                          @Param("contentId") long contentId,
                          @Param("strMatch") String strMatch);

    /**
     * 根据多条件分页查询当前最新版本的知识记录
     *
     * @param rgId 值班组ID
     * @param ticketId 语料ID
     * @param title 标题
     * @param content 内容
     * @param source 来源
     * @param creator 创建人
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param strMatch 搜索关键词
     * @param tagsIds 标签ID列表，逗号分隔，如"7,6"
     * @param limit 每页大小
     * @param offset 偏移量
     * @return 知识记录列表
     */
    List<KnowledgeBaseVersionEntity> findLatestKnowledgeListByCondition(
            @Param("rgId") long rgId,
            @Param("ticketId") String ticketId,
            @Param("title") String title,
            @Param("content") String content,
            @Param("source") String source,
            @Param("creator") String creator,
            @Param("startTime") String startTime,
            @Param("endTime") String endTime,
            @Param("strMatch") String strMatch,
            @Param("tagsIds") String tagsIds,
            @Param("limit") int limit,
            @Param("offset") int offset);

    /**
     * 统计多条件筛选后的总数
     *
     * @param rgId 值班组ID
     * @param ticketId 语料ID
     * @param title 标题
     * @param content 内容
     * @param source 来源
     * @param creator 创建人
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param strMatch 搜索关键词
     * @param tagsIds 标签ID列表，逗号分隔，如"7,6"
     * @return 符合条件的记录总数
     */
    int countCorpusByCondition(@Param("rgId") long rgId,
                              @Param("ticketId") String ticketId,
                              @Param("title") String title,
                              @Param("content") String content,
                              @Param("source") String source,
                              @Param("creator") String creator,
                              @Param("startTime") String startTime,
                              @Param("endTime") String endTime,
                              @Param("strMatch") String strMatch,
                              @Param("tagsIds") String tagsIds);

    // 根据rgId查询最新的mis_id
    @Select("SELECT mis_id FROM modified_output WHERE rg_id = #{rgId} " +
            "ORDER BY timestamp DESC LIMIT 1")
    String findLatestMisIdByRgId(@Param("rgId") Long rgId);

    /**
     * 查询包含指定标签ID的记录
     * @param tagId 标签ID
     * @return 包含该标签ID的记录列表
     */
    @Select("SELECT * FROM modified_output WHERE FIND_IN_SET(#{tagId}, tags_ids) > 0")
    List<KnowledgeBaseVersionEntity> findRecordsWithTagId(@Param("tagId") String tagId);

    /**
     * 更新指定记录的tags_ids字段
     * @param id 记录ID
     * @param tagsIds 新的标签ID字符串
     * @return 影响的行数
     */
    @Update("UPDATE modified_output SET tags_ids = #{tagsIds} WHERE id = #{id}")
    int updateTagsIds(@Param("id") Long id, @Param("tagsIds") String tagsIds);

    /**
     * 根据taskId查询知识库版本记录是否存在
     *
     * @param taskId 任务ID
     * @return 存在的记录数量
     */
    @Select("SELECT COUNT(*) FROM modified_output WHERE task_id = #{taskId} AND corpus_status = 1")
    int countByTaskId(@Param("taskId") String taskId);

    /**
     * 批量查询taskId列表中已落库的taskId
     *
     * @param taskIds 任务ID列表
     * @return 已落库的taskId列表
     */
    @Select("<script>" +
            "SELECT DISTINCT task_id FROM modified_output " +
            "WHERE task_id IN " +
            "<foreach item='item' index='index' collection='taskIds' open='(' separator=',' close=')'>" +
            "  #{item}" +
            "</foreach>" +
            " AND corpus_status = 1 AND task_id IS NOT NULL" +
            "</script>")
    List<String> findExistingTaskIds(@Param("taskIds") List<String> taskIds);

    /**
     * 查询modified_output表中所有不重复的rgid
     *
     * @return 所有使用过的rgid列表
     */
    @Select("SELECT DISTINCT rg_id FROM modified_output WHERE rg_id IS NOT NULL ORDER BY rg_id")
    List<Long> findAllDistinctRgIds();
}