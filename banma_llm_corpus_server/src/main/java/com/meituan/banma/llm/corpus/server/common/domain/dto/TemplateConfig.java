package com.meituan.banma.llm.corpus.server.common.domain.dto;

import lombok.Data;
import java.util.List;

/**
 * 模板配置DTO
 * 用于存储不同模板的配置信息
 */
@Data
public class TemplateConfig {
    /**
     * 模板名称
     */
    private String templateName;
    
    /**
     * 组织ID
     */
    private String rgId;
    
    /**
     * 模板ID
     */
    private String templateId;
    
    /**
     * 需要统计的字段列表
     */
    private List<String> fields;
} 