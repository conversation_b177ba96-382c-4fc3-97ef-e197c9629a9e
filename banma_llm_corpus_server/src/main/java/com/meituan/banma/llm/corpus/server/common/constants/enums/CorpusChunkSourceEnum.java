package com.meituan.banma.llm.corpus.server.common.constants.enums;

import lombok.Getter;

@Getter
public enum CorpusChunkSourceEnum {
    OTHER(0, "其他"),
    CORPUS_SERVICE_CONVERT(1, "语料处理服务转换"),
    KM_IMPORT_FRIDAY(2, "KM导入Friday"),
    ;
    Integer code;
    String description;
    CorpusChunkSourceEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }
    public static CorpusChunkSourceEnum fromCode(Integer code) {
        for (CorpusChunkSourceEnum value : CorpusChunkSourceEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
