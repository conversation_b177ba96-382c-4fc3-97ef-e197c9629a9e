package com.meituan.banma.llm.corpus.server.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.meituan.banma.llm.corpus.api.constants.BizCode;
import com.meituan.banma.llm.corpus.api.exception.LlmCorpusException;
import com.meituan.banma.llm.corpus.server.adaptor.XmOpenGroupServiceAdaptor;
import com.meituan.banma.llm.corpus.server.common.MtConfigService;
import com.meituan.banma.llm.corpus.server.common.constants.enums.ImGeneralMessageTypeEnum;
import com.meituan.banma.llm.corpus.server.common.constants.enums.ImMessageTypeEnum;
import com.meituan.banma.llm.corpus.server.common.domain.dto.DxChatMessageRecord;
import com.meituan.banma.llm.corpus.server.common.domain.dto.DxUserInfo;
import com.meituan.banma.llm.corpus.server.common.domain.dto.GroupInfoDto;
import com.meituan.banma.llm.corpus.server.common.domain.dto.MergedMsgResult;
import com.meituan.banma.llm.corpus.server.common.domain.dto.NoticeDetailDto;
import com.meituan.banma.llm.corpus.server.rpc.dx.XmAuthRpcCommonService;
import com.meituan.banma.llm.corpus.server.rpc.mbox.MboxRpcService;
import com.meituan.banma.llm.corpus.server.service.IDxGroupChatService;
import com.meituan.banma.llm.corpus.server.rpc.dx.XmAuthRpcService;
import com.meituan.banma.llm.corpus.server.service.IEmpQueryService;
import com.meituan.banma.llm.corpus.server.service.IMtImageService;
import com.meituan.banma.llm.corpus.server.utils.DatetimeUtils;
import com.sankuai.dxenterprise.open.gateway.service.dx.api.DxService;
import com.sankuai.dxenterprise.open.gateway.service.dx.api.req.AddBotByUserReq;
import com.sankuai.dxenterprise.open.gateway.service.dx.api.req.GetUidByEmpIdReq;
import com.sankuai.dxenterprise.open.gateway.service.dx.api.req.QueryEmpIdByUidListReq;
import com.sankuai.dxenterprise.open.gateway.service.dx.api.req.QueryEmpIdentityByMisListReq;
import com.sankuai.dxenterprise.open.gateway.service.dx.api.resp.AddBotByUserResp;
import com.sankuai.dxenterprise.open.gateway.service.dx.api.resp.GetUidByEmpIdResp;
import com.sankuai.dxenterprise.open.gateway.service.dx.api.resp.QueryEmpIdByUidListResp;
import com.sankuai.dxenterprise.open.gateway.service.dx.api.resp.QueryEmpIdentityByMisListResp;
import com.sankuai.dxenterprise.open.gateway.service.dx.api.resp.UserIdentity;
import com.sankuai.meituan.org.opensdk.model.domain.Emp;
import com.sankuai.xm.openplatform.api.entity.AddGroupMembersReq;
import com.sankuai.xm.openplatform.api.entity.*;
import com.sankuai.xm.openplatform.api.service.open.XmOpenGroupServiceI;
import com.sankuai.xm.openplatform.api.service.open.XmOpenMessageServiceI;
import com.sankuai.xm.openplatform.api.service.open.XmOpenUserServiceI;
import com.sankuai.xm.openplatform.common.entity.BooleanResp;
import groovy.util.logging.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/12/10
 * 大象群聊消息查询
 */
@Slf4j
@Service
public class DxGroupChatServiceImpl implements IDxGroupChatService {
    private static final Logger log = LoggerFactory.getLogger(DxGroupChatServiceImpl.class);

    @Resource
    private XmAuthRpcService xmAuthRpcService;

    @Resource
    private XmAuthRpcCommonService xmAuthRpcCommonService;

    @Resource
    private XmOpenMessageServiceI.Iface xmOpenMessageServiceIface;

    @Resource
    private XmOpenUserServiceI.Iface xmOpenUserServiceIface;

    @Resource
    private XmOpenGroupServiceI.Iface xmOpenGroupServiceIface;

    @Autowired
    private MboxRpcService mboxRpcService;

    @Resource
    private DxService dxService;

    @Autowired
    private IMtImageService mtImageService;

    @Autowired
    private MtConfigService mtConfigService;

    @Autowired
    private IEmpQueryService empQueryService;

    @Resource
    private XmOpenGroupServiceAdaptor xmOpenGroupServiceAdaptor;

    @Override
    public List<DxChatMessageRecord> getTTChatInfo(long groupId, String... robotAppId) {
        List<DxChatMessageRecord> historyGroupMsgs = getHistoryGroupMsgs(groupId, robotAppId);
        // 仅使用未撤回的文本消息
        List<DxChatMessageRecord> filterHistoryGroupMsgs = processMessageList(historyGroupMsgs);
        // 查询发送人Uid对应的姓名并替换
        List<Long> fromUidList = filterHistoryGroupMsgs.stream().map(e -> e.getFromUid()).collect(Collectors.toList());
        Map<Long, DxUserInfo> fromUidNameMapping = getFromUidNameMapping(fromUidList);
        return getDxChatMessageRecords(filterHistoryGroupMsgs, fromUidNameMapping);
    }

    @NotNull
    private List<DxChatMessageRecord> getDxChatMessageRecords(List<DxChatMessageRecord> filterHistoryGroupMsgs, Map<Long, DxUserInfo> fromUidNameMapping) {
        filterHistoryGroupMsgs.forEach(e -> {
            DxUserInfo dxUserInfo = fromUidNameMapping.get(e.getFromUid());
            if (dxUserInfo == null) {
                log.warn("#getTTChatInfo#warn，未查询到用户信息：uid:{}", e.getFromUid());
                e.setFromName("未知");
                return;
            }
            String fromName = dxUserInfo.getName();
            if (e.getFromUid() == 0 && e.getFromPubId() != 0) {
                e.setFromName("机器人");
            } else {
                e.setFromName(StringUtils.isNotBlank(fromName) ? fromName : "未知");
            }
            // 设置MIS ID
            e.setFromMis(dxUserInfo.getMisId());
        });

        // 从DxUserInfo获取MIS ID
        List<String> fromMisList = filterHistoryGroupMsgs.stream()
                .map(e -> {
                    DxUserInfo dxUserInfo = fromUidNameMapping.get(e.getFromUid());
                    return dxUserInfo != null ? dxUserInfo.getMisId() : null;
                })
                .filter(StringUtils::isNotBlank)
                .distinct()
                .collect(Collectors.toList());
        Map<String, Emp> fromMisEmpMapping = empQueryService.getEmpInfoByMisId(fromMisList, new Date());
        if (MapUtils.isEmpty(fromMisEmpMapping)) {
            log.warn("#getTTChatInfo#error,未查询到员工信息,fromMisList:{}，fromMisEmpMapping:{}",fromMisList, fromMisEmpMapping);
        }
        for (DxChatMessageRecord message : filterHistoryGroupMsgs) {
            Emp emp = fromMisEmpMapping.get(message.getFromMis());
            if (emp == null) {
                log.warn("#getTTChatInfo#warn，未查询到员工信息：mis:{}", message.getFromMis());
                message.setUserOrgId("");
                message.setUserOrgName("未知组织");
                continue;
            }
            message.setUserOrgId(emp.getOrgId());
            message.setUserOrgName(emp.getOrgName());
        }
        return filterHistoryGroupMsgs;
    }

    @Override
    public List<DxChatMessageRecord> getSingleMergeChat(Long creatorDxUserId) {
        List<DxChatMessageRecord> singleMergeChatMsgs = getSingleMergePubMsg(Long.parseLong(creatorDxUserId.toString()));
        // 仅使用未撤回的文本消息
        List<DxChatMessageRecord> filterSingleMergeChatMsgs = processMessageList(singleMergeChatMsgs);
        // 查询发送人Uid对应的姓名并替换
        List<Long> fromUidList = filterSingleMergeChatMsgs.stream().map(e -> e.getFromUid()).collect(Collectors.toList());
        Map<Long, DxUserInfo> fromUidNameMapping = getFromUidNameMapping(fromUidList);

        return getDxChatMessageRecords(filterSingleMergeChatMsgs, fromUidNameMapping);
    }

    @Override
    public List<DxChatMessageRecord> getTtChatInfoByTime(long groupId, long startTime, long endTime) {
        List<DxChatMessageRecord> historyGroupMsgs = getHistoryGroupMessageWithFilterRule(groupId, startTime, endTime);
        // 仅使用未撤回的文本消息
        List<DxChatMessageRecord> filterHistoryGroupMsgs = processMessageListWithoutRemove(historyGroupMsgs);
        // 查询发送人Uid对应的姓名并替换
        List<Long> fromUidList = filterHistoryGroupMsgs.stream().map(DxChatMessageRecord::getFromUid).collect(Collectors.toList());
        Map<Long, DxUserInfo> fromUidNameMapping = getFromUidNameMapping(fromUidList);
        filterHistoryGroupMsgs.forEach(e -> {
            DxUserInfo dxUserInfo = fromUidNameMapping.get(e.getFromUid());
            if (dxUserInfo == null) {
                log.warn("#getTTChatInfo#warn，未查询到用户信息：uid:{}", e.getFromUid());
                e.setFromName("未知");
                return;
            }
            String fromName = dxUserInfo.getName();
            if (e.getFromUid() == 0 && e.getFromPubId() != 0) {
                e.setFromName("机器人");
            } else {
                e.setFromName(StringUtils.isNotBlank(fromName) ? fromName : "未知");
            }
            // 设置MIS ID
            e.setFromMis(dxUserInfo.getMisId());
        });
        
        // 从DxUserInfo获取MIS ID
        List<String> fromMisList = filterHistoryGroupMsgs.stream()
                .map(e -> {
                    DxUserInfo dxUserInfo = fromUidNameMapping.get(e.getFromUid());
                    return dxUserInfo != null ? dxUserInfo.getMisId() : null;
                })
                .filter(StringUtils::isNotBlank)
                .distinct()
                .collect(Collectors.toList());
        Map<String, Emp> fromMisEmpMapping = empQueryService.getEmpInfoByMisId(fromMisList, null);
        if (MapUtils.isEmpty(fromMisEmpMapping)) {
            log.warn("#getTtChatInfoByTime#error,未查询到员工信息,fromMisList:{}，fromMisEmpMapping:{}",fromMisList, fromMisEmpMapping);
        }
        for (DxChatMessageRecord message : filterHistoryGroupMsgs) {
            Emp emp = fromMisEmpMapping.get(message.getFromMis());
            if (emp == null) {
                log.warn("#getTtChatInfoByTime#warn，未查询到员工信息：mis:{}", message.getFromMis());
                message.setUserOrgId("");
                message.setUserOrgName("");
                continue;
            }
            message.setUserOrgId(emp.getOrgId());
            message.setUserOrgName(emp.getOrgName());
        }

        // 按照cts时间戳从早到晚排序（升序）
        filterHistoryGroupMsgs.sort(Comparator.comparing(DxChatMessageRecord::getCts));

        return filterHistoryGroupMsgs;
    }

    private List<DxChatMessageRecord> processMessageList(List<DxChatMessageRecord> messageList){
        List<DxChatMessageRecord> res = new ArrayList<>();
        if (CollectionUtils.isEmpty(messageList)) {
            return res;
        }
        // 过滤掉一些无用的消息
        removeNonsenseChatMessage(messageList);
        for (DxChatMessageRecord message : messageList) {
            if (message.isCancel()){
                continue;
            }
            if (message.getType() == ImMessageTypeEnum.TEXT.getCode() || message.getType() == ImMessageTypeEnum.LINK.getCode()) {
                // 文本消息 + 链接
                res.add(message);
            }
            if (message.getType() == ImMessageTypeEnum.QUOTE.getCode()){
                // TODO: 处理引用消息
                DxChatMessageRecord processedMessage = processQuoteMessage(message);
                if (processedMessage != null) {
                    res.add(processedMessage);
                }
            }
            if (message.getType() == ImMessageTypeEnum.IMAGE.getCode()) {
                DxChatMessageRecord processedMessage = processImageMessage(message);
                if (processedMessage != null) {
                    res.add(processedMessage);
                }
            }
            if (message.getType() == ImMessageTypeEnum.GENERAL.getCode()){
                 DxChatMessageRecord processedMessage = processGeneralMessage(message);
                if (processedMessage != null) {
                    res.add(processedMessage);
                }
            }
        }

        return res;
    }

    private List<DxChatMessageRecord> processMessageListWithoutRemove(List<DxChatMessageRecord> messageList){
        List<DxChatMessageRecord> res = new ArrayList<>();
        if (CollectionUtils.isEmpty(messageList)) {
            return res;
        }

        for (DxChatMessageRecord message : messageList) {
            if (message.isCancel()){
                continue;
            }
            if (message.getType() == ImMessageTypeEnum.TEXT.getCode() || message.getType() == ImMessageTypeEnum.LINK.getCode()) {
                // 文本消息 + 链接
                res.add(message);
            }
            if (message.getType() == ImMessageTypeEnum.QUOTE.getCode()){
                // TODO: 处理引用消息
                DxChatMessageRecord processedMessage = processQuoteMessage(message);
                if (processedMessage != null) {
                    res.add(processedMessage);
                }
            }
            if (message.getType() == ImMessageTypeEnum.IMAGE.getCode()) {
                DxChatMessageRecord processedMessage = processImageMessage(message);
                if (processedMessage != null) {
                    res.add(processedMessage);
                }
            }
            if (message.getType() == ImMessageTypeEnum.GENERAL.getCode()){
                DxChatMessageRecord processedMessage = processGeneralMessage(message);
                if (processedMessage != null) {
                    res.add(processedMessage);
                }
            }
        }

        return res;
    }

    private void removeNonsenseChatMessage(List<DxChatMessageRecord> messageList){
        if (CollectionUtils.isEmpty(messageList)){
            return;
        }
        List<String> removeMessageKeywords = mtConfigService.getNonsenseMessageKeywords();
        if (CollectionUtils.isEmpty(removeMessageKeywords)){
            log.warn("#removeNonsenseChatMessage#warn,未配置无用消息关键词");
            return;
        }
        messageList.removeIf(e -> {
            if (e == null || e.getMessage() == null){
                return true;
            }
            if (e.getType() != ImMessageTypeEnum.TEXT.getCode()){
                return false;
            }
            JSONObject messageBody = e.getMessage();
            String text = messageBody.getString("text");
            if (StringUtils.isBlank(text)){
                return true;
            }
            for (String keyword : removeMessageKeywords) {
                if (text.contains(keyword)) {
                    log.info("#removeNonsenseChatMessage#info,命中关键字，text:{},keyword:{}", text, keyword);
                    return true;
                }
            }
            return false;
        });
    }
    private DxChatMessageRecord processGeneralMessage(DxChatMessageRecord message){
        if (message == null || message.getType() != ImMessageTypeEnum.GENERAL.getCode()){
            return message;
        }
        JSONObject messageBody = message.getMessage();
        if (messageBody == null || messageBody.get("type") == null){
            log.warn("#DxGroupChatServiceImpl.processGeneralMessage#warn,处理通用消息失败，消息体信息缺失，message:{}",message);
            return message;
        }
        int type = NumberUtils.toInt(messageBody.getString("type"));
        if(type == ImGeneralMessageTypeEnum.RICH_TEXT.getCode()){
            // 当前只处理 富文本 类型
            String base64Data = messageBody.getString("data");
            if (StringUtils.isBlank(base64Data)){
                log.warn("#DxGroupChatServiceImpl.processGeneralMessage#warn,处理富文本消息失败，data为空，message:{}",message);
                return null;
            }
            String data = null;
            try {
                data = new String(Base64.getDecoder().decode(base64Data));
            } catch (Exception e) {
                log.error("#DxGroupChatServiceImpl.processGeneralMessage#error,处理富文本消息失败，data解码失败，message:{}",message,e);
                return null;
            }
            if (StringUtils.isBlank(data)){
                log.warn("#DxGroupChatServiceImpl.processGeneralMessage#warn,处理富文本消息失败，data解码后为空，message:{}",message);
                return null;
            }
            
            JSONObject dataJson = null;
            try {
                dataJson = JSON.parseObject(data);
            } catch (Exception e) {
                log.error("#DxGroupChatServiceImpl.processGeneralMessage#error,处理富文本消息失败，data解析失败，message:{}",message,e);
                return null;
            }
            if (dataJson == null){
                log.warn("#DxGroupChatServiceImpl.processGeneralMessage#warn,处理富文本消息失败，dataJson为空，message:{}",message);
                return null;
            }
            if (!dataJson.containsKey("nodes")){
                log.warn("#DxGroupChatServiceImpl.processGeneralMessage#warn,处理富文本消息失败，dataJson不包含node，message:{}",message);
                return null;
            }
            JSONArray richTextList = dataJson.getJSONArray("nodes");
            if (richTextList == null || richTextList.isEmpty()){
                log.warn("#DxGroupChatServiceImpl.processGeneralMessage#warn,处理富文本消息失败，nodes为空，message:{}",message);
                return null;
            }
            JSONArray resultNodes = new JSONArray();
            for (Object richText : richTextList) {
                if (richText == null){
                    log.warn("#DxGroupChatServiceImpl.processGeneralMessage#warn,处理富文本消息失败，nodes中存在空值，message:{}",message);
                    continue;
                }
                JSONObject richTextJson = (JSONObject) richText;
                String nodeType = richTextJson.getString("t");
                if (StringUtils.isBlank(nodeType)){
                    log.warn("#DxGroupChatServiceImpl.processGeneralMessage#warn,处理富文本消息失败，nodeType为空，message:{}",message);
                    continue;
                }
                if ("text".equals(nodeType)){
                    JSONObject textNode = new JSONObject();
                    textNode.put("messageType","text");
                    textNode.put("content", richTextJson.getString("text"));
                    resultNodes.add(textNode);
                    continue;
                }
                if ("img".equals(nodeType)){
                    JSONObject imageNode = new JSONObject();
                    String imageUrl = richTextJson.getString("origin");
                    if (StringUtils.isBlank(imageUrl)){
                        log.warn("#DxGroupChatServiceImpl.processGeneralMessage#warn,处理富文本消息失败，图片链接为空，message:{}",message);
                        continue;
                    }
                    String outerUrl = getOuterUrlByImageUrl(imageUrl);
                    if (StringUtils.isBlank(outerUrl)){
                        log.warn("#DxGroupChatServiceImpl.processGeneralMessage#warn,处理富文本消息失败，图片链接转换为外网链接失败，message:{}",message);
                        continue;
                    }
                    imageNode.put("messageType","img");
                    imageNode.put("imageUrl", outerUrl);
                    resultNodes.add(imageNode);
                }
            }
            dataJson.put("nodes",resultNodes.toJSONString());
            messageBody.put("data",dataJson.toJSONString());
            message.setMessage(messageBody);
            return message;
        }
        // 其他未处理的类型直接返回null
        return null;
    }
    private DxChatMessageRecord processQuoteMessage(DxChatMessageRecord message){
        if (message == null || message.getType() != ImMessageTypeEnum.QUOTE.getCode()){
            return message;
        }
        JSONObject messageBody = message.getMessage();
        if (messageBody == null){
            log.warn("#DxGroupChatServiceImpl.processQuotaMessage#warn,处理引用消息失败，消息体信息缺失，message:{}",message);
            return message;
        }
        // 开始处理lastReply
        JSONObject lastReplyJson = messageBody.getJSONObject("lastReply");
        if (lastReplyJson == null){
            log.warn("#DxGroupChatServiceImpl.processQuotaMessage#warn,处理引用消息失败，lastReply为空，message:{}",message);
            return message;
        }
        JSONObject lastReplyMessage = lastReplyJson.getJSONObject("message");
        if (lastReplyMessage == null){
            log.warn("#DxGroupChatServiceImpl.processQuotaMessage#warn,处理引用消息失败，lastReplyMessage为空，message:{}",message);
            return message;
        }
        DxChatMessageRecord lastReplyMsg = convertQuoteMsgToDxMsgRecord(lastReplyMessage);
        if (lastReplyMsg == null){
            log.warn("#DxGroupChatServiceImpl.processQuotaMessage#warn,处理引用消息失败，lastReplyMsg为空，message:{}",message);
            return message;
        }
        if (lastReplyMsg.getType() == ImMessageTypeEnum.TEXT.getCode() || lastReplyMsg.getType() == ImMessageTypeEnum.LINK.getCode()){
            lastReplyJson.put("message", lastReplyMsg);
            messageBody.put("lastReply", lastReplyJson);
        }
        if (lastReplyMsg.getType() == ImMessageTypeEnum.IMAGE.getCode()){
            DxChatMessageRecord imageMessage = processImageMessage(lastReplyMsg);
            if (imageMessage != null){
                lastReplyJson.put("message", imageMessage);
                messageBody.put("lastReply", lastReplyJson);
            }
        }
        if (lastReplyMsg.getType() == ImMessageTypeEnum.GENERAL.getCode()){
            DxChatMessageRecord generalMessage = processGeneralMessage(lastReplyMsg);
            if (generalMessage != null){
                lastReplyJson.put("message", generalMessage);
                messageBody.put("lastReply", lastReplyJson);
            }
        }
        // 开始处理quoted
        JSONArray quoteMsg = messageBody.getJSONArray("quoted");
        if (quoteMsg == null || quoteMsg.isEmpty()){
            log.warn("#DxGroupChatServiceImpl.processQuotaMessage#warn,处理引用消息失败，引用消息为空，message:{}",message);
            return null;
        }
        JSONArray resultQuoteMsg = new JSONArray();
        for (Object msg: quoteMsg) {
            if (msg == null){
                log.warn("#DxGroupChatServiceImpl.processQuotaMessage#warn,处理引用消息失败，引用消息中存在空值，message:{}",message);
                continue;
            }
            JSONObject msgJson = (JSONObject) msg;
            JSONObject msgBody = msgJson.getJSONObject("message");
            DxChatMessageRecord outerMessage = convertQuoteMsgToDxMsgRecord(msgBody);
            if (outerMessage == null){
                log.warn("#DxGroupChatServiceImpl.processQuotaMessage#warn,处理引用消息失败，outerMessage空值，message:{}",message);
                continue;
            }
            if (outerMessage.getType() == ImMessageTypeEnum.TEXT.getCode() || outerMessage.getType() == ImMessageTypeEnum.LINK.getCode()){
                resultQuoteMsg.add(msgJson);
            }
            if (outerMessage.getType() == ImMessageTypeEnum.IMAGE.getCode()){
                DxChatMessageRecord imageMessage = processImageMessage(outerMessage);
                if (imageMessage != null){
                    msgJson.put("message", imageMessage);
                    resultQuoteMsg.add(msgJson);
                }
            }
            if (outerMessage.getType() == ImMessageTypeEnum.GENERAL.getCode()){
                DxChatMessageRecord generalMessage = processGeneralMessage(outerMessage);
                if (generalMessage != null){
                    msgJson.put("message", generalMessage);
                    resultQuoteMsg.add(msgJson);
                }
            }
            // 其他类型的消息不做处理
        }
        messageBody.put("quoted",resultQuoteMsg);
        message.setMessage(messageBody);
        return message;
    }
    private DxChatMessageRecord convertQuoteMsgToDxMsgRecord(JSONObject message){
        if (message == null || message.get("type") == null){
            return null;
        }
        DxChatMessageRecord dxChatMessageRecord = new DxChatMessageRecord();
        dxChatMessageRecord.setType(message.getInteger("type"));
        dxChatMessageRecord.setMsgId(message.getLong("msgId"));
        dxChatMessageRecord.setFromName("fromName");
        dxChatMessageRecord.setFromUid(message.getLong("fromUid"));
        dxChatMessageRecord.setCts(message.getLong("cts"));
        dxChatMessageRecord.setMessage((JSONObject) message.get("message"));
        return dxChatMessageRecord;
    }
    private DxChatMessageRecord processImageMessage(DxChatMessageRecord message){
        if (message == null || message.getType() != 4){
            return message;
        }
        JSONObject messageBody = message.getMessage();
        if (messageBody == null){
            log.warn("#DxGroupChatServiceImpl.processImageMessage#warn,处理图片消息失败，消息体信息缺失，message:{}",message);
            return message;
        }
        String imageUrl = messageBody.getString("normal");
        String imageToken = messageBody.getString("token");
        if (StringUtils.isBlank(imageUrl) || StringUtils.isBlank(imageToken)){
            log.warn("#DxGroupChatServiceImpl.processImageMessage#warn,处理图片消息失败，图片链接或token为空，message:{}",message);
            return null;
        }
        String outerUrl = getOuterUrlByImageUrl(imageUrl);
        if (StringUtils.isBlank(outerUrl)){
            log.warn("#DxGroupChatServiceImpl.processImageMessage#warn,处理图片消息失败，outerUrl为空，message:{}",message);
            return null;
        }
        message.setMessage(new JSONObject().fluentPut("imageUrl", outerUrl));
        return message;
    }
    private String getOuterUrlByImageUrl(String imageUrl){
        if (StringUtils.isBlank(imageUrl)){
            log.warn("#DxGroupChatServiceImpl.getOuterUrlByImageUrl#warn,图片链接为空");
            return null;
        }
        String noAuthUrl = mboxRpcService.changeDownloadTempUrl(imageUrl);
        if (StringUtils.isBlank(noAuthUrl)){
            log.warn("#DxGroupChatServiceImpl.getOuterUrlByImageUrl#warn,处理图片链接失败，resp为空，imageUrl:{}",imageUrl);
            return null;
        }
        String outerUrl = null;
        try {
            outerUrl = mtImageService.uploadImageByUrl(noAuthUrl);
        } catch (LlmCorpusException e) {
            log.warn("#DxGroupChatServiceImpl.getOuterUrlByImageUrl#warn,处理图片链接失败，imageUrl:{}",imageUrl,e);
            return null;
        }catch (Exception e){
            log.error("#DxGroupChatServiceImpl.getOuterUrlByImageUrl#error,处理图片链接失败，imageUrl:{}",imageUrl,e);
            return null;
        }
        return outerUrl;
    }
    /**
     * 获取大象群名称
     * @param groupId 大象群id
     * @return TT id
     * https://km.sankuai.com/collabpage/1357068390#id-7.%E6%9C%BA%E5%99%A8%E4%BA%BA%E8%8E%B7%E5%8F%96%E6%89%80%E5%9C%A8%E7%BE%A4%E7%9A%84%E7%BE%A4%E4%BF%A1%E6%81%AF
     */
    @Override
    public String getGroupNameByDxGroupId(long groupId) {
        GroupInformationReq groupInformationReq = new GroupInformationReq();
        groupInformationReq.setGid(groupId);

        try {
            GroupInformationResp groupInformationResp = xmOpenGroupServiceIface
                    .getGroupInformation(xmAuthRpcService.getToken(), groupInformationReq);

            if (groupInformationResp == null || groupInformationResp.getStatus().code != 0) {
                log.error("Failed to retrieve group information. GroupId: {}, Status code: {}", groupId,
                        groupInformationResp != null ? groupInformationResp.getStatus().code : "null");
                return "";
            }
            return groupInformationResp.getGroupInfo().getName();
        } catch (Exception e) {
            log.error("#xmOpenGroupServiceIface.getGroupInformation#error, groupInformationReq:{}", groupInformationReq, e);
            return "";
        }
    }

    /**
     * 解析大象群公告获取TT id
     * 
     * @param groupId 大象群id
     * @return TT id
     * https://km.sankuai.com/collabpage/1357068390#id-7.%E6%9C%BA%E5%99%A8%E4%BA%BA%E8%8E%B7%E5%8F%96%E6%89%80%E5%9C%A8%E7%BE%A4%E7%9A%84%E7%BE%A4%E4%BF%A1%E6%81%AF
     */
    @Override
    public String getTicketIdByDxGroupNotice(long groupId) {
        // 解析群公告中的TTid
        //GroupNoticeInfo(noticeContent:发起人：daili07 ｜ 处理人：daili07 ｜ [快捷查看|mtdaxiang://www.meituan.com/dxmp?appkey=OPEN_PLATFORM_trouble_tracker&path=%2Fdxmp-detail%3Fid%3D324915588] ｜ [系统内查看|http://tt.cloud.test.sankuai.com/ticket/detail?id=324915588], updateTime:1735182608432)
        GetGroupNoticeReq getGroupNoticeReq = new GetGroupNoticeReq().setGid(groupId);

        try {
            GetGroupNoticeResp getGroupNoticeResp = xmOpenGroupServiceIface.getGroupNotice(xmAuthRpcService.getToken(), getGroupNoticeReq);
            log.info("#xmOpenGroupServiceIface.getGroupNoticeResp:{}", getGroupNoticeResp);
            if (getGroupNoticeResp == null || getGroupNoticeResp.getStatus().code != 0) {
                log.error("Failed to retrieve group notice. GroupId: {}, Status code: {}", groupId,
                        getGroupNoticeResp != null ? getGroupNoticeResp.getStatus().code : "null");
                return null;
            }
            String groupNotice = getGroupNoticeResp.getGroupNoticeInfo().noticeContent;
            return extractTTId(groupNotice);
        } catch (Exception e) {
            log.error("#xmOpenGroupServiceIface.getGroupNotice#error, getGroupNoticeReq:{}", getGroupNoticeReq, e);
            return null;
        }
    }

    /**
     * 解析大象群公告详情
     *
     * @param groupId 大象群id
     * @return TT id
     */
    public NoticeDetailDto getNoticeDetailByDxGroupNotice(long groupId) {
        try {
            GetGroupNoticeResp getGroupNoticeResp = xmOpenGroupServiceAdaptor.getGroupNoticeResp(groupId);
            String groupNotice = getGroupNoticeResp.getGroupNoticeInfo().noticeContent;
            NoticeDetailDto noticeDetailDto = new NoticeDetailDto();
            noticeDetailDto.setTicketId(extractTTId(groupNotice));
            noticeDetailDto.setCreatorMisId(extractCreatorMisId(groupNotice));
            noticeDetailDto.setHandlerMisId(extractHandlerMisId(groupNotice));
            return noticeDetailDto;
        } catch (Exception e) {
            log.error("#xmOpenGroupServiceIface.getGroupNotice#error, groupId:{}", groupId, e);
            return null;
        }
    }

    private String extractTTId(String input) {
        // 编译正则表达式
        String regex = "detail\\?id=(\\d+)";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(input);

        // 查找并返回第一串数字
        return matcher.find() ? matcher.group(1) : "";
    }

    /**
     * 解析大象群公告获取发起人misId
     * @param input 群公告
     * @return 发起人misId
     */
    private String extractCreatorMisId(String input) {
        // 编译正则表达式，"发起人："后的非竖线字符（自动去除前后空格）
        String regex = "发起人：\\s*([^｜]+)";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(input);

        // 查找并返回发起人信息
        if (matcher.find()) {
            return matcher.group(1).trim(); // 去除捕获内容的前后空格
        }
        return "";
    }

    /**
     * 解析大象群公告获取值班人misId
     * @param input 群公告
     * @return 值班人misId
     */
    private String extractHandlerMisId(String input) {
        // 编译正则表达式，"处理人："后的非竖线字符（自动去除前后空格）
        String regex = "处理人：\\s*([^｜]+)";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(input);

        // 查找并返回处理人信息
        if (matcher.find()) {
            return matcher.group(1).trim(); // 去除捕获内容的前后空格
        }
        return "";
    }

    @Override
    public void sendMessageToGroupWithAt(long groupId, long uid, String fromName, String message) {
        try{
           // TODO 去掉
//            if (Tracer.id() != null) {
//                message += "Tracer: " + Tracer.id();
//            }
            SendGroupMsgByRobotReq sendGroupMsgByRobotReq = buildSendGroupMsgByRobotReqWithAt(groupId, uid, fromName, message);
            SendGroupMsgByRobotRes sendGroupMsgByRobotRes = xmOpenMessageServiceIface.sendGroupMsgByRobot(xmAuthRpcService.getToken(), sendGroupMsgByRobotReq);
            log.info("#xmOpenMessageServiceIface.sendGroupFMsgByRobot#response:{}", sendGroupMsgByRobotRes);
        } catch (Exception e) {
            log.error("#xmOpenMessageServiceIface.sendGroupFMsgByRobot#error, groupId:{}, message:{}", groupId, message, e);
        }
    }

    @Override
    public void sendMessageToGroup(long groupId, String message) {
        try{
            // TODO 去掉
//            if (Tracer.id() != null) {
//                message += "Tracer: " + Tracer.id();
//            }
            SendGroupMsgByRobotReq sendGroupMsgByRobotReq = buildSendGroupMsgByRobotReq(groupId, message);
            SendGroupMsgByRobotRes sendGroupMsgByRobotRes = xmOpenMessageServiceIface.sendGroupMsgByRobot(xmAuthRpcService.getToken(), sendGroupMsgByRobotReq);
            log.info("#xmOpenMessageServiceIface.sendGroupMsgByRobot#response:{}", sendGroupMsgByRobotRes);
        } catch (Exception e) {
            log.error("#xmOpenMessageServiceIface.sendGroupFMsgByRobot#error, groupId:{}, message:{}", groupId, message, e);
        }
    }

    @Override
    public void sendMessageToSingle(long uid, String message) {
        try{
            SendChatMsgByRobotReq sendChatMsgByRobotReq = buildSendChatMsgByRobotReq(uid, message);
            SendChatMsgByRobotRes sendChatMsgByRobotRes = xmOpenMessageServiceIface.sendChatMsgByRobot(xmAuthRpcService.getToken(), sendChatMsgByRobotReq);
            log.info("#xmOpenMessageServiceIface.sendChatMsgByRobot#response:{}", sendChatMsgByRobotRes);
        } catch (Exception e) {
            log.error("#xmOpenMessageServiceIface.sendGroupFMsgByRobot#error, groupId:{}, message:{}", uid, message, e);
        }
    }

    @Override
    public void sendMessageToGroupSpecifyRobot(long groupId, String message, String robotAppId, String robotAppSecret) {
        try{
            SendGroupMsgByRobotReq sendGroupMsgByRobotReq = buildSendGroupMsgByRobotReq(groupId, message);
            SendGroupMsgByRobotRes sendGroupMsgByRobotRes = xmOpenMessageServiceIface.sendGroupMsgByRobot(xmAuthRpcCommonService.getToken(robotAppId, robotAppSecret), sendGroupMsgByRobotReq);
            log.info("#xmOpenMessageServiceIface.sendMessageToGroupSpecifyRobot#response:{}", sendGroupMsgByRobotRes);
        } catch (Exception e) {
            log.error("#xmOpenMessageServiceIface.sendGroupFMsgByRobot#error, groupId:{}, message:{}", groupId, message, e);
        }
    }

    private SendGroupMsgByRobotReq buildSendGroupMsgByRobotReq(long groupId, String message) {

        // 构建message
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("text", message);

        // 创建并设置 SendMsgInfo 对象
        SendMsgInfo sendMsgInfo = new SendMsgInfo();
        sendMsgInfo.setType("text");
        sendMsgInfo.setBody(jsonObject.toJSONString());
        sendMsgInfo.setIsDynamicMsg(false);

        // 创建并设置 SendGroupMsgByRobotReq 对象
        SendGroupMsgByRobotReq sendGroupMsgByRobotReq = new SendGroupMsgByRobotReq();
        sendGroupMsgByRobotReq.setGid(groupId);
        sendGroupMsgByRobotReq.setSendMsgInfo(sendMsgInfo);

        return sendGroupMsgByRobotReq;
    }

    private SendChatMsgByRobotReq buildSendChatMsgByRobotReq(long uid, String message) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("text", message);

        SendMsgInfo sendMsgInfo = new SendMsgInfo();
        sendMsgInfo.setType("text");
        sendMsgInfo.setBody(jsonObject.toJSONString());
        sendMsgInfo.setIsDynamicMsg(false);
        SendChatMsgByRobotReq sendChatMsgByRobotReq = new SendChatMsgByRobotReq();
        sendChatMsgByRobotReq.setReceiverIds(Collections.singletonList(uid));
        sendChatMsgByRobotReq.setSendMsgInfo(sendMsgInfo);

        return sendChatMsgByRobotReq;
    }

    /**
     * 构造发消息请求
     * https://km.sankuai.com/collabpage/1357048485#id-SendMsgInfo
     */
    private SendGroupMsgByRobotReq buildSendGroupMsgByRobotReqWithAt(long groupId, long uid, String fromName, String message) {
        // 构建 JSON 字符串
        JSONObject extensionJson = new JSONObject()
                .fluentPut("extension", new JSONObject()
                .fluentPut("at", new JSONArray().fluentAdd(String.valueOf(uid))));
        
        // 构建message
        JSONObject bodyJson = new JSONObject()
                .fluentPut("text", String.format("[@%s|mtdaxiang://www.meituan.com/profile?uid=%d&isAt=true] %s", fromName, uid, message));

        // 创建并设置 SendMsgInfo 对象
        SendMsgInfo sendMsgInfo = new SendMsgInfo();
        sendMsgInfo.setType("text");
        sendMsgInfo.setBody(bodyJson.toJSONString());
        sendMsgInfo.setExtension(extensionJson.toJSONString());
        sendMsgInfo.setIsDynamicMsg(false);

        // 创建并设置 SendGroupMsgByRobotReq 对象
        SendGroupMsgByRobotReq sendGroupMsgByRobotReq = new SendGroupMsgByRobotReq();
        sendGroupMsgByRobotReq.setGid(groupId);
        sendGroupMsgByRobotReq.setSendMsgInfo(sendMsgInfo);

        return sendGroupMsgByRobotReq;
    }

    //获取最近的一条合并的记录
    private List<DxChatMessageRecord> getSingleMergePubMsg(long uid) {
        List<DxChatMessageRecord> allHistoryPubChatMsgs = Lists.newArrayList();
        boolean hasMore = true;
        long pageToken = 0;
        int cycleCount = 0;

        // 循环获取大象群消息记录，最多循环50次。
        while (hasMore && cycleCount++ < 50) {
            HistoryPubMsgReq request = createHistoryPubMsgReq(uid, pageToken);
            try {
                log.info("#xmOpenMessageServiceIface.getSingleMergePubMsg#request:{}", request);
                HistoryPubMsgResp response = xmOpenMessageServiceIface
                        .getHistoryPubMsgs(xmAuthRpcService.getToken(), request);
                if (response == null || response.getStatus().code != 0) {
                    log.error("Failed to retrieve group messages. uId: {}, Status code: {}", uid, response != null ? response.getStatus().code : "null");
                    break;
                }
                hasMore = response.hasMore;
                pageToken = response.pageToken;
                response.pubMsgList.forEach(msg -> allHistoryPubChatMsgs.add(convertToDxChatMessageRecord(msg)));
            } catch (TException e) {
                log.error("#xmOpenMessageServiceIface.getSingleMergePubMsg#timeout, HistoryPubMsgReq:{}", request);
                break;
            }
        }
        List<DxChatMessageRecord> singleChatMergedMsgs = Lists.newArrayList();
        MergedMsgResult lastMergeMsgs = findLastMergeMsgs(uid, allHistoryPubChatMsgs);
        if(lastMergeMsgs!=null){
            if(lastMergeMsgs.getChatMsgList()!=null&&!lastMergeMsgs.getChatMsgList().isEmpty()){
                lastMergeMsgs.getChatMsgList().forEach(msg -> singleChatMergedMsgs.add(convertToDxChatMessageRecord(msg)));
            }else if (lastMergeMsgs.getGroupMsgList()!=null&&!lastMergeMsgs.getGroupMsgList().isEmpty()){
                lastMergeMsgs.getGroupMsgList().forEach(msg -> singleChatMergedMsgs.add(convertToDxChatMessageRecord(msg)));
            }else {
                return null;
            }
        }else {
            return null;
        }
        return singleChatMergedMsgs;
    }



    private MergedMsgResult findLastMergeMsgs(long uid, List<DxChatMessageRecord> allHistoryGroupMsgs) {
        int size = allHistoryGroupMsgs.size();
        boolean find = false;
        long msgId = -1L;
        int i = 0;
        try {
            while(!find&&i<size){
                DxChatMessageRecord dxChatMessageRecord = allHistoryGroupMsgs.get(i);
                JSONObject msgExt = dxChatMessageRecord.getMsgExt();
                String isMergeMessage = null;
                try {
                    isMergeMessage = (String) msgExt.get("isMergeMessage");
                } catch (Exception e) {
                    isMergeMessage = "false";
                }
                if("true".equals(isMergeMessage)){
                    find = true;
                    msgId = dxChatMessageRecord.getMsgId();
                    break;
                }else{
                    i++;
                }
            }
            if(find){
                return getSingleMergedMsg(msgId, ChatType.PUB_CHAT.getValue());
            }else {
                return null;
            }
        } catch (Exception e) {
            log.error("#getHistoryOneMergePubMsg#error, uid:{}", uid, e);
            return null;
        }
    }


    /**
     * 获取大象群消息记录
     * 文档:
     * https://km.sankuai.com/collabpage/1357048485#id-8.%E8%8E%B7%E5%8F%96%E7%BE%A4%E8%81%8A%E5%8E%86%E5%8F%B2%E6%B6%88%E6%81%AF
     * 
     * @param groupId 大象群id
     */
    private List<DxChatMessageRecord> getHistoryGroupMsgs(long groupId, String... robotAppId) {
        List<DxChatMessageRecord> allHistoryGroupMsgs = Lists.newArrayList();
        boolean hasMore = true;
        long pageToken = 0;
        int cycleCount = 0;

        // 循环获取大象群消息记录，最多循环50次。
        while (hasMore && cycleCount++ < 50) {
            HistoryGroupMsgReq request = createHistoryGroupMsgReq(groupId, pageToken);
            try {
                log.info("#xmOpenMessageServiceIface.getHistoryGroupMsgs#request:{}", request);
                HistoryGroupMsgResp response = xmOpenMessageServiceIface
                        .getHistoryGroupMsgs(xmAuthRpcService.getToken(robotAppId), request);
                if (response == null || response.getStatus().code != 0) {
                    log.error("Failed to retrieve group messages. GroupId: {}, Status code: {}", groupId, response != null ? response.getStatus().code : "null");
                    break;
                }


                hasMore = response.hasMore;
                pageToken = response.pageToken;
                response.groupMsgList.forEach(msg -> allHistoryGroupMsgs.add(convertToDxChatMessageRecord(msg)));
            } catch (TException e) {
                log.error("#xmOpenMessageServiceIface.getHistoryGroupMsgs#timeout, historyGroupMsgReq:{}", request);
                break;
            }
        }
        
        // 添加日志，打印获取到的群聊消息记录
        log.info("#getHistoryGroupMsgs# 获取到的群聊消息总数: {}, groupId: {}", allHistoryGroupMsgs.size(), groupId);
        return allHistoryGroupMsgs;
    }

    private List<DxChatMessageRecord> getHistoryGroupMessageWithFilterRule(long groupId, long startTime, long endTime) {
        List<DxChatMessageRecord> allHistoryGroupMsgs = Lists.newArrayList();
        boolean hasMore = true;
        long pageToken = 0;
        int cycleCount = 0;

        // 循环获取大象群消息记录，最多循环50次。
        while (hasMore && cycleCount++ < 50) {
            HistoryGroupMsgReq request = createHistoryGroupMsgReq(groupId, pageToken, startTime, endTime, 100);
            try {
                log.info("#xmOpenMessageServiceIface.getHistoryGroupMsgs#request:{}", request);
                HistoryGroupMsgResp response = xmOpenMessageServiceIface
                        .getHistoryGroupMsgs(xmAuthRpcService.getToken(), request);
                if (response == null || response.getStatus().code != 0) {
                    log.error("Failed to retrieve group messages. GroupId: {}, Status code: {}", groupId, response != null ? response.getStatus().code : "null");
                    break;
                }
                
                // 打印完整的response对象
                log.info("#getHistoryGroupMessageWithFilterRule# 完整的响应结果: response={}", JSON.toJSONString(response));

                hasMore = response.hasMore;
                pageToken = response.pageToken;
                response.groupMsgList.forEach(msg -> allHistoryGroupMsgs.add(convertToDxChatMessageRecord(msg)));
            } catch (TException e) {
                log.error("#xmOpenMessageServiceIface.getHistoryGroupMsgs#timeout, historyGroupMsgReq:{}", request);
                break;
            }
        }
        
        // 添加日志，打印获取到的群聊消息记录
        log.info("#getHistoryGroupMessageWithFilterRule# 获取到的群聊消息总数: {}, groupId: {}, startTime: {}, endTime: {}", 
                 allHistoryGroupMsgs.size(), groupId, startTime, endTime);
        
        return allHistoryGroupMsgs;
    }
    /**
     * 创建大象群消息请求
     * 
     * @param groupId 大象群id
     * @param pageToken 分页token, 查询偏移量，初始值为0
     * @return 大象群消息请求供调rpc接口使用
     */
    private HistoryGroupMsgReq createHistoryGroupMsgReq(long groupId, long pageToken) {
        HistoryGroupMsgReq request = new HistoryGroupMsgReq();
        request.setGid(groupId);
        // 使用MtConfigService获取配置的历史消息查询年限
        Integer historyYears = mtConfigService.getHistoryMessageQueryYears();
        request.setStartTime(LocalDateTime.now().minusYears(historyYears).toInstant(ZoneOffset.UTC).toEpochMilli());
        request.setEndTime(System.currentTimeMillis());
        request.setPageSize(100);
        if (pageToken > 0) {
            request.setPageToken(pageToken);
        }
        return request;
    }

    private HistoryPubMsgReq createHistoryPubMsgReq(long uid, long pageToken) {
        HistoryPubMsgReq request = new HistoryPubMsgReq();
        request.setUid(uid);
        // 使用MtConfigService获取配置的历史消息查询年限
        Integer historyYears = mtConfigService.getHistoryMessageQueryYears();
        request.setStartTime(LocalDateTime.now().minusYears(historyYears).toInstant(ZoneOffset.UTC).toEpochMilli());
        request.setEndTime(System.currentTimeMillis());
        request.setPageSize(100);
        if (pageToken > 0) {
            request.setPageToken(pageToken);
        }
        return request;
    }

    private HistoryGroupMsgReq createHistoryGroupMsgReq(long groupId, long pageToken, long startTime, long endTime, int pageSize) {
        HistoryGroupMsgReq request = new HistoryGroupMsgReq();
        request.setGid(groupId);
        
        // 确保startTime小于endTime
        if (startTime >= endTime) {
            log.error("#createHistoryGroupMsgReq#error, 时间参数错误: startTime={}, endTime={}", startTime, endTime);
            log.error("#createHistoryGroupMsgReq#error, 时间参数格式化: startTime={}, endTime={}", 
                    DatetimeUtils.timestampToDateStr(startTime), DatetimeUtils.timestampToDateStr(endTime));
            
            // 修正时间参数，确保startTime小于endTime
            long temp = startTime;
            startTime = endTime - 86400000; // 将开始时间设为结束时间前一天
            endTime = temp + 86400000;      // 将结束时间设为开始时间后一天
            log.info("#createHistoryGroupMsgReq#info, 修正后的时间参数: startTime={}, endTime={}", 
                    DatetimeUtils.timestampToDateStr(startTime), DatetimeUtils.timestampToDateStr(endTime));
        }
        
        request.setStartTime(startTime);
        request.setEndTime(endTime);
        request.setPageSize(pageSize);
        if (pageToken > 0) {
            request.setPageToken(pageToken);
        }
        return request;
    }
    /**
     * 将大象群消息转换为DxChatMessageRecord对象
     * 
     * @param msg 大象群消息
     * @return DxChatMessageRecord对象（差一个用户名，后续查询uid转换时set）
     */
    private DxChatMessageRecord convertToDxChatMessageRecord(GroupMsg msg) {
        DxChatMessageRecord record = new DxChatMessageRecord();
        record.setMsgId(msg.msgId);
        record.setFromUid(msg.fromUid);
        record.setFromPubId(msg.fromPubId);
        record.setGid(msg.gid);
        record.setCancel(msg.isCancel);
        record.setCts(msg.cts);
        record.setType(msg.type);
        record.setMessage(JSON.parseObject(msg.message));
        record.setMsgExt(JSON.parseObject(msg.msgExt));
        return record;
    }

    private DxChatMessageRecord convertToDxChatMessageRecord(PubMsg msg) {
        DxChatMessageRecord record = new DxChatMessageRecord();
        record.setMsgId(msg.msgId);
        record.setFromUid(msg.fromId);
        record.setFromPubId(msg.toId);
        record.setGid(-1);
        record.setCancel(msg.isCancel);
        record.setCts(msg.cts);
        record.setType(msg.type);
        record.setMessage(JSON.parseObject(msg.message));
        record.setMsgExt(JSON.parseObject(msg.msgExt));
        return record;
    }

    private DxChatMessageRecord convertToDxChatMessageRecord(ChatMsg msg) {
        DxChatMessageRecord record = new DxChatMessageRecord();
        record.setMsgId(msg.msgId);
        record.setFromUid(msg.fromId);
        record.setFromPubId(msg.toId);
        record.setGid(-1);
        record.setCancel(msg.isCancel);
        record.setCts(msg.cts);
        record.setType(msg.type);
        record.setMessage(JSON.parseObject(msg.message));
        record.setMsgExt(JSON.parseObject(msg.msgExt));
        return record;
    }

    /**
     * 获取大象uid和名称的映射关系
     * 
     * @param fromUidList 大象uid列表
     * @return 映射关系,示例 : <1234567890,张三>
     *         接口文档: https://km.sankuai.com/collabpage/1357412714
     */
    private Map<Long, DxUserInfo> getFromUidNameMapping(List<Long> fromUidList, String... robotAppId) {
        UserInfosReq userInfosReq = new UserInfosReq();

        HashSet<Long> set = new HashSet<>(fromUidList);
        set.remove(0L);
        userInfosReq.setUids(set);
        Map<Long, DxUserInfo> result;
        try {
            UserInfosResp batchUserInfos = xmOpenUserServiceIface.getBatchUserInfos(xmAuthRpcService.getToken(robotAppId), userInfosReq);
            if (batchUserInfos != null && batchUserInfos.status.code == 0) {
                result = batchUserInfos.userList.stream().collect(Collectors.toMap(e -> e.uid, e -> {
                    DxUserInfo dxUserInfo = new DxUserInfo();
                    dxUserInfo.setDxUserId(e.uid);
                    dxUserInfo.setName(e.name);
                    dxUserInfo.setMisId(e.mis);
                    dxUserInfo.setAvatarUrl(e.avatarUrl);
                    dxUserInfo.setBigAvatarUrl(e.bigAvatarUrl);
                    return dxUserInfo;
                }));
                return result;
            }
        } catch (TException e) {
            log.error("#xmOpenUserServiceIface.getBatchUserInfos#error, fromUidList:{}", fromUidList, e);
        }
        return Collections.emptyMap();
    }

    /**
     * 根据uid获取mis号
     */
    @Override
    public String getMisIdByUid(long uid) {
        UserInfosReq userInfosReq = new UserInfosReq();
        userInfosReq.setUids(Collections.singleton(uid));
        try {
            UserInfosResp batchUserInfos = xmOpenUserServiceIface.getBatchUserInfos(xmAuthRpcService.getToken(), userInfosReq);
            if (batchUserInfos != null && batchUserInfos.status.code == 0) {
                return batchUserInfos.userList.get(0).mis;
            }
        } catch (TException e) {
            throw new RuntimeException(e);
        }
        return "";
    }

    /**
     * 根据uid获取用户信息
     */
    @Override
    public UserDetail getUserDetailByUid(long uid) {
        UserInfosReq userInfosReq = new UserInfosReq();
        userInfosReq.setUids(Collections.singleton(uid));
        try {
            UserInfosResp batchUserInfos = xmOpenUserServiceIface.getBatchUserInfos(xmAuthRpcService.getToken(), userInfosReq);
            if (batchUserInfos != null && batchUserInfos.status.code == 0) {
                return batchUserInfos.userList.get(0);
            }
        } catch (TException e) {
            throw new RuntimeException(e);
        }
        return null;
    }

    /**
     * 根据uid获取empId
     */
    @Override
    public Set<Long> getEmpIdListByUidList(List<Long> uidList) {
        QueryEmpIdByUidListReq req = new QueryEmpIdByUidListReq();
        req.setUidList(uidList); // 使用传入的UID列表
        try {
            QueryEmpIdByUidListResp resp = dxService.queryEmpIdByUidList(xmAuthRpcService.getToken(), req);
            if (resp != null && resp.getStatus().code == 0) {
                Set<Long> empIdList = new HashSet<>();
                for (Long uid : uidList) {
                    Long empId = resp.getData().getEmpIdMap().get(uid);
                    if (empId != null) {
                        empIdList.add(empId); // 将每个UID对应的员工ID添加到列表中
                    }
                }
                return empIdList;
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return Collections.emptySet();
    }

    /**
     * 根据单个empId获取uid
     */
    @Override
    public long getUidByEmpId(long empId) {
        GetUidByEmpIdReq req = new GetUidByEmpIdReq();
        req.setEmpId(empId); // 使用传入的UID列表
        try {
            GetUidByEmpIdResp resp = dxService.getUidByEmpId(xmAuthRpcService.getToken(), req);
            if (resp != null && resp.getStatus().code == 0) {
                return resp.getData().getUid();
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return -1;
    }

    /**
     * 添加机器人到群组
     * 参考文档：https://km.sankuai.com/collabpage/2552597188
     *
     * @param botId 机器人id
     * @param groupId 群组id
     */
    @Override
    public void addBotToGroup(Long botId, Long groupId) {
        if (Objects.isNull(botId)) {
            throw new IllegalArgumentException("botId is null");
        }
        if (Objects.isNull(groupId)) {
            throw new IllegalArgumentException("groupId is null");
        }

        try {
            AddBotByUserReq request = new AddBotByUserReq();
            AddBotByUserReq.Request req = new AddBotByUserReq.Request();
            req.setBotId(botId);
            req.setGid(groupId);
            request.setRequest(req);

            AddBotByUserResp resp = dxService.addBotByUser(xmAuthRpcService.getToken(), xmAuthRpcService.getDxSsoidToken(), request);
            if (resp != null && resp.getStatus().code == 0) {
                log.info("#addBotToGroup#success, botId:{}, groupId:{}", botId, groupId);
            } else {
                log.error("#addBotToGroup#failed, botId:{}, groupId:{}, resp:{}", botId, groupId, resp);
            }
        } catch (Exception e) {
            log.error("#addBotToGroup#error, botId:{}, groupId:{}", botId, groupId, e);
        }
    }

    /**
     * 判断指定用户是否在群里
     * 参考文档：https://km.sankuai.com/collabpage/1357048485
     *
     * @param groupId 群组id
     * @param empId 员工id
     */
    @Override
    public boolean isUserInGroup(long groupId, long empId) throws LlmCorpusException {
        if (groupId <= 0) {
            throw new IllegalArgumentException("群组ID不能为空或小于等于0");
        }
        if (empId <= 0) {
            throw new IllegalArgumentException("员工ID不能为空或小于等于0");
        }

        try {
            // 通过empId获取uid
            log.info("#isUserInGroup#开始转换empId为uid, groupId:{}, empId:{}", groupId, empId);
            long uid = getUidByEmpId(empId);
            
            if (uid <= 0) {
                log.error("#isUserInGroup#转换uid失败, groupId:{}, empId:{}", groupId, empId);
                return false;
            }

            // 构造请求参数
            GroupMemberReq memberReq = new GroupMemberReq();
            memberReq.setGid(groupId);
            memberReq.setMemberUid(uid);
            
            log.info("#isUserInGroup#检查用户是否在群里, groupId:{}, empId:{}, uid:{}", groupId, empId, uid);

            // 调用接口判断用户是否在群里
            BooleanResp booleanResp = xmOpenGroupServiceIface.inGroupMember(xmAuthRpcService.getToken(), memberReq);
            
            // 处理返回结果
            if (booleanResp == null) {
                log.error("#isUserInGroup#接口返回为空, groupId:{}, empId:{}, uid:{}", groupId, empId, uid);
                return false;
            }

            // 检查状态码
            if (booleanResp.getStatus().code != 0) {
                // 特殊处理机器人不在该群的情况
                if (booleanResp.getStatus().code == 70003) {
                    log.error("#isUserInGroup#机器人不在该群, groupId:{}, empId:{}, uid:{}, status:{}", 
                              groupId, empId, uid, booleanResp.getStatus());
                    throw new LlmCorpusException(BizCode.ROBOT_NOT_IN_GROUP.getCode(), "小助手不在该群，请先将[知识库语料小助手]拉入群聊");
                }
                
                log.error("#isUserInGroup#接口调用失败, groupId:{}, empId:{}, uid:{}, status:{}", 
                          groupId, empId, uid, booleanResp.getStatus());
                return false;
            }

            boolean result = booleanResp.isValue();
            log.info("#isUserInGroup#检查结果: {}, groupId:{}, empId:{}, uid:{}", result, groupId, empId, uid);
            return result;
        } catch (LlmCorpusException e) {
            throw e;
        } catch (Exception e) {
            log.error("#isUserInGroup#异常, groupId:{}, empId:{}", groupId, empId, e);
            return false;
        }
    }

    @Override
    public Object testGetMsg(long msgId, int chatType) throws TException {
        String accessToken = xmAuthRpcService.getToken();
        MergeMsgReq mergeMsgReq = new MergeMsgReq();
        List<MergeMsgCondition> mergeCondition = new ArrayList<>();
        MergeMsgCondition mergeMsgCondition = new MergeMsgCondition();
        mergeMsgCondition.setMsgId(msgId);
        //chatType=2  PUB_CHAT
        mergeMsgCondition.setChatType(ChatType.findByValue(chatType));
        mergeCondition.add(mergeMsgCondition);
        mergeMsgReq.setMergeCondition(mergeCondition);
        MergeMsgResp mergeMsg = xmOpenMessageServiceIface.getMergeMsg(accessToken, mergeMsgReq);
        return mergeMsg;
    }


    private MergedMsgResult getSingleMergedMsg(long msgId, int chatType) throws TException {
        String accessToken = xmAuthRpcService.getToken();
        MergeMsgReq mergeMsgReq = new MergeMsgReq();
        List<MergeMsgCondition> mergeCondition = new ArrayList<>();
        MergeMsgCondition mergeMsgCondition = new MergeMsgCondition();
        mergeMsgCondition.setMsgId(msgId);
        //chatType=2  PUB_CHAT
        mergeMsgCondition.setChatType(ChatType.findByValue(chatType));
        mergeCondition.add(mergeMsgCondition);
        mergeMsgReq.setMergeCondition(mergeCondition);
        MergeMsgResp mergeMsg = xmOpenMessageServiceIface.getMergeMsg(accessToken, mergeMsgReq);
        MergedMsgResult mergedMsgResult = new MergedMsgResult();
        if(mergeMsg.getOriginType()==ChatType.CHAT){
            List<ChatMsg> chatMsgList = mergeMsg.getChatMsgList();
            mergedMsgResult.setChatMsgList(chatMsgList);
        } else if(mergeMsg.getOriginType()==ChatType.GROUP_CHAT){
            List<GroupMsg> groupChatMsgList = mergeMsg.getGroupMsgList();
            mergedMsgResult.setGroupMsgList(groupChatMsgList);
        }
        return mergedMsgResult;
    }


    /**
     * 机器人添加群成员
     * @param groupId 大象群id
     * @param botIds 机器人id列表
     * https://km.sankuai.com/collabpage/1357068390#id-2.%E6%9C%BA%E5%99%A8%E4%BA%BA%E6%B7%BB%E5%8A%A0%E7%BE%A4%E6%88%90%E5%91%98
     */
    @Override
    public void addBotsToGroup(long groupId, List<Long> botIds) {
        AddGroupMembersReq addGroupMembersReq = new AddGroupMembersReq();
        addGroupMembersReq.setGid(groupId);
        addGroupMembersReq.setBots(new HashSet<>(botIds));

        try {
            Object resp = xmOpenGroupServiceIface.addGroupMembersByBot(xmAuthRpcService.getToken(), addGroupMembersReq);

            if (resp == null) {
                log.error("#addBotsToGroup# Response is null, GroupId: {}, BotIds: {}", groupId, botIds);
                return;
            }

            int code = -1;
            try {
                code = ((Number) resp.getClass().getMethod("getStatus").invoke(resp).getClass().getField("code").get(
                        resp.getClass().getMethod("getStatus").invoke(resp))).intValue();
            } catch (Exception e) {
                log.error("#addBotsToGroup# Failed to get status code, GroupId: {}, BotIds: {}", groupId, botIds);
                return;
            }

            if (code != 0) {
                log.error("#addBotsToGroup# Failed with code: {}, GroupId: {}, BotIds: {}", code, groupId, botIds);
                return;
            }

            log.info("#addBotsToGroup# Successfully added bots {} to group {}", botIds, groupId);
        } catch (Exception e) {
            log.error("#addBotsToGroup# Error occurred, GroupId: {}, BotIds: {}", groupId, botIds, e);
        }
    }

    /**
     * 获取群名和发起人misId
     * @param groupId 大象群id
     * @return 包含群名和发起人misId的对象
     */
    @Override
    public GroupInfoDto getGroupNameAndCreatorMisId(long groupId) {
        GroupInfoDto groupInfoDto = new GroupInfoDto();
        groupInfoDto.setGroupId(groupId);

        GroupInformationReq groupInformationReq = new GroupInformationReq();
        groupInformationReq.setGid(groupId);

        try {
            GroupInformationResp groupInformationResp = xmOpenGroupServiceIface
                    .getGroupInformation(xmAuthRpcService.getToken(), groupInformationReq);

            if (groupInformationResp == null || groupInformationResp.getStatus().code != 0) {
                log.error("Failed to retrieve group information. GroupId: {}, Status code: {}", groupId,
                        groupInformationResp != null ? groupInformationResp.getStatus().code : "null");
                return groupInfoDto;
            }

            // 设置群名称
            groupInfoDto.setGroupName(groupInformationResp.getGroupInfo().getName());

            // 获取群主uid并转换为misId
            long ownerUid = groupInformationResp.getGroupInfo().getOwnerInfo().getId();
            String creatorMisId = getMisIdByUid(ownerUid);
            groupInfoDto.setMisId(creatorMisId);

            return groupInfoDto;
        } catch (Exception e) {
            log.error("#getGroupNameAndCreatorMisId# Error retrieving group info for groupId: {}", groupId, e);
            return groupInfoDto;
        }
    }

    /**
     * 批量查询MIS列表对应的员工身份信息
     * 
     * @param misList MIS ID列表
     * @return 员工身份信息Map，key为misId，value为包含uid和empId的Map
     * @throws LlmCorpusException 如果查询失败，抛出异常
     */
    @Override
    public Map<String, Map<String, Long>> queryEmpIdentityByMisList(List<String> misList) throws LlmCorpusException {
        if (CollectionUtils.isEmpty(misList)) {
            return Collections.emptyMap();
        }
        
        if (misList.size() > 100) {
            misList = misList.subList(0, 100);
            log.warn("MIS ID列表超过100个，已截断为前100个");
        }
        
        QueryEmpIdentityByMisListReq request = new QueryEmpIdentityByMisListReq();
        request.setMisList(misList);
        
        try {
            QueryEmpIdentityByMisListResp response = dxService.queryEmpIdentityByMisList(xmAuthRpcService.getToken(), request);
            
            if (response != null && response.getStatus().getCode() == 0 && response.getData() != null) {
                Map<String, Map<String, Long>> resultMap = new HashMap<>();
                
                // 使用正确的类型获取data字段
                Map<String, UserIdentity> dataMap = response.getData().getData();
                
                for (Map.Entry<String, UserIdentity> entry : dataMap.entrySet()) {
                    String misId = entry.getKey();
                    UserIdentity userIdentity = entry.getValue();
                    
                    if (StringUtils.isNotEmpty(misId) && userIdentity != null) {
                        Map<String, Long> identityMap = new HashMap<>();
                        identityMap.put("uid", userIdentity.getUid());
                        identityMap.put("empId", userIdentity.getEmpId());
                        resultMap.put(misId, identityMap);
                    }
                }
                
                log.info("查询员工身份信息成功，misList={}, 结果数量={}", misList, resultMap.size());
                return resultMap;
            } else {
                String errorMsg = response == null ? "返回为空" : 
                    (response.getStatus() == null ? "状态为空" : 
                    String.format("错误码：%d，错误信息：%s", response.getStatus().getCode(), response.getStatus().getMsg()));
                
                log.error("查询员工身份信息失败: {}", errorMsg);
                throw new LlmCorpusException(BizCode.QUERY_DX_USER_INFO_ERROR.getCode(), "查询员工身份信息失败: " + errorMsg);
            }
        } catch (Exception e) {
            log.error("查询员工身份信息异常: {}", e.getMessage(), e);
            throw new LlmCorpusException(BizCode.QUERY_DX_USER_INFO_ERROR.getCode(), "查询员工身份信息异常: " + e.getMessage());
        }
    }

    /**
     * 解析大象群公告获取雷达事件总指挥MIS ID
     *
     * @param groupId 大象群id
     * @return 总指挥MIS ID
     */
    @Override
    public String getRadarOwnerMisIdByDxGroupNotice(long groupId) {
        try {
            GetGroupNoticeResp getGroupNoticeResp = xmOpenGroupServiceAdaptor.getGroupNoticeResp(groupId);
            String groupNotice = getGroupNoticeResp.getGroupNoticeInfo().noticeContent;
            return extractRadarOwnerMisId(groupNotice);
        } catch (Exception e) {
            log.error("#getRadarOwnerMisIdByDxGroupNotice#error, groupId:{}", groupId, e);
            return "";
        }
    }

    /**
     * 从雷达群公告中提取总指挥MIS ID
     * 
     * @param input 群公告内容
     * @return 总指挥MIS ID
     */
    private String extractRadarOwnerMisId(String input) {
        // 编译正则表达式，匹配"总指挥: "后面的非空白字符、非竖线字符和非换行符
        String regex = "总指挥:\\s*([^\\s\\|\\n]+)";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(input);

        // 查找并返回总指挥MIS ID
        if (matcher.find()) {
            return matcher.group(1).trim(); // 去除捕获内容的前后空格
        }
        return "";
    }

}
