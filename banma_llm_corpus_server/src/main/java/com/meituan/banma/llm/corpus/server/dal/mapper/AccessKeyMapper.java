package com.meituan.banma.llm.corpus.server.dal.mapper;

import com.meituan.banma.llm.corpus.server.dal.entity.AccessKeyEntity;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.apache.ibatis.annotations.Delete;

import java.util.List;

@Mapper
public interface AccessKeyMapper {
    // 新增AccessKey
    @Insert("INSERT INTO access_key (ak, ak_name, mis_id, rg_ids, default_key, count) VALUES " +
            "(#{ak}, #{akName}, #{misId}, #{rgIds}, #{defaultKey}, #{count})")
    int insert(AccessKeyEntity entity);

    // 根据ak查询
    @Select("SELECT * FROM access_key WHERE ak = #{ak}")
    AccessKeyEntity queryByAk(@Param("ak") String ak);

    // 根据ak、misId、rgId删除
    @Delete("DELETE FROM access_key WHERE ak = #{ak} AND mis_id = #{misId} AND FIND_IN_SET(#{rgId}, rg_ids)")
    int deleteByAkAndMisIdAndRgId(@Param("ak") String ak, @Param("misId") String misId, @Param("rgId") String rgId);

    // 根据rgId查询默认ak
    @Select("SELECT * FROM access_key WHERE FIND_IN_SET(#{rgId}, rg_ids) AND default_key = 1 LIMIT 1")
    AccessKeyEntity findDefaultByRgId(@Param("rgId") String rgId);
    
    // 修改ak名称
    @Update("UPDATE access_key SET ak_name = #{akName} WHERE id = #{id}")
    int updateAkName(@Param("id") Long id, @Param("akName") String akName);
    
    // 根据rgId和misId查询ak列表
    @Select("SELECT * FROM access_key WHERE FIND_IN_SET(#{rgId}, rg_ids) AND mis_id = #{misId} ORDER BY ctime DESC")
    List<AccessKeyEntity> findByRgIdAndMisId(@Param("rgId") String rgId, @Param("misId") String misId);
    
    // 修改ak使用次数
    @Update("UPDATE access_key SET count = count + 1 WHERE ak = #{ak}")
    int incrementCount(@Param("ak") String ak);
    
    // 根据ak删除AccessKey
    @Delete("DELETE FROM access_key WHERE ak = #{ak} AND mis_id = #{misId} AND default_key = 0")
    int deleteByAkAndMisId(@Param("ak") String ak, @Param("misId") String misId);
} 