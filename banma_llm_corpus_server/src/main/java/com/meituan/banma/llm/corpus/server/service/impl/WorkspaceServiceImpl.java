package com.meituan.banma.llm.corpus.server.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.meituan.banma.llm.corpus.api.constants.BizCode;
import com.meituan.banma.llm.corpus.api.exception.LlmCorpusException;
import com.meituan.banma.llm.corpus.server.common.MtConfigService;
import com.meituan.banma.llm.corpus.server.common.domain.dto.BatchDocumentRequestParams;
import com.meituan.banma.llm.corpus.server.common.domain.dto.RgListItemDTO;
import com.meituan.banma.llm.corpus.server.dal.entity.RgDatasetDocumentEntity;
import com.meituan.banma.llm.corpus.server.dal.entity.RgDocumentUrlEntity;
import com.meituan.banma.llm.corpus.server.dal.mapper.RgDatasetDocumentMapper;
import com.meituan.banma.llm.corpus.server.dal.mapper.RgDocumentUrlMapper;
import com.meituan.banma.llm.corpus.server.rpc.friday.FridayRpcService;
import com.meituan.banma.llm.corpus.server.service.IAccessKeyService;
import com.meituan.banma.llm.corpus.server.service.IKmService;
import com.meituan.banma.llm.corpus.server.service.IKnowledgeBaseService;
import com.meituan.banma.llm.corpus.server.service.ITicketQueryService;
import com.meituan.banma.llm.corpus.server.service.IWorkspaceVerifyService;
import com.meituan.banma.llm.corpus.server.service.WorkspaceService;
import com.sankuai.meituan.auth.util.UserUtils;
import com.sankuai.meituan.auth.vo.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.NameValuePair;
import org.apache.http.client.fluent.Form;
import org.apache.http.client.fluent.Request;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.context.annotation.Lazy;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * 工作空间服务实现类
 * <AUTHOR>
 */
@Service
@Slf4j
public class WorkspaceServiceImpl implements WorkspaceService {

    @Resource
    private MtConfigService mtConfigService;

    @Resource
    private FridayRpcService fridayRpcService;
    
    @Resource
    private ITicketQueryService ticketQueryService;
    
    @Resource
    private RgDatasetDocumentMapper rgDatasetDocumentMapper;

    @Autowired
    private IKnowledgeBaseService knowledgeBaseService;

    @Autowired
    private RgDocumentUrlMapper rgDocumentUrlMapper;

    @Lazy
    @Autowired
    private IKmService kmService;

    @Autowired
    private IWorkspaceVerifyService workspaceVerifyService;
    
    @Lazy
    @Autowired
    private IAccessKeyService accessKeyService;

    private static final String SUF_NAME = "值班组知识库";
    private static final String OPENAPI_PERMISSION_GUIDE_URL = "https://km.sankuai.com/collabpage/2708110417#b-fd43acb6c8524c67b591d8a80a3a9349";
    private static final String OPENAPI_PERMISSION_MSG = "当前空间无OpenAPI权限，请联系管理员添加权限，<a href='" + OPENAPI_PERMISSION_GUIDE_URL + "' target='_blank'>点此查看申请指引</a>";

    @Override
    public boolean validateWorkspaceAddCondition(Long rgId, String spaceId, String accessKey, String appSecret, String spaceName) throws LlmCorpusException{
        try {
            // 1. 验证令牌
            String accessToken = getWorkspaceAccessToken(accessKey, appSecret);
            if (accessToken == null) {
                throw LlmCorpusException.buildWithMsg(BizCode.ILLEGAL_ARGUMENT, "工作空间访问令牌获取失败，请检查accessKey和appSecret是否正确");
            }
            // 2. 验证spaceId与密钥是否匹配
            boolean checkResult = checkAppFactoryStatus(rgId, spaceId, accessToken, accessKey, appSecret, spaceName);
            return checkResult;
        } catch (LlmCorpusException e) {
            throw e;
        } catch (Exception e) {
            log.error("#validateWorkspaceAddCondition 验证工作空间添加条件异常: spaceId={}, error={}", spaceId, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public String getWorkspaceAccessToken(String accessKey, String appSecret) throws LlmCorpusException {
        List<NameValuePair> pairs = Form.form()
                .add("grant_type", "client_credentials")
                .add("client_id", accessKey)
                .add("client_secret", appSecret)
                .build();

        String res = null;
        try {
            res = Request.Post(mtConfigService.getFridayAccessTokenConfig().getAuthApi()).bodyForm(pairs).execute()
                    .returnContent().asString();

            JSONObject obj = JSON.parseObject(res);
            if (obj.getInteger("errcode") == 0) {
                String accessToken = obj.getJSONObject("data").getString("access_token");
                log.info("#getWorkspaceAccessToken 成功获取工作空间访问令牌: accessKey={}", accessKey);
                return accessToken;
            } else {
                log.warn("#getWorkspaceAccessToken 获取工作空间访问令牌失败: accessKey={}, errorCode={}, errorMsg={}",
                        accessKey, obj.getInteger("errcode"), obj.getString("errmsg"));
                throw LlmCorpusException.buildWithMsg(BizCode.ILLEGAL_ARGUMENT.getCode(),
                        "工作空间访问令牌获取失败: " + obj.getString("errmsg"));
            }
        } catch (LlmCorpusException e) {
            // 直接重新抛出LlmCorpusException异常
            throw e;
        } catch (Exception e) {
            log.error("#getWorkspaceAccessToken 获取工作空间访问令牌异常: accessKey={}, res={}, error={}",
                    accessKey, res, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 检查应用工厂状态以验证spaceId和密钥是否匹配
     * 如果发现已被虚拟删除的记录，则恢复为正常状态
     *
     * @param rgId        值班组ID
     * @param spaceId     工作空间ID
     * @param accessToken 访问令牌
     * @return 验证结果
     */
    public boolean checkAppFactoryStatus(Long rgId, String spaceId, String accessToken, String accessKey, String appSecret, String spaceName) throws LlmCorpusException{
        // 初始化重试计数器
        int retryCount = 0;
        String lastError = null;
        
        // 最多重试3次
        while (retryCount < 3) {
            try {
                // 获取当前用户
                User user = UserUtils.getUser();
                String misId = user.getLogin();

                // 先查询数据库中是否已有记录（包括已删除的记录），使用rgId和spaceId共同查询
                RgDatasetDocumentEntity rgDatasetDocumentIn = rgDatasetDocumentMapper.findByRgIdAndSpaceIdIncludeDeleted(rgId, spaceId);
                
                // 检查是否找到被虚拟删除的记录(status=1)，如果找到则恢复为正常状态(status=0)
                boolean isRecoveredRecord = false;
                if (rgDatasetDocumentIn != null && rgDatasetDocumentIn.getStatus() == 1) {
                    // 恢复已删除的工作空间记录
                    rgDatasetDocumentIn = restoreDeletedWorkspace(rgId, spaceId, spaceName);
                    isRecoveredRecord = true;
                } else if (rgDatasetDocumentIn != null && rgDatasetDocumentIn.getStatus() == 0) {
                    // 如果工作空间已存在且状态为正常(0)，则抛出异常
                    log.warn("addWorkspaceConfig# 工作空间已绑定，请勿重复绑定. rgId:{}, spaceId:{}, spaceName:{}", rgId, spaceId, spaceName);
                    throw LlmCorpusException.buildWithMsg(BizCode.WORKSPACE_ALREADY_BOUND, "该工作空间已绑定，请勿重复绑定");
                }

                // 获取值班组名称
                String rgName = getRgName(misId, rgId);
                
                // 如果不存在有效记录（status=0），则调用创建知识库API
                boolean success = false;
                String datasetIdStr;
                
                // 如果是刚恢复的记录，直接使用已有的datasetId，不需要重新创建
                if (isRecoveredRecord && rgDatasetDocumentIn != null) {
                    // 兜底检查：验证知识库是否真实存在
                    String datasetName = rgId + "|" + rgName + SUF_NAME;
                    String originalDatasetId = rgDatasetDocumentIn.getDatasetId();
                    try {
                        String verifiedDatasetId = ensureDatasetExists(datasetName, accessToken, misId, datasetName, spaceId, originalDatasetId);
                        
                        // 如果ID发生变化，说明知识库被删除后重建
                        if (!verifiedDatasetId.equals(originalDatasetId)) {
                            log.info("#checkAppFactoryStatus 已恢复记录的知识库ID已更新: rgId={}, spaceId={}, oldId={}, newId={}",
                                    rgId, spaceId, originalDatasetId, verifiedDatasetId);
                            
                            // 使用新创建的ID
                            datasetIdStr = verifiedDatasetId;
                            // 更新数据库记录
                            rgDatasetDocumentIn.setDatasetId(verifiedDatasetId);
                            rgDatasetDocumentMapper.updateAndSpaceId(rgDatasetDocumentIn);
                        } else {
                            // 使用原ID
                            datasetIdStr = originalDatasetId;
                            log.info("#checkAppFactoryStatus 已恢复记录的知识库ID验证通过: rgId={}, spaceId={}, datasetId={}",
                                    rgId, spaceId, datasetIdStr);
                        }
                        success = true;
                    } catch (LlmCorpusException e) {
                        // 验证失败，需要重新创建
                        log.warn("#checkAppFactoryStatus 已恢复记录的知识库验证失败，将尝试重新创建: rgId={}, spaceId={}, error={}",
                                rgId, spaceId, e.getMessage());
                        try {
                            // 尝试重新创建知识库
                            datasetIdStr = createNewDataset(rgId, rgName, SUF_NAME, accessToken, misId, spaceId, rgDatasetDocumentIn);
                        } catch (LlmCorpusException ex) {
                            throw ex;
                        }
                        if (datasetIdStr != null) {
                            success = true;
                            // 更新数据库记录
                            rgDatasetDocumentIn.setDatasetId(datasetIdStr);
                            rgDatasetDocumentMapper.updateAndSpaceId(rgDatasetDocumentIn);
                        } else {
                            // 增加重试次数并继续
                            retryCount++;
                            lastError = "知识库创建失败";
                            
                            // 检查是否是网络错误
                            if (!isNetworkError(e)) {
                                // 非网络错误，没必要重试
                                log.error("#checkAppFactoryStatus 非网络错误，创建知识库失败: rgId={}, spaceId={}, error={}",
                                        rgId, spaceId, e.getMessage());
                                break;
                            }
                            
                            // 如果是网络错误且还有重试次数，等待后重试
                            if (retryCount < 3) {
                                log.warn("#checkAppFactoryStatus 网络错误，将重试 ({}/3): rgId={}, spaceId={}",
                                        retryCount, rgId, spaceId);
                                try {
                                    Thread.sleep(1000 * retryCount);
                                } catch (InterruptedException ie) {
                                    Thread.currentThread().interrupt();
                                }
                                continue;
                            }
                            
                            // 重试次数用尽，记录失败任务
                            workspaceVerifyService.recordFailedTask(rgId, spaceId, spaceName, accessKey, appSecret, misId, e.getMessage());
                            return false;
                        }
                    }
                } else {
                    // 尝试创建新知识库
                    try {
                        datasetIdStr = createNewDataset(rgId, rgName, SUF_NAME, accessToken, misId, spaceId, rgDatasetDocumentIn);
                    } catch (LlmCorpusException e) {
                        throw e;
                    }
                    if (datasetIdStr != null) {
                        success = true;
                    } else {
                        // 增加重试次数并继续
                        retryCount++;
                        lastError = "知识库创建失败";
                        
                        // 如果还有重试次数，等待后重试
                        if (retryCount < 3) {
                            log.warn("#checkAppFactoryStatus 创建知识库失败，将重试 ({}/3): rgId={}, spaceId={}",
                                    retryCount, rgId, spaceId);
                            try {
                                Thread.sleep(1000 * retryCount);
                            } catch (InterruptedException ie) {
                                Thread.currentThread().interrupt();
                            }
                            continue;
                        }
                        
                        // 重试次数用尽，记录失败任务
                        workspaceVerifyService.recordFailedTask(rgId, spaceId, spaceName, accessKey, appSecret, misId, lastError);
                        return false;
                    }
                }
                
                // 处理文档
                String documentIdStr = handleDocument(rgId, spaceId, accessToken, misId, datasetIdStr, rgDatasetDocumentIn, isRecoveredRecord, SUF_NAME);
                if (documentIdStr == null) {
                    // 文档处理失败，增加重试次数
                    retryCount++;
                    lastError = "文档处理失败";
                    
                    // 如果还有重试次数，等待后重试
                    if (retryCount < 3) {
                        log.warn("#checkAppFactoryStatus 文档处理失败，将重试 ({}/3): rgId={}, spaceId={}",
                                retryCount, rgId, spaceId);
                        try {
                            Thread.sleep(1000 * retryCount);
                        } catch (InterruptedException ie) {
                            Thread.currentThread().interrupt();
                        }
                        continue;
                    }
                    
                    // 重试次数用尽，记录失败任务
                    workspaceVerifyService.recordFailedTask(rgId, spaceId, spaceName, accessKey, appSecret, misId, lastError);
                    return false;
                }
                
                // 只有在成功创建或更新了dataset或document时，才更新数据库
                if (success) {
                    // 保存或更新数据库记录
                    saveOrUpdateRecord(rgId, spaceId, spaceName, datasetIdStr, documentIdStr, accessKey, appSecret, rgDatasetDocumentIn, isRecoveredRecord);
                    
                    // 初始化文档
                    initializeWorkspaceDocuments(rgId, spaceId, misId);
                } else {
                    // 没有成功创建或更新，直接返回原有记录
                    log.info("#checkAppFactoryStatus 未进行创建或更新操作，使用现有记录: rgId={}", rgId);
                }
    
                // 如果成功创建了文档或者找到了已存在的文档记录，说明验证通过
                log.info("#checkAppFactoryStatus 工作空间验证通过: rgId={}, spaceId={}, 重试次数={}",
                        rgId, spaceId, retryCount);
                return true;
            } catch (LlmCorpusException e) {
                throw e;
            } catch (Exception e) {
                // 记录错误并增加重试次数
                String errorMsg = e.getMessage();
                lastError = errorMsg;
                retryCount++;
                
                log.warn("#checkAppFactoryStatus 验证过程发生异常 (尝试 {}/3): rgId={}, spaceId={}, error={}",
                        retryCount, rgId, spaceId, errorMsg);
                
                // 检查是否是网络错误
                if (!isNetworkError(e)) {
                    // 非网络错误，没必要重试
                    log.error("#checkAppFactoryStatus 非网络错误，不再重试: rgId={}, spaceId={}", rgId, spaceId);
                    break;
                }
                
                // 如果还有重试次数，短暂延迟后重试
                if (retryCount < 3) {
                    try {
                        Thread.sleep(1000 * retryCount); // 随着重试次数增加等待时间
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                    }
                }
            }
        }
        
        // 如果到这里，说明重试次数已用完或遇到了非网络错误
        // 记录验证失败任务
        User user = UserUtils.getUser();
        String misId = user.getLogin();
        workspaceVerifyService.recordFailedTask(rgId, spaceId, spaceName, accessKey, appSecret, misId, lastError);
        
        log.error("#checkAppFactoryStatus 工作空间验证失败，已达到最大重试次数或遇到非网络错误: rgId={}, spaceId={}, 最后错误={}",
                rgId, spaceId, lastError);
        return false;
    }
    
    /**
     * 判断是否是网络相关错误
     * 
     * @param e 异常
     * @return 是否是网络错误
     */
    private boolean isNetworkError(Exception e) {
        if (e == null) {
            return false;
        }
        
        // 检查异常类型
        boolean isNetworkException = 
                e instanceof java.net.SocketTimeoutException ||
                e instanceof java.net.ConnectException ||
                e instanceof java.net.UnknownHostException ||
                e instanceof java.io.IOException ||
                e instanceof java.net.SocketException ||
                e instanceof javax.net.ssl.SSLException;
        
        if (isNetworkException) {
            return true;
        }
        
        // 检查异常信息
        String errorMessage = e.getMessage();
        if (errorMessage != null) {
            return errorMessage.contains("timed out") ||
                   errorMessage.contains("timeout") ||
                   errorMessage.contains("Connection refused") ||
                   errorMessage.contains("网络异常") ||
                   errorMessage.contains("network") ||
                   errorMessage.contains("Network") ||
                   errorMessage.contains("连接异常") ||
                   errorMessage.contains("unable to connect");
        }
        
        // 检查异常原因
        Throwable cause = e.getCause();
        if (cause != null) {
            return isNetworkError(cause instanceof Exception ? (Exception) cause : new Exception(cause));
        }
        
        return false;
    }
    
    /**
     * 恢复已删除的工作空间记录
     * 
     * @param rgId 值班组ID
     * @param spaceId 工作空间ID
     * @param spaceName 工作空间名称
     * @return 恢复后的工作空间记录
     */
    private RgDatasetDocumentEntity restoreDeletedWorkspace(Long rgId, String spaceId, String spaceName) {
        log.info("#checkAppFactoryStatus 找到已被虚拟删除的工作空间记录，准备恢复: rgId={}, spaceId={}", rgId, spaceId);
        
        // 恢复工作空间记录状态并更新空间名称
        rgDatasetDocumentMapper.restoreByRgIdAndSpaceIdWithName(rgId, spaceId, spaceName);
        log.info("#checkAppFactoryStatus 已恢复工作空间记录并更新空间名称: rgId={}, spaceId={}, spaceName={}", 
                rgId, spaceId, spaceName);
        
        // 获取该rgId下的任意一个已有正常工作空间的URL列表
        List<RgDatasetDocumentEntity> activeWorkspaces = rgDatasetDocumentMapper.findByRgId(rgId);
        List<String> activeUrls = new ArrayList<>();
        String referenceSpaceId = null;
        
        // 查找一个参考工作空间，排除当前工作空间和默认工作空间
        String defaultSpaceId = mtConfigService.getFridaySpaceId();
        for (RgDatasetDocumentEntity workspace : activeWorkspaces) {
            if (!workspace.getSpaceId().equals(spaceId) && !workspace.getSpaceId().equals(defaultSpaceId)) {
                referenceSpaceId = workspace.getSpaceId();
                break;
            }
        }
        
        if (referenceSpaceId != null) {
            // 获取参考工作空间的所有URL
            List<RgDocumentUrlEntity> referenceDocuments = rgDocumentUrlMapper.findByRgIdAndSpaceId(rgId, referenceSpaceId);
            for (RgDocumentUrlEntity doc : referenceDocuments) {
                activeUrls.add(doc.getUrl());
            }
            
            log.info("#checkAppFactoryStatus 找到参考工作空间: rgId={}, referenceSpaceId={}, activeUrlCount={}", 
                    rgId, referenceSpaceId, activeUrls.size());
            
            // 获取要恢复的工作空间的所有已删除文档
            List<RgDocumentUrlEntity> deletedDocuments = rgDocumentUrlMapper.findDeletedByRgIdAndSpaceId(rgId, spaceId);
            int restoredCount = 0;
            
            // 只恢复在活跃工作空间中存在的URL对应的文档
            for (RgDocumentUrlEntity deletedDoc : deletedDocuments) {
                if (activeUrls.contains(deletedDoc.getUrl())) {
                    int result = rgDocumentUrlMapper.restoreByRgIdSpaceIdAndUrl(rgId, spaceId, deletedDoc.getUrl());
                    if (result > 0) {
                        restoredCount++;
                    }
                }
            }
            
            log.info("#checkAppFactoryStatus 选择性恢复文档记录: rgId={}, spaceId={}, totalDeleted={}, restored={}", 
                    rgId, spaceId, deletedDocuments.size(), restoredCount);
        } else {
//            // 如果没有找到参考工作空间，恢复所有文档
//            int restoredDocs = rgDocumentUrlMapper.restoreByRgIdAndSpaceId(rgId, spaceId);
            log.info("#checkAppFactoryStatus 没有找到参考工作空间，无恢复文档记录: rgId={}, spaceId={}",
                    rgId, spaceId);
        }
        
        // 重新查询恢复后的记录
        return rgDatasetDocumentMapper.findByRgIdAndSpaceId(rgId, spaceId);
    }
    
    /**
     * 获取值班组名称
     *
     * @param misId 用户ID
     * @param rgId 值班组ID
     * @return 值班组名称
     */
    private String getRgName(String misId, Long rgId) {
            String rgName = "";
            RgListItemDTO rgListItemDTO = ticketQueryService.queryRgInfoByRgId(misId, rgId);
            if (rgListItemDTO != null) {
                rgName = rgListItemDTO.getName();
        }
        return rgName;
    }
    
    /**
     * 创建新知识库
     * 
     * @param rgId 值班组ID
     * @param rgName 值班组名称
     * @param suffix 知识库名称后缀
     * @param accessToken 访问令牌
     * @param misId 用户ID
     * @param spaceId 工作空间ID
     * @param rgDatasetDocumentIn 现有记录（可能为null）
     * @return 知识库ID，失败返回null
     */
    private String createNewDataset(Long rgId, String rgName, String suffix, String accessToken, 
                                 String misId, String spaceId, RgDatasetDocumentEntity rgDatasetDocumentIn) throws LlmCorpusException {
        try {
            String datasetName = rgId + "|" + rgName + suffix;
            //如果创建成功，则获得到datasetIdStr
            String datasetIdStr = fridayRpcService.createDataset(datasetName, 
                    accessToken, misId, datasetName, spaceId);
            log.info("#checkAppFactoryStatus 创建知识库成功: rgId={}, spaceId={}, misId={}", rgId, spaceId, misId);
            return datasetIdStr;
        } catch (Exception e) {
            // 识别OpenAPI权限异常，直接抛出
            if (e.getMessage() != null && e.getMessage().contains("当前空间无OpenAPI权限，请联系管理员添加权限")) {
                log.warn("#checkAppFactoryStatus OpenAPI权限异常: rgId={}, spaceId={}, 错误信息={}", rgId, spaceId, e.getMessage());
                throw LlmCorpusException.buildWithMsg(BizCode.FORBIDDEN, OPENAPI_PERMISSION_MSG);
            }
            // 检查cause中是否有OpenAPI权限异常
            if (e instanceof RuntimeException && e.getMessage() != null && e.getMessage().contains("Failed to create dataset")) {
                Throwable cause = e.getCause();
                if (cause != null && cause.getMessage() != null && cause.getMessage().contains("当前空间无OpenAPI权限，请联系管理员添加权限")) {
                    log.warn("#checkAppFactoryStatus OpenAPI权限异常: rgId={}, spaceId={}, 错误信息={}", rgId, spaceId, cause.getMessage());
                    throw LlmCorpusException.buildWithMsg(BizCode.FORBIDDEN, OPENAPI_PERMISSION_MSG);
                }
            }
            if (e.getMessage() != null && e.getMessage().contains("当前请求modifier不属于当前工作空间")) {
                log.warn("#checkAppFactoryStatus 工作空间权限验证失败: rgId={}, spaceId={}, 错误信息={}", rgId, spaceId, e.getMessage());
                throw LlmCorpusException.buildWithMsg(BizCode.FORBIDDEN, "无当前工作空间权限");
            }
            // 检查是否为token不匹配的错误
            if (e instanceof RuntimeException && e.getMessage() != null && e.getMessage().contains("Failed to create dataset")) {
                Throwable cause = e.getCause();
                if (cause != null && cause.getMessage() != null && cause.getMessage().contains("当前token与工作空间不匹配")) {
                    log.warn("#checkAppFactoryStatus 工作空间token不匹配: rgId={}, spaceId={}, 错误信息={}", rgId, spaceId, cause.getMessage());
                    throw LlmCorpusException.buildWithMsg(BizCode.FORBIDDEN, "无当前工作空间权限");
                }
            }
            // 如果是其他错误（如已存在同名知识库），且已有记录，则使用已有的datasetId
            if (rgDatasetDocumentIn != null) {
                String datasetIdStr = rgDatasetDocumentIn.getDatasetId();
                return datasetIdStr;
            } else {
                // 如果没有记录又创建失败，记录警告
                log.warn("#checkAppFactoryStatus 创建知识库失败且无现有记录: rgId={}, spaceId={}, error={}", 
                        rgId, spaceId, e.getMessage());
                return null;
            }
        }
    }
    
    /**
     * 处理文档（上传或使用现有）
     * 
     * @param rgId 值班组ID
     * @param spaceId 工作空间ID
     * @param accessToken 访问令牌
     * @param misId 用户ID
     * @param datasetIdStr 知识库ID
     * @param rgDatasetDocumentIn 现有记录（可能为null）
     * @param isRecoveredRecord 是否是恢复的记录
     * @param suffix 知识库名称后缀
     * @return 文档ID，失败返回null
     */
    private String handleDocument(Long rgId, String spaceId, String accessToken, String misId, 
                                String datasetIdStr, RgDatasetDocumentEntity rgDatasetDocumentIn,
                                boolean isRecoveredRecord, String suffix) {
        // 如果是恢复的记录，不需要重新上传文档
        String documentIdStr;
        if (isRecoveredRecord && rgDatasetDocumentIn != null) {
            documentIdStr = rgDatasetDocumentIn.getDocumentId();
            log.info("#checkAppFactoryStatus 使用恢复的文档ID: rgId={}, spaceId={}, documentId={}", 
                    rgId, spaceId, documentIdStr);
        } else {
            // 处理AccessKey和上传文档
            documentIdStr = handleAccessKeyAndUploadDocument(rgId, spaceId, datasetIdStr, accessToken, 
                    misId, rgId + suffix, rgDatasetDocumentIn);
        }
        return documentIdStr;
    }
    
    /**
     * 保存或更新记录到数据库
     * 
     * @param rgId 值班组ID
     * @param spaceId 工作空间ID
     * @param spaceName 工作空间名称
     * @param datasetIdStr 知识库ID
     * @param documentIdStr 文档ID
     * @param accessKey 访问密钥
     * @param appSecret 应用密钥
     * @param rgDatasetDocumentIn 现有记录（可能为null）
     * @param isRecoveredRecord 是否是恢复的记录
     */
    private void saveOrUpdateRecord(Long rgId, String spaceId, String spaceName, String datasetIdStr,
                                 String documentIdStr, String accessKey, String appSecret,
                                 RgDatasetDocumentEntity rgDatasetDocumentIn, boolean isRecoveredRecord) {
            RgDatasetDocumentEntity rgDatasetDocument;
                if (rgDatasetDocumentIn == null) {
                    //如果不存在，则创建
                    rgDatasetDocument = RgDatasetDocumentEntity.builder()
                            .rgId(rgId)
                            .spaceId(spaceId)
                    .spaceName(spaceName)
                            .datasetId(datasetIdStr)
                            .documentId(documentIdStr)
                            .timestamp(new Timestamp(System.currentTimeMillis()))
                            .accessKey(accessKey)
                            .appSecret(appSecret)
                    .status(0) // 设置status为0（正常）
                            .build();
                    rgDatasetDocumentMapper.insertRgDatasetDocumentAndSpaceId(rgDatasetDocument);
                    log.info("#checkAppFactoryStatus 成功创建新的记录: rgId={}, spaceId={}, datasetId={}, documentId={}", 
                            rgId, spaceId, datasetIdStr, documentIdStr);
        } else if (!isRecoveredRecord) {
            //如果存在且非恢复记录，则更新
                    rgDatasetDocumentIn.setSpaceId(spaceId);
                    rgDatasetDocumentIn.setDatasetId(datasetIdStr);
                    rgDatasetDocumentIn.setDocumentId(documentIdStr);
                    rgDatasetDocumentIn.setTimestamp(new Timestamp(System.currentTimeMillis()));
                    rgDatasetDocumentIn.setAccessKey(accessKey);
                    rgDatasetDocumentIn.setAppSecret(appSecret);
            rgDatasetDocumentIn.setStatus(0); // 确保status为0（正常）
                    
                    // 使用updateByRgId方法进行更新，这样可以同时更新spaceId
                    rgDatasetDocumentMapper.updateAndSpaceId(rgDatasetDocumentIn);
                    log.info("#checkAppFactoryStatus 成功更新记录: rgId={}, spaceId={}, datasetId={}, documentId={}", 
                            rgId, spaceId, datasetIdStr, documentIdStr);
        } else {
            log.info("#checkAppFactoryStatus 使用已恢复的记录，无需更新: rgId={}, spaceId={}", rgId, spaceId);
        }
    }
    
    /**
     * 初始化工作空间文档
     * 
     * @param rgId 值班组ID
     * @param spaceId 工作空间ID
     * @param misId 用户ID
     */
    private void initializeWorkspaceDocuments(Long rgId, String spaceId, String misId) {
        try {
            // 获取该rgId下的所有工作空间
            List<RgDatasetDocumentEntity> workspaces = rgDatasetDocumentMapper.findByRgId(rgId);
            
            // 获取默认工作空间ID
            String defaultSpaceId = mtConfigService.getFridaySpaceId();
            // 找到除当前工作空间和默认工作空间外的任意一个已有工作空间
            String existingSpaceId = null;
            for (RgDatasetDocumentEntity workspace : workspaces) {
                // 排除当前新创建的工作空间和默认工作空间
                if (!workspace.getSpaceId().equals(spaceId) && !workspace.getSpaceId().equals(defaultSpaceId)) {
                    existingSpaceId = workspace.getSpaceId();
                    log.info("找到已有工作空间用于初始化: rgId={}, existingSpaceId={}", rgId, existingSpaceId);
                    break;
                }
            }
            
            // 如果找到已有工作空间，根据rgId和该spaceId获取文档数据
            if (existingSpaceId != null) {
                List<RgDocumentUrlEntity> existingDocuments = rgDocumentUrlMapper.findByRgIdAndSpaceId(rgId, existingSpaceId);
                
                if (existingDocuments != null && !existingDocuments.isEmpty()) {
                    log.info("从已有工作空间获取到文档数据: rgId={}, existingSpaceId={}, docCount={}", 
                            rgId, existingSpaceId, existingDocuments.size());
                    
                    // 转换为批量添加所需的请求参数
                    List<BatchDocumentRequestParams> batchParams = new ArrayList<>();
                    for (RgDocumentUrlEntity doc : existingDocuments) {
                        BatchDocumentRequestParams param = new BatchDocumentRequestParams();
                        param.setName(doc.getName());
                        param.setUrl(doc.getUrl());
                        param.setAutoUpdate(doc.getAutoUpdate());
                        batchParams.add(param);
                    }
                    
                    // 调用批量添加方法初始化新工作空间的文档
                    if (!batchParams.isEmpty()) {
                        // 将BatchDocumentRequestParams列表转换为API所需的参数列表
                        List<String> nameList = new ArrayList<>();
                        List<String> urlList = new ArrayList<>();
                        List<Integer> autoUpdateList = new ArrayList<>();
                        
                        for (BatchDocumentRequestParams param : batchParams) {
                            nameList.add(param.getName());
                            urlList.add(param.getUrl());
                            autoUpdateList.add(param.getAutoUpdate());
                        }
                        
                        // 调用KmService的批量添加方法，注意该方法会自动处理所有工作空间
                        List<String> failedUrls = kmService.addBatchDocumentByNameAndUrl(rgId, nameList, urlList, autoUpdateList, misId);
                        if (failedUrls.isEmpty()) {
                            log.info("成功初始化新工作空间的所有文档数据: rgId={}, docCount={}", rgId, batchParams.size());
                        } else {
                            log.warn("部分文档初始化失败: rgId={}, 成功数量={}, 失败数量={}", 
                                    rgId, batchParams.size() - failedUrls.size(), failedUrls.size());
                        }
                    }
                } else {
                    log.info("已有工作空间中未找到文档数据: rgId={}, existingSpaceId={}", rgId, existingSpaceId);
                }
            } else {
                log.info("未找到其他已有工作空间，无需初始化文档: rgId={}, spaceId={}", rgId, spaceId);
            }
        } catch (Exception e) {
            // 初始化失败不影响主流程
            log.error("初始化新工作空间文档失败，但不影响工作空间创建: rgId={}, spaceId={}, error={}", 
                    rgId, spaceId, e.getMessage(), e);
        }
    }

    /**
     * 检查并确保知识库存在，如果不存在则尝试创建
     * 如果创建成功，说明原知识库已被删除，返回新创建的知识库ID
     * 如果创建失败且提示"已存在"，说明原知识库正常，返回原ID
     *
     * @param datasetName 知识库名称
     * @param accessToken 访问令牌
     * @param misId 用户ID
     * @param description 描述
     * @param spaceId 工作空间ID
     * @param originalDatasetId 原知识库ID
     * @return 知识库ID，可能是原ID或新创建的ID
     * @throws LlmCorpusException 业务异常
     */
    public String ensureDatasetExists(String datasetName, String accessToken, String misId, 
                                     String description, String spaceId, String originalDatasetId) throws LlmCorpusException {
        log.info("#ensureDatasetExists 开始检查知识库是否存在: datasetName={}, spaceId={}, originalDatasetId={}", 
                datasetName, spaceId, originalDatasetId);
        
        // 重试次数计数器
        int retryCount = 0;
        // 最大重试次数
        final int MAX_RETRY_COUNT = 3;
        // 记录最后的异常
        Throwable lastException = null;
        // 基础等待时间（毫秒）
        final long BASE_WAIT_TIME = 1000L;
        // 最大等待时间（毫秒）
        final long MAX_WAIT_TIME = 8000L;
        
        while (retryCount < MAX_RETRY_COUNT) {
            try {
                // 尝试重新创建知识库，如果创建成功说明原知识库已被删除
                String newDatasetId = fridayRpcService.createDataset(datasetName, accessToken, misId, description, spaceId);
                
                // 创建成功，返回新的知识库ID
                log.info("#ensureDatasetExists 原知识库已不存在，成功创建新知识库: datasetName={}, spaceId={}, newDatasetId={}, 重试次数={}", 
                        datasetName, spaceId, newDatasetId, retryCount);
                return newDatasetId;
            } catch (Exception e) {
                lastException = e;
                
                // 捕获异常，检查是否是"知识库已存在"错误
                if (e.getMessage() != null && (
                        e.getMessage().contains("已存在") || 
                        e.getMessage().contains("already exists") || 
                        (e.getCause() != null && e.getCause().getMessage() != null && 
                         (e.getCause().getMessage().contains("已存在") || 
                          e.getCause().getMessage().contains("already exists"))))) {
                    
                    // 知识库已存在，说明原知识库正常，返回原ID
                    log.info("#ensureDatasetExists 知识库已存在，无需重新创建: datasetName={}, spaceId={}, originalDatasetId={}", 
                            datasetName, spaceId, originalDatasetId);
                    return originalDatasetId;
                }
                
                // 检查是否是网络错误，如果是网络错误则重试
                if (isNetworkError(e)) {
                    retryCount++;
                    if (retryCount < MAX_RETRY_COUNT) {
                        // 计算等待时间（指数退避策略）
                        long waitTime = Math.min(BASE_WAIT_TIME * (1L << retryCount), MAX_WAIT_TIME);
                        // 添加随机抖动，避免多个请求同时重试
                        waitTime += (long)(waitTime * 0.2 * Math.random());
                        
                        log.warn("#ensureDatasetExists 遇到网络错误，将进行第{}次重试: datasetName={}, spaceId={}, 等待时间={}ms, 错误信息={}", 
                                retryCount + 1, datasetName, spaceId, waitTime, e.getMessage());
                        
                        try {
                            Thread.sleep(waitTime);
                        } catch (InterruptedException ie) {
                            Thread.currentThread().interrupt();
                            log.warn("#ensureDatasetExists 重试等待被中断");
                        }
                        continue;
                    } else {
                        log.error("#ensureDatasetExists 网络错误重试次数已达上限: datasetName={}, spaceId={}, 重试次数={}", 
                                datasetName, spaceId, retryCount);
                    }
                } else {
                    log.error("#ensureDatasetExists 检查知识库存在性时发生非网络异常: datasetName={}, spaceId={}, 异常类型={}, 错误消息={}", 
                            datasetName, spaceId, e.getClass().getName(), e.getMessage());
                }
                
                // 记录失败任务以便后续定时任务处理
                recordFailedTask(datasetName, spaceId, misId, e.getMessage());
                
                throw LlmCorpusException.buildWithMsg(BizCode.FORBIDDEN,
                        "检查知识库存在性失败: " + e.getMessage());
            }
        }
        
        // 如果重试次数达到最大值还未能成功，则抛出最后捕获的异常
        log.error("#ensureDatasetExists 超过最大重试次数后仍失败: datasetName={}, spaceId={}, 重试次数={}", 
                datasetName, spaceId, retryCount);
        
        throw LlmCorpusException.buildWithMsg(BizCode.FORBIDDEN,
                "检查知识库存在性失败，已重试" + MAX_RETRY_COUNT + "次: " + 
                (lastException != null ? lastException.getMessage() : "未知错误"));
    }
    
    /**
     * 记录失败任务以便后续处理
     * 
     * @param datasetName 知识库名称
     * @param spaceId 工作空间ID
     * @param misId 用户ID
     * @param errorMessage 错误信息
     */
    private void recordFailedTask(String datasetName, String spaceId, String misId, String errorMessage) {
        try {
            User user = UserUtils.getUser();
            String currentMisId = user != null ? user.getLogin() : misId;
            
            // 提取工作空间ID的数字部分作为rgId（如果需要）
            Long rgId = null;
            try {
                // 根据实际命名规则从spaceId中提取rgId
                if (spaceId.contains("-")) {
                    String[] parts = spaceId.split("-");
                    if (parts.length > 0) {
                        rgId = Long.parseLong(parts[0]);
                    }
                } else {
                    // 尝试直接解析
                    rgId = Long.parseLong(spaceId);
                }
            } catch (NumberFormatException nfe) {
                log.warn("#recordFailedTask 无法从spaceId解析rgId: {}", spaceId);
            }
            
            if (rgId != null) {
                // 根据rgId和spaceId查询记录
                RgDatasetDocumentEntity entity = rgDatasetDocumentMapper.findByRgIdAndSpaceId(rgId, spaceId);
                if (entity != null) {
                    String accessKey = entity.getAccessKey();
                    String appSecret = entity.getAppSecret();
                    String spaceName = entity.getSpaceName();
                    
                    // 记录失败任务
                    workspaceVerifyService.recordFailedTask(rgId, spaceId, spaceName, accessKey, appSecret, currentMisId, errorMessage);
                    log.info("#recordFailedTask 已记录失败任务以便后续重试: rgId={}, spaceId={}", rgId, spaceId);
                } else {
                    log.warn("#recordFailedTask 未找到对应的工作空间记录: rgId={}, spaceId={}", rgId, spaceId);
                }
            } else {
                log.warn("#recordFailedTask 无法确定rgId: spaceId={}", spaceId);
            }
        } catch (Exception ex) {
            log.warn("#recordFailedTask 记录失败任务时发生异常: {}", ex.getMessage(), ex);
        }
    }

    /**
     * 虚拟删除工作空间（将status设置为1）
     * @param rgId 值班组ID
     * @param spaceId 工作空间ID
     * @param misId 用户ID
     * @return 删除结果
     * @throws LlmCorpusException 业务异常
     */
    @Override
    public boolean deleteWorkspace(Long rgId, String spaceId, String misId) throws LlmCorpusException {
        log.info("开始虚拟删除工作空间: rgId={}, spaceId={}, misId={}", rgId, spaceId, misId);
        
        // 验证用户权限
        boolean permission = knowledgeBaseService.validateUserPermission(misId, rgId);
        if (!permission) {
            throw new LlmCorpusException(BizCode.NO_PERMISSION.getCode(), "用户无对应值班组权限");
        }
        
        try {
            // 虚拟删除数据库中的文档记录（更新status=1）
            int deletedDocuments = rgDocumentUrlMapper.batchDeleteByRgIdAndSpaceId(rgId, spaceId);
            log.info("虚拟删除数据库文档记录: rgId={}, spaceId={}, count={}", rgId, spaceId, deletedDocuments);
            
            // 虚拟删除工作空间配置（更新status=1）
            rgDatasetDocumentMapper.deleteByRgIdAndSpaceId(rgId, spaceId);
            log.info("虚拟删除工作空间配置成功: rgId={}, spaceId={}", rgId, spaceId);
            
            return true;
        } catch (Exception e) {
            log.error("虚拟删除工作空间失败: rgId={}, spaceId={}, error={}", rgId, spaceId, e.getMessage(), e);
            throw new LlmCorpusException(BizCode.WORKSPACE_DELETE_ERROR.getCode(), "虚拟删除工作空间失败: " + e.getMessage());
        }
    }

    @Override
    public boolean createDefaultWorkspace(Long rgId, String misId) {
        try {
            // 获取该值班组下的所有工作空间配置
            List<RgDatasetDocumentEntity> rgDatasetDocuments = rgDatasetDocumentMapper.findByRgId(rgId);
            
            // 获取Friday默认空间ID
            String fridaySpaceId = mtConfigService.getFridaySpaceId();
            
            // 检查是否存在Friday默认空间配置
            boolean hasFridaySpace = false;
            if (rgDatasetDocuments != null && !rgDatasetDocuments.isEmpty()) {
                for (RgDatasetDocumentEntity doc : rgDatasetDocuments) {
                    if (fridaySpaceId.equals(doc.getSpaceId())) {
                        hasFridaySpace = true;
                        break;
                    }
                }
            }
            
            if (rgDatasetDocuments == null || rgDatasetDocuments.isEmpty() || !hasFridaySpace) {
                log.info("WorkspaceServiceImpl# 未找到rgId={}的Friday默认工作空间配置，创建默认配置", rgId);
                try {
                    // 获取默认工作空间ID和配置
                    String spaceId = mtConfigService.getFridaySpaceId();
                    String spaceName = "默认工作空间";
                    String accessKey = mtConfigService.getFridayAccessTokenConfig().getAccessKey();
                    String appSecret = mtConfigService.getFridayAccessTokenConfig().getAppSecret();
                    
                    // 获取访问令牌
                    String accessToken = getWorkspaceAccessToken(accessKey, appSecret);
                    if (accessToken == null) {
                        log.error("WorkspaceServiceImpl# 获取工作空间访问令牌失败: rgId={}", rgId);
                        return false;
                    }
                    
                    // 获取rgName
                    String rgName = getRgName(misId, rgId);
                    
                    // 创建数据集
                    String datasetIdStr = null;
                    try {
                        datasetIdStr = fridayRpcService.createDataset(rgId + "|" + rgName + SUF_NAME,
                                accessToken, mtConfigService.getFridayAccessTokenConfig().getModifier(), rgId + "|" + rgName + SUF_NAME, spaceId);
                        log.info("WorkspaceServiceImpl# 创建知识库成功: rgId={}, spaceId={}, datasetId={}", rgId, spaceId, datasetIdStr);
                    } catch (Exception e) {
                        log.error("WorkspaceServiceImpl# 创建知识库失败: rgId={}, spaceId={}, error={}", rgId, spaceId, e.getMessage(), e);
                        return false;
                    }
                    
                    // 处理AccessKey和上传文档
                    String documentIdStr = handleAccessKeyAndUploadDocument(rgId, spaceId, datasetIdStr, accessToken,
                            mtConfigService.getFridayAccessTokenConfig().getModifier(), rgId + SUF_NAME, null);
                    
                    if (documentIdStr == null) {
                        log.error("WorkspaceServiceImpl# 文档处理失败: rgId={}, spaceId={}", rgId, spaceId);
                        return false;
                    }
                    
                    // 创建并保存记录
                    RgDatasetDocumentEntity rgDatasetDocument = RgDatasetDocumentEntity.builder()
                            .rgId(rgId)
                            .spaceId(spaceId)
                            .spaceName(spaceName)
                            .datasetId(datasetIdStr)
                            .documentId(documentIdStr)
                            .timestamp(new Timestamp(System.currentTimeMillis()))
                            .accessKey(accessKey)
                            .appSecret(appSecret)
                            .build();
                    
                    // 插入到数据库
                    rgDatasetDocumentMapper.insertRgDatasetDocumentAndSpaceId(rgDatasetDocument);
                    log.info("WorkspaceServiceImpl# 已为rgId={}创建默认工作空间配置: spaceId={}, datasetId={}, documentId={}", 
                           rgId, spaceId, datasetIdStr, documentIdStr);
                    return true;
                } catch (Exception e) {
                    log.error("WorkspaceServiceImpl# 创建默认工作空间配置失败: rgId={}, error={}", rgId, e.getMessage(), e);
                    return false;
                }
            } else {
                log.info("WorkspaceServiceImpl# rgId={}已存在工作空间配置，无需创建", rgId);
                return true; // 已存在配置，视为成功
            }
        } catch (Exception e) {
            log.warn("WorkspaceServiceImpl# 检查工作空间配置异常: rgId={}, error={}", rgId, e.getMessage());
            return false;
        }
    }

    /**
     * 判断指定misId是否具有默认工作空间的权限
     * 通过尝试创建并删除测试知识库来判断
     *
     * @param misId 用户ID
     * @return 是否具有权限
     */
    @Override
    public boolean checkDefaultWorkspacePermission(String misId) {
        try {
            // 获取默认工作空间ID和配置
            String spaceId = mtConfigService.getFridaySpaceId();
            String accessKey = mtConfigService.getFridayAccessTokenConfig().getAccessKey();
            String appSecret = mtConfigService.getFridayAccessTokenConfig().getAppSecret();
            
            // 获取访问令牌
            String accessToken = getWorkspaceAccessToken(accessKey, appSecret);
            if (accessToken == null) {
                log.error("checkDefaultWorkspacePermission# 获取工作空间访问令牌失败: misId={}", misId);
                return false;
            }

            String randomStr = UUID.randomUUID().toString().substring(0, 8);
            String testDatasetName = "测试权限_" + randomStr;
            
            // 尝试创建测试知识库
            String datasetIdStr = null;
            try {
                datasetIdStr = fridayRpcService.createDataset(testDatasetName,
                        accessToken, misId, "权限测试用临时知识库", spaceId);
                log.info("checkDefaultWorkspacePermission# 创建测试知识库成功: misId={}, spaceId={}, datasetId={}", misId, spaceId, datasetIdStr);
                
                // 创建成功，说明有权限，立即删除测试知识库
                try {
                    fridayRpcService.deleteDataset(datasetIdStr, accessToken, misId, spaceId);
                    log.info("checkDefaultWorkspacePermission# 删除测试知识库成功: misId={}, spaceId={}, datasetId={}", misId, spaceId, datasetIdStr);
                } catch (Exception e) {
                    log.warn("checkDefaultWorkspacePermission# 删除测试知识库失败: misId={}, spaceId={}, datasetId={}, error={}",
                            misId, spaceId, datasetIdStr, e.getMessage());
                }
                
                return true; // 创建成功就表示有权限
            } catch (Exception e) {
                // 分析异常原因，判断是否是权限问题
                String errorMsg = String.valueOf(e.getCause());
                if (errorMsg != null && (
                        errorMsg.contains("当前请求modifier不属于当前工作空间") || 
                        errorMsg.contains("无权限") || 
                        errorMsg.contains("permission") || 
                        errorMsg.contains("权限"))) {
                    log.info("checkDefaultWorkspacePermission# 用户无默认工作空间权限: misId={}, spaceId={}, error={}",
                            misId, spaceId, errorMsg);
                    return false;
                } else {
                    // 其他错误（网络错误、服务错误等），不能确定是否有权限
                    log.error("checkDefaultWorkspacePermission# 测试权限时发生未知错误: misId={}, spaceId={}, error={}",
                            misId, spaceId, e.getMessage(), e);
                    return false;
                }
            }
        } catch (Exception e) {
            log.error("checkDefaultWorkspacePermission# 检查默认工作空间权限异常: misId={}, error={}", misId, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 检查该值班组是否具有自定义空间
     * 如果用户没有默认工作空间的权限，并且该值班组除了默认工作空间没有其他工作空间，则认为无权限
     * 
     * @param modifier 用户ID
     * @param rgId 值班组ID
     * @return 是否具有权限
     */
    @Override
    public boolean checkUserRgPermission(String modifier, Long rgId) {
        try {
            // 判断用户是否有默认工作空间权限
            boolean hasDefaultPermission = checkDefaultWorkspacePermission(modifier);
            
            // 如果有默认工作空间权限，则直接返回有权限
            if (hasDefaultPermission) {
                log.info("checkUserRgPermission# 用户具有默认工作空间权限，直接判定为有权限: modifier={}, rgId={}", modifier, rgId);
                return true;
            }
            
            // 获取该值班组下的所有工作空间配置
            List<RgDatasetDocumentEntity> rgDatasetDocuments = rgDatasetDocumentMapper.findByRgId(rgId);
            
            // 获取默认工作空间ID
            String defaultSpaceId = mtConfigService.getFridaySpaceId();
            
            // 检查是否存在非默认工作空间
            boolean hasNonDefaultWorkspace = false;
            if (rgDatasetDocuments != null && !rgDatasetDocuments.isEmpty()) {
                for (RgDatasetDocumentEntity doc : rgDatasetDocuments) {
                    if (!defaultSpaceId.equals(doc.getSpaceId())) {
                        hasNonDefaultWorkspace = true;
                        break;
                    }
                }
            }
            
            // 判断权限：如果没有默认工作空间权限，且值班组除了默认工作空间没有其他工作空间，则无权限
            boolean hasPermission = hasDefaultPermission || hasNonDefaultWorkspace;
            
            if (hasPermission) {
                log.info("checkUserRgPermission# 该值班组有自定义空间: modifier={}, rgId={}, hasNonDefaultWorkspace={}",
                        modifier, rgId, hasNonDefaultWorkspace);
            } else {
                log.warn("checkUserRgPermission# 该值班组没有自定义空间: modifier={}, rgId={}", modifier, rgId);
            }
            
            return hasPermission;
        } catch (Exception e) {
            log.error("checkUserRgPermission# 检查用户权限失败: modifier={}, rgId={}", modifier, rgId, e);
            return false;
        }
    }

    /**
     * 处理AccessKey获取/创建并上传文档
     * 
     * @param rgId 值班组ID
     * @param spaceId 工作空间ID
     * @param datasetId 数据集ID 
     * @param accessToken 访问令牌
     * @param misId 用户ID
     * @param documentName 文档名称
     * @param rgDatasetDocumentIn 可选的已有记录
     * @return 上传的文档ID，失败返回null
     */
    private String handleAccessKeyAndUploadDocument(Long rgId, String spaceId, String datasetId, 
                                    String accessToken, String misId, String documentName,
                                    RgDatasetDocumentEntity rgDatasetDocumentIn) {
        // 先检查是否已有默认AccessKey
        String defaultAk = accessKeyService.getDefaultAccessKey(String.valueOf(rgId));
        if (defaultAk != null && !defaultAk.isEmpty()) {
            log.info("WorkspaceServiceImpl# 使用已存在的默认AccessKey: rgId={}, existingAk={}", rgId, defaultAk);
        } else {
            // 如果不存在，才生成新的AccessKey
            defaultAk = accessKeyService.generateAccessKey();
            log.info("WorkspaceServiceImpl# 生成新的默认AccessKey: rgId={}, newAk={}", rgId, defaultAk);
        }
        
        // 动态生成 URL，拼接AccessKey
        String localUrl = mtConfigService.getDeliveryOpenApiUrl() + "queryLatestContentByRgId?rgId=" + rgId + "&ak=" + defaultAk;
        
        // 调用上传文档API
        String documentIdStr = null;
        try {
            // 如果创建成功，则获得到documentIdStr
            documentIdStr = fridayRpcService.uploadDocument(datasetId, localUrl, documentName, 
                    accessToken, misId, true, spaceId);
            
            // 文档上传成功后保存默认AccessKey到数据库
            if (accessKeyService.getDefaultAccessKey(String.valueOf(rgId)) == null) {
                accessKeyService.saveDefaultAccessKey(defaultAk, rgId);
                log.info("WorkspaceServiceImpl# 保存新生成的默认AccessKey到数据库: rgId={}, ak={}", rgId, defaultAk);
            }
            
            log.info("WorkspaceServiceImpl# 上传文档成功: rgId={}, spaceId={}, documentId={}", rgId, spaceId, documentIdStr);
            return documentIdStr;
        } catch (Exception e) {
            log.info("WorkspaceServiceImpl# 上传文档失败: rgId={}, error={}", rgId, e.getMessage());
            
            // 如果是其他错误（如已存在同名文档），且已有记录，则使用已有的documentId
            if (rgDatasetDocumentIn != null) {
                documentIdStr = rgDatasetDocumentIn.getDocumentId();
                return documentIdStr;
            } else {
                // 如果没有记录又创建失败，记录警告并返回null
                log.warn("WorkspaceServiceImpl# 上传文档失败且无现有记录: rgId={}, spaceId={}, datasetId={}, error={}", 
                        rgId, spaceId, datasetId, e.getMessage());
                return null;
            }
        }
    }
}