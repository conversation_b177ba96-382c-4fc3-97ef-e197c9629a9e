package com.meituan.banma.llm.corpus.server.common.domain.dto;

import java.io.Serializable;
import java.util.List;

/**
 * 用户信息DTO
 */
public class UserInfoDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    private int id;

    /**
     * 登录名
     */
    private String login;

    /**
     * 用户名
     */
    private String name;

    /**
     * 用户编码
     */
    private String code;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 角色列表
     */
    private List<String> roles;

    /**
     * 是否已验证
     */
    private Boolean isVerified;

    /**
     * 验证类型
     */
    private String verifyType;

    /**
     * 验证过期时间
     */
    private Long verifyExpireTime;

    /**
     * 通行证
     */
    private String passport;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getLogin() {
        return login;
    }

    public void setLogin(String login) {
        this.login = login;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public List<String> getRoles() {
        return roles;
    }

    public void setRoles(List<String> roles) {
        this.roles = roles;
    }

    public Boolean getIsVerified() {
        return isVerified;
    }

    public void setIsVerified(Boolean isVerified) {
        this.isVerified = isVerified;
    }

    public String getVerifyType() {
        return verifyType;
    }

    public void setVerifyType(String verifyType) {
        this.verifyType = verifyType;
    }

    public Long getVerifyExpireTime() {
        return verifyExpireTime;
    }

    public void setVerifyExpireTime(Long verifyExpireTime) {
        this.verifyExpireTime = verifyExpireTime;
    }

    public String getPassport() {
        return passport;
    }

    public void setPassport(String passport) {
        this.passport = passport;
    }
} 