package com.meituan.banma.llm.corpus.server.service.impl;

import com.dianping.cat.util.StringUtils;
import com.meituan.banma.llm.corpus.api.constants.BizCode;
import com.meituan.banma.llm.corpus.api.exception.LlmCorpusException;
import com.meituan.banma.llm.corpus.server.rpc.venus.VenusRpcService;
import com.meituan.banma.llm.corpus.server.rpc.venus.dto.UploadImageBytesDTO;
import com.meituan.banma.llm.corpus.server.service.IMtImageService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;

@Slf4j
@Service
public class MtImageServiceImpl implements IMtImageService {
    @Autowired
    private VenusRpcService venusRpcService;
    @Override
    public String uploadImageByUrl(String imageUrl) throws LlmCorpusException {
        // 请求imageUrl，获取图片byte[]
        UploadImageBytesDTO imageBytes = getImageBytes(imageUrl);
        String url = venusRpcService.uploadImageBytes(imageBytes);
        if (StringUtils.isBlank(url)) {
            log.error("#MtImageServiceImpl.uploadImageByUrl#error,上传图片失败,url:{}", imageUrl);
            throw LlmCorpusException.buildWithMsg(BizCode.IMAGE_UPLOAD_FAILED);
        }
        return url;
    }

    private UploadImageBytesDTO getImageBytes(String imageUrl) throws LlmCorpusException  {
        try {
            URL url = new URL(imageUrl);
            HttpURLConnection connection = (HttpURLConnection)url.openConnection();
            connection.setRequestMethod("GET");
            connection.connect();
            InputStream inputStream = connection.getInputStream();
            byte[] imageBytes = IOUtils.toByteArray(inputStream);
            UploadImageBytesDTO uploadImageBytesDTO = new UploadImageBytesDTO();
            uploadImageBytesDTO.setImageBytes(imageBytes);
            // 从返回头获取文件名
            String fileName = connection.getHeaderField("Content-Disposition");
            if ( StringUtils.isBlank(fileName)) {
                String[] parts = imageUrl.split("/");
                fileName = parts[parts.length - 1].split("\\?")[0];
                if (StringUtils.isBlank(fileName)) {
                    uploadImageBytesDTO.setImageName("default_name.png");
                }else {
                    uploadImageBytesDTO.setImageName(fileName);
                }
            }
            return uploadImageBytesDTO;
        } catch (Exception e) {
            log.error("#MtImageServiceImpl.getImageBytes#error,请求图片数据失败,url:{}", imageUrl, e);
            throw LlmCorpusException.buildWithMsg(BizCode.GET_IMAGE_FAILED);
        }
    }
}
