package com.meituan.banma.llm.corpus.server.common.domain.dto;

import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

/**
 * 确认样式DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ConfirmStyleDTO {
    /**
     * 提示类型
     * 1-系统对话框，2-Toast提示
     */
    private Integer promptType;
    
    /**
     * 是否需要确认
     */
    private boolean needConfirm;
    
    /**
     * 确认标题
     */
    private String confirmTitle;
    
    /**
     * 确认内容
     */
    private String confirmContent;
    
    /**
     * 创建一个简单的Toast提示样式，不需要确认
     */
    public static ConfirmStyleDTO createSimpleToast() {
        return ConfirmStyleDTO.builder()
                .promptType(1)
                .needConfirm(false)
                .confirmTitle("")
                .confirmContent("")
                .build();
    }
    
    /**
     * 创建一个需要确认的对话框样式
     */
    public static ConfirmStyleDTO createConfirmDialog(String title, String content) {
        return ConfirmStyleDTO.builder()
                .promptType(1)
                .needConfirm(true)
                .confirmTitle(title)
                .confirmContent(content)
                .build();
    }
} 