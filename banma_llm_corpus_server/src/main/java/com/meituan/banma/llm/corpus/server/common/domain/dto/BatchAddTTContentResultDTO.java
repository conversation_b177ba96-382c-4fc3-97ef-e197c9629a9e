package com.meituan.banma.llm.corpus.server.common.domain.dto;

import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 批量添加TT内容的结果DTO
 */
@Data
@Builder
public class BatchAddTTContentResultDTO {
    /**
     * 处理的总数量
     */
    private Integer total;
    
    /**
     * 成功处理的数量
     */
    private Integer success;
    
    /**
     * 失败处理的数量
     */
    private Integer failed;
    
    /**
     * 成功处理的任务ID列表
     */
    private List<String> taskIds;
    
    /**
     * 失败的TT ID列表
     */
    private List<String> failedTicketIds;
    
    /**
     * 失败原因映射，key为TT ID，value为失败原因
     */
    private Map<String, String> failureReasons;
} 