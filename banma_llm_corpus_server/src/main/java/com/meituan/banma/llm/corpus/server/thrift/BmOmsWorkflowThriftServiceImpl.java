package com.meituan.banma.llm.corpus.server.thrift;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.dianping.lion.client.util.CollectionUtils;
import com.google.common.collect.Lists;
import com.meituan.banma.llm.corpus.api.client.BmOmsWorkflowThriftService;
import com.meituan.banma.llm.corpus.api.constants.BizCode;
import com.meituan.banma.llm.corpus.api.exception.LlmCorpusException;
import com.meituan.banma.llm.corpus.api.response.CommonResponse;
import com.meituan.banma.llm.corpus.server.common.MtConfigService;
import com.meituan.banma.llm.corpus.server.common.domain.dto.FridayConversationConfig;
import com.meituan.banma.llm.corpus.server.rpc.friday.FridayRpcService;
import com.meituan.banma.llm.corpus.server.rpc.friday.dto.FridayConversationParams;
import com.meituan.banma.llm.corpus.server.rpc.friday.dto.FridayConversationResponseDataDTO;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftServer;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
@MdpThriftServer()
public class BmOmsWorkflowThriftServiceImpl implements BmOmsWorkflowThriftService {

    @Resource
    FridayRpcService fridayRpcService;

    @Resource
    MtConfigService mtConfigService;

    @Override
    public CommonResponse<String> getAccessGuideIntelligenceConfiguration(String requirementAnalyseResult) throws LlmCorpusException {
        CommonResponse<String> response = new CommonResponse<>();
        try {
            FridayConversationConfig fridayConversationConfig = mtConfigService.getOmsAccessGuideIntelligenceConfiguration();
            String token = fridayRpcService.getFridayAccessToken();
            FridayConversationParams params = new FridayConversationParams();
            params.setUserId(fridayConversationConfig.getDefaultUserId());
            params.setUserType(fridayConversationConfig.getDefaultUserType());
            params.setStream(false);
            params.setDebug(false);
            params.setMultiTurn(false);
            params.setRedo(false);
            params.setAccessToken(token);
            params.setAppId(fridayConversationConfig.getAppId());
            params.setUtterances(Lists.newArrayList(requirementAnalyseResult));
            log.info("getAccessGuideIntelligenceConfiguration# 请求参数构建完成, params:{}", JSON.toJSONString(params));

            FridayConversationResponseDataDTO fridayConversationResponseDTO = fridayRpcService.conversation(params);
            if (fridayConversationResponseDTO == null || CollectionUtils.isEmpty(fridayConversationResponseDTO.getContents())) {
                log.error("getAccessGuideIntelligenceConfiguration#error,fridayConversationResponseDTO为空 query:{},fridayConversationResponseDTO:{}", requirementAnalyseResult, fridayConversationResponseDTO);
                response.setCode(BizCode.RPC_BIZ_ERROR.getCode());
                return response;
            }

            String responseText = fridayConversationResponseDTO.getContents().get(0).getText();
            log.info("getAccessGuideIntelligenceConfiguration# 获取响应文本:{}", responseText);

            response.setData(responseText);
            return response;
        } catch (LlmCorpusException e) {
            throw e;
        } catch (Exception e) {
            log.error("#BmOmsWorkflowThriftServiceImpl.getAccessGuideIntelligenceConfiguration#error, requirementAnalyseResult:{}", requirementAnalyseResult, e);
            throw LlmCorpusException.build(BizCode.FRIDAY_CONVERT_TT_CHAT_TO_CORPUS_ERROR);
        }
    }
}
