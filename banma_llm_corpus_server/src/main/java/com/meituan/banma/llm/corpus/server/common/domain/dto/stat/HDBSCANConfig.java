package com.meituan.banma.llm.corpus.server.common.domain.dto.stat;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * HDBSCAN*聚类算法的配置参数
 */
@Data
@Builder
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class HDBSCANConfig {
    /**
     * 核心点的最小邻居数，类似于DBSCAN的minPts参数
     * 该参数决定了形成密集区域所需的最小点数
     * 值越小，算法越容易发现小的簇
     * 值越大，对噪声的容忍度越高，但可能会导致小簇被忽略
     * 推荐范围: 2-10，默认值为3
     */
    private Integer minPoints = 3;
    
    /**
     * 最小簇大小，小于此值的簇将被视为噪声
     * 该参数决定了最终结果中簇的最小大小
     * 值越小，算法越容易形成更多的小簇
     * 值越大，小簇会被视为噪声
     * 推荐范围: 与minPoints相同或略小，默认值为2
     */
    private Integer minClusterSize = 2;
    
    /**
     * 互达距离缩放因子，用于调整互达性距离
     * 该参数影响点之间的距离计算
     * 值为1.0相当于标准HDBSCAN
     * 值大于1.0会减小距离，使簇更容易形成
     * 值小于1.0会增大距离，使簇更难形成
     * 推荐范围: 0.5-2.0，默认为1.2
     */
    private Double alpha = 1.2;
    
    /**
     * 距离度量方式
     * 该参数决定了如何计算点之间的距离
     * 默认为"euclidean"（欧几里得距离）
     * 可选值: 
     * - euclidean: 欧几里得距离，适合大多数情况
     * - manhattan: 曼哈顿距离，适合高维数据
     * - cosine: 余弦距离，适合文本等方向敏感的数据
     */
    private String metric = "cosine";
    
    /**
     * 最小生成树构建算法类型
     * 可选"boruvka"或"prim"
     * boruvka算法通常更快，特别是对于大型数据集
     * prim算法在某些情况下可能更精确
     * 默认使用"boruvka"
     */
    private String algorithm = "boruvka";
    
    /**
     * 叶节点大小，用于构建树结构
     * 只在构建最小生成树时使用
     * 对于小型数据集，可以使用较小的值
     * 默认为40
     */
    private Integer leafSize = 40;
    
    /**
     * 是否使用近似最小生成树算法
     * 设置为true可以加速大型数据集的处理
     * 但可能略微降低精度
     * 默认为true
     */
    private Boolean approxMinSpanningTree = true;
    
    /**
     * 是否生成最小生成树
     * 通常不需要设置为true，除非需要可视化
     * 默认为false
     */
    private Boolean genMinSpanningTree = false;
    
    /**
     * 是否允许单簇
     * 设置为true时，如果所有点属于同一个密集区域，会生成一个单一的簇
     * 设置为false时，会强制算法尝试分割数据
     * 默认为true
     */
    private Boolean allowSingleCluster = true;
    
    /**
     * 随机种子，用于初始化随机数生成器
     * 保证相同数据和参数下的结果可重复
     * 默认为42
     */
    private Long randomSeed = 42L;
    
    /**
     * 向量维度，用于表示文本的向量维度
     * 默认为768（对应BERT的特征维度）
     */
    private Integer vectorDimension = 768;
    
    public Integer getMinClusterSize() {
        return minClusterSize != null ? minClusterSize : minPoints;
    }
} 