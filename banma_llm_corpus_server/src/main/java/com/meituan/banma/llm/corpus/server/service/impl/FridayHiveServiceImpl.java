package com.meituan.banma.llm.corpus.server.service.impl;

import com.google.common.collect.Lists;
import com.meituan.banma.llm.corpus.server.dal.entity.FridayBotConversationViewEntity;
import com.meituan.banma.llm.corpus.server.rpc.talos2.TalosTwoRpcService;
import com.meituan.banma.llm.corpus.server.service.IFridayHiveService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class FridayHiveServiceImpl implements IFridayHiveService {
    
    private static final String FRIDAY_CONVERSATION_QUERY_SQL = 
            "SELECT record_id, conversation_id, user_id, user_type, app_id, request_id, " +
            "message_id, parent_message_id, role, generate_type, message, status, add_time, " +
            "update_time, recall_info, assistant_recommendation, conversation_name, " +
            "is_new, deleted, access_channel, last_chat_time, record_add_time, " +
            "record_update_time, dt " +
            "FROM mart_peisongdispatch.friday_bot_conversation " +
            "WHERE dt = '%s' AND app_id IN (%s)";

    @Autowired
    private TalosTwoRpcService talosTwoRpcService;

    @Override
    public List<FridayBotConversationViewEntity> queryFridayConversationsByDate(List<String> appIdList, String date) {
        List<FridayBotConversationViewEntity> result = Lists.newArrayList();
        if (CollectionUtils.isEmpty(appIdList) || StringUtils.isBlank(date)) {
            log.warn("#FridayHiveServiceImpl.queryFridayConversationsByDate#warn, appIdList or date is empty,appIdList:{},date:{}", appIdList, date);
            return result;
        }

        try {
            // 构建appId IN条件
            String appIdsStr = appIdList.stream()
                    .map(appId -> "'" + appId + "'")
                    .reduce((a, b) -> a + "," + b)
                    .orElse("");

            // 构建完整SQL
            String sql = String.format(FRIDAY_CONVERSATION_QUERY_SQL, date, appIdsStr);

            // 执行查询
            List<List<Object>> queryResult = talosTwoRpcService.queryFromHive(sql);

            // 转换查询结果
            if (CollectionUtils.isNotEmpty(queryResult)) {
                for (List<Object> row : queryResult) {
                    if (CollectionUtils.isEmpty(row) || row.size() < 24) {
                        continue;
                    }
                    result.add(buildEntity(row));
                }
            }
        } catch (Exception e) {
            log.error("#FridayHiveServiceImpl.queryFridayConversationsByDate#error, appIdList:{}, date:{}", appIdList, date, e);
        }

        return result;
    }

    /**
     * 构建FridayBotConversationViewEntity对象
     * @param row 查询结果行数据
     * @return FridayBotConversationViewEntity实例
     */
    private static FridayBotConversationViewEntity buildEntity(List<Object> row) {
        FridayBotConversationViewEntity entity = new FridayBotConversationViewEntity();
        int index = 0;
        entity.setRecordId(String.valueOf(row.get(index++)));
        entity.setConversationId(String.valueOf(row.get(index++)));
        entity.setUserId(String.valueOf(row.get(index++)));
        entity.setUserType(String.valueOf(row.get(index++)));
        entity.setAppId(String.valueOf(row.get(index++)));
        entity.setRequestId(String.valueOf(row.get(index++)));
        entity.setMessageId(String.valueOf(row.get(index++)));
        entity.setParentMessageId(String.valueOf(row.get(index++)));
        entity.setRole(String.valueOf(row.get(index++)));
        entity.setGenerateType(String.valueOf(row.get(index++)));
        entity.setMessage(String.valueOf(row.get(index++)));
        entity.setStatus(String.valueOf(row.get(index++)));
        entity.setAddTime(String.valueOf(row.get(index++)));
        entity.setUpdateTime(String.valueOf(row.get(index++)));
        entity.setRecallInfo(String.valueOf(row.get(index++)));
        entity.setAssistantRecommendation(String.valueOf(row.get(index++)));
        entity.setConversationName(String.valueOf(row.get(index++)));
        entity.setIsNew(Integer.valueOf(String.valueOf(row.get(index++))));;
        entity.setDeleted(Integer.valueOf(String.valueOf(row.get(index++))));;
        entity.setAccessChannel(String.valueOf(row.get(index++)));
        entity.setLastChatTime(String.valueOf(row.get(index++)));
        entity.setRecordAddTime(String.valueOf(row.get(index++)));
        entity.setRecordUpdateTime(String.valueOf(row.get(index++)));
        entity.setDt(String.valueOf(row.get(index)));
        return entity;
    }
}

