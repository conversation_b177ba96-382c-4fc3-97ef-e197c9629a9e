package com.meituan.banma.llm.corpus.server.controller;

import com.meituan.banma.llm.corpus.api.constants.BizCode;
import com.meituan.banma.llm.corpus.api.exception.LlmCorpusException;
import com.meituan.banma.llm.corpus.server.common.CommonResult;
import com.meituan.banma.llm.corpus.server.common.domain.dto.FridayAppInfo;
import com.meituan.banma.llm.corpus.server.common.domain.dto.FridayConversationMessageDTO;
import com.meituan.banma.llm.corpus.server.common.domain.dto.stat.AppStatusDTO;
import com.meituan.banma.llm.corpus.server.common.domain.dto.stat.CorpusOperationStatusDTO;
import com.meituan.banma.llm.corpus.server.common.interceptor.MisIdInterceptor;
import com.meituan.banma.llm.corpus.server.service.IStatisticsService;
import com.meituan.banma.llm.corpus.server.utils.ThreadLocalTransferUtil;
import com.sankuai.meituan.auth.util.UserUtils;
import com.sankuai.meituan.auth.vo.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 统计服务控制器
 * <AUTHOR>
 */
@RestController
@Slf4j
@RequestMapping("/statistics")
@ResponseBody
public class StatisticsController {

    @Autowired
    private IStatisticsService statisticsService;

    /**
     * 根据应用ID和日期范围查询统计信息
     * @param appId 应用ID
     * @param beginDate 开始日期时间戳（毫秒）
     * @param endDate 结束日期时间戳（毫秒）
     * @param forceGenerate 是否强制重新生成报表，不查询已有数据，默认false
     * @return 统计结果
     */
    @GetMapping("/queryStatByAppIdAndDate")
    public Object queryStatByAppIdAndDate(@RequestParam("appId") String appId,
                                         @RequestParam("beginDate") Long beginDate,
                                         @RequestParam("endDate") Long endDate,
                                         @RequestParam(value = "forceGenerate", required = false, defaultValue = "false") Boolean forceGenerate) {
        try {
            if (StringUtils.isBlank(appId) || beginDate == null || beginDate <= 0
                    || endDate == null || endDate <= 0) {
                log.error("#StatisticsController.queryStatByAppIdAndDate#error,参数错误：appId:{},beginDate:{},endDate:{}",
                        appId, beginDate, endDate);
                return CommonResult.error(BizCode.ILLEGAL_ARGUMENT.getCode(), "参数错误，查询失败");
            }

            // 权限校验：检查当前用户是否有查看该应用统计数据的权限
            // 注意：crane定时任务调用时不会有用户上下文，UserUtils.getUser()会返回null，此时跳过权限检查
            User currentUser = UserUtils.getUser();
            if (currentUser != null) {
                // 有用户上下文，进行权限校验
                String currentMisId = currentUser.getLogin();
                if (!statisticsService.validateUserPermissionForApp(currentMisId, appId)) {
                    log.warn("#StatisticsController.queryStatByAppIdAndDate#warn,用户无权限查看应用统计数据: misId={}, appId={}",
                            currentMisId, appId);
                    return CommonResult.error(BizCode.NO_PERMISSION.getCode(), "您没有查看该应用统计数据的权限");
                }
                log.info("#StatisticsController.queryStatByAppIdAndDate#info,权限校验通过: misId={}, appId={}",
                        currentMisId, appId);
            } else {
                // 无用户上下文，可能是crane定时任务调用，跳过权限检查
                log.error("#StatisticsController.queryStatByAppIdAndDate#warn,无用户上下文: appId={}", appId);
                return CommonResult.error(-1,"未登录");
            }
            AppStatusDTO appStatusDTO = statisticsService.queryStatByAppIdAndDate(appId, beginDate, endDate, forceGenerate);
            return CommonResult.success(appStatusDTO);
        } catch (LlmCorpusException e) {
            log.error("#StatisticsController.queryStatByAppIdAndDate#error,查询失败,appId:{},beginDate:{},endDate:{},异常原因:{}",
                    appId, beginDate, endDate, e.getMessage(), e);
            return CommonResult.error(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("#StatisticsController.queryStatByAppIdAndDate#error,查询失败,appId:{},beginDate:{},endDate:{},异常原因:{}",
                    appId, beginDate, endDate, e.getMessage(), e);
            return CommonResult.error(String.format("查询失败,异常原因=%s", e.getMessage()));
        }
    }
    
    /**
     * 手动触发重新生成指定应用和时间范围的统计报表
     * @param appId 应用ID
     * @param beginDate 开始日期时间戳（毫秒）
     * @param endDate 结束日期时间戳（毫秒）
     * @return 是否重新生成成功
     */
    @GetMapping("/regenerateReport")
    public Object regenerateReport(@RequestParam("appId") String appId,
                                  @RequestParam("beginDate") Long beginDate,
                                  @RequestParam("endDate") Long endDate) {
        try {
            if (StringUtils.isBlank(appId) || beginDate == null || beginDate <= 0 
                    || endDate == null || endDate <= 0) {
                log.error("#StatisticsController.regenerateReport#error,参数错误：appId:{},beginDate:{},endDate:{}",
                        appId, beginDate, endDate);
                return CommonResult.error(BizCode.ILLEGAL_ARGUMENT.getCode(), "参数错误，重新生成失败");
            }
            
            boolean result = statisticsService.regenerateStatisticsReport(appId, beginDate, endDate);
            return CommonResult.success(result);
        } catch (LlmCorpusException e) {
            log.error("#StatisticsController.regenerateReport#error,重新生成失败,appId:{},beginDate:{},endDate:{},异常原因:{}",
                    appId, beginDate, endDate, e.getMessage(), e);
            return CommonResult.error(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("#StatisticsController.regenerateReport#error,重新生成失败,appId:{},beginDate:{},endDate:{},异常原因:{}",
                    appId, beginDate, endDate, e.getMessage(), e);
            return CommonResult.error(String.format("重新生成失败,异常原因=%s", e.getMessage()));
        }
    }
    
    /**
     * 查询所有Friday应用信息列表
     * @return Friday应用信息列表
     */
    @GetMapping("/queryFridayAppInfoList")
    public Object queryFridayAppInfoList() {
        try {
            String misId = MisIdInterceptor.getMisId();
            List<FridayAppInfo> fridayAppInfoList = statisticsService.queryFridayAppInfoListWithValidate(misId);
            return CommonResult.success(fridayAppInfoList);
        } catch (Exception e) {
            log.error("#StatisticsController.queryFridayAppInfoList#error,查询失败,异常原因:{}", e.getMessage(), e);
            return CommonResult.error(String.format("查询失败,异常原因=%s", e.getMessage()));
        }
    }
    
    /**
     * 根据会话ID查询会话的所有消息记录
     * @param conversationId 会话ID
     * @return 会话消息记录列表
     */
    @GetMapping("/queryConversationMessages")
    public Object queryConversationMessages(@RequestParam("conversationId") String conversationId) {
        try {
            if (StringUtils.isBlank(conversationId)) {
                log.error("#StatisticsController.queryConversationMessages#error,参数错误：conversationId为空");
                return CommonResult.error(BizCode.ILLEGAL_ARGUMENT.getCode(), "参数错误，会话ID不能为空");
            }
            
            List<FridayConversationMessageDTO> conversations = statisticsService.queryConversationMessages(conversationId);
            return CommonResult.success(conversations);
        } catch (LlmCorpusException e) {
            log.error("#StatisticsController.queryConversationMessages#error,查询失败,conversationId:{},异常原因:{}",
                    conversationId, e.getMessage(), e);
            return CommonResult.error(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("#StatisticsController.queryConversationMessages#error,查询失败,conversationId:{},异常原因:{}",
                    conversationId, e.getMessage(), e);
            return CommonResult.error(String.format("查询失败,异常原因=%s", e.getMessage()));
        }
    }

    /**
     * 获取语料服务运营状态报表
     * @param beginDate 开始日期时间戳（毫秒）
     * @param endDate 结束日期时间戳（毫秒）
     * @param forceGenerate 是否强制重新生成报表，不查询已有数据，默认false
     * @return 语料运营状态报表
     */
    @GetMapping("/getCorpusOperationStatus")
    public Object getCorpusOperationStatus(@RequestParam("beginDate") Long beginDate,
                                          @RequestParam("endDate") Long endDate,
                                          @RequestParam(value = "forceGenerate", required = false, defaultValue = "false") Boolean forceGenerate) {
        try {
            if (beginDate == null || beginDate <= 0 || endDate == null || endDate <= 0) {
                log.error("#StatisticsController.getCorpusOperationStatus#error,参数错误：beginDate:{},endDate:{}",
                        beginDate, endDate);
                return CommonResult.error(BizCode.ILLEGAL_ARGUMENT.getCode(), "参数错误，时间范围不能为空或小于等于0");
            }
            
            if (beginDate >= endDate) {
                log.error("#StatisticsController.getCorpusOperationStatus#error,参数错误：开始时间大于等于结束时间,beginDate:{},endDate:{}",
                        beginDate, endDate);
                return CommonResult.error(BizCode.ILLEGAL_ARGUMENT.getCode(), "参数错误，开始时间必须小于结束时间");
            }
            
            CorpusOperationStatusDTO statusDTO = statisticsService.getCorpusOperationStatus(beginDate, endDate, forceGenerate);
            return CommonResult.success(statusDTO);
        } catch (LlmCorpusException e) {
            log.error("#StatisticsController.getCorpusOperationStatus#error,查询失败,beginDate:{},endDate:{},异常原因:{}",
                    beginDate, endDate, e.getMessage(), e);
            return CommonResult.error(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("#StatisticsController.getCorpusOperationStatus#error,查询失败,beginDate:{},endDate:{},异常原因:{}",
                    beginDate, endDate, e.getMessage(), e);
            return CommonResult.error(String.format("查询失败,异常原因=%s", e.getMessage()));
        }
    }
} 