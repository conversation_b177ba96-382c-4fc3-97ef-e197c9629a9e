package com.meituan.banma.llm.corpus.server.thrift;

import com.meituan.banma.llm.corpus.api.client.CommonSqlExecuteIface;
import com.meituan.banma.llm.corpus.server.common.JacksonUtil;
import com.meituan.banma.llm.corpus.server.service.ICommonSqlExecuteService;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftServer;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;


@Slf4j
@MdpThriftServer()
public class CommonSqlExecuteIfaceImpl implements CommonSqlExecuteIface {

    @Resource
    private ICommonSqlExecuteService commonSqlExecuteService;


    /**
     * description "Execute a select SQL query and return results as a JSON string."
     *
     * @param sql SQL query statement
     * @return String
     */
    @Override
    public String queryDB(String sql, String jdbcRef) {
        return JacksonUtil.serializeWithoutException(commonSqlExecuteService.query(sql, jdbcRef));
    }

    /**
     * description "Explain a SQL query and return results in a readable format."
     *
     * @param sql SQL query statement
     * @return String
     */
    @Override
    public String explainSql(String sql, String jdbcRef) {
        return JacksonUtil.serializeWithoutException(commonSqlExecuteService.explain(sql, jdbcRef));
    }

    /**
     * description "Return all table names in the database separated by comma."
     */
    @Override
    public String listAllTableSchema() {
        return JacksonUtil.serializeWithoutException(commonSqlExecuteService.getAllTableSchema());

    }

    /**
     * 将秒级时间戳转换为易读的日期时间格式(yyyy-MM-dd HH:mm:ss)，方便查询结果中的时间戳展示。
     *
     * @param timestamp 秒级时间戳
     * @return 易读的日期时间格式
     */
    @Override
    public String formatTimestamp(Long timestamp) {
        if (timestamp == null) {
            return "";
        }
        LocalDateTime dateTime = LocalDateTime.ofInstant(
                Instant.ofEpochSecond(timestamp),
                ZoneId.systemDefault()
        );
        return dateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }
}
