package com.meituan.banma.llm.corpus.server.common.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SaveMergeCorpusParam {
    /**
     * 语料ID
     */
    private String ticketId;

    /**
     * 语料标题
     */
    private String title;

    /**
     * 语料内容
     */
    private String content;

    /**
     * 操作人ID
     */
    private String misId;

    /**
     * 值班组ID
     */
    private Long rgId;

    /**
     * 来源
     */
    private Integer source;

    /**
     * 语料类型
     */
    private Integer type;

    /**
     * 待合并的语料ID列表
     */
    private List<String> corpusIdList;

    /**
     * 合并任务id
     */
    private String taskId;

    /**
     * 标签id列表，英文逗号分隔，如1,2,3
     */
    private String tagsIds;
}
