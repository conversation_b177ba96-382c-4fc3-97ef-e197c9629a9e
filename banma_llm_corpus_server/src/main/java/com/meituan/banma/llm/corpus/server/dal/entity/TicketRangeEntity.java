package com.meituan.banma.llm.corpus.server.dal.entity;

import lombok.Data;
import java.sql.Timestamp;

/**
 * 工单范围信息实体类
 * 对应表名: ticket_range
 */
@Data
public class TicketRangeEntity {
    /**
     * 自增主键
     */
    private Long id;

    /**
     * 组织ID
     */
    private Long rgId;

    /**
     * 工单ID
     */
    private String ticketId;

    /**
     * 日期
     */
    private Timestamp date;

    /**
     * 商家ID
     */
    private String merchantId;

    /**
     * 时段
     */
    private String timeSlot;

    /**
     * 原因
     */
    private String reason;

    /**
     * 类型ID
     */
    private String typeId;

    /**
     * 类型名称
     */
    private String typeName;

    /**
     * 城市名称
     */
    private String cityName;

    /**
     * 城市ID
     */
    private String cityId;

    /**
     * 订单ID
     */
    private String orderId;

    /**
     * 地址
     */
    private String address;
} 