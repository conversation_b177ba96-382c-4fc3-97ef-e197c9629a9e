<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:task="http://www.springframework.org/schema/task"
       xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:lion="http://code.dianping.com/schema/lion"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
                           http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task-3.1.xsd
                           http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd
                           http://code.dianping.com/schema/lion http://code.dianping.com/schema/lion/lion-1.0.xsd">

    <context:component-scan base-package="com.meituan.banma.llm.corpus"/>

    <task:annotation-driven/>

    <import resource="classpath:banma_jarvis_aiops_client.xml" />

    <!-- 接入开放应用平台及学城 -->
    <bean id="openThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <!-- service接口名 -->
        <property name="serviceInterface" value="com.sankuai.xm.openplatform.auth.service.XmAuthServiceI"/>
        <!-- thrift rpc 超时时间（毫秒） -->
        <property name="timeout" value="30000"/>
        <property name="filterByServiceName" value="true"/>
        <!-- 服务端appkey，由服务方提供 -->
        <property name="remoteAppkey" value="com.sankuai.dxenterprise.open.gateway"/>
    </bean>

    <bean id="xmKmService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <!-- service接口名 -->
        <property name="serviceInterface" value="com.sankuai.xm.openplatform.api.service.open.XmOpenKmServiceI"/>
        <!-- thrift rpc 超时时间（毫秒） -->
        <property name="timeout" value="4000"/>
        <property name="filterByServiceName" value="true"/>
        <!-- 服务端appkey，由服务方提供 -->
        <property name="remoteAppkey" value="com.sankuai.dxenterprise.open.gateway"/>
    </bean>

    <bean id="xmOpenMsgService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <!-- service接口名 -->
        <property name="serviceInterface" value="com.sankuai.xm.openplatform.api.service.open.XmOpenMessageServiceI"/>
        <!-- thrift rpc 超时时间（毫秒） -->
        <property name="timeout" value="1000"/>
        <property name="filterByServiceName" value="true"/>
        <!-- 服务端appkey，由服务方提供 -->
        <property name="remoteAppkey" value="com.sankuai.dxenterprise.open.gateway"/>
    </bean>

    <bean id="xmOpenUserService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <!-- service接口名 -->
        <property name="serviceInterface" value="com.sankuai.xm.openplatform.api.service.open.XmOpenUserServiceI"/>
        <!-- thrift rpc 超时时间（毫秒） -->
        <property name="timeout" value="5000"/> <!-- 根据需要调整超时时间 -->
        <property name="filterByServiceName" value="true"/>
        <!-- 服务端appkey，由服务方提供 -->
        <property name="remoteAppkey" value="com.sankuai.dxenterprise.open.gateway"/>
    </bean>

    <bean id="xmOpenGroupService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <!-- service接口名 -->
        <property name="serviceInterface" value="com.sankuai.xm.openplatform.api.service.open.XmOpenGroupServiceI"/>
        <!-- thrift rpc 超时时间（毫秒） -->
        <property name="timeout" value="5000"/> <!-- 根据需要调整超时时间 -->
        <property name="filterByServiceName" value="true"/>
        <!-- 服务端appkey，由服务方提供 -->
        <property name="remoteAppkey" value="com.sankuai.dxenterprise.open.gateway"/>
    </bean>

    <bean id="fileDownloadService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <!-- service接口名 -->
        <property name="serviceInterface" value="com.meituan.mx.mbox.thrift.service.download.IFileDownloadService"/>
        <!-- thrift rpc 超时时间（毫秒） -->
        <property name="timeout" value="5000"/> <!-- 根据需要调整超时时间 -->
        <property name="filterByServiceName" value="true"/>
        <!-- 服务端appkey，由服务方提供 -->
        <property name="remoteAppkey" value="com.sankuai.xm.mbox.imdown.chatfile"/>
    </bean>

    <bean id="openCardService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <!-- service接口名 -->
        <property name="serviceInterface" value="com.sankuai.xm.openplatform.api.service.open.OpenCardServiceI"/>
        <!-- thrift rpc 超时时间（毫秒） -->
        <property name="timeout" value="5000"/> <!-- 根据需要调整超时时间 -->
        <property name="filterByServiceName" value="true"/>
        <!-- 服务端appkey，由服务方提供 -->
        <property name="remoteAppkey" value="com.sankuai.dxenterprise.open.gateway"/>
    </bean>
    <!--Crane-->
    <beans xmlns="http://www.springframework.org/schema/beans"
           xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
           xmlns:crane="http://code.dianping.com/schema/crane"
           xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
       http://code.dianping.com/schema/crane http://code.dianping.com/schema/crane/crane-1.0.xsd">
        <crane:annotation-driven/>
    </beans>
</beans>
