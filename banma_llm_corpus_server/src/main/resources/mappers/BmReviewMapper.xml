<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meituan.banma.llm.corpus.server.dal.mapper.ReviewMapper">

    <select id="findLatestContentsByRgId" parameterType="map" resultType="com.meituan.banma.llm.corpus.server.common.domain.dto.LatestContentDTO">
        SELECT title, content, ticket_id, tags_ids
        FROM modified_output mo
        WHERE rg_id = #{rgId}
        AND mo.corpus_status = 1
        AND version = (
        SELECT MAX(version)
        FROM modified_output
        WHERE rg_id = #{rgId} AND ticket_id = mo.ticket_id
        )
    </select>

    <!-- 插入新的版本记录，使用版本号检查来模拟乐观锁 -->
    <insert id="insertModifiedOutputWithVersionCheck" parameterType="com.meituan.banma.llm.corpus.server.dal.entity.KnowledgeBaseVersionEntity">
        INSERT INTO modified_output (ticket_id, title, content, version, mis_id, rg_id, timestamp, source, corpus_status, merged_to_id, type, content_id, background_knowledge_id, corpus_sop_id, rule_id, task_id, tags_ids)
        SELECT #{ticketId}, #{title}, #{content}, #{version}, #{misId}, #{rgId}, #{timestamp}, #{source}, #{corpusStatus}, #{mergedToId}, #{type}, #{contentId}, #{backgroundKnowledgeId}, #{corpusSopId}, #{ruleId}, #{taskId}, #{tagsIds}
        WHERE NOT EXISTS (
        SELECT 1 FROM modified_output WHERE ticket_id = #{ticketId} AND version >= #{version} AND rg_id = #{rgId}
        )
    </insert>

    <!-- 根据值班组id和ticketId 查询当前最新版本的知识记录（不分页） -->
    <select id="findAllLatestKnowledgeListByRgId"
            resultType="com.meituan.banma.llm.corpus.server.dal.entity.KnowledgeBaseVersionEntity">
        SELECT mo.*
        FROM modified_output mo
        INNER JOIN (
            SELECT ticket_id, MAX(version) AS max_version
            FROM modified_output
            WHERE rg_id = #{rgId}
            GROUP BY ticket_id
        ) max_ver ON mo.ticket_id = max_ver.ticket_id AND mo.version = max_ver.max_version
        INNER JOIN (
            SELECT ticket_id, MIN(version) AS min_version, MIN(timestamp) AS create_time
            FROM modified_output
            WHERE rg_id = #{rgId}
            GROUP BY ticket_id
        ) create_ver ON mo.ticket_id = create_ver.ticket_id
        WHERE mo.corpus_status = 1
        <if test="ticketId != null and ticketId != ''">
            AND mo.ticket_id = #{ticketId}
        </if>
        <if test="title != null and title != ''">
            AND mo.title LIKE CONCAT('%', #{title}, '%')
        </if>
        <if test="content != null and content != ''">
            AND mo.content LIKE CONCAT('%', #{content}, '%')
        </if>
        <if test="source != null">
            AND mo.source = #{source}
        </if>
        <if test="creator != null and creator != ''">
            AND mo.mis_id = #{creator}
        </if>
        <if test="startTime != null and startTime != ''">
            AND create_ver.create_time &gt;= #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            AND create_ver.create_time &lt;= #{endTime}
        </if>
        <if test="strMatch != null and strMatch != ''">
            AND (mo.content LIKE CONCAT('%', #{strMatch}, '%') OR mo.title LIKE CONCAT('%', #{strMatch}, '%') OR
            mo.ticket_id = #{strMatch} OR CAST(mo.content_id AS CHAR) = #{strMatch})
        </if>
        <if test="tagsIds != null and tagsIds != ''">
            AND (
            <bind name="tagIdArray" value="tagsIds.split(',')"/>
            <foreach collection="tagIdArray" item="tagId" separator=" AND ">
                FIND_IN_SET(TRIM(#{tagId}), mo.tags_ids) > 0
            </foreach>
            )
        </if>
        ORDER BY mo.timestamp DESC
    </select>

    <!-- 根据ticketIds 批量删除   -->
    <insert id="deleteByTicketIds">
        INSERT INTO modified_output
        (ticket_id, title, content, version, mis_id, rg_id, timestamp, source, corpus_status, merged_to_id, type, content_id, background_knowledge_id, corpus_sop_id, rule_id, tags_ids)
        SELECT
        mo.ticket_id,
        mo.title,
        mo.content,
        mo.version + 1,
        #{misId},
        mo.rg_id,
        NOW(),
        mo.source,
        3,
        mo.merged_to_id,
        mo.type,
        mo.content_id,
        mo.background_knowledge_id,
        mo.corpus_sop_id,
        mo.rule_id,
        mo.tags_ids
        FROM modified_output mo
        INNER JOIN (
        SELECT
        ticket_id,
        rg_id,
        MAX(version) AS max_version
        FROM modified_output
        WHERE
        rg_id = #{rgId}
        AND ticket_id IN
        <foreach collection="ticketIds" item="ticketId" open="(" separator="," close=")">
            #{ticketId}
        </foreach>
        GROUP BY ticket_id, rg_id
        ) max_ver
        ON mo.ticket_id = max_ver.ticket_id
        AND mo.rg_id = max_ver.rg_id
        AND mo.version = max_ver.max_version
    </insert>

    <select id="findCorpusInfoByTTidRgids" resultType="com.meituan.banma.llm.corpus.server.dal.entity.KnowledgeBaseVersionEntity">
        SELECT
        mo.*
        FROM modified_output mo
        INNER JOIN (
        SELECT ticket_id, MAX(version) AS max_version
        FROM modified_output
        WHERE rg_id = #{rgId}
        AND ticket_id IN
        <foreach item='item' index='index' collection='ticketIds' open='(' separator=',' close=')'>
            #{item}
        </foreach>
        GROUP BY ticket_id
        ) t ON mo.ticket_id = t.ticket_id AND mo.version = t.max_version
        WHERE mo.rg_id = #{rgId}
        AND mo.corpus_status = 1
        GROUP BY mo.ticket_id
    </select>

    <!-- 根据mergedToIds列表批量查询指向这些语料的记录，取最新版本 -->
    <select id="findByMergedToIds" resultType="com.meituan.banma.llm.corpus.server.dal.entity.KnowledgeBaseVersionEntity">
        SELECT mo.* FROM modified_output mo
        INNER JOIN (
        SELECT ticket_id, MAX(version) as max_version
        FROM modified_output
        WHERE rg_id = #{rgId}
        GROUP BY ticket_id
        ) t ON mo.ticket_id = t.ticket_id AND mo.version = t.max_version
        WHERE mo.rg_id = #{rgId}
        AND mo.merged_to_id IN
        <foreach item='item' index='index' collection='mergedToIds' open='(' separator=',' close=')'>
            #{item}
        </foreach>
    </select>

    <!-- 根据ticketIdList查询创建时间timestamp   -->
    <select id="findCreateTimestampListByTicketId" resultType="java.sql.Timestamp">
        SELECT timestamp FROM modified_output mo
        INNER JOIN (
        SELECT ticket_id, MIN(version) AS min_version
        FROM modified_output
        WHERE rg_id = #{rgId}
        GROUP BY ticket_id
        ) min_ver ON mo.ticket_id = min_ver.ticket_id AND mo.version = min_ver.min_version
        WHERE mo.rg_id = #{rgId} AND mo.ticket_id IN
        <foreach collection="ticketIdList" item="ticketId" open="(" separator="," close=")">
            #{ticketId}
        </foreach>
        ORDER BY mo.ticket_id ASC
    </select>

    <!-- 根据ticketIds 批量查询创建时间，以及最早的数据   -->
    <select id="findCreateKnowledgeListByTicketId" resultType="com.meituan.banma.llm.corpus.server.dal.entity.KnowledgeBaseVersionEntity">
        SELECT * FROM modified_output mo
        INNER JOIN (
        SELECT ticket_id, MIN(version) AS min_version
        FROM modified_output
        WHERE rg_id = #{rgId}
        GROUP BY ticket_id
        ) min_ver ON mo.ticket_id = min_ver.ticket_id AND mo.version = min_ver.min_version
        WHERE mo.rg_id = #{rgId} AND mo.ticket_id IN
        <foreach collection="ticketIdList" item="ticketId" open="(" separator="," close=")">
            #{ticketId}
        </foreach>
        ORDER BY mo.ticket_id ASC
    </select>
    <!-- 根据值班组id和contentId 分页查询当前最新版本的知识记录 -->
    <select id="findLatestKnowledgeListByRgIdContentId"
            resultType="com.meituan.banma.llm.corpus.server.dal.entity.KnowledgeBaseVersionEntity">
        SELECT mo.*
        FROM modified_output mo
        INNER JOIN (
        SELECT ticket_id, MAX(version) AS max_version
        FROM modified_output
        WHERE rg_id = #{rgId}
        GROUP BY ticket_id
        ) max_ver ON mo.ticket_id = max_ver.ticket_id AND mo.version = max_ver.max_version
        WHERE mo.corpus_status = 1
        AND mo.content_id = #{contentId}
        <if test="strMatch != null and strMatch != ''">
            AND (mo.content LIKE CONCAT('%', #{strMatch}, '%') OR mo.title LIKE CONCAT('%', #{strMatch}, '%') OR
            mo.ticket_id = #{strMatch})
        </if>
        ORDER BY mo.timestamp DESC
        LIMIT #{limit} OFFSET #{offset}
    </select>
    
    <!-- 根据值班组id和contentId 查询当前值班组符合条件的知识记录数量 -->
    <select id="countCorpusByRgIdContentId"
            resultType="int">
        SELECT COUNT(*)
        FROM modified_output mo
        INNER JOIN (
        SELECT ticket_id, MAX(version) as max_version
        FROM modified_output
        WHERE rg_id = #{rgId}
        GROUP BY ticket_id
        ) max_ver ON mo.ticket_id = max_ver.ticket_id AND mo.version = max_ver.max_version
        WHERE mo.corpus_status = 1
        AND mo.content_id = #{contentId}
        <if test="strMatch != null and strMatch != ''">
            AND (mo.content LIKE CONCAT('%', #{strMatch}, '%') OR mo.title LIKE CONCAT('%', #{strMatch}, '%') OR
            mo.ticket_id = #{strMatch})
        </if>
    </select>

    <!-- 根据多条件分页查询当前最新版本的知识记录 -->
    <select id="findLatestKnowledgeListByCondition"
            resultType="com.meituan.banma.llm.corpus.server.dal.entity.KnowledgeBaseVersionEntity">
        SELECT mo.*
        FROM modified_output mo
        INNER JOIN (
            SELECT ticket_id, MAX(version) AS max_version
            FROM modified_output
            WHERE rg_id = #{rgId}
            GROUP BY ticket_id
        ) max_ver ON mo.ticket_id = max_ver.ticket_id AND mo.version = max_ver.max_version
        INNER JOIN (
            SELECT ticket_id, MIN(version) AS min_version, MIN(timestamp) AS create_time
            FROM modified_output
            WHERE rg_id = #{rgId}
            GROUP BY ticket_id
        ) create_ver ON mo.ticket_id = create_ver.ticket_id
        WHERE mo.corpus_status = 1
        <if test="ticketId != null and ticketId != ''">
            AND mo.ticket_id = #{ticketId}
        </if>
        <if test="title != null and title != ''">
            AND mo.title LIKE CONCAT('%', #{title}, '%')
        </if>
        <if test="content != null and content != ''">
            AND mo.content LIKE CONCAT('%', #{content}, '%')
        </if>
        <if test="source != null">
            AND mo.source = #{source}
        </if>
        <if test="creator != null and creator != ''">
            AND mo.mis_id = #{creator}
        </if>
        <if test="startTime != null and startTime != ''">
            AND create_ver.create_time &gt;= #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            AND create_ver.create_time &lt;= #{endTime}
        </if>
        <if test="strMatch != null and strMatch != ''">
            AND (mo.content LIKE CONCAT('%', #{strMatch}, '%') OR mo.title LIKE CONCAT('%', #{strMatch}, '%') OR
            mo.ticket_id = #{strMatch} OR CAST(mo.content_id AS CHAR) = #{strMatch})
        </if>
        <if test="tagsIds != null and tagsIds != ''">
            AND (
            <bind name="tagIdArray" value="tagsIds.split(',')"/>
            <foreach collection="tagIdArray" item="tagId" separator=" AND ">
                FIND_IN_SET(TRIM(#{tagId}), mo.tags_ids) > 0
            </foreach>
            )
        </if>
        ORDER BY mo.timestamp DESC
        LIMIT #{limit} OFFSET #{offset}
    </select>

    <!-- 统计多条件筛选后的总数 -->
    <select id="countCorpusByCondition" resultType="int">
        SELECT COUNT(*)
        FROM modified_output mo
        INNER JOIN (
            SELECT ticket_id, MAX(version) AS max_version
            FROM modified_output
            WHERE rg_id = #{rgId}
            GROUP BY ticket_id
        ) max_ver ON mo.ticket_id = max_ver.ticket_id AND mo.version = max_ver.max_version
        INNER JOIN (
            SELECT ticket_id, MIN(version) AS min_version, MIN(timestamp) AS create_time
            FROM modified_output
            WHERE rg_id = #{rgId}
            GROUP BY ticket_id
        ) create_ver ON mo.ticket_id = create_ver.ticket_id
        WHERE mo.corpus_status = 1
        <if test="ticketId != null and ticketId != ''">
            AND mo.ticket_id = #{ticketId}
        </if>
        <if test="title != null and title != ''">
            AND mo.title LIKE CONCAT('%', #{title}, '%')
        </if>
        <if test="content != null and content != ''">
            AND mo.content LIKE CONCAT('%', #{content}, '%')
        </if>
        <if test="source != null">
            AND mo.source = #{source}
        </if>
        <if test="creator != null and creator != ''">
            AND mo.mis_id = #{creator}
        </if>
        <if test="startTime != null and startTime != ''">
            AND create_ver.create_time &gt;= #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            AND create_ver.create_time &lt;= #{endTime}
        </if>
        <if test="strMatch != null and strMatch != ''">
            AND (mo.content LIKE CONCAT('%', #{strMatch}, '%') OR mo.title LIKE CONCAT('%', #{strMatch}, '%') OR
            mo.ticket_id = #{strMatch} OR CAST(mo.content_id AS CHAR) = #{strMatch})
        </if>
        <if test="tagsIds != null and tagsIds != ''">
            AND (
            <bind name="tagIdArray" value="tagsIds.split(',')"/>
            <foreach collection="tagIdArray" item="tagId" separator=" AND ">
                FIND_IN_SET(TRIM(#{tagId}), mo.tags_ids) > 0
            </foreach>
            )
        </if>
    </select>

</mapper>
