<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meituan.banma.llm.corpus.server.dal.mapper.CorpusBotChatMessageMapper">
    <resultMap id="BaseResultMap" type="com.meituan.banma.llm.corpus.server.dal.entity.CorpusBotChatMessageEntity">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="msg_id" property="msgId" jdbcType="VARCHAR"/>
        <result column="from_uid" property="fromUid" jdbcType="VARCHAR"/>
        <result column="from_pub_id" property="fromPubId" jdbcType="VARCHAR"/>
        <result column="gid" property="gid" jdbcType="VARCHAR"/>
        <result column="cts" property="cts" jdbcType="BIGINT"/>
        <result column="type" property="type" jdbcType="INTEGER"/>
        <result column="message" property="message" jdbcType="VARCHAR"/>
        <result column="from_name" property="fromName" jdbcType="VARCHAR"/>
        <result column="msg_ext" property="msgExt" jdbcType="VARCHAR"/>
        <result column="user_org_id" property="userOrgId" jdbcType="VARCHAR"/>
        <result column="user_org_name" property="userOrgName" jdbcType="VARCHAR"/>
        <result column="user_org_path" property="userOrgPath" jdbcType="VARCHAR"/>
        <result column="from_mis" property="fromMis" jdbcType="VARCHAR"/>
        <result column="ctime" property="ctime" jdbcType="TIMESTAMP"/>
        <result column="utime" property="utime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, msg_id, from_uid, from_pub_id, gid, cts, type, message, from_name, msg_ext,
        user_org_id, user_org_name, user_org_path, from_mis, ctime, utime
    </sql>

    <insert id="insert" parameterType="com.meituan.banma.llm.corpus.server.dal.entity.CorpusBotChatMessageEntity"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO corpus_bot_chat_message (
            msg_id, from_uid, from_pub_id, gid, cts, type, message, from_name, msg_ext,
            user_org_id, user_org_name, user_org_path, from_mis
        )
        VALUES (
            #{msgId}, #{fromUid}, #{fromPubId}, #{gid}, #{cts}, #{type}, #{message}, #{fromName}, #{msgExt},
            #{userOrgId}, #{userOrgName}, #{userOrgPath}, #{fromMis}
        )
    </insert>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO corpus_bot_chat_message (
            msg_id, from_uid, from_pub_id, gid, cts, type, message, from_name, msg_ext,
            user_org_id, user_org_name, user_org_path, from_mis
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.msgId}, #{item.fromUid}, #{item.fromPubId}, #{item.gid}, #{item.cts}, #{item.type},
            #{item.message}, #{item.fromName}, #{item.msgExt}, #{item.userOrgId}, #{item.userOrgName},
            #{item.userOrgPath}, #{item.fromMis}
            )
        </foreach>
    </insert>

    <update id="updateById" parameterType="com.meituan.banma.llm.corpus.server.dal.entity.CorpusBotChatMessageEntity">
        UPDATE corpus_bot_chat_message
        <set>
            <if test="msgId != null">msg_id = #{msgId},</if>
            <if test="fromUid != null">from_uid = #{fromUid},</if>
            <if test="fromPubId != null">from_pub_id = #{fromPubId},</if>
            <if test="gid != null">gid = #{gid},</if>
            <if test="cts != 0">cts = #{cts},</if>
            <if test="type != 0">type = #{type},</if>
            <if test="message != null">message = #{message},</if>
            <if test="fromName != null">from_name = #{fromName},</if>
            <if test="msgExt != null">msg_ext = #{msgExt},</if>
            <if test="userOrgId != null">user_org_id = #{userOrgId},</if>
            <if test="userOrgName != null">user_org_name = #{userOrgName},</if>
            <if test="userOrgPath != null">user_org_path = #{userOrgPath},</if>
            <if test="fromMis != null">from_mis = #{fromMis},</if>
        </set>
        WHERE id = #{id}
    </update>

    <delete id="deleteById">
        DELETE FROM corpus_bot_chat_message
        WHERE id = #{id}
    </delete>

    <delete id="deleteByMsgId">
        DELETE FROM corpus_bot_chat_message
        WHERE msg_id = #{msgId}
    </delete>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM corpus_bot_chat_message
        WHERE id = #{id}
    </select>

    <select id="selectByMsgId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM corpus_bot_chat_message
        WHERE msg_id = #{msgId}
    </select>

    <select id="selectByGid" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM corpus_bot_chat_message
        WHERE gid = #{gid}
        ORDER BY cts ASC
    </select>

    <select id="selectByFromUid" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM corpus_bot_chat_message
        WHERE from_uid = #{fromUid}
        ORDER BY cts ASC
    </select>

    <select id="selectByGidAndTimeRange" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM corpus_bot_chat_message
        WHERE gid = #{gid}
        <if test="startTime != null">
            AND cts >= #{startTime}
        </if>
        <if test="endTime != null">
            AND cts &lt;= #{endTime}
        </if>
        ORDER BY cts ASC
    </select>

    <select id="selectByTimeRange" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM corpus_bot_chat_message
        WHERE 1=1
        <if test="startTime != null">
            AND cts >= #{startTime}
        </if>
        <if test="endTime != null">
            AND cts &lt;= #{endTime}
        </if>
        ORDER BY cts ASC
    </select>
</mapper> 