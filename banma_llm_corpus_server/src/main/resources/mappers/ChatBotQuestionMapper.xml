<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meituan.banma.llm.corpus.server.dal.mapper.ChatBotQuestionMapper">
    <insert id="insert" parameterType="com.meituan.banma.llm.corpus.server.dal.entity.ChatBotQuestionEntity">
        INSERT INTO chat_bot_question (question_id, question, mis_id, bot_name, retrieval_total_count,
                                       retrieval_generated_count, ctime, utime)
        VALUES (#{questionId}, #{question}, #{misId}, #{botName}, #{retrievalTotalCount}, #{retrievalGeneratedCount},
                #{ctime}, #{utime})
    </insert>

    <select id="selectByQuestionId" resultType="com.meituan.banma.llm.corpus.server.dal.entity.ChatBotQuestionEntity">
        SELECT id,
               question_id,
               question,
               mis_id,
               bot_name,
               retrieval_total_count,
               retrieval_generated_count,
               ctime,
               utime
        FROM chat_bot_question
        WHERE question_id = #{questionId}
    </select>

    <select id="selectByMisId" resultType="com.meituan.banma.llm.corpus.server.dal.entity.ChatBotQuestionEntity">
        SELECT id, question_id, question, mis_id, bot_name, retrieval_total_count, retrieval_generated_count, ctime,
        utime
        FROM chat_bot_question
        <where>
            <if test="query.misId != null and query.misId != ''">
                mis_id = #{query.misId}
            </if>
            <if test="query.startTime != null">
                AND ctime >= #{query.startTime}
            </if>
            <if test="query.endTime != null">
                AND ctime &lt;= #{query.endTime}
            </if>
        </where>
        ORDER BY ctime DESC
        <if test="query.offset != null and query.limit != null">
            LIMIT #{query.offset}, #{query.limit}
        </if>
    </select>

    <update id="updateByQuestionId"
            parameterType="com.meituan.banma.llm.corpus.server.dal.entity.ChatBotQuestionEntity">
        UPDATE chat_bot_question
        SET question                  = #{question},
            mis_id                    = #{misId},
            bot_name                  = #{botName},
            retrieval_total_count     = #{retrievalTotalCount},
            retrieval_generated_count = #{retrievalGeneratedCount},
            utime                     = #{utime}
        WHERE question_id = #{questionId}
    </update>
</mapper>
