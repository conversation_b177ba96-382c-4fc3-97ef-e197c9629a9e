<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meituan.banma.llm.corpus.server.dal.mapper.QuestionTypeMapper">
  <resultMap id="BaseResultMap" type="com.meituan.banma.llm.corpus.server.dal.entity.QuestionTypeEntity">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="question_type_id" jdbcType="INTEGER" property="questionTypeId" />
    <result column="monitoring_group_id" jdbcType="BIGINT" property="monitoringGroupId" />
    <result column="sort_order" jdbcType="INTEGER" property="sortOrder" />
    <result column="type_name" jdbcType="VARCHAR" property="typeName" />
    <result column="type_desc" jdbcType="VARCHAR" property="typeDesc" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  
  <sql id="Base_Column_List">
    id, question_type_id, monitoring_group_id, sort_order, type_name, type_desc, create_time, update_time
  </sql>
  
  <insert id="insert" parameterType="com.meituan.banma.llm.corpus.server.dal.entity.QuestionTypeEntity" useGeneratedKeys="true" keyProperty="id">
    insert into question_type (
      question_type_id, monitoring_group_id, sort_order,
      type_name, type_desc
    )
    values (
      #{questionTypeId,jdbcType=INTEGER},
      #{monitoringGroupId,jdbcType=BIGINT},
      #{sortOrder,jdbcType=INTEGER},
      #{typeName,jdbcType=VARCHAR},
      #{typeDesc,jdbcType=VARCHAR}
    )
  </insert>
  
  <insert id="batchInsert" parameterType="java.util.List">
    insert into question_type (
      question_type_id, monitoring_group_id, sort_order,
      type_name, type_desc
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
        #{item.questionTypeId,jdbcType=INTEGER},
        #{item.monitoringGroupId,jdbcType=BIGINT},
        #{item.sortOrder,jdbcType=INTEGER},
        #{item.typeName,jdbcType=VARCHAR},
        #{item.typeDesc,jdbcType=VARCHAR}
      )
    </foreach>
  </insert>
  
  <update id="updateById" parameterType="com.meituan.banma.llm.corpus.server.dal.entity.QuestionTypeEntity">
    update question_type
    <set>
      <if test="questionTypeId != null">
        question_type_id = #{questionTypeId,jdbcType=INTEGER},
      </if>
      <if test="monitoringGroupId != null">
        monitoring_group_id = #{monitoringGroupId,jdbcType=BIGINT},
      </if>
      <if test="sortOrder != null">
        sort_order = #{sortOrder,jdbcType=INTEGER},
      </if>
      <if test="typeName != null">
        type_name = #{typeName,jdbcType=VARCHAR},
      </if>
      <if test="typeDesc != null">
        type_desc = #{typeDesc,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  
  <delete id="deleteById" parameterType="java.lang.Long">
    delete from question_type
    where id = #{id,jdbcType=BIGINT}
  </delete>
  
  <delete id="deleteByMonitoringGroupId" parameterType="java.lang.Long">
    delete from question_type
    where monitoring_group_id = #{monitoringGroupId,jdbcType=BIGINT}
  </delete>
  
  <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from question_type
    where id = #{id,jdbcType=BIGINT}
  </select>
  
  <select id="selectByMonitoringGroupId" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from question_type
    where monitoring_group_id = #{monitoringGroupId,jdbcType=BIGINT}
    order by sort_order asc
  </select>
  
  <select id="selectByMonitoringGroupIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from question_type
    where monitoring_group_id in
    <foreach collection="monitoringGroupIds" item="monitoringGroupId" open="(" separator="," close=")">
      #{monitoringGroupId,jdbcType=BIGINT}
    </foreach>
    order by monitoring_group_id, sort_order asc
  </select>
</mapper> 