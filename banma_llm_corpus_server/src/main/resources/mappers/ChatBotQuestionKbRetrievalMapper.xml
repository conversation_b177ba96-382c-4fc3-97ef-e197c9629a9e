<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meituan.banma.llm.corpus.server.dal.mapper.ChatBotQuestionKbRetrievalMapper">
    <insert id="insert" parameterType="com.meituan.banma.llm.corpus.server.dal.entity.ChatBotQuestionKbRetrievalEntity">
        INSERT INTO chat_bot_question_kb_retrieval (question_id, knowledge_base_slice_id, knowledge_base_name, retrieval_score, ctime, utime)
        VALUES (#{questionId}, #{knowledgeBaseSliceId}, #{knowledgeBaseName}, #{retrievalScore}, #{ctime}, #{utime})
    </insert>

    <select id="selectByQuestionId" resultType="com.meituan.banma.llm.corpus.server.dal.entity.ChatBotQuestionKbRetrievalEntity">
        SELECT id, question_id, knowledge_base_slice_id, knowledge_base_name, retrieval_score, ctime, utime
        FROM chat_bot_question_kb_retrieval
        WHERE question_id = #{questionId}
    </select>

    <delete id="deleteByQuestionId">
        DELETE FROM chat_bot_question_kb_retrieval WHERE question_id = #{questionId}
    </delete>

    <select id="selectByQuery" resultType="com.meituan.banma.llm.corpus.server.dal.entity.ChatBotQuestionKbRetrievalEntity">
        SELECT id, question_id, knowledge_base_slice_id, knowledge_base_name, retrieval_score, ctime, utime
        FROM chat_bot_question_kb_retrieval
        <where>
            <if test="query.questionId != null and query.questionId != ''">
                question_id = #{query.questionId}
            </if>
            <if test="query.knowledgeBaseSliceId != null and query.knowledgeBaseSliceId != ''">
                AND knowledge_base_slice_id = #{query.knowledgeBaseSliceId}
            </if>
            <if test="query.startTime != null">
                AND ctime >= #{query.startTime}
            </if>
            <if test="query.endTime != null">
                AND ctime &lt;= #{query.endTime}
            </if>
        </where>
        ORDER BY retrieval_score DESC, ctime DESC
        <if test="query.offset != null and query.limit != null">
            LIMIT #{query.offset}, #{query.limit}
        </if>
    </select>

    <!-- 添加批量插入的 SQL -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO chat_bot_question_kb_retrieval
        (question_id, knowledge_base_slice_id, knowledge_base_name, retrieval_score, ctime, utime)
        VALUES
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.questionId}, #{entity.knowledgeBaseSliceId}, #{entity.knowledgeBaseName},
             #{entity.retrievalScore}, #{entity.ctime}, #{entity.utime})
        </foreach>
    </insert>
</mapper>

