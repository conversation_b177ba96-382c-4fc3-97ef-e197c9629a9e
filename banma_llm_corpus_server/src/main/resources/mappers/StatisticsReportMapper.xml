<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.meituan.banma.llm.corpus.server.dal.mapper.StatisticsReportMapper">
    <resultMap id="BaseResultMap" type="com.meituan.banma.llm.corpus.server.dal.entity.StatisticsReportEntity">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="app_id" property="appId" jdbcType="VARCHAR"/>
        <result column="start_time" property="startTime" jdbcType="BIGINT"/>
        <result column="end_time" property="endTime" jdbcType="BIGINT"/>
        <result column="report_data" property="reportData" jdbcType="LONGVARCHAR"/>
        <result column="ctime" property="ctime" jdbcType="TIMESTAMP"/>
        <result column="utime" property="utime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, app_id, start_time, end_time, report_data, ctime, utime
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO statistics_report (app_id, start_time, end_time, report_data)
        VALUES (#{appId,jdbcType=VARCHAR}, #{startTime,jdbcType=BIGINT}, 
                #{endTime,jdbcType=BIGINT}, #{reportData,jdbcType=LONGVARCHAR})
    </insert>

    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO statistics_report (app_id, start_time, end_time, report_data)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.appId,jdbcType=VARCHAR}, #{item.startTime,jdbcType=BIGINT},
             #{item.endTime,jdbcType=BIGINT}, #{item.reportData,jdbcType=LONGVARCHAR})
        </foreach>
    </insert>

    <select id="selectByAppIdAndTimeRange" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM statistics_report
        WHERE app_id = #{appId,jdbcType=VARCHAR}
          AND start_time = #{startTime,jdbcType=BIGINT}
          AND end_time = #{endTime,jdbcType=BIGINT}
        LIMIT 1
    </select>

    <update id="updateByAppIdAndTimeRange">
        UPDATE statistics_report
        SET report_data = #{reportData,jdbcType=LONGVARCHAR}
        WHERE app_id = #{appId,jdbcType=VARCHAR}
          AND start_time = #{startTime,jdbcType=BIGINT}
          AND end_time = #{endTime,jdbcType=BIGINT}
    </update>

    <delete id="deleteByAppId">
        DELETE FROM statistics_report
        WHERE app_id = #{appId,jdbcType=VARCHAR}
    </delete>
</mapper> 