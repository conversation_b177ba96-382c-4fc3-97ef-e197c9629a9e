<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meituan.banma.llm.corpus.server.dal.mapper.RgDocumentUrlMapper">


    <!-- 插入实体对象 -->
    <insert id="insertEntity" parameterType="com.meituan.banma.llm.corpus.server.dal.entity.RgDocumentUrlEntity">
        INSERT INTO rg_document_url (rg_id, space_id, document_id, name, url, mis_id, auto_update, status
        <if test="createTime != null">
            , create_time
        </if>
        )
        SELECT #{rgId}, #{spaceId}, #{documentId}, #{name}, #{url}, #{misId}, #{autoUpdate}, 0
        <if test="createTime != null">
            , #{createTime}
        </if>
        WHERE NOT EXISTS (
        SELECT 1 FROM rg_document_url WHERE rg_id = #{rgId} AND space_id = #{spaceId} AND (name = #{name} OR url = #{url}) AND status = 0
        )
    </insert>
    
    <!-- 批量插入实体对象 -->
    <insert id="batchInsertEntities" parameterType="com.meituan.banma.llm.corpus.server.dal.entity.RgDocumentUrlEntity">
        INSERT INTO rg_document_url (rg_id, space_id, document_id, name, url, mis_id, auto_update, status
        <if test="entities[0].createTime != null">
            , create_time
        </if>
        )
        VALUES
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.rgId}, #{entity.spaceId}, #{entity.documentId}, #{entity.name}, #{entity.url}, #{entity.misId}, #{entity.autoUpdate}, 0
            <if test="entity.createTime != null">
                , #{entity.createTime}
            </if>
            )
        </foreach>
    </insert>
    
    <update id="updateByRgIdSpaceIdAndDocumentId">
        UPDATE rg_document_url
        SET auto_update = #{autoUpdate}
        WHERE rg_id = #{rgId} AND space_id = #{spaceId} AND document_id = #{documentId} AND status = 0
    </update>
    
    <!-- 根据rgId和spaceId查询记录 -->
    <select id="findByRgIdAndSpaceId" resultType="com.meituan.banma.llm.corpus.server.dal.entity.RgDocumentUrlEntity">
        SELECT *
        FROM rg_document_url
        WHERE rg_id = #{rgId} AND space_id = #{spaceId} AND status = 0
        ORDER BY create_time DESC
    </select>
    
    <!-- 根据rgId、spaceId和URL查询记录 -->
    <select id="findByRgIdSpaceIdAndUrl" resultType="com.meituan.banma.llm.corpus.server.dal.entity.RgDocumentUrlEntity">
        SELECT *
        FROM rg_document_url
        WHERE rg_id = #{rgId} AND space_id = #{spaceId} AND url = #{url} AND status = 0
        LIMIT 1
    </select>
    
    <!-- 根据rgId、spaceId和URL查询记录，包括已删除的记录 -->
    <select id="findByRgIdSpaceIdAndUrlIncludeDeleted" resultType="com.meituan.banma.llm.corpus.server.dal.entity.RgDocumentUrlEntity">
        SELECT *
        FROM rg_document_url
        WHERE rg_id = #{rgId} AND space_id = #{spaceId} AND url = #{url}
        ORDER BY status ASC, create_time DESC
        LIMIT 1
    </select>
    
    <!-- url and name 只根据rgId (去重) -->
    <select id="findDistinctByUrlNameAndRgId" resultType="int">
        SELECT CASE 
               WHEN (
                   -- 获取该rgId下所有工作空间数量（从rg_dataset_document表）
                   (SELECT COUNT(DISTINCT space_id) FROM rg_dataset_document WHERE rg_id = #{rgId} AND status = 0) =
                   -- 获取同时在rg_dataset_document和包含url或name的rg_document_url表中的工作空间数量
                   (SELECT COUNT(DISTINCT space.space_id)
                    FROM rg_dataset_document space
                    WHERE space.rg_id = #{rgId} AND space.status = 0
                    AND EXISTS (
                        SELECT 1
                        FROM rg_document_url doc
                        WHERE doc.rg_id = #{rgId}
                        AND doc.space_id = space.space_id
                        AND (doc.url = #{url} OR doc.name = #{name})
                        AND doc.status = 0
                    ))
                   -- 如果两者相等且大于0，说明所有工作空间都包含该URL或name
                   AND (SELECT COUNT(DISTINCT space_id) FROM rg_dataset_document WHERE rg_id = #{rgId} AND status = 0) > 0
               ) THEN 1
               ELSE 0
           END
    </select>

    <!-- url and name 根据rgId和spaceId (去重) -->
    <select id="findDistinctByUrlNameRgIdAndSpaceId" resultType="int">
        SELECT COUNT(DISTINCT url, name)
        FROM rg_document_url
        WHERE rg_id = #{rgId} AND space_id = #{spaceId} AND (url = #{url} OR name = #{name}) AND status = 0
    </select>

    <!-- 虚拟删除记录（更新status=1） -->
    <update id="deleteByRgIdSpaceIdAndDocumentId">
        UPDATE rg_document_url
        SET status = 1
        WHERE rg_id = #{rgId} AND space_id = #{spaceId} AND document_id = #{documentId} AND status = 0
    </update>

    <!-- 批量虚拟删除文档记录，包含工作空间ID -->
    <update id="batchDeleteByRgIdSpaceIdAndDocumentIds">
        UPDATE rg_document_url
        SET status = 1
        WHERE rg_id = #{rgId} AND space_id = #{spaceId} AND document_id IN
        <foreach collection="documentIds" item="documentId" open="(" separator="," close=")">
            #{documentId}
        </foreach>
        AND status = 0
    </update>

    <!-- 检查记录是否存在，包含工作空间ID -->
    <select id="countByRgIdSpaceIdAndDocumentId" resultType="int">
        SELECT COUNT(*)
        FROM rg_document_url
        WHERE rg_id = #{rgId} AND space_id = #{spaceId} AND document_id = #{documentId} AND status = 0
    </select>

    <!-- 根据rgId、spaceId和documentId查询记录 -->
    <select id="findByRgIdSpaceIdAndDocumentId" resultType="com.meituan.banma.llm.corpus.server.dal.entity.RgDocumentUrlEntity">
        SELECT *
        FROM rg_document_url
        WHERE rg_id = #{rgId} AND space_id = #{spaceId} AND document_id = #{documentId} AND status = 0
        LIMIT 1
    </select>

    <!-- 计算符合条件的记录总数，只根据rgId并去重 -->
    <select id="countDistinctByRgId" resultType="int">
        SELECT COUNT(DISTINCT name, url)
        FROM rg_document_url
        WHERE rg_id = #{rgId} AND status = 0
        <if test="strMatch != null and strMatch != ''">
            AND (name LIKE CONCAT('%', #{strMatch}, '%') OR url LIKE CONCAT('%', #{strMatch}, '%'))
        </if>
    </select>

    <!-- 根据rgId查询去重后的文档URL列表 -->
    <select id="findDistinctByRgId" resultType="com.meituan.banma.llm.corpus.server.dal.entity.RgDocumentUrlEntity">
        SELECT t.*
        FROM rg_document_url t
        INNER JOIN (
            SELECT name, url, MAX(create_time) as latest_time
            FROM rg_document_url
            WHERE rg_id = #{rgId} AND status = 0
            <if test="strMatch != null and strMatch != ''">
                AND (name LIKE CONCAT('%', #{strMatch}, '%') OR url LIKE CONCAT('%', #{strMatch}, '%'))
            </if>
            GROUP BY name, url
        ) latest ON t.name = latest.name AND t.url = latest.url AND t.create_time = latest.latest_time
        WHERE t.rg_id = #{rgId} AND t.status = 0
        ORDER BY t.create_time DESC
        <if test="limit > 0">
            LIMIT #{limit} OFFSET #{offset}
        </if>
    </select>

    <!-- 根据name和rgId查询记录（去重） -->
    <select id="findDistinctByNameAndRgId" resultType="int">
        SELECT CASE
               WHEN (
                   -- 获取该rgId下所有工作空间数量（从rg_dataset_document表）
                   (SELECT COUNT(DISTINCT space_id) FROM rg_dataset_document WHERE rg_id = #{rgId} AND status = 0) =
                   -- 获取同时在rg_dataset_document和包含name的rg_document_url表中的工作空间数量
                   (SELECT COUNT(DISTINCT space.space_id)
                    FROM rg_dataset_document space
                    WHERE space.rg_id = #{rgId} AND space.status = 0
                    AND EXISTS (
                        SELECT 1
                        FROM rg_document_url doc
                        WHERE doc.rg_id = #{rgId}
                        AND doc.space_id = space.space_id
                        AND doc.name = #{name}
                        AND doc.status = 0
                    ))
                   -- 如果两者相等且大于0，说明所有工作空间都包含该name
                   AND (SELECT COUNT(DISTINCT space_id) FROM rg_dataset_document WHERE rg_id = #{rgId} AND status = 0) > 0
               ) THEN 1
               ELSE 0
           END
    </select>

    <!-- 根据url和rgId查询记录（去重） -->
    <select id="findDistinctByUrlAndRgId" resultType="int">
        SELECT CASE
               WHEN (
                   -- 获取该rgId下所有工作空间数量（从rg_dataset_document表）
                   (SELECT COUNT(DISTINCT space_id) FROM rg_dataset_document WHERE rg_id = #{rgId} AND status = 0) =
                   -- 获取同时在rg_dataset_document和包含url的rg_document_url表中的工作空间数量
                   (SELECT COUNT(DISTINCT space.space_id)
                    FROM rg_dataset_document space
                    WHERE space.rg_id = #{rgId} AND space.status = 0
                    AND EXISTS (
                        SELECT 1
                        FROM rg_document_url doc
                        WHERE doc.rg_id = #{rgId}
                        AND doc.space_id = space.space_id
                        AND doc.url = #{url}
                        AND doc.status = 0
                    ))
                   -- 如果两者相等且大于0，说明所有工作空间都包含该URL
                   AND (SELECT COUNT(DISTINCT space_id) FROM rg_dataset_document WHERE rg_id = #{rgId} AND status = 0) > 0
               ) THEN 1
               ELSE 0
           END
    </select>
    
    <!-- 根据rgId和spaceId批量虚拟删除文档记录 -->
    <update id="batchDeleteByRgIdAndSpaceId">
        UPDATE rg_document_url
        SET status = 1
        WHERE rg_id = #{rgId} AND space_id = #{spaceId} AND status = 0
    </update>

    <!-- 更新整个实体记录 -->
    <update id="updateEntity" parameterType="com.meituan.banma.llm.corpus.server.dal.entity.RgDocumentUrlEntity">
        UPDATE rg_document_url
        SET 
            document_id = #{documentId},
            name = #{name},
            auto_update = #{autoUpdate}
        WHERE 
            rg_id = #{rgId} 
            AND space_id = #{spaceId} 
            AND url = #{url}
            AND status = 0
    </update>

    <!-- 恢复已删除的实体记录并更新相关字段 -->
    <update id="restoreEntity" parameterType="com.meituan.banma.llm.corpus.server.dal.entity.RgDocumentUrlEntity">
        UPDATE rg_document_url
        SET 
            status = 0,
            document_id = #{documentId},
            name = #{name},
            auto_update = #{autoUpdate},
            mis_id = #{misId}
        WHERE 
            rg_id = #{rgId} 
            AND space_id = #{spaceId} 
            AND url = #{url}
            AND status = 1
    </update>

    <!-- 查询rgId和spaceId下所有已删除的文档记录 -->
    <select id="findDeletedByRgIdAndSpaceId" resultType="com.meituan.banma.llm.corpus.server.dal.entity.RgDocumentUrlEntity">
        SELECT *
        FROM rg_document_url
        WHERE rg_id = #{rgId} AND space_id = #{spaceId} AND status = 1
        ORDER BY create_time DESC
    </select>
</mapper>
