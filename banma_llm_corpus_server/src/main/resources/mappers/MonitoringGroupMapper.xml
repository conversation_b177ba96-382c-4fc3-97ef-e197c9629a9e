<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meituan.banma.llm.corpus.server.dal.mapper.MonitoringGroupMapper">
  <resultMap id="BaseResultMap" type="com.meituan.banma.llm.corpus.server.dal.entity.MonitoringGroupEntity">
    <id column="monitoring_group_id" jdbcType="BIGINT" property="monitoringGroupId" />
    <result column="monitoring_group_name" jdbcType="VARCHAR" property="monitoringGroupName" />
    <result column="monitoring_group_desc" jdbcType="VARCHAR" property="monitoringGroupDesc" />
    <result column="monitoring_group_owner" jdbcType="VARCHAR" property="monitoringGroupOwner" />
    <result column="dx_group_ids" jdbcType="VARCHAR" property="dxGroupIds" />
    <result column="monitored_org_ids" jdbcType="VARCHAR" property="monitoredOrgIds" />
    <result column="monitored_mis_ids" jdbcType="VARCHAR" property="monitoredMisIds" />
    <result column="keywords" jdbcType="VARCHAR" property="keywords" />
    <result column="monitoring_time_range_type" jdbcType="INTEGER" property="monitoringTimeRangeType" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  
  <sql id="Base_Column_List">
    monitoring_group_id, monitoring_group_name, monitoring_group_desc, monitoring_group_owner, 
    dx_group_ids, monitored_org_ids, monitored_mis_ids, keywords, 
    monitoring_time_range_type, status, create_time, update_time
  </sql>
  
  <insert id="insert" parameterType="com.meituan.banma.llm.corpus.server.dal.entity.MonitoringGroupEntity" useGeneratedKeys="true" keyProperty="monitoringGroupId">
    insert into monitoring_group (
      monitoring_group_name, monitoring_group_desc, monitoring_group_owner, 
      dx_group_ids, monitored_org_ids, monitored_mis_ids, 
      keywords, monitoring_time_range_type, status
    )
    values (
      #{monitoringGroupName,jdbcType=VARCHAR}, 
      #{monitoringGroupDesc,jdbcType=VARCHAR}, 
      #{monitoringGroupOwner,jdbcType=VARCHAR}, 
      #{dxGroupIds,jdbcType=VARCHAR}, 
      #{monitoredOrgIds,jdbcType=VARCHAR}, 
      #{monitoredMisIds,jdbcType=VARCHAR}, 
      #{keywords,jdbcType=VARCHAR}, 
      #{monitoringTimeRangeType,jdbcType=INTEGER}, 
      #{status,jdbcType=TINYINT}
    )
  </insert>
  
  <delete id="deleteById" parameterType="java.lang.Long">
    delete from monitoring_group
    where monitoring_group_id = #{monitoringGroupId,jdbcType=BIGINT}
  </delete>
  
  <update id="updateById" parameterType="com.meituan.banma.llm.corpus.server.dal.entity.MonitoringGroupEntity">
    update monitoring_group
    <set>
      <if test="monitoringGroupName != null">
        monitoring_group_name = #{monitoringGroupName,jdbcType=VARCHAR},
      </if>
      <if test="monitoringGroupDesc != null">
        monitoring_group_desc = #{monitoringGroupDesc,jdbcType=VARCHAR},
      </if>
      <if test="monitoringGroupOwner != null">
        monitoring_group_owner = #{monitoringGroupOwner,jdbcType=VARCHAR},
      </if>
      <if test="dxGroupIds != null">
        dx_group_ids = #{dxGroupIds,jdbcType=VARCHAR},
      </if>
      monitored_org_ids = #{monitoredOrgIds,jdbcType=VARCHAR},
      monitored_mis_ids = #{monitoredMisIds,jdbcType=VARCHAR},
      <if test="keywords != null">
        keywords = #{keywords,jdbcType=VARCHAR},
      </if>
      <if test="monitoringTimeRangeType != null">
        monitoring_time_range_type = #{monitoringTimeRangeType,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
    </set>
    where monitoring_group_id = #{monitoringGroupId,jdbcType=BIGINT}
  </update>
  
  <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from monitoring_group
    where monitoring_group_id = #{monitoringGroupId,jdbcType=BIGINT}
  </select>
  
  <select id="selectAll" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from monitoring_group
    order by create_time desc
  </select>
  
  <select id="selectByNameLike" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from monitoring_group
    where monitoring_group_name like concat('%', #{name,jdbcType=VARCHAR}, '%')
    order by create_time desc
  </select>
  
  <select id="selectByStatus" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from monitoring_group
    where status = #{status,jdbcType=TINYINT}
    order by create_time desc
  </select>
  
  <update id="updateStatus">
    update monitoring_group
    set status = #{status,jdbcType=TINYINT}
    where monitoring_group_id = #{monitoringGroupId,jdbcType=BIGINT}
  </update>
  
  <insert id="batchInsert" parameterType="java.util.List">
    insert into monitoring_group (
      monitoring_group_name, monitoring_group_desc, monitoring_group_owner, 
      dx_group_ids, monitored_org_ids, monitored_mis_ids, 
      keywords, monitoring_time_range_type, status
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
        #{item.monitoringGroupName,jdbcType=VARCHAR}, 
        #{item.monitoringGroupDesc,jdbcType=VARCHAR}, 
        #{item.monitoringGroupOwner,jdbcType=VARCHAR}, 
        #{item.dxGroupIds,jdbcType=VARCHAR}, 
        #{item.monitoredOrgIds,jdbcType=VARCHAR}, 
        #{item.monitoredMisIds,jdbcType=VARCHAR}, 
        #{item.keywords,jdbcType=VARCHAR}, 
        #{item.monitoringTimeRangeType,jdbcType=INTEGER}, 
        #{item.status,jdbcType=TINYINT}
      )
    </foreach>
  </insert>
  
  <delete id="batchDeleteByIds" parameterType="java.util.List">
    delete from monitoring_group
    where monitoring_group_id in
    <foreach collection="list" item="monitoringGroupId" open="(" separator="," close=")">
      #{monitoringGroupId,jdbcType=BIGINT}
    </foreach>
  </delete>
  
  <select id="selectByKeyword" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from monitoring_group
    where keywords like concat('%', #{keyword,jdbcType=VARCHAR}, '%')
    order by create_time desc
  </select>
  
  <select id="selectByMisId" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from monitoring_group
    where monitoring_group_owner like concat('%', #{misId,jdbcType=VARCHAR}, '%')
       or monitored_mis_ids like concat('%', #{misId,jdbcType=VARCHAR}, '%')
    order by create_time desc
  </select>
</mapper> 