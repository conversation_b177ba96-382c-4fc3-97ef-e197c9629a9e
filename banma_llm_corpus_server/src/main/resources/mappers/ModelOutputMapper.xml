<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meituan.banma.llm.corpus.server.dal.mapper.ModelOutputMapper">
    <!-- update -->
    <update id="updateModelOutputTaskStatusByTaskId" parameterType="com.meituan.banma.llm.corpus.server.dal.entity.ModelOutputTaskEntity">
        update model_output_task
        set task_status = #{taskStatus},
            title = #{title},
            content = #{content},
            task_missing_info = #{taskMissingInfo},
            task_message = #{taskMessage},
            tags_ids = #{tagsIds}
        where task_id = #{taskId}
    </update>
    <!-- insert -->
    <insert id="insertModelOutputTask" parameterType="com.meituan.banma.llm.corpus.server.dal.entity.ModelOutputTaskEntity">
        INSERT INTO model_output_task (
            task_id,
            task_status,
            ticket_id,
            rg_id,
            title,
            content,
            creator_dx_id,
            dx_group_id,
            creator_mis_id,
            creator_user_name,
            platform_id,
            task_missing_info,
            task_message,
            tags_ids

        ) VALUES (
            #{taskId},
            #{taskStatus},
            #{ticketId},
            #{rgId},
            #{title},
            #{content},
            #{creatorDxId},
            #{dxGroupId},
            #{creatorMisId},
            #{creatorUserName},
            #{platformId},
            #{taskMissingInfo},
            #{taskMessage},
            #{tagsIds}
        )
    </insert>

    <select id="selectByTimeRange" resultType="com.meituan.banma.llm.corpus.server.dal.entity.ModelOutputTaskEntity">
        SELECT 
            id,
            task_id as taskId,
            task_status as taskStatus,
            ticket_id as ticketId,
            rg_id as rgId,
            title,
            content,
            creator_dx_id as creatorDxId,
            dx_group_id as dxGroupId,
            creator_mis_id as creatorMisId,
            creator_user_name as creatorUserName,
            platform_id as platformId,
            task_missing_info as taskMissingInfo,
            task_message as taskMessage,
            tags_ids as tagsIds,
            create_time as createTime,
            update_time as updateTime
        FROM model_output_task
        WHERE 1=1
        <if test="startTime != null">
            AND create_time >= FROM_UNIXTIME(#{startTime}/1000)
        </if>
        <if test="endTime != null">
            AND create_time &lt;= FROM_UNIXTIME(#{endTime}/1000)
        </if>
        ORDER BY create_time DESC
    </select>
    
    <select id="selectByTimeRangeExcludeMonitoring" resultType="com.meituan.banma.llm.corpus.server.dal.entity.ModelOutputTaskEntity">
        SELECT 
            id,
            task_id as taskId,
            task_status as taskStatus,
            ticket_id as ticketId,
            rg_id as rgId,
            title,
            content,
            creator_dx_id as creatorDxId,
            dx_group_id as dxGroupId,
            creator_mis_id as creatorMisId,
            creator_user_name as creatorUserName,
            platform_id as platformId,
            task_missing_info as taskMissingInfo,
            task_message as taskMessage,
            tags_ids as tagsIds,
            create_time as createTime,
            update_time as updateTime
        FROM model_output_task
        WHERE 1=1
        AND platform_id != 4 <!-- 排除平台类型为DX_MONITORING(4)的任务 -->
        <if test="startTime != null">
            AND create_time >= FROM_UNIXTIME(#{startTime}/1000)
        </if>
        <if test="endTime != null">
            AND create_time &lt;= FROM_UNIXTIME(#{endTime}/1000)
        </if>
        ORDER BY create_time DESC
    </select>

</mapper>