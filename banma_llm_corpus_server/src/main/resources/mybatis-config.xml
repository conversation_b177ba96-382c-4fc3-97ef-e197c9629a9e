<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE configuration
        PUBLIC "-//mybatis.org//DTD Config 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-config.dtd">
<configuration>
    <settings>
        <setting name="cacheEnabled" value="false"/>
        <setting name="lazyLoadingEnabled" value="true"/>
        <setting name="aggressiveLazyLoading" value="false"/>
        <setting name="multipleResultSetsEnabled" value="true"/>
        <setting name="useColumnLabel" value="true"/>
        <setting name="defaultExecutorType" value="SIMPLE"/>
        <setting name="defaultStatementTimeout" value="10"/>
        <!-- 下划线转驼峰，开启 -->
        <setting name="mapUnderscoreToCamelCase" value="true"/>
        <!-- 是否使用插入数据后自增主键的值，需要配合keyProperty使用 -->
        <setting name="useGeneratedKeys" value="true"/>
        <setting name="logImpl" value="LOG4J2"/>
    </settings>
</configuration>