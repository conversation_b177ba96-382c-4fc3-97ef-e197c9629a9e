# MDP DOCS=https://km.sankuai.com/custom/onecloud/page/1302115131
#todo :modify prod zebra config
#mdp.zebra.jdbcRef=extra_product
mdp.zebra.zebraKey=migrate_extra_product_banmalllmcorpus_lllm_corpus_product_6278
# 对于读写分离必须强制指定类型
#mdp.zebra.dataSourceType=group

# 包含Mapper接口类的package，如果存在多个，可用逗号隔开
mdp.zebra.basePackage=com.meituan.banma.llm.corpus.server.dal.mapper

# 如果sql写在xml中则需要该配置，如果sql写在接口注解中则不需要该配置，如果存在多个，可用逗号隔开
mdp.zebra.mapperLocations=classpath:mappers/*.xml

# 连接池初始化连接数，默认取自RDS平台，建议显式配置
mdp.zebra.initialPoolSize=3

# 连接池最小连接数，默认取自RDS平台，建议显式配置
mdp.zebra.minPoolSize=4

# 连接池最大连接数，默认取自RDS平台，建议显式配置
mdp.zebra.maxPoolSize=8

mdp.zebra.configLocation=classpath:mybatis-config.xml

mdp.zebra.isJdbcRefGroup=false
mdp.zebra.extraJdbcUrlParams=useSSL=false