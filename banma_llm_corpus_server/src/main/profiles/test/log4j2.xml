<?xml version="1.0" encoding="UTF-8"?>
<configuration status="info">
    <Properties>
        <Property name="PID">????</Property>
        <Property name="LOG_EXCEPTION_CONVERSION_WORD">%xwEx</Property>
        <Property name="LOG_LEVEL_PATTERN">%5p</Property>
        <Property name="LOG_DATEFORMAT_PATTERN">yyyy-MM-dd HH:mm:ss.SSS</Property>
        <Property name="CONSOLE_LOG_PATTERN">%clr{%d{${LOG_DATEFORMAT_PATTERN}}}{faint} %clr{${LOG_LEVEL_PATTERN}} %clr{${sys:PID}}{magenta} %clr{---}{faint} %clr{[%15.15t]}{faint} %clr{%-40.40c{1.}}{cyan} %clr{:}{faint} %m%n${sys:LOG_EXCEPTION_CONVERSION_WORD}</Property>
        <Property name="FILE_LOG_PATTERN">%d{${LOG_DATEFORMAT_PATTERN}} %XMDT ${LOG_LEVEL_PATTERN} ${sys:PID} --- [%t] %-40.40c{1.} : %m%n${sys:LOG_EXCEPTION_CONVERSION_WORD}</Property>
    </Properties>
    <appenders>
        <Console name="Console" target="SYSTEM_OUT" follow="true">
            <PatternLayout pattern="${sys:CONSOLE_LOG_PATTERN}" />
        </Console>

        <!--默认按天&按512M文件大小切分日志，默认最多保留30个日志文件，非阻塞模式-->
        <XMDFile name="infoAppender" fileName="info.log" sizeBasedTriggeringSize="512M"
                 rolloverMax="30">
            <PatternLayout pattern="${sys:FILE_LOG_PATTERN}" />
            <Filters>
                <ThresholdFilter level="warn" onMatch="DENY" onMismatch="NEUTRAL"/>
                <ThresholdFilter level="info" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
        </XMDFile>

        <XMDFile name="warnAppender" fileName="warn.log" sizeBasedTriggeringSize="512M"
                 rolloverMax="30">
            <PatternLayout pattern="${sys:FILE_LOG_PATTERN}" />
            <Filters>
                <ThresholdFilter level="error" onMatch="DENY" onMismatch="NEUTRAL"/>
                <ThresholdFilter level="warn" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
        </XMDFile>

        <XMDFile name="errorAppender" fileName="error.log" sizeBasedTriggeringSize="512M"
                 rolloverMax="30">
            <PatternLayout pattern="${sys:FILE_LOG_PATTERN}" />
            <ThresholdFilter level="error" onMatch="ACCEPT" onMismatch="DENY"/>
        </XMDFile>

        <!--日志远程上报-->
        <AsyncScribe name="ScribeAsyncAppender" blocking="false">
            <!--远程日志默认使用appkey作为日志名(app.properties文件中的app.name字段)，也可自定义scribeCategory属性，scribeCategory优先级高于appkey-->
            <LcLayout/>
        </AsyncScribe>
        <CatAppender name="catAppender"/>
        <MDPTraceAppender name="mdpTrace"/>

        <AsyncScribe name="AsyncScribeAppender_llm_corpus_process_server" blocking="false">
            <Property name="scribeCategory">llm_corpus_process_server</Property>
            <!-- 如果要开启丢失率检测，请放开下面代码注释 -->
            <!-- <Property name="checkLoss">true</Property> -->
            <LcLayout/>
        </AsyncScribe>
        <XMDFile name="file" xmdFilePath="/opt/logs/logs" addAppkeyToFilePath="false" rolloverMax="24"
                 includeLocation="true"
                 fileName="banma_llm_corpus_process_server.log" timeBasedTriggeringInterval="1" sizeBasedTriggeringSize="10GB">
            <PatternLayout>
                <Pattern>%d ${sys:app.host} ${sys:app.ip} appkey=com.sankuai.deliverypaotui.llm.corpus [%thread] %-5level [%c{1}.%M:%L] %XMDT %msg%n%ex</Pattern>
            </PatternLayout>
        </XMDFile>
    </appenders>

    <loggers>
        <!--远程日志，详细使用说明参见 MDP 文档中日志中心部分 https://km.sankuai.com/custom/onecloud/page/424836119#id-3%E6%97%A5%E5%BF%97%E4%B8%AD%E5%BF%83 -->
        <logger name="scribe" level="info" additivity="false">
            <appender-ref ref="ScribeAsyncAppender" />
        </logger>
        <logger name="logger_llm_corpus_process_server" level="info" additivity="false">
            <appender-ref ref="AsyncScribeAppender_llm_corpus_process_server"/>
        </logger>
        <root level="info">
            <appender-ref ref="file"/>
            <appender-ref ref="infoAppender"/>
            <appender-ref ref="warnAppender"/>
            <appender-ref ref="errorAppender"/>
            <appender-ref ref="Console" />
            <appender-ref ref="catAppender"/>
            <appender-ref ref="mdpTrace"/>
            <appender-ref ref="AsyncScribeAppender_llm_corpus_process_server"/>
        </root>
    </loggers>
</configuration>