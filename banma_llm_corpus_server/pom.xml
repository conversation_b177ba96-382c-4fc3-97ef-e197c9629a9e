<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.meituan.banma.llm.corpus</groupId>
        <artifactId>banma_llm_corpus</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>banma_llm_corpus_server</artifactId>
    <version>${revision}</version>
    <packaging>jar</packaging>
    <name>banma_llm_corpus_server</name>

    <dependencies>
        <!-- Project module -->
        <dependency>
            <groupId>com.meituan.banma.llm.corpus</groupId>
            <artifactId>banma_llm_corpus_client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-mafka</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-leaf</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-pigeon</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-squirrel</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-thrift</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-web-base</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-web-tob</artifactId>
        </dependency>

        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>mss-java-sdk-s3</artifactId>
        </dependency>

        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-zebra</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.dianping.zebra</groupId>
                    <artifactId>zebra-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.dianping.zebra</groupId>
                    <artifactId>zebra-ds-monitor-client</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.dianping.zebra</groupId>
                    <artifactId>zebra-dao</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>mysql</groupId>
                    <artifactId>mysql-connector-java</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping.zebra</groupId>
            <artifactId>zebra-dao</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.zebra</groupId>
            <artifactId>zebra-ds-monitor-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.zebra</groupId>
            <artifactId>zebra-api</artifactId>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>
        <!-- region MDP 脚手架生成代码统计埋点 内容为空，不引入其他依赖，请勿删除 -->
        <!-- MDP LA Initializer -->
        <dependency>
            <groupId>com.sankuai.mdp</groupId>
            <artifactId>mdp-boot-initializr-mdp</artifactId>
            <version>1.0.0</version>
        </dependency>
        <!-- endregion MDP 脚手架生成代码统计埋点 -->

        <!-- Mdp Boot Starter -->
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter</artifactId>
        </dependency>

        <!-- Mdp Boot Components -->
        <dependency>
            <groupId>com.meituan.mdp.component</groupId>
            <artifactId>swagger-analysis-core</artifactId>
            <scope>compile</scope>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.component</groupId>
            <artifactId>mdp-doc</artifactId>
            <optional>true</optional>
        </dependency>

        <!-- 学城接入 -->
        <dependency>
            <groupId>com.sankuai.ead</groupId>
            <artifactId>citadel-client</artifactId>
            <version>3.0.53</version>
        </dependency>

        <!-- 第三方依赖 -->
        <dependency>
            <groupId>org.jsoup</groupId>
            <artifactId>jsoup</artifactId>
            <version>1.16.1</version>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpcore</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.fastjson2</groupId>
            <artifactId>fastjson2</artifactId>
            <version>2.0.56</version>
        </dependency>
        <!-- 开放平台token鉴权和接口接入 -->
        <dependency>
            <groupId>com.sankuai.dxenterprise.open.gateway</groupId>
            <artifactId>open-sdk</artifactId>
            <version>1.0.45-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.lion</groupId>
            <artifactId>lion-client</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.thrift</groupId>
            <artifactId>libthrift</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.service.mobile</groupId>
            <artifactId>mtthrift</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.meituan.banma</groupId>
            <artifactId>qingniu_degrade</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>
        <!--        mokito-->
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <scope>test</scope>
        </dependency>
        <!--        junit-->
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.meituan.xm.mbox</groupId>
            <artifactId>mbox-thrift-shared</artifactId>
            <version>1.3.9</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.retry</groupId>
            <artifactId>spring-retry</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.image</groupId>
            <artifactId>client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.meituan.org</groupId>
            <artifactId>open-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-crane</artifactId>
        </dependency>

        <!--Hive-->
        <dependency>
            <groupId>com.meituan.talostwo</groupId>
            <artifactId>talostwo-sdk-java</artifactId>
            <version>2.2.2-RELEASE</version>
        </dependency>

        <!-- EasyExcel依赖，用于Excel导出 -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>3.3.2</version>
        </dependency>

        <dependency>
            <groupId>com.meituan.ai</groupId>
            <artifactId>friday-java-sdk</artifactId>
            <version>0.1.0</version>
        </dependency>

        <dependency>
            <groupId>javax</groupId>
            <artifactId>javaee-api</artifactId>
            <version>8.0.1</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.banma.jarvis</groupId>
            <artifactId>banma_jarvis_aiops-api</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.security</groupId>
            <artifactId>sec-sdk</artifactId>
            <version>1.3.15</version>
        </dependency>
    </dependencies>


    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.mybatis.generator</groupId>
                <artifactId>mybatis-generator-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>com.meituan.mdp.maven.plugins</groupId>
                <artifactId>mdp-mybatis-generator-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>com.meituan.mdp.maven.plugins</groupId>
                <artifactId>swagger-analysis-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>com.meituan.mdp.maven.plugins</groupId>
                <artifactId>mdp-doc-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>com.meituan.mdp.maven.plugins</groupId>
                <artifactId>mdp-graphql-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

</project>
