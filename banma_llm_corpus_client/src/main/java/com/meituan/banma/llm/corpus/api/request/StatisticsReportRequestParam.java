package com.meituan.banma.llm.corpus.api.request;

import com.facebook.swift.codec.ThriftConstructor;
import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

import java.util.List;

@TypeDoc(description = "数据统计上报入参", authors = {"daili07"})
@ThriftStruct
public class StatisticsReportRequestParam {
    @FieldDoc(description = "召回的文档切片列表", example = {"[{}]"})
    private List<DocRecallItemRequest> docRecallItems;
    @FieldDoc(description = "问题", example = {"1234"})
    private String question;
    @FieldDoc(description = "misId", example = {"1234"})
    private String misId;

    @FieldDoc(description = "机器人名称", example = {"1234"})
    private String botName;

    @ThriftField(1)
    public List<DocRecallItemRequest> getDocRecallItems() {
        return docRecallItems;
    }

    @ThriftField
    public void setDocRecallItems(List<DocRecallItemRequest> docRecallItems) {
        this.docRecallItems = docRecallItems;
    }

    @ThriftField(2)
    public String getQuestion() {
        return question;
    }

    @ThriftField
    public void setQuestion(String question) {
        this.question = question;
    }

    @ThriftField(3)
    public String getMisId() {
        return misId;
    }

    @ThriftField
    public void setMisId(String misId) {
        this.misId = misId;
    }

    @ThriftField(4)
    public String getBotName() {
        return botName;
    }

    @ThriftField
    public void setBotName(String botName) {
        this.botName = botName;
    }
}
