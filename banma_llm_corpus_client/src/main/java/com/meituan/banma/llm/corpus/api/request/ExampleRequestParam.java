package com.meituan.banma.llm.corpus.api.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

@TypeDoc(
        description = "根据多个ab实验key获取ab实验结果参数",
        authors = {"futao02"}
)
@ThriftStruct
public class ExampleRequestParam {
    @FieldDoc(
            description = "key1",
            example = {"1234"}
    )
    private String key1;
    @FieldDoc(
            description = "key2",
            example = {"1234"}
    )
    private String key2;

    @ThriftField(1)
    public String getKey1() {
        return key1;
    }
    @ThriftField
    public void setKey1(String key1) {
        this.key1 = key1;
    }
    @ThriftField(2)
    public String getKey2() {
        return key2;
    }
}
