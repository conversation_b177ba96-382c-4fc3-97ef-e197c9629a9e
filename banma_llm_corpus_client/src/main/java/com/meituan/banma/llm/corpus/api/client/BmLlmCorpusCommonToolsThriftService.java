package com.meituan.banma.llm.corpus.api.client;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.meituan.banma.llm.corpus.api.request.ImageToTextBatchRequest;
import com.meituan.banma.llm.corpus.api.request.StatisticsReportRequestParam;
import com.meituan.banma.llm.corpus.api.response.CommonResponse;
import com.meituan.banma.llm.corpus.api.response.ImageToTextResponse;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;

import java.util.List;
import java.util.Map;

@InterfaceDoc(
        type = "octo.thrift",
        displayName = "Banma LLM Corpus Process Common Thrift Service",
        description = "提供与Banma LLM语料库处理相关的通用的大模型工具接口",
        scenarios = "Banma LLM语料库处理",
        authors = {"daili07"}
)
@ThriftService
public interface BmLlmCorpusCommonToolsThriftService {
    @MethodDoc(
            displayName = "图片转文本",
            description = "图片转文本",
            parameters = {
                    @ParamDoc(name = "baseRequest", description = "通用请求对象,通过网关获取的公共参数，适用于一切接口", example = {"BaseRequest(A:1, B:2, C:3)"})
            },
            returnValueDescription = "示例方法"
    )
    @ThriftMethod
    ImageToTextResponse imageToText(String imageUrl, String prompt, String misId);

    @MethodDoc(
            displayName = "统计数据上报",
            description = "统计数据上报",
            parameters = {
                    @ParamDoc(name = "request", description = "统计数据上报请求参数", example = {"StatisticsReportRequestParam(A:1, B:2, C:3)"})
            },
            returnValueDescription = "统计数据上报返回"
    )
    @ThriftMethod
    CommonResponse<String> statisticsReport(StatisticsReportRequestParam request);

    @MethodDoc(
            displayName = "图片转文本批量接口",
            description = "图片转文本批量接口",
            parameters = {
                    @ParamDoc(name = "request", description = "图片转文本批量请求参数", example = {"ImageToTextBatchRequest(A:1, B:2, C:3)"})
            },
            returnValueDescription = "图片转文本批量返回"
    )
    CommonResponse<List<ImageToTextResponse>> imageToTextBatch(ImageToTextBatchRequest request);

    @MethodDoc(
            displayName = "查询最新SOP模板内容",
            description = "根据给定的rgId查询最新的SOP模板内容",
            parameters = {
                    @ParamDoc(name = "rgId", description = "语料库组ID", example = {"12345"})
            },
            returnValueDescription = "返回最新SOP模板内容的字符串"
    )
    @ThriftMethod
    CommonResponse<String> queryLatestSopByRgId(long rgId);

    @MethodDoc(
            displayName = "查询最新用户自定义背景知识内容",
            description = "根据给定的rgId查询最新的用户自定义背景知识内容",
            parameters = {
                    @ParamDoc(name = "rgId", description = "值班组ID", example = {"12345"})
            },
            returnValueDescription = "返回最新用户自定义背景知识内容的字符串"
    )
    @ThriftMethod
    CommonResponse<String> queryLatestBackgroundKnowledgeByRgId(long rgId);

    @MethodDoc(
            displayName = "查询最新用户自定义规则",
            description = "根据给定的rgId查询最新的用户自定义规则",
            parameters = {
                    @ParamDoc(name = "rgId", description = "值班组ID", example = {"12345"})
            },
            returnValueDescription = "返回最新用户自定义规则的字符串"
    )
    @ThriftMethod
    CommonResponse<String> queryLatestRuleByRgId(long rgId);

    @MethodDoc(
            displayName = "查询值班组标签",
            description = "根据给定的rgId查询该值班组下的标签和对应的描述",
            parameters = {
                    @ParamDoc(name = "rgId", description = "值班组ID", example = {"12345"})
            },
            returnValueDescription = "返回标签Map，key为标签名，value为标签描述"
    )
    @ThriftMethod
    CommonResponse<Map<String, String>> queryTagsByRgId(long rgId);
}
