package com.meituan.banma.llm.corpus.api.response;

import com.facebook.swift.codec.ThriftConstructor;
import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

/**
 * Class DxGroupChatResponse
 * Project banma_llm_corpus_process_server
 *
 * <AUTHOR>
 * @date 2025/5/8
 * Description 大象群聊记录
 */

@TypeDoc(
        description = "大象群聊记录"
)
@ThriftStruct
public class DxGroupChatResponse {
    @FieldDoc(
            description = "消息内容"
    )
    private String message;
    @FieldDoc(
            description = "发送人mis号"
    )
    private String fromMis;
    @FieldDoc(
            description = "发送人姓名"
    )
    private String fromName;

    @FieldDoc(
            description = "发送时间戳"
    )
    private long timestamp;

    @ThriftConstructor
    public DxGroupChatResponse(String message, String fromMis, String fromName, long timestamp) {
        this.message = message;
        this.fromMis = fromMis;
        this.fromName = fromName;
        this.timestamp = timestamp;
    }

    public DxGroupChatResponse() {
    }

    @ThriftField(1)
    public String getMessage() {
        return message;
    }

    @ThriftField
    public void setMessage(String message) {
        this.message = message;
    }

    @ThriftField(2)
    public String getFromMis() {
        return fromMis;
    }

    @ThriftField
    public void setFromMis(String fromMis) {
        this.fromMis = fromMis;
    }

    @ThriftField(3)
    public String getFromName() {
        return fromName;
    }

    @ThriftField
    public void setFromName(String fromName) {
        this.fromName = fromName;
    }

    @ThriftField(4)
    public long getTimestamp() {
        return timestamp;
    }

    @ThriftField
    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }
}
