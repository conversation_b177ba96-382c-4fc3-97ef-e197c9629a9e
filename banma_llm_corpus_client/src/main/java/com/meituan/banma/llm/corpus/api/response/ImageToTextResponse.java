package com.meituan.banma.llm.corpus.api.response;

import com.facebook.swift.codec.ThriftConstructor;
import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

@TypeDoc(
        description = "图片转文字返回值",
        authors = {"daili07"}
)
@ThriftStruct
public class ImageToTextResponse {
    @FieldDoc(
            description = "图片URL",
            example = "http://example.com/image.jpg"
    )
    private String imageUrl;
    @FieldDoc(
            description = "文字内容",
            example = "这是一个示例文字"
    )
    private String text;

    @ThriftConstructor
    public ImageToTextResponse(String imageUrl, String text) {
        this.imageUrl = imageUrl;
        this.text = text;
    }
    public ImageToTextResponse() {}

    @ThriftField(1)
    public String getImageUrl() {
        return imageUrl;
    }
    @ThriftField
    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }
    @ThriftField(2)
    public String getText() {
        return text;
    }
    @ThriftField
    public void setText(String text) {
        this.text = text;
    }
}
