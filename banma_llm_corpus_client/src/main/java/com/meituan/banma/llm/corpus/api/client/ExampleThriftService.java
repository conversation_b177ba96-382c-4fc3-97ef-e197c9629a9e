package com.meituan.banma.llm.corpus.api.client;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.meituan.banma.llm.corpus.api.exception.LlmCorpusException;
import com.meituan.banma.llm.corpus.api.request.ExampleRequestParam;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import org.apache.thrift.TException;

@InterfaceDoc(
        type = "octo.thrift",
        displayName = "示例thrift接口服务",
        description = "示例thrift接口服务",
        scenarios = "示例thrift接口服务",
        authors = {"daili07"}
)
@ThriftService
public interface ExampleThriftService {
    @MethodDoc(
            displayName = "示例方法",
            description = "示例方法",
            parameters = {
                    @ParamDoc(name = "baseRequest", description = "通用请求对象,通过网关获取的公共参数，适用于一切接口", example = {"BaseRequest(A:1, B:2, C:3)"})
            },
            returnValueDescription = "示例方法"
    )
    @ThriftMethod
    String exampleMethod(ExampleRequestParam exampleRequestParam) throws TException, LlmCorpusException;
}
