package com.meituan.banma.llm.corpus.api.client;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.meituan.banma.llm.corpus.api.exception.LlmCorpusException;
import com.meituan.banma.llm.corpus.api.response.CommonResponse;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;

import java.util.Map;

/**
 * <AUTHOR>
 */
@InterfaceDoc(
        type = "octo.thrift",
        displayName = "Banma Oms Workflow Thrift Service",
        description = "Banma Oms-工作流接口服务",
        scenarios = "Oms工作流调用",
        authors = {"liumingcheng"}
)
@ThriftService
public interface BmOmsWorkflowThriftService {

    @MethodDoc(
            displayName = "获取接入指引智能化配置",
            description = "获取接入指引智能化配置",
            parameters = {
                    @ParamDoc(name = "requirementAnalyseResult", description = "需求分析结果", example = {""})
            },
            returnValueDescription = "LLM根据需求分析结果生成的智能化配置"
    )
    @ThriftMethod
    CommonResponse<String> getAccessGuideIntelligenceConfiguration(String requirementAnalyseResult) throws LlmCorpusException ;

}
