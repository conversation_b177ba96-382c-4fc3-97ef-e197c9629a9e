package com.meituan.banma.llm.corpus.api.constants;


import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;

/**
 * <h3>llm_corpus_server</h3>
 * <p>错误码报警级别</p>
 *
 */
public enum BizCode {
    SUCCESS(0, BizCodeLevel.NONE_LEVEL, "成功"),
    // 公用异常code从10000开始
    LOGIN_FAIL(10000, BizCodeLevel.P3, "登录信息失效，请重新登录。"),
    ILLEGAL_ARGUMENT(10001, BizCodeLevel.P3, "参数错误"),
    ILLEGAL_REQUEST(10002, BizCodeLevel.P3, "信息异常"),
    SERVER_INTERNAL_ERROR(10003, BizCodeLevel.P3, "服务器开小差啦, 请重试"),
    FUNCTION_DEGRADE(10004, BizCodeLevel.P5, "系统繁忙，请稍候重试"),
    RPC_TIMEOUT(10005, BizCodeLevel.P3, "系统繁忙，请稍候重试"),
    RPC_BIZ_ERROR(10006, BizCodeLevel.P3, "接口调用失败"),
    FRIDAY_CONVERT_TT_CHAT_TO_CORPUS_ERROR(10007, BizCodeLevel.P2, "大模型返回结果出错"),
    FRIDAY_QUERY_SIMILARITY_ERROR(10008, BizCodeLevel.P2, "Friday查询相似度失败"),
    NO_PERMISSION(10009, BizCodeLevel.P3, "没有权限"),
    IMAGE_UPLOAD_FAILED(10010, BizCodeLevel.P3, "图片上传失败"),
    GET_IMAGE_FAILED(10011, BizCodeLevel.P3, "获取图片失败"),
    FRIDAY_IMAGE_TO_TEXT_ERROR(10012, BizCodeLevel.P3, "Friday图片转文本失败"),
    REPORT_STATISTICS_ERROR(10013, BizCodeLevel.P4, "上报统计信息失败"),

    TTID_EXTRACT_ERROR(10014, BizCodeLevel.P3, "提取 ttId 失败"),
    FRIDAY_QUERY_SIMILARITY_ERROR2(100015, BizCodeLevel.P2, "Friday查询相似度失败(新接口)"),
    MERGE_CORPUS_FAILED(100016, BizCodeLevel.P2, "合并语料失败"),
    SAVE_MERGE_CORPUS_FAILED(100017, BizCodeLevel.P2, "保存合并语料失败"),
    EMP_QUERY_ERROR(10018, BizCodeLevel.P3, "查询员工信息失败"),
    DX_GROUP_QUESTION_SUMMARY_ERROR(10019, BizCodeLevel.P3, "总结大象群问题失败"),
    FRIDAY_SUMMARIZE_QUESTION_ERROR(10020, BizCodeLevel.P3, "Friday总结问题失败"),
    FRIDAY_AUTO_REPLY_ERROR(10021, BizCodeLevel.P2, "Friday自动回复失败"),
    GET_NOTICE_DETAIL_ERROR(10022, BizCodeLevel.P3, "Friday自动回复失败"),
    FRIDAY_KM_TO_CORPUS_ERROR(10023, BizCodeLevel.P3, "学城QA转换语料失败"),
    PARAM_ERROR(10024, BizCodeLevel.P3, "学城文档解析错误"),
    DOCUMENT_DEL_ERROR(10025, BizCodeLevel.P3, "文档删除操作失败"),
    ADD_DOCUMENT_ERROR(10026, BizCodeLevel.P3, "文档添加操作失败"),
    FRIDAY_CONTENT_QUALITY_ASSESSMENT_ERROR(10027, BizCodeLevel.P3, "Friday内容质量评估失败"),
    AUTHOR_ERROR(10028, BizCodeLevel.P3, "该学城文档用户无权限"),
    META_DEAL_ERROR(10029, BizCodeLevel.P3, "学城文档基本信息获取错误"),
    GET_CONTENT_ERROR(10030, BizCodeLevel.P3, "学城文档内容获取错误"),
    FRIDAY_QUESTION_CLUSTERING_ERROR(10031, BizCodeLevel.P3, "Friday问题聚类失败"),
    QUERY_CONVERSATION_ERROR(10032, BizCodeLevel.P3, "查询会话记录失败"),
    DX_GROUP_OPERATION_ERROR(10033, BizCodeLevel.P3, "改版前创建的大象群暂不支持此功能，敬请谅解"),
    AIOPS_AUTO_REPLY_ERROR(10034, BizCodeLevel.P2, "Aiops自动回复失败"),
    RESOURCE_NOT_FOUND(10035, BizCodeLevel.P3, "文件不存在或已过期"),
    FRIDAY_QUESTION_RESOLVE_STATE_ERROR(10036, BizCodeLevel.P3, "问题解决状态判断失败"),
    FRIDAY_QUESTION_PATTERN_SUM_ERROR(10037, BizCodeLevel.P3, "问题聚类名称总结失败"),
    CORPUS_SOP_UPDATE_FAIL(10038, BizCodeLevel.P3, "语料SOP模版更新失败"),
    WORKSPACE_NOT_FOUND(10039, BizCodeLevel.P3, "未找到当前工作空间"),
    WORKSPACE_DELETE_ERROR(10040, BizCodeLevel.P3, "删除工作空间失败"),
    DX_MONITOR_QUERY(10041, BizCodeLevel.P3, "大象群聊咨询问题查询失败"),
    SENDMESSAGE_ASUSER_ERROR(10042, BizCodeLevel.P3,"以用户身份发送消息失败"),
    ROBOT_NOT_IN_GROUP(10043, BizCodeLevel.P3,"小助手不在该群，请先将[知识库语料小助手]拉入群聊"),
    QUERY_DX_USER_INFO_ERROR(10044, BizCodeLevel.P3,"查询员工身份信息失败"),
    VALIDATE_MONITOR(10045, BizCodeLevel.P3,"监控权限验证失败"),
    ADD_MONITOR_GROUP_ERROR(10046, BizCodeLevel.P3,"新增监控组任务失败"),
    UPDATE_MONITOR_GROUP_ERROR(10047, BizCodeLevel.P3,"更新监控组任务失败"),
    WORKSPACE_ALREADY_BOUND(10048, BizCodeLevel.P3, "该工作空间已绑定，请勿重复绑定"),
    FORBIDDEN(10049, BizCodeLevel.P3, "当前用户无当前工作空间权限"),
    KNOWLEDGE_UPDATE_FAIL(10050, BizCodeLevel.P3, "自定义背景知识更新失败"),
    RULE_UPDATE_FAIL(10051, BizCodeLevel.P3, "自定义规则更新失败"),
    DELETE_AK_ERROR(10052, BizCodeLevel.P3, "删除AK失败"),
    ORG_ERROR(10053, BizCodeLevel.P3, "ORG操作失败")
    ;

    private int code;
    private int level;
    private String message;

    private String category;

    private static Map<Integer, BizCode> map = Maps.newHashMap();


    BizCode(int code, int level, String message) {
        this.code = code;
        this.level = level;
        this.message = message;
    }

    BizCode(int code, int level, String message, String category) {
        this.code = code;
        this.level = level;
        this.message = message;
        this.category = category;
    }

    static {
        for (BizCode bizCode : BizCode.values()) {
            map.put(bizCode.getCode(), bizCode);
        }
    }

    public int getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    public int getLevel() {
        return level;
    }

    public String getCategory() {
        return category;
    }

    public String getCodeOrCategory() {
        if (StringUtils.isNotEmpty(category)) {
            return category;
        }

        return String.format("error%s", code);
    }

    @Override
    public String toString() {
        return "BizCode{" +
                "code=" + code +
                ", level=" + level +
                ", message='" + message + '\'' +
                '}';
    }

    public static BizCode findByCode(int code) {
        return map.get(code);
    }



}
