package com.meituan.banma.llm.corpus.api.client;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.meituan.banma.llm.corpus.api.exception.LlmCorpusException;
import org.apache.thrift.TException;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;

@InterfaceDoc(
        type = "octo.thrift",
        displayName = "Banma LLM Corpus Process Thrift Service",
        description = "提供与Banma LLM语料库处理相关的服务",
        scenarios = "Banma LLM语料库处理",
        authors = {"daili07"}
)
@ThriftService
public interface BmLlmCorpusProcessThriftService {

    @MethodDoc(
            displayName = "查询最新内容",
            description = "根据给定的rgId和ak查询最新的内容",
            parameters = {
                    @ParamDoc(name = "rgId", description = "语料库组ID", example = {"12345"}),
                    @ParamDoc(name = "ak", description = "访问密钥", example = {"Dhcc0cs8yIYZJpzIP+oD3wsC1iz6KKYzeEFNR2zQLM0="})
            },
            returnValueDescription = "返回包含最新内容的HTML字符串"
    )
    @ThriftMethod
    String queryLatestContentByRgId(long rgId, String ak) throws TException, LlmCorpusException;


    @MethodDoc(
            displayName = "测试字符串",
            description = "返回一个测试用的HTML字符串",
            parameters = {},
            returnValueDescription = "返回一个测试用的HTML字符串"
    )
    @ThriftMethod
    String testString() throws TException, LlmCorpusException;

    @MethodDoc(
            displayName = "测试随机字符串",
            description = "返回一个随机选择的问题和解决方案",
            parameters = {},
            returnValueDescription = "返回一个随机选择的问题和解决方案"
    )
    @ThriftMethod
    String testRandomString() throws TException, LlmCorpusException;
}
