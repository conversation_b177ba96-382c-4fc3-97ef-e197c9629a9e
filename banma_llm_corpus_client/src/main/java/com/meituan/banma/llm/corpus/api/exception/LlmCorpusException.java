package com.meituan.banma.llm.corpus.api.exception;


import com.meituan.banma.llm.corpus.api.constants.BizCode;
import org.apache.commons.lang3.StringUtils;

/**
 * <p>业务自定义异常</p>
 **/
public class LlmCorpusException extends Exception {

    public static final String RPC_INNER_HINTS = "%s调用失败";

    private int code;
    /**
     * 数据部分
     */
    private transient Object data = null;

    public LlmCorpusException(int code, String message) {
        super(message);
        this.code = code;
    }

    public LlmCorpusException(int code, Object data, String message) {
        super(message);
        this.code = code;
        this.data = data;
    }

    public LlmCorpusException(int code, String message, Throwable throwable) {
        super(message, throwable);
        this.code = code;
    }

    public int getCode() {
        return code;
    }

    public Object getData() {
        return data;
    }

    /**
     * 构建业务异常
     *
     * @param bizCode
     * @param message
     * @return
     */
    public static LlmCorpusException buildWithMsg(BizCode bizCode, String message) {
        if (bizCode == null) {
            throw new IllegalStateException("bizCode is null");
        }
        if (StringUtils.isBlank(message)) {
            return new LlmCorpusException(bizCode.getCode(), bizCode.getMessage());
        } else {
            return new LlmCorpusException(bizCode.getCode(), message);
        }
    }

    public static LlmCorpusException buildWithMsg(int bizCode, String message) {
        return new LlmCorpusException(bizCode, message);
    }


    public static LlmCorpusException buildRpc(BizCode bizCode, String rpcCall) {
        return new LlmCorpusException(bizCode.getCode(), String.format(RPC_INNER_HINTS, rpcCall));
    }

    public static LlmCorpusException buildWithThrowable(BizCode bizCode,Throwable throwable){
        return new LlmCorpusException(bizCode.getCode(),bizCode.getMessage(),throwable);
    }
    public static LlmCorpusException buildWithData(BizCode bizCode, Object data) {
        return buildWithData(bizCode, data, bizCode.getMessage());
    }

    public static LlmCorpusException buildWithData(BizCode bizCode, Object data, String message) {
        if (StringUtils.isBlank(message)) {
            return new LlmCorpusException(bizCode.getCode(), data, bizCode.getMessage());
        } else {
            return new LlmCorpusException(bizCode.getCode(), data, message);
        }

    }

    public static LlmCorpusException buildWithData(int code, String message, Object data) {
        return new LlmCorpusException(code, data, message);
    }

    public static LlmCorpusException buildWithMsg(BizCode code) {
        return new LlmCorpusException(code.getCode(), code.getMessage());
    }

    /**
     * 构建业务异常
     *
     * @param bizCode
     * @param args
     * @return
     */
    public static LlmCorpusException build(BizCode bizCode, Object... args) {
        if (bizCode == null) {
            throw new IllegalStateException("bizCode is null");
        }
        if (args == null) {
            return new LlmCorpusException(bizCode.getCode(), bizCode.getMessage());
        } else {
            String msg = bizCode.getMessage();
            if (StringUtils.isBlank(msg)) {
                return new LlmCorpusException(bizCode.getCode(), bizCode.getMessage());
            } else {
                msg = String.format(msg, args);
                return new LlmCorpusException(bizCode.getCode(), msg);
            }
        }
    }

    public String toErrorString() {
        return "{code: "
                + this.code
                + ", message: "
                + this.getMessage()
                + "}";
    }

}