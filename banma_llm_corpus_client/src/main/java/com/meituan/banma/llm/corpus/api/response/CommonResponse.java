package com.meituan.banma.llm.corpus.api.response;

import com.facebook.swift.codec.ThriftConstructor;
import com.facebook.swift.codec.ThriftField;

public class CommonResponse<T> {
    public static final int SUCCESS_CODE = 0;

    private Integer code;
    private String message;
    private T data;

    @ThriftConstructor
    public CommonResponse() {}

    @ThriftField(value = 1, requiredness = ThriftField.Requiredness.REQUIRED)
    public Integer getCode() {
        return this.code;
    }

    @ThriftField
    public void setCode(Integer code) {
        this.code = code;
    }

    @ThriftField(value = 2, requiredness = ThriftField.Requiredness.REQUIRED)
    public String getMessage() {
        return this.message;
    }

    @ThriftField
    public void setMessage(String message) {
        this.message = message;
    }

    @ThriftField(value = 3, requiredness = ThriftField.Requiredness.OPTIONAL)
    public T getData() {
        return this.data;
    }

    @ThriftField
    public void setData(T data) {
        this.data = data;
    }

}
