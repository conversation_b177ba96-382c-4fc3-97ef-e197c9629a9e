package com.meituan.banma.llm.corpus.api.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

import java.util.List;

@TypeDoc(
        description = "批量图片识别",
        authors = {"daili07"}
)
@ThriftStruct
public class ImageToTextBatchRequest {
    @FieldDoc(
            description = "prompt",
            example = {"xxx"}
    )
    @JsonProperty("prompt")
    private String prompt;
    @FieldDoc(
            description = "图片url列表",
            example = {"http://xxx"}
    )
    @JsonProperty("imageUrls")
    private List<String> imageUrls;

    @FieldDoc(
            description = "misId",
            example = {"xxx"}
    )
    @JsonProperty("misId")
    private String misId;

    @ThriftField(1)
    public String getPrompt() {
        return prompt;
    }
    @ThriftField
    public void setPrompt(String prompt) {
        this.prompt = prompt;
    }
    @ThriftField(2)
    public List<String> getImageUrls() {
        return imageUrls;
    }
    @ThriftField
    public void setImageUrls(List<String> imageUrls) {
        this.imageUrls = imageUrls;
    }
    @ThriftField(3)
    public String getMisId() {
        return misId;
    }
    @ThriftField
    public void setMisId(String misId) {
        this.misId = misId;
    }
}
