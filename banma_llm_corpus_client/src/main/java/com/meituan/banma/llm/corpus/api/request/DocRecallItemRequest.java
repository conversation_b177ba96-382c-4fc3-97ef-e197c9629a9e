package com.meituan.banma.llm.corpus.api.request;

import com.facebook.swift.codec.ThriftConstructor;
import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;

@ThriftStruct
public class DocRecallItemRequest {
    @FieldDoc(
            description = "数据id",
            example = {"1234"}
    )
    private String dataId;
    @FieldDoc(
            description = "文本",
            example = {"1234"}
    )
    private String text;
    @FieldDoc(
            description = "文档名称",
            example = {"1234"}
    )
    private String documentName;

    @ThriftConstructor
    public DocRecallItemRequest(String dataId, String text, String documentName){
        this.dataId = dataId;
        this.text = text;
        this.documentName = documentName;
    }
    public DocRecallItemRequest(){}
    @ThriftField(1)
    public String getDataId() {
        return dataId;
    }
    @ThriftField
    public void setDataId(String dataId) {
        this.dataId = dataId;
    }
    @ThriftField(2)
    public String getText() {
        return text;
    }
    @ThriftField
    public void setText(String text) {
        this.text = text;
    }
    @ThriftField(3)
    public String getDocumentName() {
        return documentName;
    }
    @ThriftField
    public void setDocumentName(String documentName) {
        this.documentName = documentName;
    }
}
