<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:util="http://www.springframework.org/schema/util"
       xmlns="http://www.springframework.org/schema/beans"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
		http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.0.xsd
		 http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util-3.0.xsd">

    <import resource="classpath:banma_common_client.xml" />

    <bean id="bmLlmCorpusThriftPoolConfig"
          class="com.meituan.service.mobile.mtthrift.client.pool.MTThriftPoolConfig">
        <property name="maxActive" value="100"/>
        <property name="maxIdle" value="20"/>
        <property name="minIdle" value="1"/>
        <property name="maxWait" value="5000"/>
        <property name="testOnBorrow" value="true"/>
        <property name="testOnReturn" value="false"/>
        <property name="testWhileIdle" value="false"/>
    </bean>

    <bean id="dxGroupChatThriftService"
          class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="bmLlmCorpusThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.meituan.banma.llm.corpus.api.client.DxGroupChatThriftService"/>
        <property name="timeout" value="20000"/><!-- thrift rpc 超时时间（毫秒） -->
        <property name="appKey" ref="appKey"/>
        <property name="remoteAppkey" value="com.sankuai.deliverypaotui.llm.corpus"/>
        <property name="filterByServiceName" value="true" /> <!-- 默认false -->
        <property name="nettyIO" value="true"/> <!-- 开启 Netty IO  -->
    </bean>

</beans>
